const fs = require('fs')
const path = require('path')
const chalk = require('chalk')
const { cdn } = require('./build')

class AddCdnPlugin {
  // compiler => webpack配置对象
  apply(compiler) {
    if (!cdn) {
      return
    }
    // console.log('AddCdnPlugin 启动')
    // 调用emit勾子，挂载函数
    // compilation => 此次打包的上下文
    compiler.hooks.done.tap('AddCdnPlugin', (compilation) => {
      // 遍历资源文件信息
      // 键name为每个资源的名称
      let filePath = path.resolve(__dirname, '../dist')
      fs.readdir(filePath, (err, files) => {
        if (err) {
          console.log(chalk.yellow(`读取文件异常: \n ${err.message} \n`))
          return
        }
        files.forEach((filename) => {
          if (filename === 'index.html') {
            let filedir = path.resolve(filePath, filename)
            fs.readFile(filedir, 'utf-8', (err, data) => {
              if (err) {
                console.log(chalk.yellow(`读取index.html文件失败: \n ${err.message} \n`))
                return
              }
              let res = data
              let hasCdnGlobal = res.indexOf('src="https://global-res')
              let hasCdnUs = res.indexOf('src="https://us-res')
              res = res.replace(/service-worker/g, `service-worker00`)
              console.log(chalk.cyan(`是否包含 global:${hasCdnGlobal} us:${hasCdnUs}  \n`))
              if (hasCdnGlobal === -1 && hasCdnUs === -1) {
                res = res.replace(/includes\//g, `${cdn}/includes/`).replace(/static\/js\//g, `${cdn}/static/js/`)
              } else {
                return
              }
              fs.writeFile(filedir, res, (err) => {
                if (err) {
                  console.log(chalk.yellow(`写入index.html文件失败: \n ${err.message} \n`))
                  return
                }
                console.log(chalk.cyan(`写入index.html文件成功 ${cdn}  \n`))
              })
            })
          }
        })
      })
    })
  }
}

module.exports = AddCdnPlugin
