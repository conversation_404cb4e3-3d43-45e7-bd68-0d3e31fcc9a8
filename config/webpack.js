const path = require('path')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const webpack = require('webpack')
const AddCdnPlugin = require('./addCdnPlugin')
const { cdn } = require('./build')
const isQA = process.env.BAOXIAOMI_IS_QA === 'true' ? true : false

// ****************************************************************************

let cdnObj = {}
if (cdn) {
  cdnObj = {
    publicPath: cdn,
  }
}

/** @type {Object} 基础配置 */
const configBase = {
  entry: {
    /**
     * 自定入口文件，需要手动编写使用逻辑
     * - 该模板项目中，本 `critical` 入口的结果会被自动写入到 HTML 结果内，位于 `<body>` 标签中所有自动插入的 `<script>` 标签之前
     * - 详见模板文件 `/src/index.ejs` 内的 `<%- content('critical.js') %>`
     */
    critical: [path.resolve(__dirname, '../src/critical.js')],

    /**
     * Koot.js 会自动加入一个名为 `client` 的入口，其中包含所有 React 相关逻辑
     * - 模板中的 `<%- inject.scripts %>` 会被自动替换为 `client` 入口的相关内容
     */
    svgs: [path.resolve(__dirname, '../src/app/svgs.js')],
  },

  module: {
    rules: [
      /**
       * Koot.js 会为以下类型的文件自动添加 loader，无需进行配置
       * - `js` `mjs` `jsx`
       * - `css` `sass` `less`
       */
      {
        test: /\.(ico|gif|jpg|jpeg|png|webp)$/,
        use: [
          {
            // loader: 'file-loader?context=static&name=assets/[hash:32].[ext]',
            loader: 'file-loader',
            context: 'static',
            name: 'assets/[hash:32].[ext]',
            exclude: /node_modules/,
            options: {
              esModule: false,
            },
          },
        ],
        // loader: 'file-loader?context=static&name=assets/[hash:32].[ext]',
        // exclude: /node_modules/,
        // options: {
        //     esModule: false
        // }
      },
      {
        // test: /\.svg$/,
        // loader: 'svg-url-loader',
        // exclude: /node_modules/,
        test: /\.(eot|woff|woff2|ttf|svg)$/,
        use: [
          {
            loader: 'svg-url-loader',
            name: 'fonts/[name].[ext]',
            exclude: /node_modules/,
            options: {
              esModule: true,
              noquotes: true,
            },
          },
        ],
      },
      {
        loader: 'webpack-ant-icon-loader',
        enforce: 'pre',
        options: {
          chunkName: 'antd-icons',
        },
        include: [require.resolve('@ant-design/icons/lib/index')],
      },
    ],
  },

  plugins: [new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/)],
}

// ****************************************************************************

/**
 * 生成 Webpack 配置
 * @returns {Object} Webpack 配置
 */
const factoryConfig = async () => {
  // 针对：开发环境
  if (process.env.WEBPACK_BUILD_ENV === 'dev') return configBase

  const config = {
    entry: {
      commons: [
        'react',
        'react-dom',

        'redux',
        'redux-thunk',
        'react-redux',

        'react-router',
        'react-router-redux',

        'react-transition-group',

        'metas',
        'classnames',
        'js-cookie',
        'axios',
      ],
      ...configBase.entry,
    },

    output: {
      filename: `core.[chunkhash].js`,
      chunkFilename: `chunk.[chunkhash].js`,
      ...cdnObj,
    },

    optimization: {
      splitChunks: {
        cacheGroups: {
          commons: {
            name: 'commons',
            chunks: 'initial',
            minChunks: 2,
          },
        },
      },
    },

    plugins: [
      ...configBase.plugins,
      new CopyWebpackPlugin(
        [
          // {
          //     from: path.resolve(__dirname, '../src/assets/app-icon-100.png'),
          //     to: '../includes/assets/'
          // },
          // {
          //     from: path.resolve(__dirname, '../src/app/manifest.json'),
          //     to: '../public/'
          // },
          // {
          //     from: path.resolve(src, 'app/assets/svg-symbols/symbol-defs.svg'),
          //     to: 'assets/symbols.svg'
          // },
        ],
        {},
      ),
      new AddCdnPlugin(),
    ],
  }
  if (isQA) {
    config.devtool = 'source-map'
  }
  // 针对：生产环境
  // `entry` 项仅针对：客户端
  return Object.assign({}, configBase, config)
}

module.exports = factoryConfig
