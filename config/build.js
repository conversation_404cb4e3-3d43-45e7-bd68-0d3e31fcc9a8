const fs = require('fs')
const path = require('path')

let cdn = '' // test dev 国内正式 三个环境
if (process.env.target === 'g') {
  cdn = 'https://global-res.orionstar.com/web/robot_home' // global
} else if (process.env.target === 'us') {
  cdn = 'https://us-res.orionstar.com/web/robot_home' // us
}

/**
 * 创建路径
 * @param {string} path 路径
 */
function mkdir(path) {
  return new Promise((resolve, reject) => {
    fs.mkdir(path, (err) => {
      if (err) {
        console.log(err)
        resolve(false)
      } else {
        resolve(true)
      }
    })
  })
}
/**
 * 剪切路径
 * @param {string} oldPath 旧路径
 * @param {string} newPath 新路径
 */

function rename(oldPath, newPath) {
  return new Promise((resolve, reject) => {
    fs.rename(oldPath, newPath, (err) => {
      if (err) {
        console.log(err)
        resolve(false)
      } else {
        resolve(true)
      }
    })
  })
}
function copyFolder(copiedPath, resultPath, direct) {
  if (!direct) {
    // copiedPath = path.join(__dirname, copiedPath)
    // resultPath = path.join(__dirname, resultPath)
  }

  function createDir(dirPath) {
    fs.mkdirSync(dirPath)
  }

  if (fs.existsSync(copiedPath)) {
    createDir(resultPath)
    /**
     * @des 方式一：利用子进程操作命令行方式
     */
    // child_process.spawn('cp', ['-r', copiedPath, resultPath])

    /**
     * @des 方式二：
     */
    const files = fs.readdirSync(copiedPath, { withFileTypes: true })
    for (let i = 0; i < files.length; i++) {
      const cf = files[i]
      const ccp = path.join(copiedPath, cf.name)
      const crp = path.join(resultPath, cf.name)
      if (cf.isFile()) {
        /**
         * @des 创建文件,使用流的形式可以读写大文件
         */
        const readStream = fs.createReadStream(ccp)
        const writeStream = fs.createWriteStream(crp)
        readStream.pipe(writeStream)
      } else {
        try {
          /**
           * @des 判断读(R_OK | W_OK)写权限
           */
          fs.accessSync(path.join(crp, '..'), fs.constants.W_OK)
          copyFolder(ccp, crp, true)
        } catch (error) {
          console.log('folder write error:', error)
        }
      }
    }
  } else {
    console.log('do not exist path: ', copiedPath)
  }
}

const isQA = process.env.BAOXIAOMI_IS_QA === 'true' ? true : false
const after = async () => {
  await mkdir(path.resolve(__dirname, '../dist/public/'))
  // 移动index.html
  await rename(path.resolve(__dirname, '../dist/index.html'), path.resolve(__dirname, '../dist/public/index.html'))
  // 移动service-worker.js
  await rename(
    path.resolve(__dirname, '../dist/service-worker.js'),
    path.resolve(__dirname, '../dist/public/service-worker.js'),
  )
  // 移动includes
  await rename(path.resolve(__dirname, '../dist/includes/'), path.resolve(__dirname, '../dist/public/includes/'))
  copyFolder(path.resolve(__dirname, '../static/'), path.resolve(__dirname, '../dist/public/static/'))
}

/**
 * 获取所有语言
 */
function getLanguages() {
  const filePath = '../jiedai_web_locales/locales'
  if (!filePath && typeof filePath != 'string') {
    return []
  }
  var files = fs.readdirSync(filePath)
  var languages = files.filter(function (item) {
    return item.endsWith('.js')
  })
  languages = languages.map(function (item) {
    return [item.split('.')[0], filePath + '/' + item]
  })
  return languages
}
module.exports = {
  after,
  getLanguages,
  cdn,
}
