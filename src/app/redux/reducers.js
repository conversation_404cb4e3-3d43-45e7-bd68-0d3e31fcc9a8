/**
 * Reducer 命名规则
 *
 * 页面专用存储空间：名称以 _ 开头，如 _replies
 */

import user from '@api/user/reducer'
import view from '@api/view/reducer'
import modal from '@api/modal/reducer'
import robots from '@api/robots/reducer'

import _replies from '@api/_replies/reducer'
import _newReplies from '@api/_newReplies/reducer'
import _reading from '@api/_reading/reducer'
import _visitors from '@api/_visitors/reducer'
import _permissions from '@api/_permissions/reducer'
import _staff from '@api/_staff/reducer'
import _awaken from '@api/_awaken/reducer'
import _guide from '@api/_guide/reducer'
import _reception from '@api/_reception/reducer'
import _module from '@api/_scene/reducer'
import _advert from '@api/_advert/reducer'
import _home from '@api/_home/reducer'
import _corpus from '@api/_corpus/reducer'
import _propaganda from '@api/_propaganda/reducer'
import _businessGuide from '@api/_businessGuide/reducer'
import _routeConfig from '@api/_route/reducer'
import _ttsSetting from '@api/_ttsSetting/reducer'
import _expostor from '@api/_expostor/reducer'
import _map from '@api/_map/reducer'
import _report from '@api/_report/reducer'

export default {
  // 数据相关
  user,
  robots,

  // UI 相关
  modal,

  // 页面 / 视图
  view,
  _replies, // MeUI自定义 / 快捷回复
  _newReplies,
  _reading,
  _visitors, // 用户管理 / 访客管理
  _permissions, // 用户管理 / 管理员权限
  _staff, // 用户管理 / 管理员权限
  _awaken, //自定义唤醒页
  _guide, //自定义引导/ 自定义场景
  _reception, //自定义引导/ 自定义场景
  _advert, //自定义/广告宣传
  _module,
  _home, //自定义首页
  _corpus, //自定义语料
  _propaganda, //自定义/宣传介绍
  _businessGuide, //业务指南
  _routeConfig, //路由配置（后端返回的值）
  _ttsSetting, //快速播报
  _expostor, //讲解员
  _map, //地图
  _report, //播报
}
