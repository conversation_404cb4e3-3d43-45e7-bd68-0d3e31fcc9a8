import routeCheck from 'koot/React/route-check'
import Root from '@ui/app'
function getQueryVal(k, search, uri) {
  if (search == undefined) {
    search = location.search
  }
  var reg = new RegExp('(^|&)' + k + '=([^&]*)(&|$)')
  var s = (uri && '?' + uri.split('?')[1]) || search
  var r = s.slice(1).match(reg)
  if (r != null) {
    return decodeURIComponent(r[2])
  }
  return null
}
let routes = {
  // path: __SPA__ ? '/' : '',
  component: Root,
  name: 'app-root',
  indexRoute: {
    name: '基础状态',
    icon: 'secend_skill_home',
    iconActive: 'secend_skill_home_click',
    id: '100',
  },
  childRoutes: [
    {
      path: 'home',
      name: '自定义首页',
      id: '103',
      show: true,
      icon: 'fix_robot',
      iconActive: 'fix_robot_click',
      getComponent: (nextState, cb) => {
        require.ensure(
          [],
          (require) => {
            let robot_type = getQueryVal('robotType') //"=bao_xiao_mi"
            let isKTV = location.href.indexOf('ktv.ainirobot.com') > -1 ? true : false
            isKTV = false
            if (routeCheck(nextState))
              cb(
                null,
                true || (robot_type == 'bao_xiao_mi' && !isKTV)
                  ? require('@ui/pages/meui/homeOptimized').default
                  : require('@ui/pages/meui/home').default,
              )
          },
          '自定义首页',
        )
      },
    },
    {
      path: 'customHome',
      name: '自定义首页',
      id: '103',
      show: true,
      icon: 'fix_robot',
      iconActive: 'fix_robot_click',
      getComponent: (nextState, cb) => {
        require.ensure(
          [],
          (require) => {
            if (routeCheck(nextState)) cb(null, require('@ui/pages/meui/customHome').default)
          },
          '自定义首页',
        )
      },
    },
    {
      path: 'homeOpt',
      name: '自定义首页',
      id: '103',
      show: true,
      icon: 'fix_robot',
      iconActive: 'fix_robot_click',
      getComponent: (nextState, cb) => {
        require.ensure(
          [],
          (require) => {
            if (routeCheck(nextState)) cb(null, require('@ui/pages/meui/homeOptimized').default)
          },
          '自定义首页',
        )
      },
    },
    {
      path: 'homeold',
      name: '自定义首页',
      id: '103',
      show: true,
      icon: 'fix_robot',
      iconActive: 'fix_robot_click',
      getComponent: (nextState, cb) => {
        require.ensure(
          [],
          (require) => {
            if (routeCheck(nextState)) cb(null, require('@ui/pages/meui/home').default)
          },
          '自定义首页',
        )
      },
    },
    {
      path: 'gov',
      name: '自定义首页',
      id: '103',
      show: true,
      icon: 'fix_robot',
      iconActive: 'fix_robot_click',
      getComponent: (nextState, cb) => {
        require.ensure(
          [],
          (require) => {
            if (routeCheck(nextState)) cb(null, require('@ui/pages/meui/home/<USER>').default)
          },
          '自定义首页',
        )
      },
    },
    {
      path: 'tourHome',
      name: '导览首页',
      id: '103',
      show: true,
      icon: 'fix_robot',
      iconActive: 'fix_robot_click',
      getComponent: (nextState, cb) => {
        require.ensure(
          [],
          (require) => {
            if (routeCheck(nextState)) cb(null, require('@ui/pages/meui/tourHome').default)
          },
          '导览首页',
        )
      },
    },

    {
      path: 'minihome',
      name: 'mini首页',
      id: '103',
      show: true,
      icon: 'fix_robot',
      iconActive: 'fix_robot_click',
      getComponent: (nextState, cb) => {
        require.ensure(
          [],
          (require) => {
            if (routeCheck(nextState)) cb(null, require('@ui/pages/meui/miniHome').default)
          },
          '导览首页',
        )
      },
    },
    {
      path: ':notfound',
      name: '404',
      show: false,
      indexRoute: {
        name: 'notfound',
        show: false,
        icon: '',
        iconActive: '',
        getComponent: (nextState, cb) => {
          require.ensure(
            [],
            (require) => {
              if (routeCheck(nextState)) cb(null, require('@ui/pages/404').default)
            },
            '404',
          )
        },
      },
    },
  ],
}

if (__DEV__) {
  routes.childRoutes.push({
    path: 'dev',
    name: '开发',
    show: true,
    icon: 'side_telecontrol',
    iconActive: 'side_telecontrol_click',
    indexRoute: {
      name: '全局样式',
      show: true,
      icon: 'rd_eraser',
      iconActive: 'rd_eraser_click',
      getComponent: (nextState, cb) => {
        require.ensure(
          [],
          (require) => {
            if (routeCheck(nextState)) cb(null, require('@ui/pages/dev/globals').default)
          },
          '开发/全局样式',
        )
      },
    },
    childRoutes: [
      {
        path: 'components',
        name: '通用组件',
        show: true,
        icon: 'rd_new',
        iconActive: 'rd_new_click',
        getComponent: (nextState, cb) => {
          require.ensure(
            [],
            (require) => {
              if (routeCheck(nextState)) cb(null, require('@ui/pages/dev/components').default)
            },
            '开发/通用组件',
          )
        },
      },
      {
        path: 'forms',
        name: '复用表单',
        show: true,
        icon: 'rd_concentrated',
        iconActive: 'rd_concentrated_click',
        getComponent: (nextState, cb) => {
          require.ensure(
            [],
            (require) => {
              if (routeCheck(nextState)) cb(null, require('@ui/pages/dev/forms').default)
            },
            '开发/复用表单',
          )
        },
      },
      {
        path: 'actions',
        name: '通用操作',
        show: true,
        icon: 'rd_drag',
        iconActive: 'rd_drag_click',
        getComponent: (nextState, cb) => {
          require.ensure(
            [],
            (require) => {
              if (routeCheck(nextState)) cb(null, require('@ui/pages/dev/actions').default)
            },
            '开发/通用操作',
          )
        },
      },
      {
        path: 'icons',
        name: '图标',
        show: true,
        icon: 'rd_pen',
        iconActive: 'rd_pen_click',
        getComponent: (nextState, cb) => {
          require.ensure(
            [],
            (require) => {
              if (routeCheck(nextState)) cb(null, require('@ui/pages/dev/icons').default)
            },
            '开发/图标',
          )
        },
      },
      {
        path: 'title-block',
        name: '页面标题栏',
        show: true,
        icon: 'secend_control_selfchecking',
        iconActive: 'secend_control_selfchecking_click',
        getComponent: (nextState, cb) => {
          require.ensure(
            [],
            (require) => {
              if (routeCheck(nextState)) cb(null, require('@ui/pages/dev/title-block').default)
            },
            '开发/页面标题栏',
          )
        },
      },
      {
        path: 'ajax',
        name: '异步请求',
        show: true,
        icon: 'rd_zoomin',
        iconActive: 'rd_zoomin_click',
        getComponent: (nextState, cb) => {
          require.ensure(
            [],
            (require) => {
              if (routeCheck(nextState)) cb(null, require('@ui/pages/dev/freeajax').default)
            },
            '开发/异步请求',
          )
        },
      },
    ],
  })
}

export default routes
