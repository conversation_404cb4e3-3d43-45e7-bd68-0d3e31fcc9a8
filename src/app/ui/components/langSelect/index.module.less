@import '~base.less';

.component {
  position: relative;
  margin-right: 40px;
  display: flex;

  .dropdown_contents {
    display: inline-block;
    cursor: pointer;
    width: 180px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border: solid 1px #3776ef;
    font-size: 12px;
    border-radius: 8px;

    .label {
      font-size: 14px;
      color: #3776ef;

      &:after {
        right: 6px !important;
        color: @color-theme;
        background: url('@assets/svg-icon/more-theme.svg') no-repeat 50% 50% / contain;
      }
    }

    .menu-container {
      left: 0px;
      top: 7px;
      position: relative;
      width: 100%;
      z-index: 2;

      & > div {
        width: 100%;

        & > div {
          width: 100%;
        }
      }

      .menu {
        max-height: 300px;
        overflow-y: auto;

        ::-webkit-scrollbar {
          width: 6px;
          height: 119px;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.06);
          // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          border-radius: 10px;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
          height: 67px;
          border-radius: 10px;
          background: @color-theme;
          // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
          background: @color-theme;
        }
      }

      .menu-item {
        height: 44px;
        line-height: 44px;
        padding: 0 44px 0 16px;
        cursor: pointer;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        color: #555d61;
        position: relative;

        &:hover,
        &.cur {
          background: rgba(0, 146, 245, 0.1);
        }

        &.cur {
          cursor: default;

          &:after {
            display: block;
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            right: 20px;
            top: 10px;
            background: url('@assets/svg-icon/clear_blue.svg') no-repeat center right;
            background-size: 28px;
            right: 0;
          }
        }
      }
    }
  }

  .help {
    display: flex;
    align-items: center;
    height: 32px;
    // min-width:100px;

    .blue {
      color: #3776ef;
    }

    .icon {
      width: 14px;
      height: 14px;
      margin-left: 10px;
    }

    .helpBtn {
      font-size: 14px;
      color: #3776ef;
      margin-left: 10px;

      &:hover {
        color: #3776ef;
        text-decoration: underline;
      }
    }
  }
}
