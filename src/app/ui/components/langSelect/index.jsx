import React from 'react'
import { extend } from 'koot'
import { SET_CUR_LANG } from '@redux/action-types'
import { Icon, Dropdown, MenuItem } from 'biz-components'
import styles from './index.module.less'
import { Popover } from 'antd'

// Component Class ============================================================

@extend({
  connect: (state) => {
    return {
      langData: state.user.langData,
      curLang: state.user.curLang,
    }
  },
  styles,
})
class LangSelect extends React.Component {
  changeDropDown(curLang) {
    this.props.dispatch({
      type: SET_CUR_LANG,
      data: {
        curLang,
      },
    })
    window.parent.postMessage({ command: 'updateData', data: { lang: JSON.stringify(curLang) } }, '*')
    if (this.props.changeDropDown) {
      const timer = setTimeout(() => {
        timer && clearTimeout(timer)
        this.props.changeDropDown(curLang)
      }, 100)
    }
  }
  render() {
    const { curLang, langData } = this.props
    return (
      <div
        className={this.props.className}
        data-custom-props={this.props.customProps}
        data-class-name={this.props['data-class-name']}
      >
        <Dropdown
          className='dropdown_contents'
          label={curLang ? curLang.name : __('GIVE&ASK_FOR_DIRECTIONS.PLEASE_CHOOSE')}
        >
          {langData.map((el, idx) => (
            <MenuItem
              key={idx}
              className={curLang.lang_code === el.lang_code ? 'cur' : ''}
              children={el.name}
              onClick={this.changeDropDown.bind(this, el)}
            />
          ))}
        </Dropdown>
        <Popover content={__('LANGUAGE_SELECT.TIP')} placement='bottomLeft'>
          <div className='help'>
            <Icon icon='help' className='icon helpIcon blue' />
            <a href='javascript:void(0);' className='helpBtn'>
              {__('customHome.btn.help')}
            </a>
          </div>
        </Popover>
      </div>
    )
  }
}

export default LangSelect
