import React from 'react'
import { extend } from 'koot'

// Component Class ============================================================

class ShowList extends React.Component {
  render() {
    const { data } = this.props
    return (
      <div className='show-list-main'>
        <p className='show-list-main-title'>请将敏感词移除后再保存发布</p>
        <p className='show-list-main-data'>{data}</p>
      </div>
    )
  }
}

export default ShowList
