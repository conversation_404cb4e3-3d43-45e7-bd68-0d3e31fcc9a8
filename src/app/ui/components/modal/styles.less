@import '~base.less';

.component {
  position: relative;
  background: @color-background;
  border-radius: 15px;
  border: 20px solid transparent;

  & > .btn-close {
    @size: 40px;
    position: absolute;
    z-index: 100;
    width: @size;
    height: @size;
    top: (0 - @size / 1);
    right: (0 - @size / 1);
    appearance: none;
    background: @color-accent;
    border: 0;
    border-radius: 50%;
    &:hover {
      background: @color-accent-2;
    }
  }

  & > .wrapper {
    position: relative;
    z-index: 1;
    max-width: 100vw;
    max-width: calc(~'100vw - 80px');
  }

  &[data-modal-id='debug-info'] {
    & > .wrapper {
      overflow: auto;
      width: calc(100vw - 4rem);
      height: calc(100vh - 8rem);
    }
    textarea {
      display: block;
      border: 0;
      width: 100%;
      height: 100%;
    }
  }
  .show-list-main {
    text-align: center;
    .show-list-main-title {
    }
    .show-list-main-data {
      color: #f75f4b;
    }
  }
}
