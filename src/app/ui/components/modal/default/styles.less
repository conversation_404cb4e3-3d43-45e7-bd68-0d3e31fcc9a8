@import '~base.less';
.component {
  &[data-modal-info] {
    cursor: default;
    border-width: 0;
    text-align: center;
    background: #fff;
    min-width: 300px;
    max-width: calc(~'100vw - 30px');
    overflow: hidden;
    box-shadow: 0 0 15px fade(#000, 15%);
    & > .btn-close {
      @gutter: 17px;
      @icon-size: 11px;
      @size: (@icon-size + @gutter * 2);
      width: @size;
      height: @size;
      top: 0;
      right: 0;
      position: absolute;
      background: none transparent;
      opacity: 0.25;
      cursor: pointer;
      &:hover {
        opacity: 0.65;
        background: none transparent;
      }
      &:active {
        opacity: 0.15;
        background: none transparent;
      }
      &:focus {
        outline: 0;
      }
      .icon {
        position: absolute;
        top: @gutter;
        right: @gutter;
        width: @icon-size;
        height: @icon-size;
      }
    }
  }
  & > .title {
    display: block;
    padding: 30px 20px 0 20px;
    font-weight: @font-weight-medium;
    font-size: 20px;
  }
  & > .content {
    border: 30px solid transparent;
    border-top-width: 30px;
    border-bottom-width: 30px;
    font-size: 16px;
    &.is-component {
      text-align: initial;
    }
  }
  & > .title + .content {
    border-top-width: 20px;
  }
  & > .actions {
    display: flex;
    flex-flow: row nowrap;
    overflow: hidden;
    justify-content: flex-end;
    align-items: center;
    padding: 30px 34px;
    &.btnCenter {
      justify-content: center;
    }
    .btn {
      appearance: none;
      display: block;
      padding: 0px 22px;
      border-radius: 18px;
      border: 1px solid transparent;
      font-size: 15px;
      position: relative;
      height: 36px;
      line-height: 36px;
      cursor: pointer;
      &:focus {
        outline: 0;
      }
      transition: all ease 0.2s;
    }
    .btn-confirm {
      color: #fff;
      background-color: @color-theme;
      border-color: @color-theme;
      &:hover {
        background: #6696f3;
        border-color: #6696f3;
      }
    }
    .btn-cancel {
      color: #99a3a8;
      background-color: #fff;
      border-color: #99a3a8;
      margin-right: 30px;
      &:hover {
        color: @color-theme;
        border-color: @color-theme;
      }
    }
  }
}
.component.is-form {
  & > .title {
    font-size: 24px;
    line-height: 33px;
    color: @color-accent;
    padding-top: 25px;
  }
  & > .content {
    border-width: 25px;
  }
  .modal-form {
    display: block;
  }
  .modal-form-submit {
    width: 100%;
    margin-top: 25px;
  }
}
.component.is-only-content {
  & > .title,
  & > .actions {
    display: none;
  }
  & > .content {
    border-width: 25px;
  }
}
