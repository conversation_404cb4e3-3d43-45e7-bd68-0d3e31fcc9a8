import React from 'react'
import classNames from 'classnames'
import { extend } from 'koot'

import { close as modalClose } from '@api/modal'
import Modal from '@ui/components/modal'
import { Icon } from 'biz-components'
import getStore from '@utils/store'

const close = (id) => getStore().dispatch(modalClose(id))

export default extend({
  styles: require('./styles.less'),
})(
  ({
    title,
    children,

    confirm = '确认',
    cancel = '取消',

    id = 'modal-window',

    beforeOnConfirm = false,
    onConfirm = () => {},
    onCancel = () => {},

    ...props
  }) => {
    const hasConfirm = typeof confirm !== 'undefined' && confirm !== false
    const hasCancel = typeof cancel !== 'undefined' && cancel !== false
    const hasAction = hasConfirm || hasCancel

    return (
      <Modal {...props} id={id} hasWrapper={false} closeButton={false} data-modal-info>
        <button
          className='btn btn-close'
          type='button'
          onClick={(evt) => {
            evt.target.blur()
            close(id)
            onCancel()
          }}
          children={<Icon className='icon' icon='map_point_x' />}
        />

        {typeof title !== 'undefined' && <div className='title'>{title}</div>}

        <div
          className={classNames({
            content: true,
            'is-component': React.isValidElement(children),
          })}
        >
          {children}
        </div>

        {hasAction && (
          <div
            className={classNames({
              actions: true,
              btnCenter: props.btnCenter,
            })}
          >
            {hasCancel && (
              <button
                className='btn btn-cancel'
                type='button'
                onClick={(evt) => {
                  evt.target.blur()
                  close(id)
                  onCancel()
                }}
                children={cancel}
              />
            )}
            {hasConfirm && (
              <button
                className='btn btn-confirm'
                type='button'
                onClick={(evt) => {
                  evt.target.blur()
                  if (beforeOnConfirm) {
                    beforeOnConfirm().then((res) => {
                      close(id)
                      onConfirm()
                    })
                  } else {
                    close(id)
                    onConfirm()
                  }
                }}
                children={confirm}
              />
            )}
          </div>
        )}
      </Modal>
    )
  },
)
