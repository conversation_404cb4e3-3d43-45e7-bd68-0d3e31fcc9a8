import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import { close as modalClose } from '@api/modal'

@connect()
@extend({
  styles: require('./styles.less'),
})
export default class Modal extends React.Component {
  componentDidMount() {
    if (__QA__ && !this.id) console.warn('🐾 Please give Modal component property: id')

    if (this.closeAfter) {
      this.timeoutCloseAfter = setTimeout(() => {
        this.close()
      }, this.closeAfter)
    }
  }
  componentWillUnmount() {
    if (this.timeoutCloseAfter) clearTimeout(this.timeoutCloseAfter)
  }

  close() {
    if (this.id) this.props.dispatch(modalClose(this.id))
  }

  render() {
    const {
      children,

      id,
      'data-modal-id': modalId,

      closeButton = true,
      closeAfter = undefined,

      hasWrapper = true,

      ...props
    } = this.props

    // console.log('modalId', modalId)

    this.id = id || modalId || ''
    this.closeAfter = closeAfter

    delete props.dispatch

    const BtnClose = React.isValidElement(closeButton) ? closeButton : undefined
    const isDebug = this.id === 'debug-info'
    const content = isDebug ? <textarea defaultValue={children} /> : children

    return (
      <div {...props} data-modal-id={this.id}>
        {BtnClose &&
          React.cloneElement(BtnClose, {
            onClick: () => this.close(),
          })}
        {closeButton && typeof this.id !== 'undefined' && !BtnClose && (
          <button type='button' className='btn-close' onClick={() => this.close()} />
        )}

        {hasWrapper && <div className='wrapper'>{content}</div>}
        {!hasWrapper && content}
      </div>
    )
  }
}
