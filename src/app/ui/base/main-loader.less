#main-loader {
  @size: 64px;
  @color: #3776ef;
  @width: 4px;
  @duration: 0.75s;

  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f9fafa;
  z-index: @z-index-main-loader;
  // transition: opacity 1s linear;
  &.fading-out {
    animation: fade-out 0.3s linear;
    // opacity: 0;
  }

  &::after {
    position: absolute;
    // content: '\6b63\5728\52a0\8f7d\4e2d\ff0c\8bf7\7a0d\540e\000d\000a\2026';
    content: 'Loading...';
    color: @color;
    top: 50%;
    left: 0;
    right: 0;
    line-height: 25px;
    margin-top: 20px;
    text-align: center;
    letter-spacing: 1px;
  }

  & > span {
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    margin-left: (0 - @size / 2);
    margin-top: (0 - @size / 2 - 20px);
    width: (@size / 2);
    height: @size;
    overflow: hidden;
    transform-origin: 100% 50%;
    box-sizing: border-box;
    animation: spinning @duration linear infinite;

    &::before {
      display: block;
      content: '';
      box-sizing: inherit;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: -100%;
      border-radius: 50%;
      border: @width solid transparent;
      border-top-color: @color;
      border-left-color: @color;
      animation: spinning-inner (@duration * 2) ease-in infinite;
    }
  }
}

@keyframes spinning-inner {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(130deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
