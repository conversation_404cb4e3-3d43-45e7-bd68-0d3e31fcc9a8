@root-font-size: 20px;
@base-font-size-small: 14px;
@base-font-size-medium: 24px;
@base-font-size: 16px;
@base-font-weight: 400;
@base-line-height: 21px;
@base-transition-duration: 0.2s;
@base-transition-timing-function: ease-out;
@base-border-radius: 3px;
@icon-size: 16px;

// FONT
// ref: http://zenozeng.github.io/fonts.css/
@fontbase-hei: 'Liberation Sans', 'Microsoft YaHei UI', 'Microsoft YaHei', 'Hiragino Sans GB', 'Wenquanyi Micro Hei',
  'WenQuanYi Zen Hei', 'ST Heiti', SimHei, 'WenQuanYi Zen Hei Sharp';
// 黑体
// font-family: "Helvetica Neue", Helvetica, "Nimbus Sans L", Arial, "Liberation Sans", "Hiragino Sans GB", "Microsoft YaHei", "Wenquanyi Micro Hei", "WenQuanYi Zen Hei", "ST Heiti", <PERSON><PERSON><PERSON><PERSON>, "WenQuanYi Zen Hei Sharp", sans-serif;
@fontbase-kai: 'Liberation Serif', STKaiti, 'AR PL UKai CN', 'AR PL UKai HK', 'AR PL UKai TW', 'AR PL UKai TW MBE',
  'AR PL KaitiM GB', KaiTi, 'KaiTi_GB2312', 'TW\-Kai';
// 楷体
// font-family: Baskerville, Georgia, "Liberation Serif", STKaiti, "AR PL UKai CN", "AR PL UKai HK", "AR PL UKai TW", "AR PL UKai TW MBE", "AR PL KaitiM GB", KaiTi, KaiTi_GB2312, TW\-Kai, serif;
@fontbase-song: STSong, 'AR PL SungtiL GB', NSimSun, SimSun, 'TW\-Sung', 'WenQuanYi Bitmap Song', 'AR PL UMing CN',
  'AR PL UMing HK', 'AR PL UMing TW', 'AR PL UMing TW MBE';
// 宋体
// font-family: Georgia, "Nimbus Roman No9 L", STSong, "AR PL SungtiL GB", NSimSun, SimSun, TW\-Sung, "WenQuanYi Bitmap Song", "AR PL UMing CN", "AR PL UMing HK", "AR PL UMing TW", "AR PL UMing TW MBE", serif;
@fontbase-fang: 'Liberation Serif', STFangsong, FangSong, 'FangSong_GB2312', 'CWTEX\-F';
// 仿宋
// font-family: Baskerville, "Times New Roman", "Liberation Serif", STFangsong, FangSong, FangSong_GB2312, CWTEX\-F, serif;
@fontbase-ming: 'AR PL UMing CN', 'AR PL UMing HK', 'AR PL UMing TW', 'AR PL UMing TW MBE', PMingLiU, MingLiU;
// 明体
// font-family: Georgia, "Nimbus Roman No9 L", "AR PL UMing CN", "AR PL UMing HK", "AR PL UMing TW", "AR PL UMing TW MBE", PMingLiU, MingLiU, serif;

// Better Helvetica - https://css-tricks.com/snippets/css/better-helvetica/
@font-family-sans: Roboto, 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, 'Nimbus Sans L',
  Arial, 'Lucida Grande', @fontbase-hei, sans-serif, caption;
//@font-family-sans:			Metronic, "Myriad Pro", Myriad, Arial, "Lucida Grande", @fontbase-hei ,sans-serif, caption;
@font-family-title: 'Segoe UI', Segoe, @font-family-sans;
@font-famliy-serif-number: Baskerville, Georgia, 'Times New Roman', @fontbase-hei, serif, caption;
@font-famliy-serif-title: Baskerville, 'Times New Roman', @fontbase-kai, serif, caption;
//@font_famliy_Song:		Baskerville, "Times New Roman", "Liberation Serif", "FZDaBiaoSong-b06regular", "MS PMincho", STKaiti, "AR PL UKai CN", "AR PL UKai HK", "AR PL UKai TW", "AR PL UKai TW MBE", "AR PL KaitiM GB", KaiTi, "KaiTi_GB2312", "TW\-Kai", serif;
@font-famliy-Hei: 'Helvetica Neue', Helvetica, 'Nimbus Sans L', Arial, @fontbase-hei, 'Meiryo', sans-serif;
@font-famliy-Song: @font-famliy-Hei;

@font-weight-medium: 500;

// ease
@ease-out-sine: cubic-bezier(0.39, 0.575, 0.565, 1);

// Screen width breaking point
@screen-width-min: 1000px;
@layout-width-max: 1440px;
@layout-height-max: 760px;
@screen-t0: 1290px;
@screen-t1: 1050px;
@screen-tablet: 850px;
@screen-phone: 660px;
@screen-phone-small: 480px;

// sizing
@nav-width: 180px;
@nav-all-width: 240px;
@nav-item-height: 46px;
@header-height: 60px;
@main-gutter-horizontal: 80px;
@main-gutter-horizontal-small: 40px;
@main-gutter-horizontal-small-breakpoint: @screen-t0;
@main-gutter-vertical: (@header-height + 19);

// z-index
@z-index-app: 10;
@z-index-nav: 100;
@z-index-header: 90;
@z-index-modal: 100;
@z-index-main-loader: 10000;

// durations
@duration-main-transition: 200ms;

// elements
@button-height-default: 44px;

// auto-modified
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
