// Base Color
@color-background: #f8fafa;
@color-theme: #3776ef;
@color-base: #555d61;
// @color-fade:            desaturate(mix(@color-base, @theme-bluegrey-600), 3.5%);
// @color-base-dimmed:     mix(@theme-bluegrey-200, @theme-lightblue-200, 85%);
@color-base-dimmed: fade(desaturate(darken(mix(@theme-bluegrey-200, @theme-lightblue-200, 85%), 5%), 5%), 90%);
@color-fade: fade(@color-base, 55%);
@color-fade-2: fade(#fff, 50%);
// @color-main:            hsl(210, 7%, 89%);
@color-main: #fff;
// @color-main-dimmed:     fade(@color-main, 95%);
@color-main-dimmed: hsl(0, 0%, 95%);
@color-accent: #3776ef;
@color-accent-active: #0f75de;
@color-accent-2: hsl(40, 100%, 62%);
@color-alt: hsl(0, 0%, 90%);
@color-active: fade(@color-base, 50%);
@color-link-active: @color-active;
// @color-border:          fade(@theme-bluegrey-300, 50%);
@color-border: #e1e7ea;
@color-border-2: fade(@theme-bluegrey-50, 7.5%);
@color-background-main: fade(@theme-bluegrey-900, 70%);

@color-input-border: #e1e3e9;
@color-input-placeholder: #ccd2d6;
@color-input-block-bg: #f3f5f6;
@color-input-block-ph: #99a3a8;

@color-link-dimmed: lighten(@theme-lightblue-a100, 15%);

@color-black-fade: fade(#000, 15%);
@color-black-fade-2: fade(#000, 25%);
@color-black-fade-3: fade(#000, 20%);

@color-positive: @theme-green-a200;
@color-negative: @theme-red-a100;

@color-badge-on: @theme-green-a100;
@color-badge-on-background: darken(@theme-green-900, 5%);
@color-badge-on-border: @theme-green-700;
@color-badge-off: @color-fade;
@color-badge-off-background: fade(@theme-bluegrey-900, 50%);
@color-badge-off-border: @color-border;

@color-badge-blue-text: @color-link-dimmed;
@color-badge-blue-border: @theme-lightblue-800;
@color-badge-blue-background: @theme-lightblue-800;
@color-badge-blue-hover-text: #fff;
@color-badge-blue-hover-border: @theme-lightblue-600;
@color-badge-blue-hover-background: @theme-lightblue-600;
@color-badge-blue-active-text: @color-link-dimmed;
@color-badge-blue-active-border: @theme-lightblue-800;
@color-badge-blue-active-background: @theme-lightblue-900;

@color-speed-15: #d6f4ff;
@color-speed-20: #abeeff;

@color-improvement-star:lighten (#45a945, 15%);

@color-table-highlight: @color-black-fade;

@color-stat-fire: hsl(6, 85%, 40%);
@color-stat-torpedo:hsl (207, 85%, 40%);
@color-stat-night: mix(hsl(6, 85%, 40%), hsl(207, 85%, 40%));
@color-stat-aa: hsl(26, 85%, 40%);
@color-stat-armor: hsl(39, 85%, 40%);

@color-fuel: hsl(112, 35%, 60%);
@color-ammo: hsl(47, 38%, 68%);
@color-steel: hsl(0, 0%, 89%);
@color-bauxite: hsl(26, 79%, 76%);

@base-theme: 'bluegrey';

@themes: red, pink, purple, deeppurple, indigo, blue, lightblue, cyan, teal, green, lightgreen, lime, yellow, amber,
  orange, deeporange;

// Mixin for Theme
.theme( @property, @colorname ) {
  & {
    @ref: 'theme-@{base-theme}-@{colorname}';
    @{property}: @@ref;
  }
  .applytheme( @themename ) {
    //body.theme-@{themename} &{
    .theme-@{themename} & {
      @ref: 'theme-@{themename}-@{colorname}';
      @{property}: @@ref;
    }
  }
  .all-themes(@i: length(@themes)) when (@i > 0) {
    .all-themes(@i - 1);
    @theme: extract(@themes, @i);
    .applytheme( @theme );
  }
  .all-themes();
}

// Color Theme: Blue Grey
@theme-bluegrey-50: hsl(204, 15%, 94%); // #edf0f1
@theme-bluegrey-100: hsl(198, 16%, 84%);
@theme-bluegrey-200: hsl(200, 15%, 73%);
@theme-bluegrey-300: hsl(200, 16%, 62%);
@theme-bluegrey-400: hsl(200, 15%, 54%);
@theme-bluegrey-500: hsl(200, 18%, 46%);
@theme-bluegrey-600: hsl(199, 18%, 40%);
@theme-bluegrey-700: hsl(199, 18%, 33%);
@theme-bluegrey-800: hsl(200, 18%, 26%);
@theme-bluegrey-900: hsl(200, 19%, 18%);
@theme-bluegrey-a100: hsl(198, 50%, 75%);
@theme-bluegrey-a200: hsl(200, 50%, 66%);
@theme-bluegrey-a400: hsl(200, 50%, 55%);
@theme-bluegrey-a700: hsl(199, 50%, 42%);

// Color Theme: Red
@theme-red-50: hsl(351, 100%, 96%);
@theme-red-100: hsl(354, 100%, 90%);
@theme-red-200: hsl(0, 73%, 77%);
@theme-red-300: hsl(0, 69%, 67%);
@theme-red-400: hsl(1, 83%, 63%);
@theme-red-500: hsl(4, 90%, 58%);
@theme-red-600: hsl(1, 77%, 55%);
@theme-red-700: hsl(0, 65%, 51%);
@theme-red-800: hsl(0, 66%, 47%);
@theme-red-900: hsl(0, 73%, 41%);
@theme-red-a100: hsl(5, 100%, 75%);
@theme-red-a200: hsl(0, 100%, 66%);
@theme-red-a400: hsl(348, 100%, 55%);
@theme-red-a700: hsl(0, 100%, 42%);

// Color Theme: Pink
@theme-pink-50: #fce4eb;
@theme-pink-100: #f8bbd0;
@theme-pink-200: #f48fb1;
@theme-pink-300: #f06292;
@theme-pink-400: #ec407a;
@theme-pink-500: #e91e63;
@theme-pink-600: #d81b60;
@theme-pink-700: #c2185b;
@theme-pink-800: #ad1457;
@theme-pink-900: #880e4f;
@theme-pink-a100: #ff80ab;
@theme-pink-a200: #ff4081;
@theme-pink-a400: #f50057;
@theme-pink-a700: #c51162;

// Color Theme: Purple
@theme-purple-50: #f3e5f5;
@theme-purple-100: #e1bee7;
@theme-purple-200: #ce93d8;
@theme-purple-300: #ba68c8;
@theme-purple-400: #ab47bc;
@theme-purple-500: #9c27b0;
@theme-purple-600: #8e24aa;
@theme-purple-700: #7b1fa2;
@theme-purple-800: #6a1b9a;
@theme-purple-900: #4a148c;
@theme-purple-a100: #ea80fc;
@theme-purple-a200: #e040fb;
@theme-purple-a400: #d500f9;
@theme-purple-a700: #aa00ff;

// Color Theme: Deep Purple
@theme-deeppurple-50: #ede7f6;
@theme-deeppurple-100: #d1c4e9;
@theme-deeppurple-200: #b39ddb;
@theme-deeppurple-300: #9575cd;
@theme-deeppurple-400: #7e57c2;
@theme-deeppurple-500: #673ab7;
@theme-deeppurple-600: #5e35b1;
@theme-deeppurple-700: #512da8;
@theme-deeppurple-800: #4527a0;
@theme-deeppurple-900: #311b92;
@theme-deeppurple-a100: #b388ff;
@theme-deeppurple-a200: #7c4dff;
@theme-deeppurple-a400: #651fff;
@theme-deeppurple-a700: #6200ea;

// Color Theme: Indigo
@theme-indigo-50: #e8eaf6;
@theme-indigo-100: #c5cae9;
@theme-indigo-200: #9fa8da;
@theme-indigo-300: #7986cb;
@theme-indigo-400: #5c6bc0;
@theme-indigo-500: #3f51b5;
@theme-indigo-600: #3949ab;
@theme-indigo-700: #303f9f;
@theme-indigo-800: #283593;
@theme-indigo-900: #1a237e;
@theme-indigo-a100: #8c9eff;
@theme-indigo-a200: #536dfe;
@theme-indigo-a400: #3d5afe;
@theme-indigo-a700: #304ffe;

// Color Theme: Blue
@theme-blue-50: #e3f2fd;
@theme-blue-100: #bbdefb;
@theme-blue-200: #90caf9;
@theme-blue-300: #64b5f6;
@theme-blue-400: #42a5f5;
@theme-blue-500: #2196f3;
@theme-blue-600: #1e88e5;
@theme-blue-700: #1976d2;
@theme-blue-800: #1565c0;
@theme-blue-900: #0d47a1;
@theme-blue-a100: #82b1ff;
@theme-blue-a200: #448aff;
@theme-blue-a400: #2979ff;
@theme-blue-a700: #2962ff;

// Color Theme: Light Blue
@theme-lightblue-50: #e1f5fe;
@theme-lightblue-100: #b3e5fc;
@theme-lightblue-200: #81d4fa;
@theme-lightblue-300: #4fc3f7;
@theme-lightblue-400: #29b6f6;
@theme-lightblue-500: #03a9f4;
@theme-lightblue-600: #039be5;
@theme-lightblue-700: #0288d1;
@theme-lightblue-800: #0277bd;
@theme-lightblue-900: #01579b;
@theme-lightblue-a100: #80d8ff;
@theme-lightblue-a200: #40c4ff;
@theme-lightblue-a400: #00b0ff;
@theme-lightblue-a700: #0091ea;

// Color Theme: Cyan
@theme-cyan-50: #e0f7fa;
@theme-cyan-100: #b2ebf2;
@theme-cyan-200: #80deea;
@theme-cyan-300: #4dd0e1;
@theme-cyan-400: #26c6da;
@theme-cyan-500: #00bcd4;
@theme-cyan-600: #00acc1;
@theme-cyan-700: #0097a7;
@theme-cyan-800: #00838f;
@theme-cyan-900: #006064;
@theme-cyan-a100: #84ffff;
@theme-cyan-a200: #18ffff;
@theme-cyan-a400: #00e5ff;
@theme-cyan-a700: #00b8d4;

// Color Theme: Teal
@theme-teal-50: #e0f2f1;
@theme-teal-100: #b2dfdb;
@theme-teal-200: #80cbc4;
@theme-teal-300: #4db6ac;
@theme-teal-400: #26a69a;
@theme-teal-500: #009688;
@theme-teal-600: #00897b;
@theme-teal-700: #00796b;
@theme-teal-800: #00695c;
@theme-teal-900: #004d40;
@theme-teal-a100: #a7ffeb;
@theme-teal-a200: #64ffda;
@theme-teal-a400: #1de9b6;
@theme-teal-a700: #00bfa5;

// Color Theme: Green
@theme-green-50: #e8f5e9;
@theme-green-100: #c8e6c9;
@theme-green-200: #a5d6a7;
@theme-green-300: #81c784;
@theme-green-400: #66bb6a;
@theme-green-500: #4caf50;
@theme-green-600: #43a047;
@theme-green-700: #388e3c;
@theme-green-800: #2e7d32;
@theme-green-900: #1b5e20;
@theme-green-a100: #b9f6ca;
@theme-green-a200: #69f0ae;
@theme-green-a400: #00e676;
@theme-green-a700: #00c853;

// Color Theme: Light Green
@theme-lightgreen-50: #f1f8e9;
@theme-lightgreen-100: #dcedc8;
@theme-lightgreen-200: #c5e1a5;
@theme-lightgreen-300: #aed581;
@theme-lightgreen-400: #9ccc65;
@theme-lightgreen-500: #8bc34a;
@theme-lightgreen-600: #7cb342;
@theme-lightgreen-700: #689f38;
@theme-lightgreen-800: #558b2f;
@theme-lightgreen-900: #33691e;
@theme-lightgreen-a100: #ccff90;
@theme-lightgreen-a200: #b2ff59;
@theme-lightgreen-a400: #76ff03;
@theme-lightgreen-a700: #64dd17;

// Color Theme: Lime
@theme-lime-50: #f9fbe7;
@theme-lime-100: #f0f4c3;
@theme-lime-200: #e6ee9c;
@theme-lime-300: #dce775;
@theme-lime-400: #d4e157;
@theme-lime-500: #cddc39;
@theme-lime-600: #c0ca33;
@theme-lime-700: #afb42b;
@theme-lime-800: #9e9d24;
@theme-lime-900: #827717;
@theme-lime-a100: #f4ff81;
@theme-lime-a200: #eeff41;
@theme-lime-a400: #c6ff00;
@theme-lime-a700: #aeea00;

// Color Theme: Yellow
@theme-yellow-50: #fffde7;
@theme-yellow-100: #fff9c4;
@theme-yellow-200: #fff59d;
@theme-yellow-300: #fff176;
@theme-yellow-400: #ffee58;
@theme-yellow-500: #ffeb3b;
@theme-yellow-600: #fdd835;
@theme-yellow-700: #fbc02d;
@theme-yellow-800: #f9a825;
@theme-yellow-900: #f57f17;
@theme-yellow-a100: #ffff8d;
@theme-yellow-a200: #ffff00;
@theme-yellow-a400: #ffea00;
@theme-yellow-a700: #ffd600;

// Color Theme: Amber
@theme-amber-50: #fff8e1;
@theme-amber-100: #ffecb3;
@theme-amber-200: #ffe082;
@theme-amber-300: #ffd54f;
@theme-amber-400: #ffca28;
@theme-amber-500: #ffc107;
@theme-amber-600: #ffb300;
@theme-amber-700: #ffa000;
@theme-amber-800: #ff8f00;
@theme-amber-900: #ff6f00;
@theme-amber-a100: #ffe57f;
@theme-amber-a200: #ffd740;
@theme-amber-a400: #ffd740;
@theme-amber-a700: #ffab00;

// Color Theme: Orange
@theme-orange-50: #fff3e0;
@theme-orange-100: #ffe0b2;
@theme-orange-200: #ffcc80;
@theme-orange-300: #ffb74d;
@theme-orange-400: #ffa726;
@theme-orange-500: #ff9800;
@theme-orange-600: #fb8c00;
@theme-orange-700: #f57c00;
@theme-orange-800: #ef6c00;
@theme-orange-900: #e65100;
@theme-orange-a100: #ffd180;
@theme-orange-a200: #ffab40;
@theme-orange-a400: #ff9100;
@theme-orange-a700: #ff6d00;

// Color Theme: Deep Orange
@theme-deeporange-50: #fbe9e7;
@theme-deeporange-100: #ffccbc;
@theme-deeporange-200: #ffab91;
@theme-deeporange-300: #ff8a65;
@theme-deeporange-400: #ff7043;
@theme-deeporange-500: #ff5722;
@theme-deeporange-600: #f4511e;
@theme-deeporange-700: #e64a19;
@theme-deeporange-800: #d84315;
@theme-deeporange-900: #bf360c;
@theme-deeporange-a100: #ff9e80;
@theme-deeporange-a200: #ff6e40;
@theme-deeporange-a400: #ff3d00;
@theme-deeporange-a700: #dd2c00;

// Color Theme: grey
@theme-grey-50: #fafafa;
@theme-grey-100: #f5f5f5;
@theme-grey-200: #eeeeee;
@theme-grey-300: #e0e0e0;
@theme-grey-400: #bdbdbd;
@theme-grey-500: #9e9e9e;
@theme-grey-600: #757575;
@theme-grey-700: #616161;
@theme-grey-800: #424242;
@theme-grey-900: #212121;
@theme-grey-a100: hsl(0, 0%, 75%);
@theme-grey-a200: hsl(0, 0%, 66%);
@theme-grey-a400: hsl(348, 0%, 55%);
@theme-grey-a700: hsl(0, 0%, 42%);

// Color Theme: brown
@theme-brown-50: #efebe9;
@theme-brown-100: #d7ccc8;
@theme-brown-200: #bcaaa4;
@theme-brown-300: #a1887f;
@theme-brown-400: #8d6e63;
@theme-brown-500: #795548;
@theme-brown-600: #6d4c41;
@theme-brown-700: #5d4037;
@theme-brown-800: #4e342e;
@theme-brown-900: #3e2723;
@theme-brown-a100: hsl(25, 50%, 75%);
@theme-brown-a200: hsl(25, 50%, 66%);
@theme-brown-a400: hsl(25, 50%, 55%);
@theme-brown-a700: hsl(25, 50%, 42%);
