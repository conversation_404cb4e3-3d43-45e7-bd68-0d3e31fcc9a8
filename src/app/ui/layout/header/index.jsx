import React from 'react'
import { connect } from 'react-redux'
import classNames from 'classnames'
import { extend, getLocaleId } from 'koot'

import { Icon, Dropdown, MenuItem } from 'biz-components'
import { resetLogin, resetLoginStatus } from '@api/user'
import { initRobots } from '@api/robots'
import { replace as historyReplace, push } from '@utils/history'
import { getConfiguration } from '@api/_replies'
import { selectRobot, getRobotProfile, getRobotStatistics } from '@src/app/api/robots/index'
import modal, { close as closeModal } from '@src/app/utils/modal'
import ForgetPassword from '../../pages/login/forgetPassword'
import ResetPassword from '../../pages/login/resetPassword'
import { webOperator } from '@utils/operatorRobot'
import { isCnHl } from '@utils/tools'
import { getNewWindows } from '@api/_route'
import Cookies from 'js-cookie'
import WebLang from '@ui/components/webLang'

@extend({
  styles: require('./styles.less'),
})
@connect((state) => {
  const windowUrl = window.location.href,
    testUrl = 'test-jiedai.ainirobot.com',
    environment = windowUrl.indexOf(testUrl)
  return {
    environment: environment,
    robots: state.robots.robots,
    currentId: state.robots.current,
    id: state.user.uuid,
    role: state.user.detail.role_id,
    pathName: state.routing.locationBeforeTransitions.pathname,
  }
})
export default class Header extends React.Component {
  componentDidMount() {
    const _this = this
    setTimeout(() => {}, 200)
    webOperator.fetch_robot_list(_this.initRobot.bind(this), () => {})
  }
  componentDidUpdate() {}
  initRobot(robots) {
    this.props.dispatch(initRobots(robots))
    this.props.dispatch(selectRobot(Object.keys(robots)[0]))
  }
  render() {
    const { dispatch, currentId, ...others } = { ...this.props }
    const token = Cookies.get('token')
    const environment = this.props.environment
    const customizationUrl =
      environment > 0 ? 'http://t-custom.cmcm.com?token=' + token : 'https://dingzhi.cmcm.com?token=' + token

    return (
      <div {...others}>
        {isCnHl() && this.props.role !== undefined && this.props.role === '9' && (
          <a
            href='javascript:void(0)'
            className='industry-order'
            onClick={() => {
              getNewWindows('capi/v1/corp/sso_nlpcp')
            }}
          >
            <Icon icon='nlp_customize'></Icon>
            <span>NLP定制</span>
          </a>
        )}
        {isCnHl() && (
          <a href={customizationUrl} target='_blank' className='industry-order'>
            <Icon icon='head_industry_order1'></Icon>
            <span>行业定制</span>
          </a>
        )}
        {isCnHl() && ((this.props.id && this.props.id.indexOf('SSOUID.') < 0) || this.props.id == undefined) && (
          <a
            href='javascript:void(0)'
            id='record'
            className='data-center'
            onClick={() => {
              getNewWindows('capi/v1/corp/sso_bdcp')
            }}
          >
            <Icon icon='head_data_center'></Icon>
            <span>数据中心</span>
          </a>
        )}
        {isCnHl() && (
          <a
            href='https://bbs.ainirobot.com/?refer=jiedai.ainirobot.com&from=jiedai'
            target='_blank'
            className='forum'
            onClick={() => {
              _czc.push(['_trackEvent', 'bbs', 'view'])
            }}
          >
            <Icon icon='fix_community_click' />
            社区
          </a>
        )}
        <WebLang />
        {this.props.id && this.props.id.indexOf('SSOUID.') >= 0 && (
          <div className='tip'>注意：当前为免登陆，操作完成请务必点击右上角退出登录</div>
        )}
        <OptionUser />
      </div>
    )
  }
}

@connect(
  (state) => ({
    robots: state.robots.robots,
    currentId: state.robots.current,
  }),
  (dispatch) => ({
    initRobots: (robots) => dispatch(initRobots(robots)),
    selectRobot: (id) => dispatch(selectRobot(id)),
    getRobotStatistics: (id) => dispatch(getRobotStatistics(id)),
    getRobotProfile: (id) => dispatch(getRobotProfile(id)),
    getConfiguration: (id) => dispatch(getConfiguration(id)),
  }),
)
class OptionRobot extends React.Component {
  state = {
    currentRobotIsActive: false,
    flag: false,
    stateList: [],
  }
  componentDidMount() {
    webOperator.fetch_robot_list(this.props.initRobots, () => {})
  }
  fetch_robot_init(robots) {
    this.props.initRobots(robots)
    setInterval(() => {
      const stateList = Object.keys(robots).map((el, idx) => {
        return robots[el].state || 0
      })
      if (stateList.toString() != this.state.stateList.toString()) {
        this.setState({
          flag: !this.state.flag,
        })
      }
      this.state.stateList = stateList
    }, 3000)
    let ids = []
    for (let i in robots) {
      const robotId = robots[i].robot_id
      ids.push(robotId)
    }
    this.props.getRobotStatistics(ids[0])
    this.props.getRobotProfile(ids[0])
    this.props.getConfiguration(ids[0]) //获取配置zip文件信息
  }
  componentDidUpdate() {
    if (this.props.robots[this.props.currentId].state == 0) {
      if (this.state.currentRobotIsActive != false) {
        this.setState({
          currentRobotIsActive: false,
        })
      }
    } else {
      if (this.state.currentRobotIsActive != true) {
        this.setState({
          currentRobotIsActive: true,
        })
      }
    }
  }
  render() {
    const robots = this.props.robots
    const currentRobotName = robots[this.props.currentId] && robots[this.props.currentId].robot_name
    return (
      <Dropdown
        className={classNames({
          'dropdown dropdown-robot': true,
          'is-active': this.state.currentRobotIsActive,
        })}
        label={
          <span
            className={classNames({
              status: true,
            })}
          >
            <Icon className='icon' icon='side_robotstate' />
            {currentRobotName}
          </span>
        }
      >
        {Object.keys(this.props.robots).map((robot, index) => (
          <MenuItem
            children={
              <div
                className={classNames({
                  robotListContent: true,
                  online: this.props.robots[robot].state,
                })}
              >
                <Icon className='icon' icon='side_robotstate' />
                <span className='robotName'>{this.props.robots[robot].robot_name}</span>
              </div>
            }
            key={index}
            onClick={() => {
              this.props.selectRobot(this.props.robots[robot].robot_id)
              this.props.getRobotStatistics(this.props.robots[robot].robot_id)
              this.props.getRobotProfile(this.props.robots[robot].robot_id)
              this.props.getConfiguration(this.props.robots[robot].robot_id) //获取配置zip文件信息
              if (this.props.robots[this.props.currentId].state == 0) {
                this.state.currentRobotIsActive = false
              } else {
                this.state.currentRobotIsActive = true
              }
            }}
          />
        ))}
      </Dropdown>
    )
  }
}

@connect((state) => ({
  avatar: state.user.detail.avatar_url,
  name: state.user.detail.full_name,
  role: state.user.detail.role_id,
  user: state.user,
}))
class OptionUser extends React.Component {
  state = {}
  render() {
    const isAdmin = this.props.role == 5
    const isSuperAdmin = this.props.role == 9
    const forgetPassword = (
      <div style={{ width: '20em' }}>
        <ForgetPassword
          title={__('HEADER.CHANGE_PASSWORD')}
          user={this.props.user}
          dispatch={this.props.dispatch}
          onSet={() => {
            closeModal()
            modal({
              content: resetPassword,
              confirm: false,
              cancel: false,
            })
          }}
        />
      </div>
    )

    const resetPassword = (
      <div style={{ width: '20em' }}>
        <ResetPassword
          onSet={() => {}}
          onModalConfirm={() => {
            this.props.dispatch(resetLogin())
          }}
        />
      </div>
    )
    return (
      <Dropdown
        className='dropdown dropdown-user'
        label={
          <span
            className={classNames({
              avatar: true,
              'is-admin': isAdmin,
              'is-super-admin': isSuperAdmin,
            })}
          >
            {this.props.avatar && this.props.avatar != '' ? (
              <img className='img' src={this.props.avatar} />
            ) : (
              <div className='img no-img'></div>
            )}
            {isAdmin && <Icon className='icon-admin' icon='topbar_vip_n' />}
            {isSuperAdmin && <Icon className='icon-admin' icon='topbar_vip_ss' />}
            {this.props.name}
          </span>
        }
      >
        <MenuItem
          children={__('HEADER.CHANGE_PASSWORD')}
          key={__('HEADER.CHANGE_PASSWORD')}
          onClick={() =>
            modal({
              content: forgetPassword,
              confirm: false,
              cancel: false,
            })
          }
        />
        <MenuItem
          children={__('HEADER.LOG_OUT')}
          key={__('HEADER.LOG_OUT')}
          onClick={() =>
            modal({
              title: __('HEADER.LOG_OUT'),
              content: __('HEADER.LOG_OUT_CONFIRM'),
              onConfirm: () => {
                {
                  /* resetLoginStatus(); */
                }
                historyReplace('/')
                this.props.dispatch(resetLogin())
              },
              onCancel: () => {},
            })
          }
        />
      </Dropdown>
    )
  }
}
