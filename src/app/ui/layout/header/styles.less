@import '~base.less';
.component {
  height: @header-height;
  line-height: @header-height;
  position: fixed;
  top: 0;
  left: @nav-width;
  right: 0;
  border-bottom: 1px solid @color-border;
  text-align: right;
  padding-right: @main-gutter-horizontal; // background: @color-background;
  background: #fff;
  z-index: @z-index-header;
  :lang(en) {
    left: @nav-all-width;
  }
  @media screen and (max-width: @main-gutter-horizontal-small-breakpoint) {
    padding-right: @main-gutter-horizontal-small;
  }
  .tip {
    position: absolute;
    left: 4rem;
    width: 100%;
    text-align: left;
    color: #ff713c;
    top: 0;
    z-index: -1;
  }
  > a {
    margin-right: 18px;
    &:hover {
      color: @color-theme !important;
    }
    &:active {
      color: @color-theme !important;
    }
  }
  .industry-order {
    padding-right: 18px;
    border-right: 1px solid #ebebeb;
    span {
      vertical-align: middle;
    }
    svg {
      width: 16px;
      height: 16px;
      margin-right: 10px;
      vertical-align: middle;
    }
  }
  .data-center {
    padding-right: 18px;
    border-right: 1px solid #ebebeb;
    span {
      vertical-align: middle;
    }
    svg {
      width: 16px;
      height: 16px;
      margin-right: 10px;
      vertical-align: middle;
    }
  }
  .forum {
    @size-icon: 30px;
    margin-right: 60px;
    svg {
      margin-right: 10px;
      vertical-align: middle;
    }
  }
  .dropdown {
    & + .dropdown {
      margin-left: 40px;
    }
    &.dropdown-robot {
      @size-icon: 30px;
      &.is-active .label::after {
        background: url('@assets/svg-icon/more-theme.svg') no-repeat 50% 50% / contain !important;
      }
      .status {
        padding-left: (@size-icon + 12);
      }
      &.is-active .status {
        color: @color-accent;
      }
      .icon {
        width: @size-icon;
        height: @size-icon;
        display: block;
        position: absolute;
        top: 50%;
        left: 0;
        margin-top: (0 - @size-icon / 2);
      }
    }
    &.dropdown-user {
      @size-avatar: 36px;
      .avatar {
        display: block;
        padding-left: (@size-avatar + 15);
        .img {
          width: @size-avatar;
          height: @size-avatar;
          position: absolute;
          top: 50%;
          left: 0;
          margin-top: (0 - @size-avatar / 2);
          background: fade(#000, 5%);
          border-radius: 50%;
          display: block;
          z-index: 1;
          border-radius: 50%;
          border: 0;
          overflow: hidden;
          &.no-img {
            background: transparent url('@assets/png/user/user_card_pic_none.png') no-repeat center center;
            background-size: cover;
          }
        }
        .icon-admin {
          @size-badge: 14px;
          position: absolute;
          top: 50%;
          left: (@size-avatar - @size-badge + 2);
          margin-top: (0 + @size-avatar / 2 - @size-badge + 2);
          width: @size-badge;
          height: @size-badge;
          z-index: 2;
        }
      }
    }
    .robotListContent {
      position: relative;
      padding-left: 2.5em;
      .robotName {
        color: #99a3a8;
      }
      &.online {
        color: @color-theme;
        .robotName {
          color: @color-theme;
        }
      }
    }
  }
  @media screen and (max-width: 1360px) {
    .tip {
      text-align: left;
    }
  }
}
