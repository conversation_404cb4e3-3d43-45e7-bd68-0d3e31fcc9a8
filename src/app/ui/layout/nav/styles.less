@import '~base.less';

.component {
  position: fixed;
  top: 0;
  left: 0; // width: @nav-all-width;
  height: 100%;
  height: 100vh;
  background: @color-accent;
  color: #fff;
  z-index: @z-index-nav; // overflow: hidden;

  .logo {
    height: 60px; // border-bottom: 1px solid fade(#fff, 15%); // background: fade(#fff, 15%);
    position: relative;
    overflow: hidden;

    .svg {
      @width: 120px;
      background: url('~Assets/svg-icon/logo-1.svg') no-repeat center center;
      background-size: contain;
      position: absolute;
      top: 10px;
      left: 50%;
      width: @width;
      height: 20px;
      margin-left: (0 - @width / 2);
    }

    &::after {
      content: '猎户智能机器人平台';
      text-align: center;
      line-height: 0;
      position: absolute;
      top: 30px;
      left: 0;
      right: 0;
      font-size: 12px;
      line-height: 20px;
      color: #fff;
      letter-spacing: 1px;
    }
  }

  .floatMenu {
    width: @nav-width;
    :lang(en) {
      width: @nav-all-width;
    }

    .menuList::-webkit-scrollbar {
      display: none; //Safari and Chrome
    }

    .menuList::-moz-scrollbar {
      display: none;
    }

    .menuList::-ms-scrollbar {
      display: none;
    }

    .menuList::-o-scrollbar {
      display: none;
    }

    .menuList {
      height: calc(100% - 60px);
      overflow: auto;
    }

    .nav-item {
      @line-height: 20px;
      margin: 2px 0 1px 0;
      position: relative;

      .link {
        display: block;
        color: inherit;
        font-size: @base-font-size-small;
        padding: (floor(@nav-item-height - @line-height) / 2) 0 (ceil(@nav-item-height - @line-height) / 2) 64px;
        line-height: @line-height;
        position: relative;

        .icon {
          @icon-size: 20px;
          position: absolute;
          top: 50%;
          left: 30px;
          width: @icon-size;
          height: @icon-size;
          margin-top: (0 - @icon-size / 2);
        }

        .is-hover( {
                        color: inherit;
                        background-color: @color-accent-active;
                    }

                    ;

                        {
                        color: inherit;
                        background-color: darken(@color-accent-active, 5%);
                    }

                );

        .arrow {
          @icon-size: 20px;
          position: absolute;
          top: 50%;
          right: 10px;
          width: @icon-size;
          height: @icon-size;
          margin-top: (0 - @icon-size / 2);
        }
      }

      &.on .link {
        cursor: default;
        background-color: @color-accent-active;
      }

      &.on {
        .nav-sub {
          &.on {
            opacity: 1;
            pointer-events: all;
          }
        }
      }

      &.cur {
        background-color: @color-accent-active;
      }
    }

    .nav-sub {
      position: fixed;
      left: @nav-width;
      width: 120px;
      top: 0;
      height: 100%;
      opacity: 0;
      background: #fff;
      box-shadow: 0 2px 50px 0 rgba(161, 170, 178, 0.3);
      pointer-events: none;
      text-align: center;
      overflow: auto;
      padding-top: 20px;
      padding-bottom: 60px;
      :lang(en) {
        left: @nav-all-width;
      }

      &.on {
        // opacity: 1;
        // pointer-events: all;
      }
    }

    .nav-sub-item {
      @height: @nav-item-height;
      line-height: @height;
      @icon-size: 20px;
      display: block;
      text-align: left; // padding-top: (@icon-size + 6);
      color: @color-base;
      font-size: 12px;
      position: relative;

      .icon {
        width: @icon-size;
        height: @icon-size;
        float: left;
        margin: (floor(@height - @icon-size) / 2) 4px 0 10px;
      }

      .is-hover( {
                    color: @color-accent;
                }

                ;

                    {
                    color: fade(@color-base, 50%);
                }

            );

      .arrow {
        width: 24px;
        height: 12px;
        color: @color-accent;
        position: absolute;
        bottom: (floor(@height / 2 - 8));
        right: 8px;
      }

      &.on {
        background: rgba(0, 146, 245, 0.1);
        cursor: default;
      }
    }
  }

  .foldMenu {
    #cuslogo {
      opacity: 0;
      display: none;
    }

    .customlogo {
      height: 3rem;
      position: relative;
      overflow: hidden;
      text-align: center;
    }

    .customlogoimg {
      width: 135px;
      height: 50px;
      display: inline-block;
      // top: 50%;
      // margin-top: -1.25rem;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: 50% 50%;

      &.default {
        height: 24px;
        margin-top: 10px;
        background: url('@assets/png/login/logo.png') no-repeat center center;
        background-size: contain;
      }
    }

    .company_name {
      height: 17px;
      font-size: 12px;
      line-height: 14px;
      text-align: center;
    }

    // width: @nav-all-width;
    height: 100vh; // overflow: scroll;

    .menuList::-webkit-scrollbar {
      display: none; //Safari and Chrome
    }

    .menuList::-moz-scrollbar {
      display: none;
    }

    .menuList::-ms-scrollbar {
      display: none;
    }

    .menuList::-o-scrollbar {
      display: none;
    }

    .selectArrow {
      display: inline-block;
      border-top: 2px solid;
      border-right: 2px solid;
      width: 14px;
      height: 14px;
      border-color: #fff;
      transform: rotate(-45deg);
    }

    .scrollBtn {
      width: 110px;
      height: 29px;
      opacity: 0.8;
      background: #42aef7;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
      border-radius: 8px;
      text-align: center;
      position: fixed;

      &:hover {
        cursor: pointer;
      }

      &.up {
        top: 72px;
        left: 26px;

        svg {
          margin-top: 2px;
        }
      }

      &.down {
        bottom: 12px;
        left: 26px;

        svg {
          margin-top: 1px;
          transform: rotate(-180deg);
        }
      }
    }

    .menuBox {
      width: @nav-width;
      height: calc(100vh-60px);
      overflow: scroll;
      :lang(en) {
        width: @nav-all-width;
      }

      &::-webkit-scrollbar {
        width: 0 !important;
      }

      &::-webkit-scrollbar {
        -ms-overflow-style: none;
      }

      &::-webkit-scrollbar {
        overflow: -moz-scrollbars-none;
      }

      // position: relative;
      .menuList {
        // padding-bottom: 90px;
        // overflow-x: hidden;
        // overflow-y: scroll;
        // position: absolute;
        // left: 0;
        // top: 0;
        // right: -30px;
        // bottom: 0;
        .clickFlag {
          pointer-events: none;
          background: url('@assets/png/menuIcon.png') no-repeat center;
          background-size: 90%;

          .link {
            opacity: 1;
            text-shadow: 1px 1px 0 #0f75de;
            text-align: center;
            padding-left: 0;
          }
        }

        .subTitle {
          opacity: 1;
        }
      }
    }

    // .menuBox-en {
    //     width: 240px;
    // }

    .nav-item {
      @line-height: 20px;
      margin: 0px 0 0 0;
      position: relative;

      .link {
        display: block;
        color: inherit;
        border-left: 4px solid transparent;
        font-size: @base-font-size-small;
        padding: (floor(@nav-item-height - @line-height) / 2) 0 (ceil(@nav-item-height - @line-height) / 2) 64px;
        line-height: @line-height;
        opacity: 0.9;
        position: relative;

        &:hover {
          opacity: 1;
          background-color: @color-accent-active;
          border-left: 4px solid #50e3c2;
        }

        .icon {
          @icon-size: 20px;
          position: absolute;
          top: 50%;
          left: 30px;
          width: @icon-size;
          height: @icon-size;
          margin-top: (0 - @icon-size / 2);
        }

        .is-hover( {
                        color: inherit;
                        background-color: @color-accent-active;
                    }

                    ;

                        {
                        color: inherit;
                        background-color: darken(@color-accent-active, 5%);
                    }

                );

        .arrow {
          @icon-size: 20px;
          position: absolute;
          top: 50%; // right: 40px;
          right: 10px;
          width: @icon-size;
          height: @icon-size;
          margin-top: (0 - @icon-size / 2);
          transform: rotate(90deg);
        }
      }

      &.cur .link {
        opacity: 1;
        font-weight: bold;
        background-color: @color-accent-active;
      }

      // &.cur {
      //     background-color: @color-accent-active;
      //     .nav-sub {
      //         display: block;
      //     }
      //     .arrow {
      //         transform: rotate(-90deg)
      //     }
      // }
    }

    .nav-sub {
      height: 100%; // pointer-events: none;
      text-align: center;
      color: #fff;
      display: none;

      &.on {
        display: block;
      }

      a:active {
        color: #fff;
      }
    }

    .showChild {
      background-color: @color-accent-active;

      .nav-sub {
        display: block;
      }

      .arrow {
        transform: rotate(-90deg);
      }
    }

    .nav-sub-item {
      @height: @nav-item-height;
      line-height: @height;
      @icon-size: 20px;
      display: block;
      text-align: left; // padding-top: (@icon-size + 6);
      color: #fff;
      font-size: 12px;
      padding-left: 42px;
      opacity: 0.9;
      cursor: pointer;
      border-left: 4px solid transparent;
      position: relative;

      &:last-child {
        padding-bottom: 6px;
      }

      .icon {
        width: @icon-size;
        height: @icon-size;
        float: left;
        margin: (floor(@height - @icon-size) / 2) 4px 0 10px;
      }

      .arrow {
        width: 24px;
        height: 12px;
        color: @color-accent;
        position: absolute;
        bottom: (floor(@height / 2 - 8));
        right: 8px;
      }

      &.on {
        background: #0060c3;
        cursor: default;
        opacity: 1;
        font-weight: bold;
      }

      &:hover {
        opacity: 1;
        font-weight: bold;
        background: #0060c3;
        border-left: 4px solid #50e3c2;
      }
    }
  }
}
