import React from 'react'
import { connect } from 'react-redux'
import classNames from 'classnames'
import { extend, getLocaleId } from 'koot'

import routes from '@app/router'

import { Link } from 'biz-components'
import { Icon } from 'biz-components'

import { getNewWindow } from '@api/_route'
import { replace as historyReplace, push } from '@utils/history'
import { isCnHl } from '@utils/tools'

//const en_menu = ["综合服务员", "全局功能", "基础状态", "自定义首页", "招揽", "欢迎语", "自定义问答", "问答库", "智能推荐", "对话引导", "导览讲解", "问路&引领", "设置", '企业信息', '用户管理', '员工管理', '权限管理', '沉默搭讪', 'VIP服务', "来访接待", '关键词问答', '专属词库', '操作记录', '远程维护', '轻应用中心','对话播报'];
const en_menu = [
  '综合服务员',
  '全局功能',
  '基础状态',
  '自定义首页',
  '招揽',
  '欢迎语',
  '自定义问答',
  '问答库',
  '智能推荐',
  '对话引导',
  '导览讲解',
  '问路&引领',
  '设置',
  '企业信息',
  '用户管理',
  '员工管理',
  '权限管理',
  '专属词库',
  '轻应用中心',
]

@extend({
  styles: require('./styles.less'),
})
@connect((state) => {
  // if (__DEV__) console.log('页面：自定义场景', state._skills)

  return {
    // routes: state._routeConfig.routeConfig
    routeLocation: state.realtimeLocation.routeConfig,
    langData: state.user.langData,
    userLocaleId: state.user.userLocaleId,
    corp: state.user.corp,
  }
})
export default class extends React.Component {
  // routes = routes.childRoutes.filter(route => !!route.show)
  routes = this.props.routes.childRoutes.filter((route) => !!route.show)

  constructor(props) {
    super(props)

    this.state = {
      activePathName: '',
      menuoffsetTop: 0,
      menuoffsetHeight: 0,
      ticking: false,
      downTick: false,
      downShow: false,
      downAction: false,
      upAction: false,
      scrollDownHeight: 0,
    }
    this.scrollMenuDown = this.scrollMenuDown.bind(this)
    this.scrollMenuTop = this.scrollMenuTop.bind(this)
  }
  componentDidMount() {
    const menu = document.getElementsByClassName('menuList')[0],
      navBox = document.getElementsByClassName('menuBox')[0],
      _self = this,
      scrollDownHeight = menu.scrollHeight - navBox.clientHeight
    this.setState({
      downShow: scrollDownHeight > 0 ? true : false,
      scrollDownHeight: scrollDownHeight,
      menuoffsetTop: navBox.scrollTop,
    })
    navBox.addEventListener('scroll', function () {
      let last_known_scroll_position = navBox.scrollTop,
        scrollDownHeight = menu.scrollHeight - navBox.clientHeight
      _self.setState({
        menuoffsetTop: last_known_scroll_position,
        scrollDownHeight: scrollDownHeight,
        downShow: scrollDownHeight - last_known_scroll_position > 0 ? true : false,
        // ticking: last_known_scroll_position > 0 ? true : false,
        // downTick: scrollDownHeight - last_known_scroll_position > 0  ? true : false
        downAction: scrollDownHeight - last_known_scroll_position > 0 ? true : false,
        tickAction: last_known_scroll_position > 0 ? true : false,
      })
    })

    setTimeout((e) => {
      this.setLogo()
    }, 900)
  }
  setLogo() {
    let host = __DEV__ ? 'https://test-jiedai.ainirobot.com' : location.origin
    let customlogo = document.querySelector('.customlogo')
    let customlogoimg = document.querySelector('.customlogoimg')
    let corp_logo
    try {
      corp_logo = sessionStorage.getItem('customimgurl') || ''
    } catch (error) {
      let corp_logo = ''
    }

    if (corp_logo.length > 0 && customlogoimg) {
      customlogoimg.style.backgroundImage = `url(${corp_logo})`
    } else {
      // customlogoimg.style.backgroundImage = `url('${host}/media/icon/logo/default_logo.png')`
      // customlogoimg.style.backgroundImage = `url('@assets/png/login/logo.png')`
      // customlogo.style.display='none';
    }
  }
  scrollMenuDown() {
    let element = document.getElementsByClassName('menuBox')[0],
      downTick = this.state.downTick,
      downAction = this.state.downAction,
      scrollDownHeight = this.state.scrollDownHeight

    if (scrollDownHeight > 0 && downTick && downAction) {
      element.scrollTop += 1
      window.requestAnimationFrame(this.scrollMenuDown)
    }
  }

  scrollMenuTop() {
    let element = document.getElementsByClassName('menuBox')[0],
      ticking = this.state.ticking,
      tickAction = this.state.tickAction

    if (element.scrollTop > 0 && ticking && tickAction) {
      element.scrollTop -= 2
      this.setState({
        menuoffsetTop: element.scrollTop,
      })
      window.requestAnimationFrame(this.scrollMenuTop)
    }
  }

  changeMenuOverHeight() {
    setTimeout(() => {
      const menu = document.getElementsByClassName('menuList')[0],
        navBox = document.getElementsByClassName('menuBox')[0],
        menuoffsetTop = this.state.menuoffsetTop,
        // scrollDownHeight = menu.clientHeight - navBox.clientHeight
        scrollDownHeight = menu.clientHeight - navBox.clientHeight
      this.setState({
        downShow: scrollDownHeight > 0 && menuoffsetTop < scrollDownHeight ? true : false,
        scrollDownHeight: scrollDownHeight,
      })
    }, 10)
  }
  openNewWindow() {
    return getNewWindow()
      .then((res) => {
        const url = res.redirect_url
        window.open(url, '新窗口')
        console.log('获取跳转页面成功')
      })
      .catch((err) => {
        console.log(err)
        console.log('获取跳转页面失败')
      })
  }
  changeActivePathName(pathname, openWindow, route) {
    if (openWindow) {
      const url = route.url
      this.openNewWindow()
      console.log(route)
    } else {
      this.setState(
        {
          activePathName: this.state.activePathName == pathname ? '' : pathname,
        },
        () => {
          console.info(this.state.activePathName)
        },
      )
    }
  }
  render() {
    const { routes, userLocaleId, langData } = this.props
    const isInUserLangDatas = langData && langData.some((el) => el.lang_code === userLocaleId)
    return (
      <div className={this.props.className}>
        <div className='foldMenu'>
          <div className='logo' id='cuslogo'>
            <div className='svg'></div>
          </div>
          <div className='customlogo'>
            {this.props.corp && (
              <React.Fragment>
                <div className={'customlogoimg ' + (this.props.corp.corp_logo.length === 0 ? 'default' : '')}></div>
                {this.props.corp.corp_logo.length === 0 && <p className='company_name'>{__('LOGIN.ORIONSTART')}</p>}
              </React.Fragment>
            )}
          </div>
          <div className={classNames({ menuBox: true, 'menuBox-en': isCnHl() })}>
            <div className='menuList'>
              <NavItem
                route={routes.indexRoute}
                activePathName={this.state.activePathName}
                changeActivePathName={this.changeActivePathName.bind(this)}
                changeMenuOverHeight={this.changeMenuOverHeight.bind(this)}
              />
              {this.routes.map((route, index) => {
                if (
                  (!isInUserLangDatas && en_menu.indexOf(route.name) > -1) ||
                  (isInUserLangDatas && isCnHl()) ||
                  (!isCnHl() && en_menu.indexOf(route.name) > -1)
                ) {
                  return (
                    <NavItem
                      route={route}
                      key={index}
                      num={index}
                      activePathName={this.state.activePathName}
                      changeActivePathName={this.changeActivePathName.bind(this)}
                      changeMenuOverHeight={this.changeMenuOverHeight.bind(this)}
                    />
                  )
                }
              })}
              {this.state.menuoffsetTop > 0 && (
                <div
                  className='up scrollBtn'
                  onMouseEnter={(e) => {
                    e.stopPropagation()
                    this.setState(
                      {
                        ticking: true,
                      },
                      () => {
                        window.requestAnimationFrame(this.scrollMenuTop)
                      },
                    )
                  }}
                  onMouseLeave={(e) => {
                    e.stopPropagation()
                    this.setState({
                      ticking: false,
                    })
                  }}
                  onClick={(e) => {
                    e.stopPropagation()
                    this.setState({
                      ticking: false,
                    })
                    document.getElementsByClassName('menuBox')[0].scrollTop = 0
                  }}
                >
                  <Icon className='icon' icon='move' />
                </div>
              )}
              {this.state.downShow && (
                <div
                  className='down scrollBtn'
                  onMouseEnter={(e) => {
                    e.stopPropagation()
                    this.setState(
                      {
                        downTick: true,
                      },
                      () => {
                        window.requestAnimationFrame(this.scrollMenuDown)
                      },
                    )
                  }}
                  onMouseLeave={(e) => {
                    e.stopPropagation()

                    this.setState(
                      {
                        downTick: false,
                        // ticking: false
                      },
                      () => {
                        window.requestAnimationFrame(this.scrollMenuDown)
                      },
                    )
                    // console.log(this.state.downTick);
                  }}
                  onClick={(e) => {
                    e.stopPropagation()
                    const distance = this.state.scrollDownHeight
                    console.log('distance' + distance)
                    document.getElementsByClassName('menuBox')[0].scrollTop = distance
                  }}
                >
                  <Icon className='icon' icon='move' />
                  {/* <span className="selectArrow"></span> */}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }
}

@connect((state) => ({
  // routeLocation: state.realtimeLocation.routeConfig,
  currentPathname: state.routing.locationBeforeTransitions.pathname,
}))
class NavItem extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isRemote: false,
    }

    this.pathname = props.route.path || ''
    this.pathname = (this.pathname.substr(0, 1) !== '/' ? '/' : '') + this.pathname
    this.childRoutes = (props.route.childRoutes || []).filter((route) => route.show)
  }
  componentDidUpdate(prevProps) {
    if (prevProps.currentPathname != this.props.currentPathname) {
      // console.log('prevProps====>', prevProps.currentPathname, this.props.currentPathname, this.state.isRemote)
      if (prevProps.currentPathname == '/remote') {
        //上一次是远程维护 但这次不是
        if (this.state.isRemote) {
          // console.log('change isRemote 1 上一次是远程维护 但这次不是')
          return this.setState({
            isRemote: false,
          })
        }
      }
      if (this.props.currentPathname == '/remote') {
        //上一次不是远程维护 当前是远程维护
        if (!this.state.isRemote) {
          // console.log('change isRemote 2 上一次不是远程维护 当前是远程维护')
          return this.setState({
            isRemote: true,
          })
        }
      }
      if (this.props.currentPathname != '/remote') {
        //上次不是远程维护 当前不是远程维护
        if (this.state.isRemote) {
          // console.log('change isRemote 3 上次不是远程维护 当前不是远程维护')
          return this.setState({
            isRemote: false,
          })
        }
      }
    } else {
      if (this.props.currentPathname == '/remote') {
        //上一次是远程维护 当前是远程维护
        if (!this.state.isRemote) {
          // console.log('change isRemote 2 上一次不是远程维护 当前是远程维护')
          return this.setState({
            isRemote: true,
          })
        }
      }
    }
  }
  createLinkElement(params = {}) {
    const _this = this
    const {
      pathname,
      isActive,
      iconActive,
      icon,
      name,
      type,
      clickFlag,
      // changeActivePathName
    } = params
    if (type == 1) {
      if (this.state.isRemote) {
        if (isCnHl() || (!isCnHl() && en_menu.indexOf(name) > -1)) {
          return (
            <div
              className='link'
              to={pathname}
              onClick={(e) => {
                e.stopPropagation()
                this.openConfirmModal(pathname).then(() => {
                  _this.reportInfomation(pathname)
                })
              }}
            >
              <Icon className='icon' icon={isActive ? iconActive : icon} />
              {!isCnHl() ? this.renderMenuText(name) : name}
            </div>
          )
        }
      }
      if (isCnHl() || (!isCnHl() && en_menu.indexOf(name) > -1)) {
        return (
          <Link
            className='link'
            to={pathname}
            onClick={(e) => {
              this.props.changeActivePathName(this.pathname, this.props.route.openWindow, this.props.route)
              e.stopPropagation()
              _this.reportInfomation(pathname)
            }}
          >
            <Icon className='icon' icon={isActive ? iconActive : icon} />
            {!isCnHl() ? this.renderMenuText(name) : name}
          </Link>
        )
      }
    } else {
      if (this.state.isRemote) {
        if (isCnHl() || (!isCnHl() && en_menu.indexOf(name) > -1)) {
          return (
            <div
              className={classNames({
                'nav-sub-item': true,
                on: isActive,
                clickFlag: clickFlag ? true : false,
              })}
              to={pathname}
              key={pathname}
              data-pathname={pathname}
              onClick={(e) => {
                e.stopPropagation()
                this.openConfirmModal(pathname).then(() => {
                  _this.reportInfomation(pathname)
                  this.props.changeMenuOverHeight()
                })
              }}
            >
              <Icon className='icon' icon={isActive ? iconActive : icon} />
              {!isCnHl() ? this.renderMenuText(name) : name}
            </div>
          )
        }
      }
      if (isCnHl() || (!isCnHl() && en_menu.indexOf(name) > -1)) {
        return (
          <Link
            className={classNames({
              'nav-sub-item': true,
              on: isActive,
              clickFlag: clickFlag ? true : false,
            })}
            to={pathname}
            key={pathname}
            data-pathname={pathname}
            onClick={(e) => {
              e.stopPropagation()
              _this.reportInfomation(pathname)
              this.props.changeMenuOverHeight()
            }}
          >
            <Icon className='icon' icon={isActive ? iconActive : icon} />
            {!isCnHl() ? this.renderMenuText(name) : name}
          </Link>
        )
      }
    }
  }
  openConfirmModal(pathname) {
    closeModal()
    //判断存在机器人 弹窗提示
    // const remoteRobot = localStorage.getItem('remoteRobot');
    // if (remoteRobot) {
    //     return modal({
    //         title: '提示',
    //         content: '确认要退出并断开远程维护吗？',
    //     })
    //         .then(() => {
    //             console.log('断开机器连接=============》》》》》断开机器连接===========》》》》》断开机器连接')
    //             push(pathname)

    //         })
    // }
    return Promise.resolve(push(pathname))
  }

  renderMenuText(name) {
    switch (name) {
      case '综合服务员':
        return __('MENU.GENERAL_SERVICE')
        break
      case '全局功能':
        return __('MENU.GLOBAL_FUNCTION')
        break
      case '基础状态':
        return __('MENU.BASIC_STATUS')
        break
      case '自定义首页':
        return __('MENU.CUSTOMIZE_HOMEPAGE')
        break
      case '招揽':
        return __('MENU.DISTANT_GREETINGS')
        break
      case '欢迎语':
        return __('MENU.GREETINGS')
        break
      case '自定义问答':
        return __('MENU.Q&A')
        break
      case '关键词问答':
        return __('MENU.KEYWORD_Q&A')
        break
      case '专属词库':
        return __('MENU.Terminology')
        break
      case '智能学习':
        return __('MENU.Learning')
        break
      case '问答库':
        return __('MENU.Q&As')
        break
      case '智能推荐':
        return __('MENU.INTELLIGENT_RECOMMENDATION')
        break
      case '对话引导':
        return __('MENU.DIALOGUE_GUIDE')
        break
      case '导览讲解':
        return __('MENU.NAVI_INTRO')
        break
      case '问路&引领':
        return __('MENU.GUIDE')
        break
      case '设置':
        return __('MENU.SETTINGS')
        break
      case '企业信息':
        return __('MENU.ENTERPRISE_INFO')
        break
      case '用户管理':
        return __('MENU.USER_MANAGEMENT')
        break
      case '员工管理':
        return __('MENU.EMPLOYEE_MANAGEMENT')
        break
      case '权限管理':
        return __('MENU.ACCESS_MANAGEMENT')
        break
      case '沉默搭讪':
        return __('MENU.ICEBREAKER')
        break
      case '来访接待':
        return __('MENU.RECEPTION')
        break
      case 'VIP服务':
        return __('MENU.VIP_SERVICES')
        break
      case '操作记录':
        return __('MENU.OPERATION_HISTORY')
        break
      case '专属词库':
        return __('MENU.Terminology')
        break
      case '远程维护':
        return __('MENU.REMOTE_CONTROL')
        break
      case '轻应用中心':
        return __('MENU.MINI_APP')
        break
      case '对话播报':
        return __('MENU.BROADCAST_RECOMMENDATION')
        break
    }
  }

  render() {
    const { name, icon, iconActive, clickFlag, subTitle } = this.props.route
    const hasChildRoutes = Array.isArray(this.childRoutes) && this.childRoutes.length > 0
    let isActive =
      this.pathname === '/'
        ? this.pathname === this.props.currentPathname
        : this.props.currentPathname.includes(this.pathname) && this.props.currentPathname !== '/'

    return (
      <div
        className={classNames({
          'nav-item': true,
          // 'cur': this.props.activePathName == this.pathname || isActive,
          cur: isActive,
          showChild: this.props.activePathName != this.pathname || this.props.activePathName == '' ? false : true,
          clickFlag: clickFlag,
          subTitle: subTitle,
        })}
        key={this.pathname}
        data-pathname={this.pathname}
        onClick={(e) => {
          this.setState({
            showChild: !this.state.showChild,
          })
          e.stopPropagation()
          this.props.changeActivePathName(this.pathname, this.props.route.openWindow, this.props.route)
          this.props.changeMenuOverHeight()
        }}
      >
        {!hasChildRoutes ? (
          this.createLinkElement({
            pathname: this.pathname,
            isActive,
            iconActive,
            icon,
            name,
            type: 1,
            // changeActivePathName:this.props.changeActivePathName(this.pathname, this.props.route.openWindow, this.props.route)
          })
        ) : (
          <span className='link'>
            <Icon className='icon' icon={isActive ? iconActive : icon} />
            {/* 由于部分菜单项没给到外文翻译文案，目前只转英文环境的文案 */}
            {!isCnHl() ? this.renderMenuText(name) : name}
            {hasChildRoutes && <Icon icon='fix_next' className='arrow' />}
          </span>
        )}
        {hasChildRoutes && (
          <div
            className={classNames({
              'nav-sub': true,
            })}
            key={this.pathname + '-sub'}
          >
            {this.renderSubItem(this.pathname, this.props.route.indexRoute)}
            {this.childRoutes.map((childRoute) =>
              // this.renderSubItem(`${this.pathname}/${childRoute.path}`, childRoute)
              this.renderSubItem(`${childRoute.path}`, childRoute),
            )}
          </div>
        )}
      </div>
    )
  }

  renderSubItem(pathname, { name, icon, iconActive, clickFlag }) {
    pathname = (pathname.substr(0, 1) !== '/' ? '/' : '') + pathname
    const isActive =
      this.props.currentPathname === pathname ||
      (this.props.currentPathname == '/guide/scenes/subResources' && pathname == '/guide/package') ||
      (this.props.currentPathname == '/guide/scenes/subResources3' && pathname == '/guide') ||
      (this.props.currentPathname == '/guide/scenes' && pathname == '/guide/scene') ||
      (this.props.currentPathname == '/advert/add' && pathname == '/advert/list') ||
      (this.props.currentPathname == '/replies/addCard' && pathname == '/replies') ||
      (this.props.currentPathname == '/replies/reading/addReading' && pathname == '/replies/reading') ||
      (this.props.currentPathname == '/reception/VIPservice/addWelcome' && pathname == '/reception/VIPservice') ||
      (this.props.currentPathname == '/reception/addReception_package' && pathname == '/reception')
    return this.createLinkElement({
      pathname,
      isActive,
      iconActive,
      icon,
      name,
      clickFlag,
    })
  }
  reportInfomation(pathname) {
    // console.log(pathname)
    switch (pathname) {
      case '/': //首页
        _czc.push(['_trackEvent', 'index', 'view'])
        break
      case '/home': //自定义首页
        _czc.push(['_trackEvent', 'home', 'view'])
        break
      case '/solicit': //招揽
        _czc.push(['_trackEvent', 'solicit', 'view'])
        break
      case '/corpus': //欢迎语
        _czc.push(['_trackEvent', 'greeting', 'view'])
        break
      case '/askWay': //问路引领
        _czc.push(['_trackEvent', 'leading', 'view'])
        break
      case '/visitors': //访客管理
        _czc.push(['_trackEvent', 'visitors', 'view'])
        break
      case '/staff': //员工管理
        _czc.push(['_trackEvent', 'staff', 'view'])
        break
      case '/permissions': //管理员权限
        _czc.push(['_trackEvent', 'permissions', 'view'])
        break
      case '/replies': //企业问答库
        _czc.push(['_trackEvent', 'replies', 'view'])
        break
      case '/replies/words': //专属词库
        _czc.push(['_trackEvent', 'replies', 'nav_words'])
        break
      case '/replies/learn': //智能学习
        _czc.push(['_trackEvent', 'replies', 'nav_learn'])
        break
      case '/replies/voice': //语音报表
        _czc.push(['_trackEvent', 'replies', 'nav_voice'])
        break
      case '/guide': //导览 进入"v3.3及以上"
        _czc.push(['_trackEvent', 'guide', 'view_3.3'])
        break
      case '/guide/package': //导览 进入"v3.2"
        _czc.push(['_trackEvent', 'guide', 'view_3.2'])
        break
      case '/guide/scene': //导览 进入"v3.1"
        _czc.push(['_trackEvent', 'guide', 'view_3.1'])
        break
      case '/advert': //广告 进入"v3.3及以上"
        _czc.push(['_trackEvent', 'advert', 'view_3.3'])
        break
      case '/advert/list': //广告 进入"v3.2"
        _czc.push(['_trackEvent', 'advert', 'view_3.2'])
        break
      case '/reception': //接待
        _czc.push(['_trackEvent', 'reception', 'view'])
        break
      case '/reception/SMS': //短信
        _czc.push(['_trackEvent', 'reception', 'sms'])
        break
    }
  }
}
