import React from 'react'
import { connect } from 'react-redux'
import classNames from 'classnames'
import { extend } from 'koot'

import routes from '@app/router'

import { Link } from 'biz-components'
import { Icon } from 'biz-components'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  routes = routes.childRoutes.filter((route) => !!route.show)
  constructor(props) {
    super(props)
  }
  render() {
    return (
      <div className={this.props.className}>
        <div className='floatMenu'>
          <div className='logo'>
            <div className='svg'></div>
            {/* <Icon icon="logo-2" className="svg" /> */}
          </div>
          <div className='menuList'>
            <NavItem route={routes.indexRoute} />
            {this.routes.map((route, index) => (
              <NavItem route={route} key={index} num={index} />
            ))}
          </div>
        </div>
      </div>
    )
  }
}

@connect((state) => ({
  currentPathname: state.routing.locationBeforeTransitions.pathname,
}))
class NavItem extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      showSub: false,
      navFlag: true,
      show: false,
    }

    this.pathname = props.route.path || ''
    this.pathname = (this.pathname.substr(0, 1) !== '/' ? '/' : '') + this.pathname

    this.childRoutes = (props.route.childRoutes || []).filter((route) => route.show)
    document.body.addEventListener('click', () => {
      this.setState({
        showSub: false,
      })
    })
  }

  // timeoutHideSub: undefined

  render() {
    const { name, icon, iconActive } = this.props.route

    const hasChildRoutes = Array.isArray(this.childRoutes) && this.childRoutes.length > 0
    let isActive =
      this.pathname === '/'
        ? this.pathname === this.props.currentPathname
        : this.props.currentPathname.includes(this.pathname) && this.props.currentPathname !== '/'

    return (
      <div
        className={classNames({
          'nav-item': true,
          // 'on': isActive,
          // 'on': hasChildRoutes ? this: isActive,
          // 'on': hasChildRoutes ? true: isActive,
          on: this.state.navFlag && this.state.showSub,
          cur: isActive,
        })}
        key={this.pathname}
        data-pathname={this.pathname}
        onMouseEnter={() => {
          clearTimeout(this.timeoutHideSub)
          this.setState({
            showSub: true,
            navFlag: true,
          })
        }}
        onMouseLeave={() => {
          clearTimeout(this.timeoutHideSub)
          this.timeoutHideSub = setTimeout(() => {
            this.setState({
              showSub: false,
              navFlag: false,
            })
          }, 50)
        }}
        onClick={(e) => {
          e.stopPropagation()
          // clearTimeout(this.timeoutHideSub)
          // if(hasChildRoutes){
          //     this.setState({
          //         showSub: true
          //     })
          // }else{
          //     this.setState({
          //         showSub: false
          //     })
          // }

          // this.setState({
          //     showSub: navFlag == true ? hasChildRoutes : false
          // })
          this.setState({
            // showSub: hasChildRoutes,
            showSub: true,
            navFlag: true,
            // show:true
          })
        }}
      >
        {!hasChildRoutes ? (
          <Link className='link' to={this.pathname}>
            <Icon className='icon' icon={isActive ? iconActive : icon} />
            {name}
            {hasChildRoutes && <Icon icon='fix_next' className='arrow' />}
          </Link>
        ) : (
          <span
            className='link'
            // to={this.pathname}
          >
            <Icon className='icon' icon={isActive ? iconActive : icon} />
            {name}
            {hasChildRoutes && <Icon icon='fix_next' className='arrow' />}
          </span>
        )}

        {hasChildRoutes && (
          <div
            className={classNames({
              'nav-sub': true,
              on: this.state.showSub,
              // 'on': this.state.navFlag && this.state.showSub,
            })}
            key={this.pathname + '-sub'}
          >
            {this.renderSubItem(this.pathname, this.props.route.indexRoute)}
            {this.childRoutes.map((childRoute) =>
              this.renderSubItem(`${this.pathname}/${childRoute.path}`, childRoute),
            )}
          </div>
        )}
      </div>
    )
  }

  renderSubItem(pathname, { name, icon, iconActive }) {
    const isActive = this.props.currentPathname === pathname

    return (
      <Link
        className={classNames({
          'nav-sub-item': true,
          on: isActive,
        })}
        to={pathname}
        key={pathname}
        data-pathname={pathname}
        onClick={(e) => {
          e.stopPropagation()
          setTimeout(() => {
            this.setState({
              navFlag: false,
            })
          }, 450)
        }}
      >
        <Icon className='icon' icon={isActive ? iconActive : icon} />
        {name}
      </Link>
    )
  }
}
