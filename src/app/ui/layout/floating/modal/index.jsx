import React from 'react'
import { connect } from 'react-redux'
import classNames from 'classnames'
import { extend } from 'koot'

import { closeTop as modalCloseTop } from '@api/modal'
import Floating from '@ui/layout/floating'

@connect((state) => ({
  current: Array.isArray(state.modal.modals) ? state.modal.modals[0] : undefined,
  closeOnMask: state.modal.closeOnMask,
}))
@extend({
  styles: require('./styles.less'),
})
export default class ModalContainer extends React.Component {
  // timeoutCloseOnMask = undefined
  state = {
    fullscreen: true,
  }

  isOn(props = this.props) {
    return Array.isArray(props.current) && typeof props.current[1] === 'object'
  }

  isFullscreen(props = this.props) {
    return this.isOn() && props.current[1].props.className.split(' ').includes('is-fullscreen')
  }

  onContainerClick() {
    if (this.timeoutCloseOnMask) {
      clearTimeout(this.timeoutCloseOnMask)
      this.timeoutCloseOnMask = undefined
    }
    if (this.clickInside) {
      this.clickInside = false
      return
    }
    this.timeoutCloseOnMask = setTimeout(() => {
      if (this.clickInside) return
      if (!this.props.closeOnMask) return
      this.props.dispatch(modalCloseTop())
    }, 10)
  }

  onTransitionEnd = (evt) => {
    // if (evt.target === evt.currentTarget) {
    //     console.log(123)
    // }
  }

  componentDidUpdate(prevProps) {
    if (this.mounted && !this.isOn(prevProps) && this.isOn()) {
      this.setState({
        fullscreen: this.isFullscreen(),
      })
    }
    //     const [id, component] = this.props.current || []
    //     if (component) {
    //         component.props.className.split(' ').includes('is-fullscreen')
    // }
  }

  componentDidMount() {
    this.mounted = true
    if (this.isOn()) {
      this.setState({
        fullscreen: this.isFullscreen(),
      })
    }
  }

  componentWillUnmount() {
    this.mounted = false
  }

  render() {
    const [id, component] = this.props.current || []
    const classNameThis = this.props.className.split(' ')[0]
    return (
      <Floating
        className={classNames({
          [this.props.className]: true,
          on: this.isOn(),
          'is-fullscreen': this.state.fullscreen,
        })}
        data-role='modal-container'
        onClick={this.onContainerClick.bind(this)}
        childrenId={id}
        onTransitionEnd={this.onTransitionEnd}
      >
        {component && (
          <div
            className={classNameThis + '-wrapper'}
            onClick={() => (this.clickInside = true)}
            data-modal-id={id}
            children={component}
          />
        )}
      </Floating>
    )
  }
}
