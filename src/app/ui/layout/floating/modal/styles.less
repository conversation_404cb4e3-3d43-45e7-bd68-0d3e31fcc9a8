@import '~base.less';

.component {
  position: fixed;
  z-index: @z-index-modal;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  // :lang(en){
  //     left: @nav-all-width;
  //     width: calc(~"100% - @{nav-all-width}");
  // }

  &:after {
    content: '';
    position: absolute;
    z-index: -100;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: rgba(248, 250, 250, 0.9);
    opacity: 0;
    transition-property: opacity;
  }

  &.is-fullscreen {
    left: 0;
    width: 100%;
    &:after {
      // background: rgba(43, 43, 43, .6);
    }
  }

  &.on {
    pointer-events: all;
    // backdrop-filter: blur(20px);
    &:after {
      opacity: 1;
    }
  }
}

.component-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: 0% 0%;

  // &.transition-enter,
  // &.transition-exit{
  // }

  &.transition-enter {
    transform: scale(0.01) translate(-50%, -50%);
  }
  &.transition-enter-active {
    transform: scale(1) translate(-50%, -50%);
  }

  &.transition-exit {
    opacity: 1;
  }
  &.transition-exit-active {
    opacity: 0.01;
  }
}
