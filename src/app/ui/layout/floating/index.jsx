import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom'
import TransitionGroup from 'react-transition-group/TransitionGroup'
import CSSTransition from 'react-transition-group/CSSTransition'

export default ({
  children,
  childrenId,

  transitionClassName = 'transition',
  transitionTimeout = 250,

  ...props
}) =>
  ReactDOM.createPortal(
    <TransitionGroup
      component='div'
      // ref={el => this._container = ReactDOM.findDOMNode(el)}
      {...props}
    >
      {!!children && (
        <CSSTransition
          classNames={transitionClassName}
          timeout={transitionTimeout}
          key={childrenId}
          children={children}
        />
      )}
    </TransitionGroup>,
    document.body,
  )
