@import '~base.less';

.component {
  border: 60px solid transparent;
  border-top-width: @main-gutter-vertical;
  border-left-width: (@nav-width + @main-gutter-horizontal);
  border-left-width: 2rem;
  border-right-width: @main-gutter-horizontal;
  min-height: 100vh;
  position: relative;

  &.noneBorder {
    border-width: 0 30px !important;
  }
  &.border0 {
    border: 0;
  }

  // 分页样式
  .rc-pagination {
    display: inline-block;
    position: absolute;
    right: 0;

    // left:0;
    li {
      margin: 0 4px !important;
    }

    li.rc-pagination-item,
    li.rc-pagination-prev,
    li.rc-pagination-next {
      background: rgba(85, 195, 251, 0.2) !important;
      border: none !important;
      width: 30px;
      height: 30px;
      line-height: 30px;
      outline: none !important;

      &:hover {
        background: rgba(85, 195, 251, 0.4) !important;
      }

      a {
        color: @color-theme !important;
      }

      &.rc-pagination-item-active {
        background: @color-theme !important;

        a {
          color: #fff !important;
        }
      }

      &.rc-pagination-disabled {
        opacity: 0.5;

        &:hover {
          background: rgba(85, 195, 251, 0.2) !important;
        }
      }
    }

    .rc-select-selection {
      background: rgba(85, 195, 251, 0.2) !important;
      border: none !important;
      height: 30px;
      line-height: 30px;

      .rc-select-selection-selected-value {
        height: 30px;
        line-height: 30px;
        color: @color-theme !important;
      }

      .rc-select-arrow-icon {
        border-top-color: @color-theme !important;
      }
    }

    .rc-select-dropdown-menu {
      li:hover {
        background: rgba(85, 195, 251, 0.2) !important;
        color: @color-theme !important;
      }

      .rc-select-dropdown-menu-item-selected {
        background: @color-theme !important;
        color: #fff !important;
      }
    }

    .rc-pagination-options-quick-jumper {
      color: @color-theme;

      input {
        width: 70px;
        height: 30px;
        border: none;
        background: rgba(85, 195, 251, 0.2);
        color: @color-theme;
      }

      button {
        height: 30px;
        line-height: 30px;
        border: none;
        background: @color-theme;
        color: #fff;
        outline: none !important;
      }
    }

    .rc-pagination-total-text {
      color: #555d61;
      font-size: 14px;
      margin-right: 20px;
    }
  }

  // 分页样式 end
}
