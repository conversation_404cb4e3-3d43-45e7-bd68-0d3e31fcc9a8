@import '~base.less';

.component {
  .corp-tips {
    text-align: left;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: normal;
    color: #555d61;
    font-weight: 400;
    width: 17.85rem;
    margin: 0 auto;
    margin-top: 20px;
    height: 22px;
    line-height: 22px;
    margin-bottom: 4px;
  }
  .text-tips {
    color: rgba(0, 0, 0, 0.65);
    text-align: left;
    margin-bottom: 30px;
    font-size: 17px;
    font-weight: 600;
    width: 357px;
    color: #555d61;
    margin-left: 162px;
    margin-right: 162px;
    margin-bottom: 34px;
    .red {
      color: #9c0202;
    }
    margin: 0 auto;
  }
  .left {
    width: 110px;
    height: 110px;
    background: url('@assets/png/alert/icon_warning.png') no-repeat;
    background-size: cover;
    position: absolute;
    left: 20px;
    top: 32px;
  }
  .right {
    width: 105px;
    height: 100px;
    background: url('@assets/png/alert/icon_unusable.png') no-repeat;
    background-size: cover;
    position: absolute;
    right: 0;
    top: 40px;
  }
  .module-list {
    display: flex;
    position: relative;
    flex-direction: column;
    // height: 240px;
    max-height: 330px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: 34.5px;
    cursor: pointer;
    .line {
      width: 736px;
      height: 2px;
      background-color: #eff1f2;
      position: absolute;
      // margin:0 20px;
      // top: 23px;
      // left: 62px;
    }
    .tobe {
      font-size: 17px;
      color: #9c0202;
      // margin:10px  auto 10px 20px;
      height: 44px;
      line-height: 44px;
      margin-left: 1rem;
      text-align: left;
      margin-top: 0.1rem;
    }
    .module-scroll {
      overflow-y: auto;
      ::-webkit-scrollbar {
        width: 6px;
      }
      ::-webkit-scrollbar-thumb {
        border-radius: 10px;
        height: 20px;
        background: #ccd2d6; // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
      }
    }

    &.scroll {
      overflow-y: scroll;

      ::-webkit-scrollbar {
        width: 6px;
      }

      /* 滚动槽 */
      ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0);
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
      }

      /* 滚动条滑块 */
      ::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: @color-theme; // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
      }

      ::-webkit-scrollbar-thumb:window-inactive {
        background: @color-theme;
      }
    }
    .module {
      position: relative;
      margin-right: 60px;
      z-index: 9;
      width: 600px;
      margin-bottom: 20px;
      // &:last-child{
      //     margin-right: 0;
      // }
      .status-icon {
        width: 14px;
        height: 14px;
        position: absolute;
        left: 18px;
        top: 22px;
        top: 50%;
        transform: translateY(-50%);
      }
      .green {
        color: #00cb9d;
      }
      .orange {
        color: #ff7d1e;
      }
      .deepred {
        color: #d2322a;
      }
      .hover-status {
        cursor: pointer;
      }
      .description {
        // width: 96px;
        width: 548px;
        position: relative;
        text-align: left;
        padding-left: 40px;
        font-size: 14px;
        color: @color-base;
        margin-bottom: 0;
        cursor: pointer;
        // margin: 10px auto 0px 0;
        &:hover {
          color: #3776ef;
        }
        span {
          // color: #3776ef;
          // text-decoration: underline;
          cursor: pointer;
        }
        height: 36px;
        line-height: 36px;
        border: solid 1px #eff1f2;
        border-radius: 18px;
        .more {
          // #3776ef
          color: #3776ef;
          position: absolute;
          right: 30px;
          top: 0;
        }
        .more_icon {
          display: inline-block;
          position: absolute;
          right: -20px;
          width: 9px;
          height: 10px;
          color: #3776ef;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .dm {
        background-color: #f7f7f7;
        color: #3776ef;
      }
      .deta {
        font-size: 12px;
        text-align: left;
        .detail {
          // margin-left:15px;
          // margin-top: 10px;
          height: 30px;
          line-height: 30px;
          padding-top: 0.5rem;
          background: #f7f7f7;
          padding-left: 0.75rem;
          margin-top: -20px;
          width: 548px;
          height: 50px;
          line-height: 50px;
          border-bottom-left-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }
      .hidden {
        display: none;
      }
      .robotlist {
        // display: flex;
        margin: 16px;
        width: 548px;
        background: #f7f7f7;
        overflow: hidden;
        margin-top: -22px;
        margin-left: 0;
        padding-top: 30px;
        padding-left: 16px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;

        p {
          height: 1.1rem;
          line-height: 1.1rem;
          border: 0.05rem solid #3776ef;
          float: left;
          margin-right: 10px;
          font-size: 12px;
          padding-left: 8px;
          padding-right: 10px;
          border-radius: 4px;
          white-space: nowrap;
          color: #3776ef;
          &:hover {
            background-color: #3776ef;
            color: #fff;
          }
        }
        p:last-child {
          padding-bottom: 20px;
        }
      }
      .config-btn {
        width: 130px;
        height: 30px;
        font-size: 14px;
        text-align: center;
        line-height: 30px;
        border: none;
        border-radius: 16px;
        outline: none;
        position: absolute;
        right: -49px;
        // top: 12px;
        width: 80px;
        top: 50%;
        transform: translateY(-50%);
        &:disabled {
          background-color: #eff1f2;
          color: @color-base;
        }
      }
      .clickable {
        background-color: @color-theme;
        color: #ffffff;
        cursor: pointer;
      }
    }
  }
}
