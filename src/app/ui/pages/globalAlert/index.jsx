import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import classNames from 'classnames'
import modal, { close as closeModal } from '@utils/modal'
import { push } from '@src/app/utils/history'
import { Icon } from 'biz-components'
import { getRobotStatistics, selectRobot, getRobotProfile } from '@api/robots'
import { setLocalStorageItem } from '@utils/tools'

// import { getConfigStatus } from '@api/user'

import { Popover } from 'antd'

@connect((state) => {
  let devUrl = ''
  console.log(state)
  if (__DEV__) {
    devUrl = 'http://tjd.ainirobot.com/'
  }
  return {
    user: state.user,
  }
})
@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      // isComplete: true,
      // globalConfigTips: [{
      //     configName: 'top_qa',
      //     isComplete: true,
      //     doneTips: '高频问答的数量合格',
      //     doneBtnText: '数量越多越好',
      //     undoneTips: '高频问答数量不足30条',
      //     undoneBtnText: '去配置问答'
      // }, {
      //     configName: 'skill_home',
      //     isComplete: true,
      //     doneTips: '所有机器人首页已定制',
      //     doneBtnText: '建议定期更新哦',
      //     undoneTips: '存在机器人未定制专属首页',
      //     undoneBtnText: '去配置首页',
      //     robots: []
      // }, {
      //     configName: 'greet',
      //     isComplete: true,
      //     doneTips: '所有机器人招揽语已定制',
      //     doneBtnText: '建议定期更新哦',
      //     undoneTips: '存在机器人未定制招揽语',
      //     undoneBtnText: '去配置招揽语',
      //     robots: []
      // }, {
      //     configName: 'welcome',
      //     isComplete: true,
      //     doneTips: '所有机器人欢迎语已定制',
      //     doneBtnText: '建议定期更新哦',
      //     undoneTips: '存在机器人未配置欢迎语',
      //     undoneBtnText: '去配置欢迎语',
      //     robots: []
      // }, {
      //     configName: 'location',
      //     isComplete: true,
      //     doneTips: '位置点的数量合格',
      //     doneBtnText: '数量越多越好',
      //     undoneTips: '位置点数量不足10个',
      //     undoneBtnText: '去配置问路引领'
      // }]
      display: {
        top_qa: false,
        skill_home: false,
        greet: false,
        welcome: false,
        location: false,
      },
    }
  }

  componentDidMount() {
    // this.getConfigStatistics()
  }

  componentDidUpdate() {
    // let showModal = false;
    // this.state.globalConfigTips.map((item, i) => {
    //     if(!item.isComplete){
    //         showModal = true
    //     }
    // })
    // if(!showModal){
    // closeModal();
    // }
  }
  goPage(configName) {
    switch (configName) {
      case 'top_qa':
        localStorage.setItem('top_qa', true)
        push('/replies/newReplies')
        break
      case 'skill_home':
        push('/home')
        break
      case 'greet':
        push('/solicit')
        break
      case 'welcome':
        push('/corpus')
        break
      case 'location':
        push('/askWay')
        break
      default:
        console.log('others')
    }
  }

  render() {
    let corp = ''
    if (this.props.user.corp != undefined && this.props.user.corp.corp_name) {
      corp = this.props.user.corp.corp_name
    }
    let globalConfigTips = [
      {
        configName: 'top_qa',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            高频库问答的数量
            <br />
            合格
          </p>
        ),
        doneBtnText: '数量越多越好',
        undoneTips: '高频库问答数量不足30条',
        undoneBtnText: '去配置',
      },
      {
        configName: 'skill_home',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            所有机器人首页
            <br />
            已定制
          </p>
        ),
        doneBtnText: '建议定期更新哦',
        undoneTips: '存在机器人未定制专属首页',
        undoneBtnText: '去配置',
        robots: [],
      },
      {
        configName: 'greet',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            所有机器人招揽语
            <br />
            已定制
          </p>
        ),
        doneBtnText: '建议定期更新哦',
        undoneTips: '存在机器人未定制招揽语',
        undoneBtnText: '去配置',
        robots: [],
      },
      {
        configName: 'welcome',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            所有机器人欢迎语
            <br />
            已定制
          </p>
        ),
        doneBtnText: '建议定期更新哦',
        undoneTips: '存在机器人未配置欢迎语',
        undoneBtnText: '去配置',
        robots: [],
      },
      {
        configName: 'location',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            位置点的数量
            <br />
            合格
          </p>
        ),
        doneBtnText: '数量越多越好',
        undoneTips: '位置点数量不足3个',
        undoneBtnText: '去配置',
      },
    ]
    globalConfigTips[0].isComplete = this.props.configStatus.top_qa.satisfied
    globalConfigTips[0].undoneTips = (
      <p className='tips-text'>问答库和关键词问答总数不足{this.props.configStatus.top_qa.expect}条</p>
    )
    globalConfigTips[0].detail = (
      <p className='detail'>目前您所在企业只配置了{this.props.configStatus.top_qa.count}条问答，不满足最低要求</p>
    )
    globalConfigTips[1].isComplete = this.props.configStatus.skill_home.satisfied
    globalConfigTips[1].robots = this.props.configStatus.skill_home.robots
    globalConfigTips[1].undoneTips = (
      <p className='tips-text'>
        {/* <Popover placement="rightBottom" arrowPointAtCenter content={
            <div className="robot-popover">
                {globalConfigTips[1].robots.map((robot, i) => {
                    return <p key={robot.uuid}>{` ${robot.name}`}</p>
                })}
            </div>
        } trigger="hover"></Popover> */}
        <span>{this.props.configStatus.skill_home.unset_count}台</span>机器人未定制专属首页
      </p>
    )
    globalConfigTips[1].undoneRobots = (
      <div className='robotlist'>
        {globalConfigTips[1].robots.map((robot, i) => {
          return (
            <p
              key={robot.uuid}
              onClick={() => {
                let selRobotId = robot.uuid
                this.props.dispatch(selectRobot(selRobotId))
                setLocalStorageItem('localRobotId', selRobotId)
                setLocalStorageItem('globallRobotId', selRobotId)
                setLocalStorageItem('receptionRobotId', selRobotId)
                this.goPage('skill_home')
              }}
            >{` ${robot.name}`}</p>
          )
        })}
      </div>
    )
    globalConfigTips[2].isComplete = this.props.configStatus.greet.satisfied
    globalConfigTips[2].robots = this.props.configStatus.greet.robots
    globalConfigTips[2].undoneTips = (
      <p className='tips-text'>
        <Popover
          placement='rightBottom'
          arrowPointAtCenter
          content={
            <div className='robot-popover'>
              {globalConfigTips[2].robots.map((robot, i) => {
                return (
                  <p
                    key={robot.uuid}
                    onClick={() => {
                      let selRobotId = robot.uuid
                      // this.props.dispatch(selectRobot(selRobotId));
                      setLocalStorageItem('localRobotId', selRobotId)
                      this.goPage('greet')
                    }}
                  >{`${i + 1}. ${robot.name}`}</p>
                )
              })}
            </div>
          }
          trigger='hover'
        ></Popover>
        <span>{this.props.configStatus.greet.unset_count}</span>台机器人未定制招揽语
      </p>
    )
    globalConfigTips[2].undoneRobots = (
      <div className='robotlist'>
        {globalConfigTips[2].robots.map((robot, i) => {
          return (
            <p
              key={robot.uuid}
              onClick={() => {
                let selRobotId = robot.uuid
                // this.props.dispatch(selectRobot(selRobotId));
                setLocalStorageItem('localRobotId', selRobotId)
                this.goPage('greet')
              }}
            >{` ${robot.name}`}</p>
          )
        })}
      </div>
    )

    globalConfigTips[3].isComplete = this.props.configStatus.welcome.satisfied
    globalConfigTips[3].robots = this.props.configStatus.welcome.robots
    globalConfigTips[3].undoneTips = (
      <p className='tips-text'>
        <Popover
          placement='rightBottom'
          arrowPointAtCenter
          content={
            <div className='robot-popover'>
              {globalConfigTips[3].robots.map((robot, i) => {
                return (
                  <p
                    key={robot.uuid}
                    onClick={() => {
                      let selRobotId = robot.uuid
                      // this.props.dispatch(selectRobot(selRobotId));
                      setLocalStorageItem('localRobotId', selRobotId)
                      this.goPage('welcome')
                    }}
                  >{`. ${robot.name}`}</p>
                )
              })}
            </div>
          }
          trigger='hover'
        ></Popover>
        <span>{this.props.configStatus.welcome.unset_count}台</span>机器人未配置欢迎语
      </p>
    )
    globalConfigTips[3].undoneRobots = (
      <div className='robotlist'>
        {globalConfigTips[3].robots.map((robot, i) => {
          return (
            <p
              key={robot.uuid}
              onClick={() => {
                let selRobotId = robot.uuid
                // this.props.dispatch(selectRobot(selRobotId));
                setLocalStorageItem('localRobotId', selRobotId)
                this.goPage('welcome')
              }}
            >{` ${robot.name}`}</p>
          )
        })}
      </div>
    )

    globalConfigTips[4].isComplete = this.props.configStatus.location.satisfied
    globalConfigTips[4].undoneTips = <p className='tips-text'>位置点数量不足3个</p>
    globalConfigTips[4].detail = (
      <p className='detail'>目前您所在企业只添加了{this.props.configStatus.location.count}个地点词，不满足最低要求</p>
    )

    let toEdit = globalConfigTips.filter((item) => {
      return !item.isComplete
    })
    return (
      <div className={this.props.className}>
        <p className='corp-tips'>Hi，{corp}的管理员</p>
        <p className='text-tips'>
          经检测：您企业下的机器人
          <span className='red'> 配置情况极其糟糕， 如不优化，将严重损害您企业形象和产品口碑！ 请立即配置！</span>
        </p>
        <div className='left'></div>
        <div className='right'></div>

        <div className='module-list'>
          <div className='line'></div>
          <span className='tobe'>待优化项（{toEdit.length}项）</span>
          <div className='module-scroll'>
            {globalConfigTips.map((item, i) => {
              if (item.isComplete) {
                // return <div className="module" key={i}>
                //     <Icon icon="fix_edit_done_click" className="status-icon green" />
                //     <p className="description">{item.doneTips}</p>
                //     <button className="config-btn" disabled>{item.doneBtnText}</button>
                // </div>
              } else {
                let popover = ''
                if (item.configName == 'skill_home' || item.configName == 'greet' || item.configName == 'welcome') {
                  popover = <Icon icon='icon_Warning' className='status-icon deepred hover-status' />
                  // <Popover placement="rightBottom" arrowPointAtCenter content={
                  //     // <div className="robot-popover">
                  //     //     {item.robots.map((robot, i) => {
                  //     //         return <p key={robot.uuid}>{`${i+1}. ${robot.name}`}</p>
                  //     //     })}
                  //     // </div>
                  // } trigger="hover">

                  // </Popover>
                } else {
                  popover = <Icon icon='icon_Warning' className='status-icon deepred' />
                }
                return (
                  <div className='module' key={i}>
                    <p
                      className={
                        !!this.state.display[globalConfigTips[i].configName] ? 'description dm' : 'description '
                      }
                      onClick={(e) => {
                        // debugger;
                        let val = this.state.display[globalConfigTips[i].configName]
                        if (val === true) return
                        this.state.display[globalConfigTips[i].configName] = !val
                        console.log(this.state.display)
                        this.setState({
                          display: this.state.display,
                        })
                      }}
                    >
                      {' '}
                      {popover}
                      {item.undoneTips}{' '}
                      <span
                        className='more'
                        onClick={(e) => {
                          e.stopPropagation()
                          e.preventDefault()
                          let val = this.state.display[globalConfigTips[i].configName]
                          this.state.display[globalConfigTips[i].configName] = !val
                          console.log(this.state.display)
                          this.setState({
                            display: this.state.display,
                          })
                        }}
                      >
                        详情
                        <Icon
                          icon={
                            !!this.state.display[globalConfigTips[i].configName]
                              ? 'issue_pack_up                            '
                              : 'issue_unfold'
                          }
                          className='more_icon'
                        />
                      </span>
                    </p>
                    <div
                      className={!!this.state.display[globalConfigTips[i].configName] ? 'deta ' : 'deta hidden'}
                      attr={item.display}
                    >
                      {item.detail && item.detail}
                      {item.undoneRobots && item.undoneRobots}
                    </div>

                    <button
                      className='config-btn clickable'
                      onClick={() => {
                        if (item.robots && item.robots.length != 0) {
                          let selRobotId = item.robots[0].uuid
                          this.props.dispatch(selectRobot(selRobotId))
                          setLocalStorageItem('localRobotId', selRobotId)
                          setLocalStorageItem('globallRobotId', selRobotId)
                          setLocalStorageItem('receptionRobotId', selRobotId)
                        }
                        switch (item.configName) {
                          case 'top_qa':
                            // localStorage.setItem('top_qa', true);
                            push('/replies/newReplies')
                            break
                          case 'skill_home':
                            push('/home')
                            break
                          case 'greet':
                            push('/solicit')
                            break
                          case 'welcome':
                            push('/corpus')
                            break
                          case 'location':
                            push('/askWay')
                            break
                          default:
                            console.log('others')
                        }
                      }}
                    >
                      {item.undoneBtnText}
                    </button>
                  </div>
                )
              }
            })}
          </div>
        </div>
      </div>
    )
  }
}
