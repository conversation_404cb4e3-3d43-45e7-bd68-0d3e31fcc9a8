import React from 'react'
import { extend } from 'koot'

import request from '@utils/request'

import { Section } from '../../index'
// import { Button } from 'biz-components'
import ReactJson from 'react-json-view'
import { Input, Button, message } from 'antd'
// import { message } from 'node_modules/antd/lib/index';

const { TextArea } = Input

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      res: null,
      loading: false,
      param: `{
                page: 1,
                page_rows: 20,
}`,
    }
  }
  checkParam() {
    let param = this.state.param
    let err = 0
    try {
      JSON.parse(param)
    } catch (error) {
      err = 1
    }
    if (err == 1) {
      return false
    }
  }
  render() {
    return (
      <Section name='AJAX自由请求' className={this.props.className}>
        <pre>
          {/* <code>{`import request from '@utils/request'

request('/capi/v1/corp/user_list', {
    method: 'GET',
    data: {
        page: 1,
        page_rows: 20,
    }
}).then(res => {
    console.log(res)
})`}
                    </code> */}
        </pre>
        <Input placeholder='url' value='capi/v1/corp/user_profile'></Input>
        <TextArea
          onChange={(e) => {
            console.log(e.target.value)
            this.setState({
              param: e.target.value,
            })
          }}
          value={this.state.param}
        ></TextArea>
        <Button
          type='button'
          loading={this.state.loading}
          onClick={(evt) => {
            evt.preventDefault()
            let url
            url = 'capi/v1/corp/user_profile'
            // url = '/api/control/robot_list';
            if (!this.checkParam) {
              message.warn('参数格式错误')
              return false
            }
            this.setState({
              loading: true,
            })
            request(url, {
              method: 'GET',
              data: {
                page: 1,
                page_rows: 20,
              },
            })
              .then((res) => {
                console.log(res)
                this.setState({ res })
              })
              .catch((err) => {
                console.log(err)
                this.setState({ res: err })
              })
              .finally((err) => {
                this.setState({
                  loading: false,
                })
              })
          }}
          children={__('MODAL.COMMIT')}
        />
        {this.state.res && <ReactJson src={this.state.res} />}
      </Section>
    )
  }
}
