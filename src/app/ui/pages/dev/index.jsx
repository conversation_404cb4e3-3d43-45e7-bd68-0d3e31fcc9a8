import React from 'react'
// import { connect } from 'react-redux'
import classNames from 'classnames'
import { extend } from 'koot'
// import superPage from 'super-ui-page'

const Section = ({ name, className, children, ...props }) => (
  <div className={classNames(['section', className])} data-section={name} {...props}>
    <h2 className='title'>{name}</h2>
    {children}
  </div>
)

const Group = ({ gap = 15, width = 200, style, wrap = false, children, ...props }) => {
  const content = wrap ? (
    Array.isArray(children) ? (
      children.map((child, index) => (
        <div className='wrapper' key={index}>
          {child}
        </div>
      ))
    ) : (
      <div className='wrapper'>{children}</div>
    )
  ) : (
    children
  )

  return (
    <div
      style={Object.assign({}, style, {
        display: 'grid',
        gridGap: `${gap}px`,
        gridTemplateColumns: `repeat(auto-fill,minmax(${width}px, 1fr))`,
      })}
      {...props}
      children={content}
    />
  )
}

const Set = ({ title, children, className, ...props }) => (
  <fieldset className={classNames(['set', className])} {...props}>
    <legend className='title'>{title}</legend>
    {children}
  </fieldset>
)

export default extend({
  styles: require('./styles.less'),
})(({ className, children }) => <div className={className} children={children} />)

export { Section, Group, Set }
