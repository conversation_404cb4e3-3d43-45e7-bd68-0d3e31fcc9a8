import React from 'react'
import { extend } from 'koot'

import request from '@utils/request'

import { Section } from '../../index'
import { Button } from 'biz-components'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <Section name='AJAX请求' className={this.props.className}>
        <pre>
          <code>
            {`import request from '@utils/request'

request('/capi/v1/corp/user_list', {
    method: 'GET',
    data: {
        page: 1,
        page_rows: 20,
    }
}).then(res => {
    console.log(res)
})`}
          </code>
        </pre>
        <Button
          type='button'
          onClick={(evt) => {
            evt.preventDefault()
            request('/capi/v1/corp/user_list', {
              method: 'GET',
              data: {
                page: 1,
                page_rows: 20,
              },
            })
              .then((res) => {
                console.log(res)
              })
              .catch((err) => {
                console.log(err)
              })
          }}
          children='test'
        />
      </Section>
    )
  }
}
