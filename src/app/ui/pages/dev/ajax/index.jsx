import React from 'react'
import { extend } from 'koot'

import { Title } from 'biz-components'

import SectionRequest from './request'

@extend({
  // styles: require('./styles.less'),
  pageinfo: () => ({
    title: '异步请求 - 开发专用区域',
  }),
})
export default class extends React.Component {
  render = () => (
    <div className={this.props.className}>
      <Title title='异步请求' component='h1' icon='rd_zoomin' />

      <SectionRequest />
    </div>
  )
}
