@import '~base.less';

.component {
  .current {
    @size: 256px;
    width: (@size + 160);
    margin: 0 auto;
    position: relative;
    &::after {
      @_size: (@size + 30);
      content: '';
      position: absolute;
      top: -15px;
      left: 50%;
      width: @_size;
      height: @_size;
      margin-left: (0 - @_size / 2);
      background: #fff;
      border-radius: 5px;
      border: 5px solid @color-border;
      z-index: -1;
    }
    .icon {
      width: @size;
      height: @size;
      display: block;
      margin: 0 auto 30px auto;
    }
    .infos {
      @dt-width: 5.5em;
      display: block;
      margin: 0;
      padding-left: @dt-width;
      position: relative;
      font-size: 16px;
      dt {
        position: absolute;
        left: 0;
        width: @dt-width;
        text-align: right;
        padding-right: 0.5em;
        line-height: 28px;
        margin: 5px 0 0 0;
        &:first-child {
          margin-top: 0;
        }
      }
      dd {
        margin: 5px 0 0 0;
        padding: 0;
      }
    }
  }

  .list {
    margin-top: 30px;
    margin-right: -10px;
    border-top: 2px solid @color-border;
    padding-top: 30px;
    position: relative;
    .select-size {
      flex: none;
      position: absolute;
      background: @color-border;
      top: -15px;
      right: 0;
      font-size: 14px;
      line-height: 1em;
      padding: 5px 5px 5px 7px;
      border-radius: 10px;
      .select {
        display: inline-block;
        margin-left: 5px;
        border-radius: 6px;
        border: 0;
      }
    }
    .icons {
      display: grid;
      grid-gap: 15px;
      grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
    }
    .icon {
      position: relative;
      line-height: 0;
      cursor: pointer;
      &::after {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        bottom: -5px;
        right: -5px;
        background: @color-border;
        border-radius: 5px;
        border: 2px solid @color-border;
        z-index: -1;
      }
      &:hover::after {
        border-color: @color-accent;
      }
      &.is-on::after {
        cursor: default;
        background: #fff;
        border-color: @color-accent;
      }
    }
  }
}
