import React from 'react'
import classNames from 'classnames'
import { extend } from 'koot'

import { Title, Icon } from 'biz-components'

@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: '图标 - 开发专用区域',
  }),
})
export default class extends React.Component {
  state = {
    current: __SVG_IDS__[0],
    size: 32,
  }

  sizes = [16, 32, 48, 64]

  render = () => (
    <div className={this.props.className}>
      <Title title='图标' component='h1' icon='rd_pen' />

      <div className='current'>
        <Icon className='icon' id={this.state.current} />
        <dl className='infos'>
          <dt>图标ID</dt>
          <dd>
            <pre className='code'>
              <code>{this.state.current}</code>
            </pre>
          </dd>
          <dt>React模板</dt>
          <dd>
            <pre className='code'>
              <code>{`<Icon icon="${this.state.current}" />`}</code>
            </pre>
          </dd>
        </dl>
      </div>

      <div className='list'>
        <div className='select-size'>
          <span>预览尺寸</span>
          <select
            className='select'
            onChange={(evt) => {
              this.setState({
                size: evt.target.value,
              })
            }}
            defaultValue={this.state.size}
          >
            {this.sizes.map((name) => (
              <option value={name} key={name} children={`${name}px`} />
            ))}
          </select>
        </div>

        <div
          className='icons'
          style={{
            gridTemplateColumns: `repeat(auto-fill,minmax(${this.state.size}px,1fr))`,
            // 'grid-gap': `${Math.ceil(this.state.size * 0.25)}px`,
          }}
        >
          {__SVG_IDS__.map((icon) => (
            <span
              className={classNames({
                icon: true,
                'is-on': this.state.current === icon,
              })}
              key={icon}
            >
              <Icon
                icon={icon}
                onClick={() =>
                  this.setState({
                    current: icon,
                  })
                }
                style={{
                  width: `${this.state.size}px`,
                  height: `${this.state.size}px`,
                }}
              />
            </span>
          ))}
        </div>
      </div>
    </div>
  )
}
