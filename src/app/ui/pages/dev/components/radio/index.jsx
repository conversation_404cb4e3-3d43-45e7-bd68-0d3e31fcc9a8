import React from 'react'
import { extend } from 'koot'

import { Section, Group } from '../../index'
import { Radio } from 'biz-components'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <Section name='Radio' className={this.props.className}>
        <p>支持所有 HTML 默认的 input[type="radio"] 元素属性</p>

        <h3>默认</h3>
        <Group width={100}>
          <Radio name='radio-group-1' />
          <Radio name='radio-group-1' defaultChecked />
          <Radio name='radio-group-1' />
        </Group>

        <h3>属性: label</h3>
        <Group width={100}>
          <Radio name='radio-group-2' label='文本标签-1' defaultChecked />
          <Radio name='radio-group-2' label='文本标签-2' />
        </Group>
      </Section>
    )
  }
}
