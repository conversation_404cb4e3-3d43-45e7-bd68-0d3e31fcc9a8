import React from 'react'
import { extend } from 'koot'

import { Section, Group } from '../../index'
import { Input } from 'biz-components'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <Section name='Input' className={this.props.className}>
        <p>支持所有 HTML 默认的 input 元素属性</p>

        <h3>默认</h3>
        <Group width={300}>
          <Input />
        </Group>

        <h3>属性: appearance</h3>
        <Group width={300}>
          <Input type='text' placeholder='默认' />
          <Input type='text' placeholder={`appearance="underline"`} appearance='underline' />
          <Input type='text' placeholder={`appearance="block"`} appearance='block' />
        </Group>

        <h3>属性: icon (WORKING...)</h3>
        <Group width={300}>
          <Input type='text' placeholder='默认文字' icon='login_phone' />
          <Input type='text' placeholder='默认文字' appearance='block' icon='login_phone' />
        </Group>
      </Section>
    )
  }
}
