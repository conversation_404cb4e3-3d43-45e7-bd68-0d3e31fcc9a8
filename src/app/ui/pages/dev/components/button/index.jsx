import React from 'react'
import { extend } from 'koot'

import { Section, Group } from '../../index'
import { Button, ButtonSelectFile } from 'biz-components'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <Section name='Button' className={this.props.className}>
        <p>支持所有 HTML 默认的 button 元素属性</p>

        <h3>默认</h3>
        <Group wrap={true}>
          <Button label='按钮' />
          <Button label='禁用状态' disabled />
          <Button>
            按钮组件内可为
            <br />
            <strong>
              <em>任意组件</em>
            </strong>
          </Button>
          <Button label='文字默认居中' style={{ width: '100%' }} />
        </Group>

        <h3>属性: appearance</h3>
        <Group wrap={true}>
          <Button label='默认 (填充)' />
          <Button appearance='solid' label='填充 (solid)' />
          <Button appearance='hollow' label='镂空 (hollow)' />
        </Group>

        <h3>属性: size</h3>
        <Group wrap={true}>
          <Button label='默认 (中)' />
          <Button size='small' label='小 (small)' />
          <Button size='medium' label='中 (medium)' />
          <Button size='large' label='大 (large)' />
        </Group>

        <h3>属性: color</h3>
        <Group wrap={true}>
          <Button label='默认 (蓝)' />
          <Button color='blue' label='蓝 (blue)' />
          <Button color='black' label='黑 (black)' />
          <Button color='yellow' label='黄 (yellow)' />
          <Button color='red' label='红 (red)' />
          <Button color='transparent' label='透明 (transparent)' />
        </Group>

        <h3>属性: icon</h3>
        <Group wrap={true}>
          <Button icon='secend_employee' label='带图标的按钮' />
          <Button icon='secend_right_click' appearance='hollow' label='镂空样式' />
          <Button icon='secend_skill_pic' label='默认居中' style={{ width: '100%' }} />
        </Group>

        <h3>属性: loading</h3>
        <Group wrap={true}>
          <Button loading={true} label='Exercitation elit' />
          <Button loading={true} color='red' size='small' label='Exercitation elit' />
          <Button loading={true} color='black' size='large' appearance='hollow' label='Exercitation elit' />
        </Group>

        <h3>混合使用示例</h3>
        <Group wrap={true}>
          <Button appearance='hollow' color='black' label='镂空，黑色' />
          <Button appearance='hollow' color='yellow' label='镂空，禁用 (所有的禁用状态颜色相同)' disabled />
          <Button size='small' color='yellow' label='小，禁用' disabled />
          <Button appearance='hollow' color='red' size='large' icon='rd_open' label='镂空，红色，大，带图标' />
        </Group>

        <h3>功能型：选择文件</h3>
        <p>
          详见代码中的 <em>onSelect</em> 回调函数
        </p>
        <Group wrap={true}>
          <ButtonSelectFile
            appearance='hollow'
            color='black'
            label='镂空，黑色'
            onSelect={(evt, files) => console.log(files)}
          />
          <ButtonSelectFile color='red' label='填充，红色' onSelect={(evt, files) => console.log(files)} />
        </Group>

        <h3>示例：点击后进入 loading 状态</h3>
        <p>2秒后取消</p>
        <Group wrap={true}>
          <SampleButtonLoading />
        </Group>
      </Section>
    )
  }
}

class SampleButtonLoading extends React.Component {
  state = {
    loading: false,
  }
  render = () => (
    <Button
      loading={this.state.loading}
      onClick={() => {
        this.setState({
          loading: true,
        })
        setTimeout(() => {
          this.setState({
            loading: false,
          })
        }, 2000)
      }}
      label='点我'
    />
  )
}
