import React from 'react'
import { extend } from 'koot'

import { Section, Group } from '../../index'
import { Checkbox } from 'biz-components'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <Section name='Checkbox' className={this.props.className}>
        <p>支持所有 HTML 默认的 input[type="checkbox"] 元素属性</p>

        <h3>默认</h3>
        <Group width={100}>
          <Checkbox />
        </Group>

        <h3>默认选中</h3>
        <Group width={100}>
          <Checkbox defaultChecked />
        </Group>

        <h3>属性: label</h3>
        <Group width={100}>
          <Checkbox label='文本标签' />
          <Checkbox label='默认选中' defaultChecked />
        </Group>

        <h3>属性: icon (WORKING...)</h3>
        <Group width={100}>
          <Checkbox icon='phone' />
          <Checkbox icon='phone' label='文本标签' />
        </Group>
      </Section>
    )
  }
}
