import React from 'react'
// import classNames from 'classnames'
import { extend } from 'koot'

import { Title } from 'biz-components'
import { Dropdown, MenuItem } from 'biz-components'

import SectionButton from './button'
import SectionInput from './input'
import SectionCheckbox from './checkbox'
import SectionRadio from './radio'
import SectionLoader from './loader'

@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: '通用组件 - 开发专用区域',
  }),
})
export default class extends React.Component {
  constructor() {
    super()

    this.components = [
      ['按钮 / Button', <SectionButton />],
      ['文本输入框 / Input', <SectionInput />],
      ['复选框 / Checkbox', <SectionCheckbox />],
      ['单选框 / Radio', <SectionRadio />],
      ['载入中提示元素 / Loader', <SectionLoader />],
    ]
    this.state = {
      current: this.components[0],
    }
  }

  render = () => (
    <div className={this.props.className}>
      <div className='title'>
        <Title title='通用组件' component='h1' icon='rd_new' className='text' />
        <Dropdown
          className='dropdown'
          label={`当前：${this.state.current[0]}`}
          labelButton={{
            appearance: 'hollow',
            color: 'black',
          }}
        >
          {this.components.map((arr) => (
            <MenuItem
              key={arr[0]}
              onClick={() => {
                this.setState({
                  current: arr,
                })
              }}
            >
              {arr[0]}
            </MenuItem>
          ))}
        </Dropdown>
      </div>

      {this.state.current[1]}
    </div>
  )
}
