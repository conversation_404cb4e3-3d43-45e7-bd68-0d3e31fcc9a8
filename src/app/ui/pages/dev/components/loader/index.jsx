import React from 'react'
import { extend } from 'koot'

import { Section, Group } from '../../index'
import { Loader } from 'biz-components'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <Section name='Loader' className={this.props.className}>
        <fieldset>
          <legend>spinner (默认)</legend>

          <h3>默认样式</h3>
          <Group width={50}>
            <Loader />
            <Loader type='spinner' />
          </Group>

          <h3>属性：size</h3>
          <Group width={100}>
            <Loader size='100' />
            <Loader size='10' />
          </Group>

          <h3>属性：color</h3>
          <Group width={50}>
            <Loader color='a00' />
            <Loader color='0a0' />
          </Group>
        </fieldset>
      </Section>
    )
  }
}
