import React from 'react'
import { extend } from 'koot'

import { Section, Group } from '../../index'
import { Button } from 'biz-components'
import { Input } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <React.Fragment>
        <Section name='弹窗 / modal' className={this.props.className}>
          <ModalDefault />
          <ModalFrom />
        </Section>
      </React.Fragment>
    )
  }
}

const ModalDefault = () => (
  <fieldset>
    <legend>信息框</legend>
    <Group>
      <Button
        label='默认'
        onClick={() => {
          modal({
            title: '这是标题',
            content: '这是内容，本例为一行字',
          })
        }}
      />
    </Group>

    <h3>方法：下一步</h3>
    <Group>
      <Button
        label='回调'
        onClick={() => {
          modal({
            title: '这是标题',
            content: '这是内容，本例为一行字',
            onConfirm: () => alert('点击了确认'),
            onCancel: () => alert('点击了取消'),
          })
        }}
      />
      <Button
        label='Promise'
        onClick={() => {
          modal({
            title: '这是标题',
            content: '这是内容，本例为一行字',
          })
            .then(() => alert('点击了确认'))
            .catch(() => alert('点击了取消'))
        }}
      />
    </Group>

    <h3>定制</h3>
    <Group>
      <Button
        label='没有标题'
        onClick={() => {
          modal({
            content: '这是内容，本例为一行字',
          })
        }}
      />
      <Button
        label='内容可为组件'
        onClick={() => {
          modal({
            title: '这是标题',
            content: (
              <div>
                <strong>strong</strong>
                <ul>
                  <li>aaa</li>
                  <li>bbb</li>
                </ul>
              </div>
            ),
          })
        }}
      />
      <Button
        label='没有取消按钮'
        onClick={() => {
          modal({
            title: '这是标题',
            content: '这是内容，本例为一行字',
            cancel: false,
            confirm: false,
            onCancel: () => {
              alert('cancel')
            },
          })
        }}
      />
      <Button
        label='没有确认按钮'
        onClick={() => {
          modal({
            title: '这是标题',
            content: '这是内容，本例为一行字',
            confirm: false,
          })
        }}
      />
      <Button
        label='自定义按钮文字'
        onClick={() => {
          modal({
            title: '这是标题',
            content: '这是内容，本例为一行字',
            cancel: '上一步',
            confirm: '下一步',
          })
        }}
      />
      <Button
        label='没有按钮'
        onClick={() => {
          modal({
            title: '这是标题',
            content: '这是内容，本例为一行字',
            confirm: false,
            cancel: false,
          })
        }}
      />
      <Button
        label='没有按钮，没有标题'
        onClick={() => {
          modal({
            content: '这是内容，本例为一行字',
            confirm: false,
            cancel: false,
          })
        }}
      />
      <Button
        label='非全屏'
        onClick={() => {
          modal({
            content: '这是内容，本例为一行字',
            fullscreen: false,
          })
        }}
      />
    </Group>

    <h3>方法：手动关闭</h3>
    <Group>
      <Button
        label='弹窗内部手动关闭弹窗'
        onClick={() => {
          modal({
            title: '这是标题',
            content: (
              <div>
                <p>Magna do labore nostrud eiusmod ullamco aute proident excepteur qui ex voluptate.</p>
                <Button label='关闭' onClick={() => closeModal()} />
              </div>
            ),
            onConfirm: () => alert('点击了确认'),
            onCancel: () => alert('点击了取消'),
          })
        }}
      />
    </Group>
  </fieldset>
)

const ModalFrom = () => {
  // modal 内存在 form，所以这里的表单内容不需要使用 form 标签
  const sampleForm = (
    <React.Fragment>
      <Input name='name' type='text' placeholder='测试' />
      <Input name='number' type='input' placeholder='测试: 数字输入' />
    </React.Fragment>
  )
  return (
    <fieldset>
      <legend>表单</legend>
      <Group>
        <Button
          label='type: form'
          onClick={() => {
            modal({
              type: 'form',
              title: '这是标题',
              form: sampleForm,
              onSubmit: (evt) => {
                const form = evt.target
                const formData = new FormData(form)
                for (let pair of formData.entries()) {
                  console.log({
                    name: pair[0],
                    value: pair[1],
                  })
                }
              },
            })
          }}
        />
      </Group>

      <h3>定制</h3>
      <Group>
        <Button
          label='标题和提交按钮文字'
          onClick={() => {
            modal({
              type: 'form',
              title: '标题',
              submit: __('MODAL.COMMIT'),
              form: sampleForm,
              onSubmit: (evt) => {
                const form = evt.target
                const formData = new FormData(form)
                for (let pair of formData.entries()) {
                  console.log({
                    name: pair[0],
                    value: pair[1],
                  })
                }
              },
            })
          }}
        />
        <Button
          label='提交按钮样式'
          onClick={() => {
            modal({
              type: 'form',
              title: '标题标题标题',
              submit: __('MODAL.COMMIT'),
              submitButton: {
                appearence: 'hollow',
                color: 'red',
                icon: 'tabbar_camera_click',
              },
              form: sampleForm,
              onSubmit: (evt) => {
                const form = evt.target
                const formData = new FormData(form)
                for (let pair of formData.entries()) {
                  console.log({
                    name: pair[0],
                    value: pair[1],
                  })
                }
              },
            })
          }}
        />
        <Button
          label='非全屏'
          onClick={() => {
            modal({
              type: 'form',
              title: '这是标题',
              fullscreen: false,
              form: sampleForm,
              onSubmit: (evt) => {
                const form = evt.target
                const formData = new FormData(form)
                for (let pair of formData.entries()) {
                  console.log({
                    name: pair[0],
                    value: pair[1],
                  })
                }
              },
            })
          }}
        />
      </Group>
    </fieldset>
  )
}
