import React from 'react'
import { extend } from 'koot'

import { Section, Group } from '../../index'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <Section name='.link' className={this.props.className}>
        <p>链接样式，提供默认样式以及指向、点击时的样式变化。适用于任何文本元素</p>

        <Group wrap={true}>
          <span className='link'>默认样式</span>
          <span className='link color-base'>样式：黑变蓝 (.color-base)</span>
        </Group>
      </Section>
    )
  }
}
