import React from 'react'
import { extend } from 'koot'

import { Title } from 'biz-components'

import SectionLink from './link'

@extend({
  pageinfo: () => ({
    title: '全局样式 - 开发专用区域',
  }),
})
export default class extends React.Component {
  render = () => (
    <div className={this.props.className}>
      <Title title='全局样式' component='h1' icon='rd_new' />

      <SectionLink />
    </div>
  )
}
