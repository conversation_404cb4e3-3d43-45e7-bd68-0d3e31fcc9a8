import React from 'react'
import { extend } from 'koot'

import { TitleBlock } from 'biz-components'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <div>
        <TitleBlock title='TitleBlock' component='h1' icon='secend_control_selfchecking'>
          右侧边
        </TitleBlock>

        <p>该标题组件会固定在顶部。见上面的代码，下文都是占位符</p>
        <p>注：右侧边内容的垂直位置没有做默认处理，需自己在组件中调整</p>

        <p>
          Tempor fugiat culpa in labore ex velit excepteur cillum. Irure aute aliquip amet ea eu labore veniam
          consectetur ut adipisicing. Sit laboris pariatur cillum reprehenderit laboris dolor ipsum veniam mollit ut ea
          consequat est.
        </p>

        <p>
          Do ut aliqua deserunt dolor officia laborum enim. Mollit laborum exercitation aute sint sunt adipisicing
          fugiat esse aute adipisicing. Incididunt eu nulla ad magna. Excepteur adipisicing quis consequat consectetur
          id commodo nostrud consectetur pariatur eu.
        </p>

        <p>
          Sint incididunt in sint anim sint sint. Nisi amet nisi consectetur ea aute consectetur pariatur labore irure
          reprehenderit velit. Aute occaecat aliqua fugiat magna veniam aliquip est est nostrud deserunt ullamco
          officia. Fugiat ea sit est sunt duis esse. In elit reprehenderit consectetur occaecat anim ipsum sint. Sunt ea
          magna irure minim incididunt laborum.
        </p>

        <p>
          Eiusmod in aliqua consectetur officia excepteur sunt commodo dolor occaecat dolor nulla cillum est excepteur.
          In deserunt aliquip ea do occaecat laboris veniam officia fugiat eu pariatur reprehenderit. Magna non
          adipisicing aliquip officia ea cillum in sit.
        </p>

        <p>
          Esse non cillum ipsum elit labore sit laboris. Laboris velit eu esse dolor ad sit. Velit cillum cupidatat
          eiusmod aliquip amet officia velit aute in velit qui magna proident nisi. Qui ad occaecat ullamco cupidatat
          amet labore qui ipsum cillum aliqua fugiat fugiat.
        </p>

        <p>
          Magna irure proident non ad eu ex deserunt nisi fugiat anim aute excepteur. Quis nostrud laboris proident
          magna enim. Enim excepteur adipisicing nostrud anim dolore id eiusmod fugiat tempor.
        </p>

        <p>
          Sunt sunt officia enim ut consequat elit velit tempor est. Quis mollit anim qui ipsum anim proident incididunt
          ullamco non labore culpa est eiusmod. Velit eu commodo pariatur dolore incididunt occaecat cupidatat ut tempor
          ullamco ea. Excepteur laborum laboris est voluptate exercitation adipisicing. Labore proident do enim minim
          labore sunt. Aliquip occaecat velit in amet commodo in do veniam anim. Ad elit quis mollit ex eiusmod nulla
          sunt magna id incididunt enim.
        </p>

        <p>
          Velit occaecat pariatur commodo elit exercitation commodo deserunt adipisicing velit. Non eiusmod eu sint enim
          dolore fugiat consequat. Veniam reprehenderit labore officia id. Ex qui sint et mollit amet ex dolore quis
          eiusmod dolor eu ad. Sunt do dolor consequat qui ad fugiat occaecat occaecat labore quis cupidatat laboris
          mollit. Nostrud eiusmod nostrud culpa duis id laborum. Ea do anim non nulla elit aliqua nisi nulla officia.
        </p>

        <p>
          Lorem id commodo eiusmod tempor magna elit cupidatat dolore in. Aliqua in irure duis non fugiat nulla labore
          ipsum aliquip et. Ut voluptate irure ipsum ullamco in eiusmod cupidatat esse anim labore cupidatat anim.
          Officia ipsum laboris voluptate consequat pariatur quis Lorem sunt culpa culpa elit ea.
        </p>

        <p>
          Laboris exercitation aliqua ad do excepteur nulla ex eu voluptate nostrud elit incididunt enim voluptate.
          Magna aliqua sit aute sunt et laboris. Ullamco occaecat id ex enim est consequat ut mollit eu consequat culpa
          cillum est. Commodo laboris tempor voluptate duis dolore reprehenderit occaecat minim laboris minim minim
          quis. Ad minim adipisicing sit laboris cillum et sint quis adipisicing incididunt nostrud consectetur et. Sit
          elit commodo veniam minim commodo. Eu anim irure consequat sunt amet aliqua exercitation in consequat.
        </p>

        <p>
          Tempor fugiat culpa in labore ex velit excepteur cillum. Irure aute aliquip amet ea eu labore veniam
          consectetur ut adipisicing. Sit laboris pariatur cillum reprehenderit laboris dolor ipsum veniam mollit ut ea
          consequat est.
        </p>

        <p>
          Do ut aliqua deserunt dolor officia laborum enim. Mollit laborum exercitation aute sint sunt adipisicing
          fugiat esse aute adipisicing. Incididunt eu nulla ad magna. Excepteur adipisicing quis consequat consectetur
          id commodo nostrud consectetur pariatur eu.
        </p>

        <p>
          Sint incididunt in sint anim sint sint. Nisi amet nisi consectetur ea aute consectetur pariatur labore irure
          reprehenderit velit. Aute occaecat aliqua fugiat magna veniam aliquip est est nostrud deserunt ullamco
          officia. Fugiat ea sit est sunt duis esse. In elit reprehenderit consectetur occaecat anim ipsum sint. Sunt ea
          magna irure minim incididunt laborum.
        </p>

        <p>
          Eiusmod in aliqua consectetur officia excepteur sunt commodo dolor occaecat dolor nulla cillum est excepteur.
          In deserunt aliquip ea do occaecat laboris veniam officia fugiat eu pariatur reprehenderit. Magna non
          adipisicing aliquip officia ea cillum in sit.
        </p>

        <p>
          Esse non cillum ipsum elit labore sit laboris. Laboris velit eu esse dolor ad sit. Velit cillum cupidatat
          eiusmod aliquip amet officia velit aute in velit qui magna proident nisi. Qui ad occaecat ullamco cupidatat
          amet labore qui ipsum cillum aliqua fugiat fugiat.
        </p>

        <p>
          Magna irure proident non ad eu ex deserunt nisi fugiat anim aute excepteur. Quis nostrud laboris proident
          magna enim. Enim excepteur adipisicing nostrud anim dolore id eiusmod fugiat tempor.
        </p>

        <p>
          Sunt sunt officia enim ut consequat elit velit tempor est. Quis mollit anim qui ipsum anim proident incididunt
          ullamco non labore culpa est eiusmod. Velit eu commodo pariatur dolore incididunt occaecat cupidatat ut tempor
          ullamco ea. Excepteur laborum laboris est voluptate exercitation adipisicing. Labore proident do enim minim
          labore sunt. Aliquip occaecat velit in amet commodo in do veniam anim. Ad elit quis mollit ex eiusmod nulla
          sunt magna id incididunt enim.
        </p>

        <p>
          Velit occaecat pariatur commodo elit exercitation commodo deserunt adipisicing velit. Non eiusmod eu sint enim
          dolore fugiat consequat. Veniam reprehenderit labore officia id. Ex qui sint et mollit amet ex dolore quis
          eiusmod dolor eu ad. Sunt do dolor consequat qui ad fugiat occaecat occaecat labore quis cupidatat laboris
          mollit. Nostrud eiusmod nostrud culpa duis id laborum. Ea do anim non nulla elit aliqua nisi nulla officia.
        </p>

        <p>
          Lorem id commodo eiusmod tempor magna elit cupidatat dolore in. Aliqua in irure duis non fugiat nulla labore
          ipsum aliquip et. Ut voluptate irure ipsum ullamco in eiusmod cupidatat esse anim labore cupidatat anim.
          Officia ipsum laboris voluptate consequat pariatur quis Lorem sunt culpa culpa elit ea.
        </p>

        <p>
          Laboris exercitation aliqua ad do excepteur nulla ex eu voluptate nostrud elit incididunt enim voluptate.
          Magna aliqua sit aute sunt et laboris. Ullamco occaecat id ex enim est consequat ut mollit eu consequat culpa
          cillum est. Commodo laboris tempor voluptate duis dolore reprehenderit occaecat minim laboris minim minim
          quis. Ad minim adipisicing sit laboris cillum et sint quis adipisicing incididunt nostrud consectetur et. Sit
          elit commodo veniam minim commodo. Eu anim irure consequat sunt amet aliqua exercitation in consequat.
        </p>
      </div>
    )
  }
}
