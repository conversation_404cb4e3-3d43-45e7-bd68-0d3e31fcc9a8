import React from 'react'
import { extend } from 'koot'

import { Section, Group, Set } from '../../index'
import { Button, FormXlsUpload } from 'biz-components'
import modal from '@utils/modal'

import request, { getUrl as getApiUrl } from '@utils/request'
import downloadUrl from '@utils/download-url'

@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  render() {
    return (
      <Section name='FormXlsUpload' className={this.props.className}>
        <p>支持所有 HTML 默认的 form 元素属性</p>

        <Set title='默认调用 (批量导入员工)'>
          <FormUploadStaff />
        </Set>

        <Set title='示例：弹框中的使用'>
          <SampleButtonModal />
        </Set>
      </Section>
    )
  }
}

class FormUploadStaff extends React.Component {
  state = {
    // 上传失败，且有错误提示表格
    errorDownload: false,
  }

  /**
   * 点击下载模板按钮时
   */
  onDownload = () => {
    downloadUrl(getApiUrl('/capi/v1/corp/download_import_user_tpl'))
  }

  /**
   * 点击下载错误提示表格时
   */
  onErrorDownload = () => {
    downloadUrl(getApiUrl(this.state.errorDownload))
  }

  /**
   * 选择文件后，开始上传
   * @param {File} file
   * @returns {Promise}
   */
  startUploading = (file) => {
    const data = new FormData()
    data.set('import_content', file)

    return request('/capi/v1/corp/import_user', {
      method: 'POST',
      data,
    }).then((res) => {
      this.setState({
        errorDownload: parseInt(res.fail_count) ? res.fail_url : false,
      })
    })
  }

  /**
   * 点击提交按钮时，表示全部流程结束
   */
  onComplete = () => {
    console.log('onComplete')
  }

  render() {
    return (
      <FormXlsUpload
        errorDownload={this.state.errorDownload}
        onDownload={this.onDownload}
        onErrorDownload={this.onErrorDownload}
        startUploading={this.startUploading}
        onComplete={this.onComplete}
      />
    )
  }
}

const SampleButtonModal = () => (
  <Button
    onClick={() => {
      modal({
        title: false,
        confirm: false,
        cancel: false,
        content: <FormUploadStaff />,
      })
    }}
    label='点我'
  />
)
