import React from 'react'
// import classNames from 'classnames'
import { extend } from 'koot'

import { Title } from 'biz-components'
import { Dropdown, MenuItem } from 'biz-components'

import SectionXlsUpload from './xls-upload'

@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: '复用表单 - 开发专用区域',
  }),
})
export default class extends React.Component {
  constructor() {
    super()

    this.components = [['XLS上传表单', <SectionXlsUpload />]]
    this.state = {
      current: this.components[0],
    }
  }

  render = () => (
    <div className={this.props.className}>
      <div className='title'>
        <Title title='复用表单' component='h1' icon='rd_new' className='text' />
        <Dropdown
          className='dropdown'
          label={`当前：${this.state.current[0]}`}
          labelButton={{
            appearance: 'hollow',
            color: 'black',
          }}
        >
          {this.components.map((arr) => (
            <MenuItem
              key={arr[0]}
              onClick={() => {
                this.setState({
                  current: arr,
                })
              }}
            >
              {arr[0]}
            </MenuItem>
          ))}
        </Dropdown>
      </div>

      {this.state.current[1]}
    </div>
  )
}
