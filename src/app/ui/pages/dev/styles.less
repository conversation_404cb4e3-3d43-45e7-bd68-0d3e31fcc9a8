@import '~base.less';

.component {
  user-select: none;
  @gutter: 3em;
  .section {
    position: relative;
    border-top: @gutter solid transparent;
    border-bottom: @gutter solid transparent;
    &::before,
    &::after {
      content: '';
      position: absolute;
      left: 2em;
      right: 2em;
      height: 2px;
    }
    &::before {
      top: (0 - @gutter);
      background: linear-gradient(to bottom, #fff 50%, fade(#fff, 0%));
    }
    &::after {
      bottom: (0 - @gutter);
      background: linear-gradient(to top, @color-border 50%, fade(@color-border, 0%));
    }
    &:first-child {
      border-top: 0;
      &::before {
        content: initial;
      }
    }
    &:last-child {
      border-bottom: 0;
      &::after {
        content: initial;
      }
    }
  }

  pre {
    font-size: 14px;
  }
}
