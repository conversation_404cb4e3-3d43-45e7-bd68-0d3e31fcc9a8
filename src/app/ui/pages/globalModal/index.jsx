import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import classNames from 'classnames'
import modal, { close as closeModal } from '@utils/modal'
import { push } from '@src/app/utils/history'
import { Icon } from 'biz-components'
import { getRobotStatistics, selectRobot, getRobotProfile } from '@api/robots'
import { setLocalStorageItem } from '@utils/tools'
// import { getConfigStatus } from '@api/user'

import { Popover } from 'antd'

@connect((state) => {
  let devUrl = ''
  console.log(state)
  if (__DEV__) {
    devUrl = 'http://tjd.ainirobot.com/'
  }
  return {
    user: state.user,
  }
})
@extend({
  styles: require('./styles.less'),
})
export default class extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      // isComplete: true,
      // globalConfigTips: [{
      //     configName: 'top_qa',
      //     isComplete: true,
      //     doneTips: '高频问答的数量合格',
      //     doneBtnText: '数量越多越好',
      //     undoneTips: '高频问答数量不足30条',
      //     undoneBtnText: '去配置问答'
      // }, {
      //     configName: 'skill_home',
      //     isComplete: true,
      //     doneTips: '所有机器人首页已定制',
      //     doneBtnText: '建议定期更新哦',
      //     undoneTips: '存在机器人未定制专属首页',
      //     undoneBtnText: '去配置首页',
      //     robots: []
      // }, {
      //     configName: 'greet',
      //     isComplete: true,
      //     doneTips: '所有机器人招揽语已定制',
      //     doneBtnText: '建议定期更新哦',
      //     undoneTips: '存在机器人未定制招揽语',
      //     undoneBtnText: '去配置招揽语',
      //     robots: []
      // }, {
      //     configName: 'welcome',
      //     isComplete: true,
      //     doneTips: '所有机器人欢迎语已定制',
      //     doneBtnText: '建议定期更新哦',
      //     undoneTips: '存在机器人未配置欢迎语',
      //     undoneBtnText: '去配置欢迎语',
      //     robots: []
      // }, {
      //     configName: 'location',
      //     isComplete: true,
      //     doneTips: '位置点的数量合格',
      //     doneBtnText: '数量越多越好',
      //     undoneTips: '位置点数量不足10个',
      //     undoneBtnText: '去配置问路引领'
      // }]
    }
  }

  componentDidMount() {
    // this.getConfigStatistics()
  }

  componentDidUpdate() {
    // let showModal = false;
    // this.state.globalConfigTips.map((item, i) => {
    //     if(!item.isComplete){
    //         showModal = true
    //     }
    // })
    // if(!showModal){
    // closeModal();
    // }
  }

  render() {
    let corp = ''
    if (this.props.user.corp != undefined && this.props.user.corp.corp_name) {
      corp = this.props.user.corp.corp_name
    }
    let globalConfigTips = [
      {
        configName: 'top_qa',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            高频问答的数量
            <br />
            合格
          </p>
        ),
        doneBtnText: '数量越多越好',
        undoneTips: '高频问答数量不足30条',
        undoneBtnText: '去配置问答',
      },
      {
        configName: 'skill_home',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            所有机器人首页
            <br />
            已定制
          </p>
        ),
        doneBtnText: '建议定期更新哦',
        undoneTips: '存在机器人未定制专属首页',
        undoneBtnText: '去配置首页',
        robots: [],
      },
      {
        configName: 'greet',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            所有机器人招揽语
            <br />
            已定制
          </p>
        ),
        doneBtnText: '建议定期更新哦',
        undoneTips: '存在机器人未定制招揽语',
        undoneBtnText: '去配置招揽语',
        robots: [],
      },
      {
        configName: 'welcome',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            所有机器人欢迎语
            <br />
            已定制
          </p>
        ),
        doneBtnText: '建议定期更新哦',
        undoneTips: '存在机器人未配置欢迎语',
        undoneBtnText: '去配置欢迎语',
        robots: [],
      },
      {
        configName: 'location',
        isComplete: true,
        doneTips: (
          <p className='tips-text'>
            位置点的数量
            <br />
            合格
          </p>
        ),
        doneBtnText: '数量越多越好',
        undoneTips: '位置点数量不足3个',
        undoneBtnText: '去配置问路引领',
      },
    ]
    globalConfigTips[0].isComplete = this.props.configStatus.top_qa.satisfied
    globalConfigTips[0].undoneTips = (
      <p className='tips-text'>
        高频问答数量
        <br />
        不足{this.props.configStatus.top_qa.expect}条
      </p>
    )
    globalConfigTips[1].isComplete = this.props.configStatus.skill_home.satisfied
    globalConfigTips[1].robots = this.props.configStatus.skill_home.robots
    globalConfigTips[1].undoneTips = (
      <p className='tips-text'>
        <Popover
          placement='rightBottom'
          arrowPointAtCenter
          content={
            <div className='robot-popover'>
              {globalConfigTips[1].robots.map((robot, i) => {
                return <p key={robot.uuid}>{`${i + 1}. ${robot.name}`}</p>
              })}
            </div>
          }
          trigger='hover'
        >
          <span>{this.props.configStatus.skill_home.unset_count}台</span>
        </Popover>
        机器人
        <br />
        未定制专属首页
      </p>
    )
    globalConfigTips[2].isComplete = this.props.configStatus.greet.satisfied
    globalConfigTips[2].robots = this.props.configStatus.greet.robots
    globalConfigTips[2].undoneTips = (
      <p className='tips-text'>
        <Popover
          placement='rightBottom'
          arrowPointAtCenter
          content={
            <div className='robot-popover'>
              {globalConfigTips[2].robots.map((robot, i) => {
                return <p key={robot.uuid}>{`${i + 1}. ${robot.name}`}</p>
              })}
            </div>
          }
          trigger='hover'
        >
          <span>{this.props.configStatus.greet.unset_count}</span>台
        </Popover>
        机器人
        <br />
        未定制招揽语
      </p>
    )

    globalConfigTips[3].isComplete = this.props.configStatus.welcome.satisfied
    globalConfigTips[3].robots = this.props.configStatus.welcome.robots
    globalConfigTips[3].undoneTips = (
      <p className='tips-text'>
        <Popover
          placement='rightBottom'
          arrowPointAtCenter
          content={
            <div className='robot-popover'>
              {globalConfigTips[3].robots.map((robot, i) => {
                return <p key={robot.uuid}>{`${i + 1}. ${robot.name}`}</p>
              })}
            </div>
          }
          trigger='hover'
        >
          <span>{this.props.configStatus.welcome.unset_count}台</span>
        </Popover>
        机器人
        <br />
        未配置欢迎语
      </p>
    )
    globalConfigTips[4].isComplete = this.props.configStatus.location.satisfied
    globalConfigTips[4].undoneTips = (
      <p className='tips-text'>
        位置点数量
        <br />
        不足3个
      </p>
    )
    return (
      <div className={this.props.className}>
        <p className='corp-tips'>Hi，{corp}的管理员</p>
        <p className='text-tips'>为保证您企业下的所有机器人基本可用和正常问答，需要您对如下选项进行配置</p>
        <div className='module-list'>
          <div className='line'></div>
          {globalConfigTips.map((item, i) => {
            if (item.isComplete) {
              return (
                <div className='module' key={i}>
                  <Icon icon='fix_edit_done_click' className='status-icon green' />
                  <p className='description'>{item.doneTips}</p>
                  <button className='config-btn' disabled>
                    {item.doneBtnText}
                  </button>
                </div>
              )
            } else {
              let popover = ''
              if (item.configName == 'skill_home' || item.configName == 'greet' || item.configName == 'welcome') {
                popover = (
                  <Popover
                    placement='rightBottom'
                    arrowPointAtCenter
                    content={
                      <div className='robot-popover'>
                        {item.robots.map((robot, i) => {
                          return <p key={robot.uuid}>{`${i + 1}. ${robot.name}`}</p>
                        })}
                      </div>
                    }
                    trigger='hover'
                  >
                    <Icon icon='icon_Warning' className='status-icon orange hover-status' />
                  </Popover>
                )
              } else {
                popover = <Icon icon='icon_Warning' className='status-icon orange' />
              }
              return (
                <div className='module' key={i}>
                  {popover}
                  <p className='description'>{item.undoneTips}</p>
                  <button
                    className='config-btn clickable'
                    onClick={() => {
                      if (item.robots && item.robots.length != 0) {
                        let selRobotId = item.robots[0].uuid
                        this.props.dispatch(selectRobot(selRobotId))
                        setLocalStorageItem('localRobotId', selRobotId)
                        setLocalStorageItem('globallRobotId', selRobotId)
                        setLocalStorageItem('receptionRobotId', selRobotId)
                      }
                      switch (item.configName) {
                        case 'top_qa':
                          localStorage.setItem('top_qa', true)
                          push('/replies/newReplies')
                          break
                        case 'skill_home':
                          push('/home')
                          break
                        case 'greet':
                          push('/solicit')
                          break
                        case 'welcome':
                          push('/corpus')
                          break
                        case 'location':
                          push('/askWay')
                          break
                        default:
                          console.log('others')
                      }
                    }}
                  >
                    {item.undoneBtnText}
                  </button>
                </div>
              )
            }
          })}
        </div>
      </div>
    )
  }

  // getConfigStatistics() {
  //     getConfigStatus().then((res) => {
  //         let globalConfigTips = this.state.globalConfigTips;
  //         globalConfigTips[0].isComplete = res.top_qa.satisfied;
  //         globalConfigTips[1].isComplete = res.skill_home.satisfied;
  //         globalConfigTips[1].undoneTips = `${res.skill_home.unset_count}台机器人未定制专属首页`;
  //         globalConfigTips[1].robots = res.skill_home.robots;
  //         globalConfigTips[2].isComplete = res.greet.satisfied;
  //         globalConfigTips[2].undoneTips = `${res.greet.unset_count}台机器人未定制招揽语`;
  //         globalConfigTips[2].robots = res.greet.robots;
  //         globalConfigTips[3].isComplete = res.welcome.satisfied;
  //         globalConfigTips[3].undoneTips = `${res.welcome.unset_count}台机器人未配置欢迎语`;
  //         globalConfigTips[3].robots = res.welcome.robots;
  //         globalConfigTips[4].isComplete = res.location.satisfied;
  //         this.setState({
  //             globalConfigTips: globalConfigTips
  //         })
  //     }).catch((err) => {

  //     })
  // }
}
