@import '~base.less';

.component {
  .corp-tips {
    font-size: 16px;
    color: @color-theme;
    text-align: left;
    margin-bottom: 10px;
  }
  .text-tips {
    color: rgba(0, 0, 0, 0.65);
    text-align: left;
    margin-bottom: 30px;
  }
  .module-list {
    display: flex;
    position: relative;
    .line {
      width: 736px;
      height: 2px;
      background-color: #eff1f2;
      position: absolute;
      top: 23px;
      left: 62px;
    }
    .module {
      margin-right: 54px;
      z-index: 9;
      &:last-child {
        margin-right: 0;
      }
      .status-icon {
        width: 48px;
        height: 48px;
      }
      .green {
        color: #00cb9d;
      }
      .orange {
        color: #ff7d1e;
      }
      .hover-status {
        cursor: pointer;
      }
      .description {
        // width: 96px;
        text-align: center;
        font-size: 14px;
        color: @color-base;
        margin: 10px auto 20px;
        span {
          color: #3776ef;
          text-decoration: underline;
          cursor: pointer;
        }
      }
      .config-btn {
        width: 130px;
        height: 32px;
        font-size: 14px;
        text-align: center;
        line-height: 32px;
        border: none;
        border-radius: 16px;
        outline: none;
        &:disabled {
          background-color: #eff1f2;
          color: @color-base;
        }
      }
      .clickable {
        background-color: @color-theme;
        color: #ffffff;
        cursor: pointer;
      }
    }
  }
}
