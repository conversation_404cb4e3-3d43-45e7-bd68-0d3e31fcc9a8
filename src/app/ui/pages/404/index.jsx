import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import { Title } from 'biz-components'

@connect((state) => ({}))
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: '页面不存在',
  }),
})
export default class extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      img: require('@assets/png/404.png'),
    }
  }
  render() {
    return (
      <div className={this.props.className}>
        <Title title='页面不存在' component='h1' />
        <div>
          <img src={this.state.img} />
        </div>
      </div>
    )
  }
}
