import React from 'react'
import { connect } from 'react-redux'
import { extend, getLocaleId } from 'koot'
import { Icon } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
// import { CHANGE_INFO } from '@redux/action-types'
import { TitleBlock } from 'biz-components'

import { Button } from 'biz-components'
import { GET_HOME_MODULE, CHANGE_INFO, CHANGE_MODULE_SKILL, ORIGIN_HOME_MODULE } from '@redux/action-types'
import {
  getModuleConfigList,
  getModuleConfig,
  uploadResource,
  publishModule,
  getModuleStatus,
  directPublishModule,
  recoverDefaultStatus,
  searchAnswer,
} from '@api/_home'
import recommendQues from '@api/_home/recommendQues'
import { getRobotStatistics, selectRobot, getRobotOpkList, UPDATE_ROBOT_DATA } from '@api/robots'
import { RobotList, ChooseRobot } from 'biz-components'
import { checkReleaseRaw } from '@utils/tools'
import { setLocalStorageItem } from '@utils/tools'
import { message } from 'antd'
import { webOperator } from '@utils/operatorRobot'
window.webOperator = webOperator
import { Tabs } from 'antd'
import 'antd/dist/antd.css'
import { Select } from 'antd'

const { Option } = Select

const { TabPane } = Tabs
@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    // console.log('页面：自定义首页', state._home)
    devUrl = 'http://test-jiedai.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    user: state.user,
    brand: state.brand || 'ktv',
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  state = {}
  initRobotNum = 0
  componentDidMount() {
    //只执行一次。
    window.getPropsSet = () => {
      console.log(this.props)
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {}
  componentWillUnmount() {
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (
      this.props.currentRobotId != undefined &&
      prevProps.currentRobotId != undefined &&
      prevProps.currentRobotId != this.props.currentRobotId
    ) {
      console.log('componentDidUpdate  acaction ', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (
      this.props.homeModule != undefined &&
      prevProps.homeModule &&
      prevProps.homeModule == undefined &&
      this.props.homeModule != undefined
    ) {
      console.log('componentDidUpdate  acaction  props', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          console.log('-----------------', this.props.robots[el].version, versionGap)
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }

    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      prevProps.homeModule.templateInfo.bubbleContents &&
      prevProps.homeModule.templateInfo.bubbleContents != this.props.homeModule.templateInfo.bubbleContents
    ) {
      this.setState({
        Bubble1: '',
      })
    }
    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo.background &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      this.state.templateInfoBackground != this.props.homeModule.templateInfo.background
    ) {
      this.setState({
        templateInfoBackground: this.props.homeModule.templateInfo.background,
      })
    }
  }

  render() {
    const robotName = this.state.robotName != undefined && this.state.robotName

    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'Simple') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }

      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.state.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };

      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.state.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.state.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.state.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.state.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
    }
    return (
      <div className={this.props.className}>
        <div className='set'>
          <Select defaultValue='问路引领' style={{ width: 120 }} onChange={() => {}}>
            <Option value='jack'>Jack</Option>
            <Option value='lucy'>Lucy</Option>
            <Option value='disabled'>dis</Option>
            <Option value='Yiminghe'>yiminghe</Option>
          </Select>{' '}
          <input type='text' /> <span>6</span>
          <span>/8</span>
        </div>
      </div>
    )
  }
}
