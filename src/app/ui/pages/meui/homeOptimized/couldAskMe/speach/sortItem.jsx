import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import { Icon, Button } from 'biz-components'
import { message, Popover, Input, Select, AutoComplete } from 'antd'
const { Option } = Select
import 'antd/dist/antd.css'
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc'

import CardTitle from '../cardTitle'
import { close as closeModal } from '@utils/modal'
import { getWebQuery } from '@api/_home'
import recommendQues from '@api/_home/recommendQues'
import { checkReleaseRaw, inputMaxCnQ, inputMaxEnQ, inputMaxCnA, inputMaxEnA } from '@utils/tools'
import QueryList from './queryList'
import Upload from './speach/upload'
import { webOperator } from '@utils/operatorRobot'
window.webOperator = webOperator

let righttip = (value) => {
  return (
    <div>
      <span>{value.title ? value.title.length : value.length}</span> <span>/8</span>{' '}
    </div>
  )
}

const DragHandle = SortableHandle(() => <Icon icon='move2' className='sort_button ask_sort_button' />)
let SortableItem = SortableElement(({ modalStyle, idx, value, onRemove, onDelQuery, onDelFun, _this }) => {
  return (
    <p className={modalStyle == 'Card' ? 'sort_info2 sort_info' : 'sort_info'}>
      <Popover content={modalStyle != 'Technical' ? value.title : value.tip}>
        <span className='sort_content'>{modalStyle != 'Technical' ? value.title : value.tip}</span>
      </Popover>
      {modalStyle == 'Standard' && (
        <Icon
          icon='rd_trash'
          className='icon toright'
          onClick={() => {
            console.log('to right click')
            // onDelQuery(idx)
            onDelFun(idx)
          }}
        />
      )}
      {modalStyle == 'Simple' && (
        <Icon
          icon='rd_trash'
          className='icon toright'
          onClick={() => {
            console.log('to right click')
            // onDelQuery(idx)
            onDelFun(idx)
          }}
        />
      )}
      <span className='sort_switch' onClick={() => onRemove(idx, value.display)}>
        <em className='switch_on'></em>
      </span>
      <DragHandle />
    </p>
  )
})
//<div style={{ marginBottom: 16 }}>
//<Input className='abc' addonBefore={selectBefore} suffix={righttip} defaultValue="mysite" />
//</div>
const selectBefore = (_this, idx, type) => {
  return (
    <div style={{ width: '140px' }}>
      <Select
        value1='问路引领'
        value={
          _this.props.homeModule.skills[idx].function == 'chat'
            ? _this.props.homeModule.skills[idx].label
            : _this.props.homeModule.skills[idx].function
        }
        className='select-before'
        onChange={function (val, b) {
          console.log(_this, type, idx)
          let defaultSkill = {}
          let defaultPIC = '',
            defaultPIC2 = ''

          // e={target:{value:va}};
          // return;

          let fun = val == 'chat1' || val == 'chat2' ? 'chat' : val
          let tit =
            val == 'chat1' || val == 'chat2'
              ? idx == 0
                ? 'check in'
                : 'check out'
              : idx == 0
              ? 'Navigation'
              : 'check out'
          let tip =
            val == 'chat1' || val == 'chat2'
              ? idx == 0
                ? 'How to check in'
                : 'How to check out'
              : idx == 0
              ? 'Take me to toilet'
              : 'Take me to XXX'
          _this.setState({
            // selectedIndex:
          })
          //更换function
          _this.props.homeModule.skills[idx].function = fun
          _this.props.dispatch({
            type: 'CHANGE_MODULE_SKILL',
            data: {
              type: 'function',
              index: idx,
              content: _this.props.homeModule.skills[idx],
            },
          })

          // 更换title
          _this.props.homeModule.skills[idx].title =
            _this.props.lang == 'chs' || _this.props.lang == 'english'
              ? _this.props.homeModule.skills[idx].title
              : idx != 0 && idx != 1
              ? _this.props.homeModule.skills[idx].title
              : tit
          _this.props.dispatch({
            type: 'CHANGE_MODULE_SKILL',
            data: {
              type: 'title',
              index: idx,
              content: _this.props.homeModule.skills[idx],
            },
          })

          //更换tip
          // _this.props.skills[idx].tip = _this.state.recmmendQuesList[fun][0];
          _this.props.homeModule.skills[idx].tip =
            _this.props.lang == 'chs' || _this.props.lang == 'english'
              ? _this.state.recmmendQuesList[fun][0]
              : idx != 0 && idx != 1
              ? _this.props.homeModule.skills[idx].tip
              : tip
          _this.props.dispatch({
            type: 'CHANGE_MODULE_SKILL',
            data: {
              type: 'tip',
              index: idx,
              content: _this.props.homeModule.skills[idx],
            },
          })

          if (val == 'chat1' || val == 'chat2') {
            _this.props.homeModule.skills[idx]['label'] = val
            _this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'tip',
                index: idx,
                content: _this.props.homeModule.skills[idx],
              },
            })
          } else if (_this.props.homeModule.skills[idx]['label']) {
            delete _this.props.homeModule.skills[idx]['label']
            _this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'tip',
                index: idx,
                content: _this.props.homeModule.skills[idx],
              },
            })
          }
          //更改json图片
          //由于执行顺序，不能使上以上defaultPIC
          // for (let i = 0; i < _this.state.funcConf.length; i++) {
          // 	if (_this.state.funcConf[i].key == _this.props.homeModule.skills[idx].function) {
          // 		if (_this.props.skills[idx].label == 'chat2') {	//聊天
          // 			defaultSkill = _this.state.funcConf[i + 1]
          // 		} else {
          // 			defaultSkill = _this.state.funcConf[i]
          // 		}
          // 		break;
          // 	}
          // }
          if (_this.props.types == 'list_icon1') {
            defaultPIC2 = _this.state.funcConf[idx].list_icon2
          } else if (_this.props.types == 'list_icon2') {
            defaultPIC2 = _this.state.funcConf[idx].list_icon1
          } else if (_this.props.types == 'card_icon') {
            defaultPIC2 = _this.state.funcConf[idx].card_icon
          } else if (_this.props.types == 'standard1_icon') {
            defaultPIC2 = _this.state.funcConf[idx].standard1_icon
          } else if (_this.props.types == 'standard2_icon') {
            defaultPIC2 = _this.state.funcConf[idx].standard2_icon
          }
          defaultPIC2 = _this.state.funcConf[0].standard2_icon
          _this.props.homeModule.skills[idx].icon = defaultPIC2
          _this.props.dispatch({
            type: 'CHANGE_MODULE_SKILL',
            data: {
              type: 'icon',
              key: 34,
              index: idx,
              content: _this.props.homeModule.skills[idx],
            },
          })
          // let fun = e.target.value == 'chat1' || e.target.value == 'chat2' ? 'chat' : e.target.value;
          // // chat1==问答；chat2==聊天
          // // let fun = e.target.value;
          // let defaultPic = '',
          //     labelObj = {};
          // if (e.target.value == 'chat1') {
          //     labelObj.label = 'chat1';
          //     _this.setState({
          //         whichChat: 1
          //     })
          // } else if (e.target.value == 'chat2') {
          //     labelObj.label = 'chat2';
          //     _this.setState({
          //         whichChat: 2
          //     })
          // } else {
          //     if (_this.state.skill['label']) {
          //         delete _this.state.skill['label']
          //     }
          //     _this.setState({
          //         whichChat: ""
          //     })
          // }
          // if (_this.props.modalStyle == 'List') {
          //     defaultPic = _this.state.funcConf[e.target.selectedIndex].list_icon1;
          // } else if (_this.props.modalStyle == 'Card') {
          //     defaultPic = _this.state.funcConf[e.target.selectedIndex].card_icon;
          // } else if (_this.props.modalStyle == 'Standard') {
          //     defaultPic = _this.state.funcConf[e.target.selectedIndex].standard1_icon;
          // }
          // let skill = Object.assign({}, _this.state.skill, { 'function': fun, tip: _this.state.recmmendQuesList[fun][0], icon: defaultPic }, labelObj)
          // _this.setState({
          //     funcIndex: e.target.selectedIndex,
          //     skill: skill
          // })
        }.bind(_this)}
      >
        {/* <Option value="http://">问答</Option>
            <Option value="https://">问路</Option> */}
        {_this.state.funcConf.map((item, index) => {
          if ((item.name === 'Chat' || item.name === '聊天') && _this.props.lang === 'arabic') {
            return ''
          }
          return (
            <Option key={index} value={item.key == 'chat' ? item.label : item.key}>
              {item.name}
            </Option>
          )
        })}
      </Select>
      <Icon icon='guide_voice' className='icon1' style={{ marginLeft: '20px' }} />
    </div>
  )
}
// let
SortableItem = SortableElement(({ modalStyle, idx, value, onRemove, onDelQuery, onDelFun, _this, type }) => {
  let suggQuerys = []
  let fun = ''
  if (_this.props.homeModule.skills[idx]) {
    suggQuerys = _this.state.recmmendQuesList[_this.props.homeModule.skills[idx].function]
  }
  return (
    <p className={modalStyle == 'Card' ? 'sort_info2 ask_sort_info sort_info' : 'ask_sort_info sort_info'}>
      <div className='viewAsk 5' style={{ position: 'relative' }}>
        <Input
          className='abc'
          addonBefore={type == 'query' ? '' : selectBefore(_this, idx, type)}
          suffix={righttip(value)}
          onChange={(e) => {
            console.log(_this, idx)
            return
            if (type != 'query') {
              let skills = _this.props.homeModule.skills
              let skill = skills[idx]
              skill.tip = skill.title = e.target.value
              _this.props.dispatch({
                type: 'CHANGE_MODULE_SKILL',
                data: {
                  type: 'title',
                  index: idx,
                  content: skill,
                },
              })
            } else {
              _this.props.dispatch({
                type: 'CHANGE_MODULE_QUERIES',
                data: {
                  index: idx,
                  content: e.target.value,
                },
              })
            }

            console.log(e.target.value)
          }}
          value1={type == 'query' ? value : value.title}
        />
        <Select
          showSearch
          style={{
            width: 200,
            position: 'absolute',
            right: '30px',
            zIndex: 10000,
            //    display:'none'
          }}
          showArrow={false}
          suffixIcon={<div></div>}
          bordered={false}
          placeholder='请输入'
          value={type == 'query' ? value : value.title}
          optionFilterProp='children'
          getPopupContainer={(triggerNode) =>
            triggerNode.parentElement.parentElement.parentElement.parentElement.parentElement
          }
          onChange={(val) => {
            if (type != 'query') {
              let skills = _this.props.homeModule.skills
              let skill = skills[idx]
              skill.tip = skill.title = val
              _this.props.dispatch({
                type: 'CHANGE_MODULE_SKILL',
                data: {
                  type: 'title',
                  index: idx,
                  content: skill,
                },
              })
            } else {
              _this.props.dispatch({
                type: 'CHANGE_MODULE_QUERIES',
                data: {
                  index: idx,
                  content: val,
                },
              })
            }
          }}
          onFocus={() => {}}
          onBlur={() => {}}
          onSearch={(val) => {
            if (val.length == 0) {
              return
            }
            if (type != 'query') {
              let skills = _this.props.homeModule.skills
              let skill = skills[idx]
              skill.tip = skill.title = val
              _this.props.dispatch({
                type: 'CHANGE_MODULE_SKILL',
                data: {
                  type: 'title',
                  index: idx,
                  content: skill,
                },
              })
            } else {
              _this.props.dispatch({
                type: 'CHANGE_MODULE_QUERIES',
                data: {
                  index: idx,
                  content: val,
                },
              })
            }
          }}
          filterOption={
            (input, option) => true
            // option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {' '}
          {suggQuerys &&
            suggQuerys.map((item) => {
              return <Option value={item}>{item}</Option>
            })}
        </Select>
        <AutoComplete
          style={{
            width: 200,
            position: 'absolute',
            right: '30px',
            zIndex: 10000,
            display: 'none',
          }}
          // style={{ width: '70%' }}
          placeholder=''
          bordered={false}
          value='12323'
          getPopupContainer={(triggerNode) =>
            triggerNode.parentElement.parentElement.parentElement.parentElement.parentElement
          }
          options={(() => {
            // return [{ value: 'text 1' }, { value: 'text 2' }]
            let res = []
            suggQuerys &&
              suggQuerys.map((item) => {
                //  <Option value={item}>{item}</Option>
                res.push({ value: item })
              })
          })()}
        />
      </div>
      <Upload></Upload>
      {false && this.props.lang !== 'arabic' ? (
        <QueryList
          type='0'
          className='querySelCon'
          querys={suggQuerys}
          selectQuery={(query) => {
            let skill = Object.assign({}, this.state.skill, { tip: query })
            this.setState({
              skill: skill,
            })
            this.setState({
              showQueryTips: true,
              showQueryList: false,
            })
          }}
        ></QueryList>
      ) : (
        ''
      )}
      {/* <div>let </div> <span> big</span> */}

      <DragHandle />
      <Icon
        icon='rd_trash'
        className='icon asktoright'
        onClick={() => {
          console.log('to right click')
          // onDelQuery(idx)
          if (type == 'query') {
            onDelQuery(idx)
          } else {
            onDelFun(idx)
          }
        }}
      />
    </p>
  )
})

const SortableList = SortableContainer(({ modalStyle, items, onRemove, onDelQuery, onDelFun, _this, type }) => {
  return (
    <ul className={modalStyle != 'Card' ? 'sort_area1' : 'sort_area1'}>
      {items.map((item, index) => {
        if (true || item.display) {
          return (
            <SortableItem
              key={`item-${index}`}
              _this={_this}
              onDelQuery={onDelQuery}
              modalStyle={modalStyle}
              type={type}
              index={index}
              idx={index}
              value={item}
              onDelFun={onDelFun}
              onRemove={onRemove}
            />
          )
        }
      })}
    </ul>
  )
})

@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    // console.log('页面：自定义首页', state._home)
    devUrl = 'http://test-jiedai.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    modalStyle: state._home.homeModule ? state._home.homeModule.templateInfo.templateCode : '',
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    user: state.user,
    brand: state.brand || 'ktv',
    // lang:
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  constructor(props) {
    super(props)
    let _defaultPic = ''
    switch (this.props.modalStyle) {
      case 'List':
        _defaultPic = 'module_public/module_skill_home/List1_interview.png'
        break
      case 'Card':
        _defaultPic = 'module_public/module_skill_home/Card_interview.png'
        break
      case 'Standard':
        _defaultPic = 'module_public/module_skill_home/Standard1_interview.png'
        break
      default:
        _defaultPic = 'module_public/module_skill_home/List1_interview.png'
    }
    this.state = {
      recmmendQuesList: recommendQues.recommendQues,
      whichChat: '',
      skill: {
        title: '',
        icon: _defaultPic,
        tip: '我来找人',
        function: 'interview',
        display: true,
      },
      queries: recommendQues.allQuesList[0],
      showQueryTips: true,
      showPicTips: false,
      picTips: '',
      skills: [],
      funcConf: recommendQues.funcConfig,
      allQuesList: recommendQues.allQuesList,
      showQueryList: false,
      isShowFuncWordTip: false,
      isShowQueryWordTip: false,
      lang: this.props.lang || 'chs',
      maxFunLength: this.props.lang == 'english' || this.props.lang == 'arabic' ? inputMaxEnQ : inputMaxCnQ,
      maxRecLength: this.props.lang == 'english' || this.props.lang == 'arabic' ? inputMaxEnA : inputMaxCnA,
    }
  }

  initRobotNum = 0

  componentDidMount() {
    window.getPropsSet = () => {
      console.log(this.props)
    }

    const formData = new FormData()
    formData.append('lang', this.props.curLang.lang_code)
    getWebQuery(formData).then((res) => {
      let webQuery = [],
        appQuery = []
      res.items.map((item, index) => {
        if (item.type == 0) {
          webQuery.push(item.norm_word)
          item.similar_words.map((el, id) => {
            webQuery.push(el)
          })
        } else {
          appQuery.push(item.norm_word)
          item.similar_words.map((el, id) => {
            appQuery.push(el)
          })
        }
      })
      const web_set = new Set(webQuery)
      webQuery = [...web_set]
      const app_set = new Set(appQuery)
      appQuery = [...app_set]
      this.state.recmmendQuesList['web'] = webQuery
      this.state.recmmendQuesList['open_app'] = appQuery
      this.setState(
        {
          recmmendQuesList: this.state.recmmendQuesList,
        },
        () => {
          console.log(this.state.recmmendQuesList, 'this.state.recmmendQuesList')
        },
      )
    })
  }

  UNSAFE_componentWillReceiveProps(nextProps) {}
  componentWillUnmount() {
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (this.props.homeModule && this.props.homeModule != prevProps.homeModule && prevProps.homeModule == undefined) {
      // debugger;
      this.setState({
        skills: this.props.homeModule.skills,
      })
    }
    if (
      this.props.currentRobotId != undefined &&
      prevProps.currentRobotId != undefined &&
      prevProps.currentRobotId != this.props.currentRobotId
    ) {
      console.log('componentDidUpdate  acaction ', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (
      this.props.homeModule != undefined &&
      prevProps.homeModule &&
      prevProps.homeModule == undefined &&
      this.props.homeModule != undefined
    ) {
      console.log('componentDidUpdate  acaction  props', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          console.log('-----------------', this.props.robots[el].version, versionGap)
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }
  }
  onSortEnd = ({ oldIndex, newIndex }) => {
    // if (this.props.modalStyle == "List") {
    // 	oldIndex = oldIndex + 2;
    // 	newIndex = newIndex + 2
    // }else if(this.props.modalStyle == "Standard") {
    // 	oldIndex = oldIndex + 1;
    // 	newIndex = newIndex + 1
    // }else if(this.props.modalStyle == "Simple") {
    // 	oldIndex = oldIndex ;
    // 	newIndex = newIndex
    // }
    this.props.dispatch({
      type: 'CHANGE_QUERY_SORT',
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
      },
    })
  }
  onDelQuery = (idx) => {
    console.log('del query')
    let standardQueries
    this.props.homeModule.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
    })
    console.log('获取query数量' + standardQueries.length)
    if (standardQueries.length <= 4) {
      message.error(__('customHome.modalMsg.keepRecommendedNum', { num: 4 }))
      // modal({
      // 	title: __('customHome.modalMsg.failedToDelete'),
      // 	content: __('customHome.modalMsg.keepRecommendedNum',{num:4}),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除index：', idx)
      this.props.dispatch({
        type: 'REMOVE_HOMEMODULE_QUERY',
        data: {
          index: idx,
        },
      })
    }
  }
  onDelFun = (id) => {
    console.log('del fun')
    let idx = id
    let standardFuns = []
    this.props.homeModule.skills.forEach((item) => {
      standardFuns.push(item)
    })
    console.log('获取query数量' + standardFuns.length)
    if (standardFuns.length <= 3) {
      message.error(__('customHome.modalMsg.keepFunction', { num: 3 }))
      // modal({
      // 	title:  __('customHome.modalMsg.failedToDelete'),
      // 	content: __('customHome.modalMsg.keepFunction',{num:3}),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      standardFuns.splice(idx, 1)
      this.props.dispatch({
        type: 'UPDATE_SKILL',
        data: {
          skills: standardFuns,
        },
      })
    }
  }
  newAddQueries(text) {
    this.props.dispatch({
      type: 'ADD_NEW_QUERIES',
      data: {
        index: 0,
        content: text,
      },
    })
    closeModal()
  }
  newAddSkill(skill) {
    console.log(skill)
    // debugger
    if (this.props.modalStyle == 'List') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 0,
          skill: skill,
        },
      })
    } else if (this.props.modalStyle == 'Standard') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 0,
          skill: skill,
        },
      })
    } else if (this.props.modalStyle == 'Simple') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 0,
          skill: skill,
        },
      })
    } else {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 0,
          skill: skill,
        },
      })
    }
    closeModal()
  }
  render() {
    const robotName = this.state.robotName != undefined && this.state.robotName
    const skills = this.props.homeModule ? this.props.homeModule.skills : []
    let querys = []
    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'Simple') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }

      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.state.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };

      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.state.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.state.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.state.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.state.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
      this.props.homeModule.queries.map((e) => {
        // console.log(e.name)
        if (e.name == 'standardMain') {
          querys = e.query
        }
      })
    }
    // let skills = [{}, {}, {}]
    return (
      <div className={this.props.className}>
        {/* <a name="couldAskMe">&nbsp;</a> */}
        <CardTitle title={__('customHome.btn.Ask')} tips='配置问法' desc={__('customHome.btn.TTS')}>
          <Button
            size='small'
            appearance='hollow'
            label='新增'
            onClick={() => {
              if (this.props.homeModule.skills.length < 16) {
                let fun = 'chat'
                // chat1==问答；chat2==聊天
                // let fun = e.target.value;
                let defaultPic = '',
                  labelObj = {}
                labelObj.label = 'chat1'
                if (this.props.modalStyle == 'List') {
                  defaultPic = this.state.funcConf[0].list_icon1
                } else if (this.props.modalStyle == 'Card') {
                  defaultPic = this.state.funcConf[0].card_icon
                } else if (this.props.modalStyle == 'Standard') {
                  defaultPic = this.state.funcConf[0].standard1_icon
                }
                let skill = Object.assign(
                  {},
                  '',
                  {
                    function: fun,
                    tip: this.state.recmmendQuesList[fun][0],
                    icon: defaultPic,
                    title: this.state.recmmendQuesList[fun][0],
                  },
                  labelObj,
                )
                this.newAddSkill(skill)
              } else {
                message.error(__('customHome.modalMsg.recommendedNum', { num: 16 }))
              }
            }}
          />
        </CardTitle>

        {/* <Input className='abc' addonBefore={selectBefore} suffix={righttip} defaultValue="介绍" /> */}
        {/* <Speach></Speach> */}
        {this.props.homeModule && (
          <div className='asksort a5'>
            {this.props.homeModule.templateInfo.templateCode != 'Simple' && (
              <SortableList
                modalStyle={this.props.modalStyle}
                items={skills}
                axis='xy'
                onSortStart={() => {}}
                onSortEnd={this.onSortEnd}
                _this={this}
                useDragHandle={true}
                onDelFun={(idx) => this.onDelFun(idx)}
                onDelQuery={(idx) => this.onDelQuery(idx)}
                onRemove={(idx, display) => this.remove(idx, display)}
              />
            )}

            {this.props.homeModule.templateInfo.templateCode == 'Simple' && (
              <SortableList
                modalStyle={this.props.modalStyle}
                items={querys}
                axis='xy'
                onSortStart={() => {}}
                onSortEnd={this.onSortEnd}
                _this={this}
                type='query'
                useDragHandle={true}
                onDelFun={(idx) => this.onDelFun(idx)}
                onDelQuery={(idx) => this.onDelQuery(idx)}
                onRemove={(idx, display) => this.remove(idx, display)}
              />
            )}

            {this.props.homeModule.templateInfo.templateCode != 'Simple' && (
              <>
                {querys && (
                  <CardTitle title='推荐问法' tips=''>
                    <Button
                      size='small'
                      appearance='hollow'
                      label='新增'
                      onClick={() => {
                        let queries = []
                        // "standardMain"
                        this.props.homeModule.queries.map((item) => {
                          if (item.name == 'standardMain') {
                            queries = item.query
                          }
                        })
                        if (queries.length < 16) {
                          let text = '我来找人'
                          this.newAddQueries(text)
                        } else {
                          message.error(__('customHome.modalMsg.recommendedNum', { num: 16 }))
                        }
                      }}
                    />
                  </CardTitle>
                )}
                <SortableList
                  modalStyle={this.props.modalStyle}
                  items={querys}
                  axis='xy'
                  onSortStart={() => {}}
                  onSortEnd={this.onSortEnd}
                  _this={this}
                  type='query'
                  useDragHandle={true}
                  onDelFun={(idx) => this.onDelFun(idx)}
                  onDelQuery={(idx) => this.onDelQuery(idx)}
                  onRemove={(idx, display) => this.remove(idx, display)}
                />
              </>
            )}
          </div>
        )}
      </div>
    )
  }
}
