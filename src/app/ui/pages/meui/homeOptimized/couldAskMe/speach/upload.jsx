import React from 'react'
import { connect } from 'react-redux'
import { extend, getLocaleId } from 'koot'
import { Icon } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
// import { CHANGE_INFO } from '@redux/action-types'
import { TitleBlock } from 'biz-components'

import { Button } from 'biz-components'
import { GET_HOME_MODULE, CHANGE_INFO, CHANGE_MODULE_SKILL, ORIGIN_HOME_MODULE } from '@redux/action-types'
import {
  getModuleConfigList,
  getModuleConfig,
  uploadResource,
  publishModule,
  getModuleStatus,
  directPublishModule,
  recoverDefaultStatus,
  searchAnswer,
} from '@api/_home'
import recommendQues from '@api/_home/recommendQues'
import { getRobotStatistics, selectRobot, getRobotOpkList, UPDATE_ROBOT_DATA } from '@api/robots'
import { <PERSON><PERSON><PERSON>, ChooseRobot } from 'biz-components'
import { checkReleaseRaw } from '@utils/tools'
import { setLocalStorageItem } from '@utils/tools'
import { message } from 'antd'
import { webOperator } from '@utils/operatorRobot'
import { getImageSize } from '@utils/image-size'
window.webOperator = webOperator
import { Tabs } from 'antd'
import 'antd/dist/antd.css'
import { Select, Popover } from 'antd'
import { Upload } from 'antd'
import ImgCrop from 'antd-img-crop'
import 'antd/es/modal/style'
import 'antd/es/slider/style'
// import { Popover } from 'node_modules/antd/lib/index'

const { Option } = Select

const { TabPane } = Tabs
@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    // console.log('页面：自定义首页', state._home)
    devUrl = 'http://test-jiedai.ainirobot.com/'
    // devUrl = 'http://beta-jiedai.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    user: state.user,
    brand: state.brand || 'ktv',

    skills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
    editIndex: state._home.editIndex || 0,
    curLang: state.user.curLang,
  }
})
@extend({
  styles: require('./upload.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  state = {
    funcConf:
      this.props.lang == 'english'
        ? recommendQues.funcConfigEng
        : this.props.lang === 'chs'
        ? recommendQues.funcConfig
        : recommendQues.funcConfigArabic,
    // funcConf: recommendQues.funcConfig,
    // recmmendQuesList: this.props.lang=='english'?recommendQues.recommendQuesEng:recommendQues.recommendQues,
    recmmendQuesList:
      this.props.lang == 'english'
        ? recommendQues.recommendQuesEng
        : this.props.lang === 'chs'
        ? recommendQues.recommendQues
        : recommendQues.recommendQuesArabic,
    allQuesList: recommendQues.allQuesList,
  }
  initRobotNum = 0
  componentDidMount() {
    //只执行一次。
    window.getPropsSet = () => {
      console.log(this.props)
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {}
  componentWillUnmount() {
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (
      this.props.currentRobotId != undefined &&
      prevProps.currentRobotId != undefined &&
      prevProps.currentRobotId != this.props.currentRobotId
    ) {
      console.log('componentDidUpdate  acaction ', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (
      this.props.homeModule != undefined &&
      prevProps.homeModule &&
      prevProps.homeModule == undefined &&
      this.props.homeModule != undefined
    ) {
      console.log('componentDidUpdate  acaction  props', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          console.log('-----------------', this.props.robots[el].version, versionGap)
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }

    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      prevProps.homeModule.templateInfo.bubbleContents &&
      prevProps.homeModule.templateInfo.bubbleContents != this.props.homeModule.templateInfo.bubbleContents
    ) {
      this.setState({
        Bubble1: '',
      })
    }
    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo.background &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      this.state.templateInfoBackground != this.props.homeModule.templateInfo.background
    ) {
      this.setState({
        templateInfoBackground: this.props.homeModule.templateInfo.background,
      })
    }
  }
  mouseOutfun() {
    function simulateMouseOut(obj) {
      var event = new MouseEvent('mouseout', {
        bubbles: true,
        cancelable: true,
      })
      obj.dispatchEvent(event)
    }
    let defaultImg = document.querySelectorAll('.defaultImg')
    let len = defaultImg.length
    for (let i = 0; i < len; i++) {
      simulateMouseOut(defaultImg[i])
    }
  }
  render() {
    let defaultSkill = {}

    for (let i = 0; i < this.state.funcConf.length; i++) {
      if (
        this.props.skills[this.props.editIndex] &&
        this.state.funcConf[i].key == this.props.skills[this.props.editIndex].function
      ) {
        if (this.props.skills[this.props.editIndex].label == 'chat2') {
          //聊天
          defaultSkill = this.state.funcConf[i + 1]
        } else {
          defaultSkill = this.state.funcConf[i]
        }
        break
      }
    }
    const robotName = this.state.robotName != undefined && this.state.robotName
    let defaultPIC = '',
      defaultPIC2 = ''
    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'Simple') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }

      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.state.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };

      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.state.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.state.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.state.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.state.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
    }

    var image_url = this.props.devUrl + 'media/'
    let types
    if (this.props.type == 'question' && this.props.modalStyle == 'Standard' && this.props.idx == 0) {
      types = 'standard2_icon'
    }
    if (this.props.type == 'question' && this.props.modalStyle == 'Standard' && this.props.idx > 0) {
      types = 'standard1_icon'
    }
    if (
      this.props.type == 'question' &&
      this.props.modalStyle == 'List' &&
      (this.props.idx == 0 || this.props.idx == 1)
    ) {
      types = 'list_icon1'
    }
    if (this.props.type == 'question' && this.props.modalStyle == 'List' && this.props.idx > 1) {
      types = 'list_icon2'
    }
    if (this.props.type == 'question' && this.props.modalStyle == 'Card') {
      types = 'card_icon'
    }
    if (this.props.modalStyle == 'Buttons') {
      types = 'buttons_icon'
    }

    var sizeRule = { width: 920, height: 160 }
    var imageStyle = 'defaultImg  buttons_icon_bg'
    if (types == 'list_icon1') {
      var imageStyle = 'defaultImg  optsquareImg'
      var newIconStyle = 'newIcon_square'
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：490px*260px</span>
      defaultPIC = defaultSkill.list_icon2
      sizeRule = { width: 490, height: 260 }
    } else if (types == 'list_icon2') {
      var imageStyle = 'defaultImg  optcircleImg'
      var newIconStyle = 'newIcon_circle'
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：110px*110px</span>
      defaultPIC = defaultSkill.list_icon1
      sizeRule = { width: 110, height: 110 }
    } else if (types == 'card_icon') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：550px*380px</span>
      var imageStyle = 'defaultImg  optrectangleImg1'
      var newIconStyle = 'newIcon_rectangle1'
      defaultPIC = defaultSkill.card_icon
      sizeRule = { width: 550, height: 380 }
    } else if (types == 'standard1_icon') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：420px*230px</span>
      var imageStyle = 'defaultImg  optrectangleImg2'
      var newIconStyle = 'newIcon_rectangle2'
      defaultPIC = defaultSkill.standard1_icon
      sizeRule = { width: 420, height: 230 }
    } else if (types == 'standard2_icon') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：392px*480px</span>
      var imageStyle = 'defaultImg  optrectangleImg3'
      var newIconStyle = 'newIcon_rectangle3'
      defaultPIC = defaultSkill.standard2_icon
      sizeRule = { width: 392, height: 480 }
    } else if (types == 'buttons_icon') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：956px*160px</span>
      var imageStyle = 'defaultImg  buttons_icon_bg'
      var newIconStyle = 'newIcon_rectangle1'
      defaultPIC = defaultSkill.standard2_icon
      sizeRule = { width: 956, height: 160 }
    }
    sizeRule = getImageSize(this.props.robotType, this.props.modalStyle, this.props.idx)
    let url = this.props.skills[this.props.idx] ? image_url + this.props.skills[this.props.idx].icon : ''
    if (types == 'buttons_icon') {
      // url = 'http://test-jiedai.ainirobot.com/media/orics/down/b016_20201223_819ebf6c3db83c656facae7bc3711a5d'
    }
    let whichChat = ''
    if (this.props.skills[this.props.idx].label == 'chat1') {
      whichChat = 1
    } else if (this.props.skills[this.props.idx].label == 'chat2') {
      whichChat = 2
    } else {
      whichChat = ''
    }
    //initUrl 的地址里有时会多一个2 ，用defaultPIC2 覆盖initUrl
    let initUrl =
      'module_public/module_skill_home/' +
      this.props.modalStyle +
      (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
      '_' +
      this.props.skills[this.props.idx].function +
      whichChat +
      '.png'
    if (
      (this.props.modalStyle.toLowerCase() === 'list' && this.props.idx <= 1) ||
      (this.props.modalStyle.toLowerCase() === 'standard' && this.props.idx == 0)
    ) {
      initUrl =
        'module_public/module_skill_home/' +
        this.props.modalStyle +
        '2' +
        '_' +
        this.props.skills[this.props.idx].function +
        whichChat +
        '.png'
    }

    if (this.props.modalStyle == 'Standard' && this.props.idx == 0) {
      types = 'standard2_icon'
    }
    if (this.props.modalStyle == 'Standard' && this.props.idx > 0) {
      types = 'standard1_icon'
    }

    if (this.props.modalStyle == 'List' && (this.props.idx == 0 || this.props.idx == 1)) {
      types = 'list_icon1'
    }

    if (this.props.modalStyle == 'List' && this.props.idx > 1) {
      types = 'list_icon2'
    }
    if (this.props.modalStyle == 'Card') {
      types = 'card_icon'
    }

    if (this.props.modalStyle == 'Buttons') {
      types = 'buttons_icon'
    }
    let _this = this
    let home_function = this.props.homeModule.skills[this.props.idx].function || ''
    let home_label = this.props.homeModule.skills[this.props.idx].label || ''
    let home_index = 0
    _this.state.funcConf.map((e, index) => {
      // console.log(e.skey);
      if (home_label) {
        if (e.label && home_label == e.label) {
          home_index = index
        }
      } else if (e.key == home_function) {
        home_index = index
      }
    })
    defaultPIC2 = _this.state.funcConf[home_index].card_icon
    if (types == 'list_icon1') {
      defaultPIC2 = _this.state.funcConf[home_index].list_icon2
    } else if (types == 'list_icon2') {
      defaultPIC2 = _this.state.funcConf[home_index].list_icon1
    } else if (types == 'card_icon') {
      defaultPIC2 = _this.state.funcConf[home_index].card_icon
    } else if (types == 'standard1_icon') {
      defaultPIC2 = _this.state.funcConf[home_index].standard1_icon
    } else if (types == 'standard2_icon') {
      defaultPIC2 = _this.state.funcConf[home_index].standard2_icon
    } else if (types == 'buttons_icon') {
      defaultPIC2 = 'http://test-jiedai.ainirobot.com/media/orics/down/b016_20201223_819ebf6c3db83c656facae7bc3711a5d'
      defaultPIC2 = _this.state.funcConf[home_index].buttons_icon
    }
    initUrl = defaultPIC2 || initUrl

    return (
      <div className={this.props.className}>
        <Popover
          // trigger={'click'}
          getPopupContainer={(triggerNode) =>
            triggerNode.parentElement.parentElement.parentElement.parentElement.parentElement.parentElement
          }
          content={
            <div className='uploadTip' style={{}}>
              <div className='tips-container'>
                <span className='tips-text'>{__('customHome.pic.imageFormat')}：JPG/PNG/JPEG</span>
                <span className='tips-text'>
                  {__('customHome.pic.pictureSize')}：{sizeRule.width}px*{sizeRule.height}px
                </span>
              </div>
              <span>{__('customHome.btn.changeIcon')}：</span>{' '}
              <ImgCrop
                // aspect={sizeRule}
                aspect={sizeRule.width / sizeRule.height}
                grid
                modalTitle={__('customHome.btn.img_cut')} //
                modalWidth='650px'
                modalOk={__('customHome.btn.confirm')}
                modalCancel={__('customHome.btn.cancel2')}
                fillColor={'transparent'}
              >
                <Upload
                  customRequest={() => {}}
                  className='upload-btn square'
                  listType='text'
                  beforeUpload={(e) => {
                    console.log(e)
                    let _this = this
                    let img = e
                    let reader = new FileReader()
                    reader.readAsDataURL(img)
                    reader.onload = function (e) {
                      let image = new Image()
                      image.onload = function () {
                        let mycanvas = document.querySelector('#upmybackCanvas' + _this.props.idx)
                        let ctx = mycanvas.getContext('2d')
                        ctx.clearRect(0, 0, sizeRule.width, sizeRule.height)
                        ctx.drawImage(image, 0, 0, sizeRule.width, sizeRule.height)
                        let dataurl = mycanvas.toDataURL('image/png')
                        function dataURLtoBlob(dataurl) {
                          let arr = dataurl.split(','),
                            mime = arr[0].match(/:(.*?);/)[1],
                            bstr = atob(arr[1]),
                            n = bstr.length,
                            u8arr = new Uint8Array(n)
                          while (n--) {
                            u8arr[n] = bstr.charCodeAt(n)
                          }
                          return new Blob([u8arr], { type: mime })
                        }
                        // _this.props.selectImg(dataURLtoBlob(dataurl), "backImg", "")
                        _this.props.selectImg(dataURLtoBlob(dataurl), types, _this.props.idx)
                        _this.mouseOutfun()
                        return
                        if (_this.props.types == 'list_icon1') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'list_icon1', _this.props.editIndex)
                        } else if (_this.props.types == 'list_icon2') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'list_icon2', _this.props.editIndex)
                        } else if (_this.props.types == 'card_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'card_icon', _this.props.editIndex)
                        } else if (_this.props.types == 'standard1_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'standard1_icon', _this.props.editIndex)
                        } else if (_this.props.types == 'standard2_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'standard2_icon', _this.props.editIndex)
                        }
                      }
                      image.src = e.target.result
                    }
                    return true
                  }}
                  onChange={(e) => {
                    // console.log(e)
                  }}
                >
                  {__('customHome.modalMsg.imgUpload')}
                </Upload>
              </ImgCrop>
              <span
                types={this.props.types}
                type={this.props.type}
                idx={this.props.idx}
                icon={this.props.skills[this.props.idx].icon}
                initurl={initUrl}
                home_index={home_index}
                defaultPIC2={defaultPIC2}
                home_function={home_function}
                home_label={home_label}
                className={this.props.skills[this.props.idx].icon == initUrl ? 'not_allowed' : 'del'}
                onClick={() => {
                  this.props.skills[this.props.idx].icon = initUrl
                  this.props.dispatch({
                    type: 'CHANGE_MODULE_SKILL',
                    data: {
                      type: 'icon',
                      key: 35,
                      index: this.props.idx,
                      content: this.props.skills[this.props.idx],
                    },
                  })
                }}
              >
                {' '}
                {__('DISTANT_GREETINGS.DELETE')}
              </span>
            </div>
          }
        >
          <img
            src={url}
            src1='/media/module_public/module_skill_home/Standard1_group_photo.png'
            className1='defaultImg  viewImg2'
            types={this.props.types}
            type={this.props.type}
            idx={this.props.idx}
            className={imageStyle + '   '}
            modalStyle={this.props.modalStyle}
          ></img>
          <canvas
            id={'upmybackCanvas' + this.props.idx}
            style={{ display: 'none' }}
            width={sizeRule.width}
            height={sizeRule.height}
          ></canvas>
        </Popover>

        {false && (
          <div className='list' style={{ display: 'none' }}>
            <span className='title'>{__('customHome.label.icon')}</span>

            <p
              className={
                this.props.editIndex &&
                this.props.skills[this.props.editIndex].icon != undefined &&
                this.props.skills[this.props.editIndex].icon != defaultPIC
                  ? 'list_area upload_area'
                  : 'list_area'
              }
            >
              {/* <ButtonSelectFile
								className={this.props.types == 'list_icon2' ? 'upload-btn  circle' : 'upload-btn  square'}
								onSelect={(evt) => {
									if (this.props.types == 'list_icon1') {
										this.props.selectImg(evt, "list_icon1", this.props.editIndex)
									} else if (this.props.types == "list_icon2") {
										this.props.selectImg(evt, "list_icon2", this.props.editIndex)
									} else if (this.props.types == "card_icon") {
										this.props.selectImg(evt, "card_icon", this.props.editIndex)
									} else if (this.props.types == "standard1_icon") {
										this.props.selectImg(evt, "standard1_icon", this.props.editIndex)
									} else if (this.props.types == "standard2_icon") {
										this.props.selectImg(evt, "standard2_icon", this.props.editIndex)
									}
								}}
							/> */}
              <ImgCrop
                aspect={sizeRule.width / sizeRule.height}
                grid
                modalTitle={__('customHome.btn.img_cut')} //
                modalWidth='650px'
                modalOk={__('customHome.btn.confirm')}
                modalCancel={__('customHome.btn.cancel2')}
                fillColor={'transparent'}
              >
                <Upload
                  //   className="upload-btn square"
                  //   className={this.props.modalStyle == "List" ? 'upload-btn  circle' : 'upload-btn  square'}

                  customRequest={() => {}}
                  className={this.props.types == 'list_icon2' ? 'upload-btn  circle' : 'upload-btn  square'}
                  listType='picture-card'
                  beforeUpload={(e) => {
                    console.log(e)
                    let _this = this
                    let img = e
                    let reader = new FileReader()
                    reader.readAsDataURL(img)
                    reader.onload = function (e) {
                      let image = new Image()
                      image.onload = function () {
                        let mycanvas = document.querySelector('#myQueCanvas')
                        let ctx = mycanvas.getContext('2d')
                        ctx.clearRect(0, 0, sizeRule.width, sizeRule.height)
                        ctx.drawImage(image, 0, 0, sizeRule.width, sizeRule.height)
                        let dataurl = mycanvas.toDataURL('image/png')
                        function dataURLtoBlob(dataurl) {
                          let arr = dataurl.split(','),
                            mime = arr[0].match(/:(.*?);/)[1],
                            bstr = atob(arr[1]),
                            n = bstr.length,
                            u8arr = new Uint8Array(n)
                          while (n--) {
                            u8arr[n] = bstr.charCodeAt(n)
                          }
                          return new Blob([u8arr], { type: mime })
                        }
                        // _this.props.selectImg(dataURLtoBlob(dataurl), "backImg", "")
                        // if (_this.state.tabIndex == 0) {
                        //     _this.props.selectImg(dataURLtoBlob(dataurl), "backImg", "")
                        // } else {
                        //     _this.props.selectImg(dataURLtoBlob(dataurl), "facebackImg", "")
                        // }

                        //👍
                        if (_this.props.types == 'list_icon1') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'list_icon1', _this.props.editIndex)
                        } else if (_this.props.types == 'list_icon2') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'list_icon2', _this.props.editIndex)
                        } else if (_this.props.types == 'card_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'card_icon', _this.props.editIndex)
                        } else if (_this.props.types == 'standard1_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'standard1_icon', _this.props.editIndex)
                        } else if (_this.props.types == 'standard2_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'standard2_icon', _this.props.editIndex)
                        }
                      }
                      image.src = e.target.result
                    }
                    return true
                  }}
                  onChange={(e) => {
                    // console.log(e)
                  }}
                ></Upload>
              </ImgCrop>
              <canvas style={{ display: 'none' }} id='myQueCanvas' width={sizeRule.width} height={sizeRule.height} />
              <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
              {this.props.skills[this.props.editIndex].icon != defaultPIC ? (
                <img src={image_url + this.props.skills[this.props.editIndex].icon} className={newIconStyle} />
              ) : (
                ''
              )}
            </p>
            <p
              className='list_area default_area'
              onClick={() => {
                this.props.skills[this.props.editIndex].icon = defaultPIC
                this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'icon',
                    key: 36,
                    index: this.props.editIndex,
                    content: this.props.skills[this.props.editIndex],
                  },
                })
              }}
            >
              <img src={image_url + defaultPIC} className={imageStyle} />
              <span className='defaultInfo'>{__('customHome.pic.default')}</span>
            </p>
            <p className='list_area'>
              <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
              {imageSizeTip}
              <span className='uploadTips'>
                2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
              </span>
              <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
            </p>
          </div>
        )}
      </div>
    )
  }
}
