@import '~base.less';

.component {
  background: #f6f9f9;
  position: relative;
  overflow: hidden;

  .card-container {
    background: #fff;
  }

  .card-container p {
    margin: 0;
  }

  .card-container > .ant-tabs-card .ant-tabs-content {
    // height: 120px;
    // margin-top: -16px;
  }

  .card-container > .ant-tabs-card .ant-tabs-content > .ant-tabs-tabpane {
    background: #fff;
    padding: 16px;
  }

  .card-container > .ant-tabs-card > .ant-tabs-tab {
    border-top-color: #3776ef;
    border-top-width: 2px;
    border-top-style: solid;
  }

  .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {
    border-top-color: #3776ef;
    border-top-width: 2px;
    border-top-style: solid;
  }

  .card-container > .ant-tabs-card > .ant-tabs-nav::before {
    /* display:block; */
  }

  .card-container > .ant-tabs-card .ant-tabs-tab,
  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-tab {
    border-color: transparent;
    background: transparent;
  }

  .card-container > .ant-tabs-card .ant-tabs-tab-active,
  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-tab-active {
    border-color: #fff;
    background: #fff;
    border-top-color: #3776ef;
    border-top-width: 2px;
    border-top-style: solid;
    border-style: solid;
    border-right: solid 1px #f0f0f0;
    border-bottom-style: none;
  }

  .code-box-demo {
    background: #f5f5f5;
    overflow: hidden;
    padding: 24px;
  }

  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-content {
    height: 120px;
    margin-top: -8px;
  }

  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-tab {
    border-color: transparent;
    background: transparent;
  }

  [data-theme='dark'] .code-box-demo {
    background: #000;
  }

  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-content > .ant-tabs-tabpane {
    background: #141414;
  }

  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-tab-active {
    border-color: #141414;
    background: #141414;
  }

  .index-set {
    display: flex;
    flex-direction: row;

    .modules {
      width: 123px;
      font-size: 14px;

      background: #dce7fd;
      opacity: 0.2;
      .modu {
        height: 45px;
        line-height: 45px;
        // text-align: center;
        padding-left: 19px;
      }
    }
    .module-detail {
      flex: 1;
      margin-left: 27px;
      overflow-y: scroll;
    }
  }
}
