import React, { useState, useRef } from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import { <PERSON><PERSON>, Button } from 'biz-components'
import { Popover, Input, Select, AutoComplete, Switch, message } from 'antd'
import { CaretDownOutlined, SmileOutlined } from '@ant-design/icons'
const { Option } = Select
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc'

import 'antd/dist/antd.css'

import modal, { close as closeModal } from '@utils/modal'
import { searchAnswer, qaKewordAdd, getWebQuery } from '@api/_home'
import { ADD_HOMEMODULE_SKILL } from '@redux/action-types'
import { recommendQues, recommendQuesMiniIn } from '@api/_home/recommendQues'
import {
  checkReleaseRaw,
  inputMaxCnQ,
  inputMaxEnQ,
  inputMaxCnA,
  inputMaxEnA,
  parseQuery,
  onLangLen,
} from '@utils/tools'
import QueryList from './queryList'
import Upload from './speach/upload'
import { webOperator } from '@utils/operatorRobot'
window.webOperator = webOperator
import CardTitle from '../cardTitle'
import Answer from './answerModal'
import { qaAdd } from '@api/_webApp'

let righttip = (value, max) => {
  max = max || 8
  return (
    <div>
      <span>{value.title ? value.title.length : value.length}</span> <span>/{max}</span>{' '}
    </div>
  )
}
class righttip1 extends React.Component {
  render() {
    return (
      <div>
        <span>1</span> <span>/2</span>{' '}
      </div>
    )
  }
}

const DragHandle = SortableHandle(() => {
  return (
    <Popover content={__('customHome.modalMsg.pressOrder')}>
      <Icon icon='move2' className='drag_button' />
    </Popover>
  )
})
let DragHandle2 = SortableHandle(() => <Icon icon='move2' className='sort_button ask_sort_button2' />)
let SortableItem = SortableElement(({ modalStyle, idx, value, onRemove, onDelQuery, onDelFun, _this }) => {
  return (
    <p className={modalStyle == 'Card' ? 'sort_info2 sort_info' : 'sort_info'}>
      <Popover content={modalStyle != 'Technical' ? value.title : value.tip}>
        <span className='sort_content'>{modalStyle != 'Technical' ? value.title : value.tip}</span>
      </Popover>
      {modalStyle == 'Standard' && (
        <Icon
          icon='rd_trash'
          className='icon toright'
          onClick={() => {
            console.log('to right click')
            // onDelQuery(idx)
            onDelFun(idx)
          }}
        />
      )}
      {modalStyle == 'Simple' && (
        <Icon
          icon='rd_trash'
          className='icon toright'
          onClick={() => {
            console.log('to right click')
            // onDelQuery(idx)
            onDelFun(idx)
          }}
        />
      )}
      <span className='sort_switch' onClick={() => onRemove(idx, value.display)}>
        <em className='switch_on'></em>
      </span>
      <DragHandle />
    </p>
  )
})
//<div style={{ marginBottom: 16 }}>
//<Input className='abc' addonBefore={selectBefore} suffix={righttip} defaultValue="mysite" />
//</div>
const selectBefore = (_this, idx, type, errShow) => {
  let value = ''
  let text = ''

  if (type == 'question') {
    value =
      _this.props.homeModule.skills[idx] && _this.props.homeModule.skills[idx].function == 'chat'
        ? _this.props.homeModule.skills[idx].label
        : _this.props.homeModule.skills[idx].function
    text = _this.props.homeModule.skills[idx].tip
  } else if (type == 'questionExt') {
    value =
      _this.props.homeModule.questionExt[idx] && _this.props.homeModule.questionExt[idx].function == 'chat'
        ? _this.props.homeModule.questionExt[idx].label
        : _this.props.homeModule.questionExt[idx].function
    text = _this.props.homeModule.questionExt[idx].tip
  } else {
    value = _this.state.funcConf[0].name
    text = ''
  }
  return (
    <div style={{ width: '140px' }}>
      <Select
        value1='问路引领'
        value={value}
        valetemp={value}
        className='select-before'
        onChange={function (val, b) {
          console.log(_this, type, idx)
          // return false;
          let defaultSkill = {}
          let defaultPIC = '',
            defaultPIC2 = ''
          // e={target:{value:va}};
          // return;
          let fun = val == 'chat1' || val == 'chat2' ? 'chat' : val
          let tit =
            val == 'chat1' || val == 'chat2'
              ? idx == 0
                ? 'check in'
                : 'check out'
              : idx == 0
              ? 'Navigation'
              : 'check out'
          let tip =
            val == 'chat1' || val == 'chat2'
              ? idx == 0
                ? 'How to check in'
                : 'How to check out'
              : idx == 0
              ? 'Take me to toilet'
              : 'Take me to XXX'
          _this.setState({
            // selectedIndex:
          })

          if (type == 'questionExt') {
            let ques = _this.props.homeModule.questionExt[idx]
            ques.function = fun
            if (fun == 'chat') {
              ques.label = val
            }
            _this.props.dispatch({
              type: 'CHANGE_MODULE_QUESTION',
              data: {
                type: 'function',
                index: idx,
                content: ques,
              },
            })
            if (fun == 'chat') {
              _this.props.dispatch({
                type: 'CHANGE_MODULE_QUESTION',
                data: {
                  type: 'label',
                  index: idx,
                  content: ques,
                },
              })
            }
          } else {
            if (fun == 'chat') {
              _this.props.homeModule.skills[idx].label = val
            } else {
              delete _this.props.homeModule.skills[idx].label
            }
            //更换function
            _this.props.homeModule.skills[idx].function = fun
            _this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'function',
                index: idx,
                content: _this.props.homeModule.skills[idx],
              },
            })

            // 更换title
            // _this.props.homeModule.skills[idx].title =
            //   _this.props.lang == 'chs' || _this.props.lang == 'english'
            //     ? _this.props.homeModule.skills[idx].title
            //     : idx != 0 && idx != 1
            //     ? _this.props.homeModule.skills[idx].title
            //     : tit
            _this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'title',
                index: idx,
                content: _this.props.homeModule.skills[idx],
              },
            })

            //更换tip
            // _this.props.skills[idx].tip = _this.state.recmmendQuesList[fun][0];
            //切换function 不改变 tip
            // _this.props.homeModule.skills[idx].tip = (_this.props.lang == 'chs' || _this.props.lang == 'english') ? _this.state.recmmendQuesList[fun][0] : (idx != 0 && idx != 1 ? _this.props.homeModule.skills[idx].tip : tip);
            // _this.props.dispatch({
            //     type: 'CHANGE_MODULE_SKILL',
            //     data: {
            //         type: 'tip',
            //         index: idx,
            //         content: _this.props.homeModule.skills[idx]
            //     }
            // })

            // if (val == 'chat1' || val == 'chat2') {
            //     _this.props.homeModule.skills[idx]['label'] = val;
            //     _this.props.dispatch({
            //         type: 'CHANGE_MODULE_SKILL',
            //         data: {
            //             type: 'tip',
            //             index: idx,
            //             content: _this.props.homeModule.skills[idx]
            //         }
            //     })

            // } else if (_this.props.homeModule.skills[idx]['label']) {
            //     delete _this.props.homeModule.skills[idx]['label'];
            //     _this.props.dispatch({
            //         type: 'CHANGE_MODULE_SKILL',
            //         data: {
            //             type: 'tip',
            //             index: idx,
            //             content: _this.props.homeModule.skills[idx]
            //         }
            //     })
            // }
            //切换function 不改变 tip end
            //更改json图片
            //由于执行顺序，不能使上以上defaultPIC
            for (let i = 0; i < _this.state.funcConf.length; i++) {
              if (_this.state.funcConf[i].key == _this.props.homeModule.skills[idx].function) {
                if (_this.props.skills[idx].label == 'chat2') {
                  //聊天
                  defaultSkill = _this.state.funcConf[i + 1]
                } else {
                  defaultSkill = _this.state.funcConf[i]
                }
                break
              }
            }

            let types
            if (type == 'question' && this.props.modalStyle == 'Standard' && idx == 0) {
              types = 'standard2_icon'
            }
            if (type == 'question' && this.props.modalStyle == 'Standard' && idx > 0) {
              types = 'standard1_icon'
            }

            if (type == 'question' && this.props.modalStyle == 'List' && (idx == 0 || idx == 1)) {
              types = 'list_icon1'
            }

            if (type == 'question' && this.props.modalStyle == 'List' && idx > 1) {
              types = 'list_icon2'
            }
            if (type == 'question' && this.props.modalStyle == 'Card') {
              types = 'card_icon'
            }
            if (this.props.modalStyle == 'Buttons') {
              types = 'buttons_icon'
            }
            let home_function = _this.props.homeModule.skills[idx].function || ''
            let home_label = ''
            if (val == 'chat1' || val == 'chat2') {
              home_label = val
            }
            // this.props.homeModule.skills[idx].label || '';
            let home_index = 0
            _this.state.funcConf.map((e, index) => {
              // console.log(e.key);
              // if (e.key == home_function) {
              //     home_index = index
              // }

              if (home_label) {
                if (e.label && home_label == e.label) {
                  home_index = index
                }
              } else if (e.key == home_function) {
                home_index = index
              }
            })

            if (types == 'list_icon1') {
              defaultPIC2 = _this.state.funcConf[home_index].list_icon2
            } else if (types == 'list_icon2') {
              defaultPIC2 = _this.state.funcConf[home_index].list_icon1
            } else if (types == 'card_icon') {
              defaultPIC2 = _this.state.funcConf[home_index].card_icon
            } else if (types == 'standard1_icon') {
              defaultPIC2 = _this.state.funcConf[home_index].standard1_icon
            } else if (types == 'standard2_icon') {
              defaultPIC2 = _this.state.funcConf[home_index].standard2_icon
            } else if ((types = 'buttons_icon')) {
              defaultPIC2 = _this.state.funcConf[home_index].buttons_icon
            }
            // defaultPIC2 = _this.state.funcConf[0].standard2_icon
            _this.props.homeModule.skills[idx].icon = defaultPIC2
            _this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: idx,
                key: 30,
                content: _this.props.homeModule.skills[idx],
              },
            })
          }
          // let fun = e.target.value == 'chat1' || e.target.value == 'chat2' ? 'chat' : e.target.value;
          // // chat1==问答；chat2==聊天
          // // let fun = e.target.value;
          // let defaultPic = '',
          //     labelObj = {};
          // if (e.target.value == 'chat1') {
          //     labelObj.label = 'chat1';
          //     _this.setState({
          //         whichChat: 1
          //     })
          // } else if (e.target.value == 'chat2') {
          //     labelObj.label = 'chat2';
          //     _this.setState({
          //         whichChat: 2
          //     })
          // } else {
          //     if (_this.state.skill['label']) {
          //         delete _this.state.skill['label']
          //     }
          //     _this.setState({
          //         whichChat: ""
          //     })
          // }
          // if (_this.props.modalStyle == 'List') {
          //     defaultPic = _this.state.funcConf[e.target.selectedIndex].list_icon1;
          // } else if (_this.props.modalStyle == 'Card') {
          //     defaultPic = _this.state.funcConf[e.target.selectedIndex].card_icon;
          // } else if (_this.props.modalStyle == 'Standard') {
          //     defaultPic = _this.state.funcConf[e.target.selectedIndex].standard1_icon;
          // }
          // let skill = Object.assign({}, _this.state.skill, { 'function': fun, tip: _this.state.recmmendQuesList[fun][0], icon: defaultPic }, labelObj)
          // _this.setState({
          //     funcIndex: e.target.selectedIndex,
          //     skill: skill
          // })
        }.bind(_this)}
      >
        {/* <Option value="http://">问答</Option>
            <Option value="https://">问路</Option> */}
        {_this.state.funcConf.map((item, index) => {
          if ((item.name === 'Chat' || item.name === '聊天') && _this.props.lang === 'arabic') {
            return ''
          }
          if (
            _this.props.lang != 'chs' &&
            (item.name === 'Q&A' ||
              item.name === '小食分发' ||
              item.name === '餐厅揽客' ||
              item.name === '送餐' ||
              item.name === 'Weather' ||
              item.name === 'Calendar' ||
              item.name === 'Group photo')
          ) {
            return ''
          }
          return (
            <Option key={index} value={item.key == 'chat' ? item.label : item.key}>
              {item.name}
            </Option>
          )
        })}
      </Select>
      <Icon
        icon='guide_voice'
        text={text}
        attr={value}
        className={'icon1 iconV 4567' + (text.length > 0 ? ' icon_theme ' : '') + (errShow ? ' icon_error ' : '')}
        style={{ marginLeft: '20px', display: 'block' }}
      />
    </div>
  )
}
const selectBeforePrefixSelector = (_this, idx, type, setActiveIndex, activeIndex, setActiveFn, activeFn) => {
  return (
    <Select
      className='select-before'
      value={activeIndex}
      onChange={function (val, b) {
        let fn = val == 'chat1' || val == 'chat2' ? 'chat' : val
        setActiveIndex(val)
        setActiveFn(fn)
      }}
    >
      {_this.state.funcConf.map((item, index) => {
        if ((item.name === 'Chat' || item.name === '聊天') && _this.props.lang === 'arabic') {
          return ''
        }
        return (
          <Option key={index} value={item.key == 'chat' ? item.label : item.key}>
            {item.name}
          </Option>
        )
      })}
    </Select>
  )
}
// let
SortableItem = SortableElement(
  ({ modalStyle, idx, value, onRemove, onDelQuery, onDelFun, _this, type, hideDelRemove, prefixSelector }) => {
    let suggQuerys = []
    let inputTimeout = null
    let titleInputTimeout = null
    const [activeIndex, setActiveIndex] = useState('chat1')
    const [activeFn, setActiveFn] = useState('chat')
    const inputR = useRef()

    let fun = ''
    if (_this.props.homeModule.skills[idx]) {
      if (type != 'query' && type != 'questionExt') {
        suggQuerys = _this.state.recmmendQuesList[_this.props.homeModule.skills[idx].function]
      } else if (type == 'questionExt') {
        suggQuerys = _this.state.recmmendQuesList[_this.props.homeModule.questionExt[idx].function]
      } else {
        suggQuerys = _this.state.recmmendQuesList[activeFn]
      }
    } else {
      suggQuerys = _this.state.recmmendQuesList[activeFn]
    }

    if (type == 'questionExt') {
      suggQuerys = _this.state.recmmendQuesList[_this.props.homeModule.questionExt[idx].function]
    }
    if (_this.props.lang == 'english') {
      suggQuerys = []
    }
    const [errShow, setErrShow] = useState(false)
    const [checkmsg, setCheckMsg] = useState('')
    const [showtip, setShowTip] = useState(false)

    const changAnswer = function (data) {
      _this.setState({
        getModalData: data,
      })
    }
    let m_fun =
      type != 'query'
        ? _this.props.homeModule.skills[idx] &&
          _this.props.homeModule.skills[idx].function &&
          _this.props.homeModule.skills[idx].function
        : activeFn
    if (type == 'questionExt') {
      m_fun = _this.props.homeModule.questionExt[idx] && _this.props.homeModule.questionExt[idx].function
    }
    const setQuestionAnswer = function () {
      modal({
        title: __('customHome.modalMsg.set'),
        content: (
          <Answer
            query={type == 'query' ? value : value.tip} //_this.props.modalStyle.toLowerCase() === 'standard' ? value.title :
            getText={changAnswer}
            fun={m_fun}
          />
        ),
      })
        .then(() => {
          let fun =
            type != 'query'
              ? _this.props.homeModule.skills[idx] &&
                _this.props.homeModule.skills[idx].function &&
                _this.props.homeModule.skills[idx].function
              : activeFn
          if (type == 'questionExt') {
            fun = _this.props.homeModule.questionExt[idx] && _this.props.homeModule.questionExt[idx].function
          }
          const { getModalData } = _this.state
          if (fun == 'web' || fun == 'open_app') {
            // 功能网址和app
            const formData = new FormData()
            let name = type == 'query' ? value : value.tip //_this.props.modalStyle.toLowerCase() === 'standard' ? value.title :
            let norm_word = name
            let similar_words = new Array(name)
            formData.append('name', name)
            formData.append('norm_word', norm_word)
            formData.append('similar_words', similar_words)
            if (fun == 'web') {
              // web
              formData.append('type', 0)
              formData.append('app_url', getModalData.webCom)
            } else {
              let param_json = JSON.stringify({
                class_name: getModalData.appLess,
                params: [
                  {
                    key: getModalData.keyparamer,
                    value: getModalData.valueparamer,
                  },
                ],
              })
              formData.append('app_url', getModalData.appName)
              formData.append('type', 1)
              formData.append('param_json', param_json)
            }
            qaAdd(formData)
              .then((res) => {
                message.success('添加成功')
                setErrShow(false)
              })
              .catch((err) => {
                switch (err.code) {
                  case 406:
                    message.error('名称已存在')
                    break
                  case 407:
                    message.error('标准问法已存在')
                    break
                  case 408:
                    message.error('AppURL已存在')
                    break
                  case 409:
                    message.error('URL审核不通过')
                    break
                  case 410:
                    message.error('轻应用模板ID无效')
                    break
                  default:
                    message.error(err.msg)
                }
              })
          } else {
            // 提交问答
            const formData = new FormData()
            let data = {
              media_type: '0',
              media_info: '[]',
              query: JSON.stringify(type == 'query' ? [value] : [value.tip]), //_this.props.modalStyle.toLowerCase() === 'standard' ? [value.title] :
              answer: JSON.stringify([getModalData.data]),
              enterprise_id: _this.props.cropId,
              group_id_list: '[]',
              group_id: '',
              intent: 'orion_baike',
              keyword: '[]',
            }
            formData.append('data', JSON.stringify([data]))
            formData.append('lang', _this.props.curLang.lang_code)
            qaKewordAdd(formData)
              .then((res) => {
                // console.log(1111111111444333,res);
                message.success('添加成功')
                setErrShow(false)
              })
              .catch((err) => {
                console.log(err)
                message.error(err.msg)
              })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    }

    // _this.state.recmmendQuesList[_this.props.homeModule.skills[idx].function]
    let _fun = ''
    if (type == 'question') {
      _fun =
        _this.props.homeModule.skills[idx] && _this.props.homeModule.skills[idx].function == 'chat'
          ? _this.props.homeModule.skills[idx].label
          : _this.props.homeModule.skills[idx].function
    } else {
      _fun = _this.state.funcConf[0].name
    }
    let _value = type == 'query' ? value : value.tip // _this.props.modalStyle.toLowerCase() === 'standard' ? value.title :
    let hide =
      (modalStyle.toLowerCase() === 'list' && type === 'question' && idx < 2 && !hideDelRemove) ||
      (modalStyle.toLowerCase() === 'standard' && type === 'question' && idx < 1 && !hideDelRemove)

    return (
      <div className={hide ? 'hide sort_parent' : 'sort_parent'} key={_fun}>
        <p
          className={modalStyle == 'Card' ? 'sort_info2 ask_sort_info sort_info ' : 'ask_sort_info sort_info '}
        >
          <div className='label-text' 
          >{__('customHome.face.copywriter')}</div>
          <div className='content-box' 
          >
            <div className='viewAsk-box'>
              {type != 'query' && type != 'questionExt' && modalStyle != 'Buttons' && (
                <div className='viewAsk 2' 
                >
                  <Input
                    className='abc'
                    defaultValue={value.title}
                    maxLength={onLangLen(_this.props.lang, 'q')}
                    suffix={righttip(value.title, onLangLen(_this.props.lang, 'q'))}
                    onKeyDown={(e) => {
                      if (e.keyCode == 8) {
                      }
                    }}
                    onkeyUp={() => {}}
                    onChange={(e) => {
                      e.persist && e.persist()
                      let val = e.target.value
                      titleInputTimeout && clearTimeout(titleInputTimeout)
                      titleInputTimeout = setTimeout(() => {
                        let skills = _this.props.homeModule.skills
                        let skill = skills[idx]
                        skill.title = val

                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_SKILL',
                          data: {
                            type: 'title',
                            index: idx,
                            content: skill,
                          },
                        })
                      }, 600)
                    }}
                  />
                </div>
              )}
              {/* && _this.props.modalStyle != 'Standard' */}
              <div
                className={type != 'query' ? 'viewAsk 3 viewAskl2' : 'viewAsk 4 '}
                style={{ position: 'relative' }}
              >
                <Input
                  className='abc noevent'
                  addonBefore={
                    type == 'query'
                      ? prefixSelector
                        ? selectBeforePrefixSelector(
                            _this,
                            idx,
                            type,
                            setActiveIndex,
                            activeIndex,
                            setActiveFn,
                            activeFn,
                          )
                        : ''
                      : selectBefore(_this, idx, type, errShow)
                  }
                  suffix={righttip(_value, onLangLen(_this.props.lang, 'a'))}
                  onChange={(e) => {
                    console.log(_this, idx)
                    return
                    if (type != 'query') {
                      let skills = _this.props.homeModule.skills
                      let skill = skills[idx]
                      skill.tip = e.target.value
                      //  = skill.title
                      _this.props.dispatch({
                        type: 'CHANGE_MODULE_SKILL',
                        data: {
                          type: 'title',
                          index: idx,
                          content: skill,
                        },
                      })
                    } else {
                      _this.props.dispatch({
                        type: 'CHANGE_MODULE_QUERIES',
                        data: {
                          index: idx,
                          content: e.target.value,
                        },
                      })
                    }

                    console.log(e.target.value)
                  }}
                  value1={_value}
                />
                <Select
                  showSearch
                  style={{
                    width: 200,
                    position: 'absolute',
                    right: '30px',
                    zIndex: 10000,
                    paddingLeft: 60,
                    display: 'none',
                  }}
                  showArrow={false}
                  suffixIcon={<div></div>}
                  bordered={false}
                  placeholder='请输入'
                  value={_value}
                  optionFilterProp='children'
                  getPopupContainer={(triggerNode) => triggerNode}
                  onInputKeyDown={(val) => {}}
                  onChange={(val) => {
                    if (type != 'query') {
                      let skills = _this.props.homeModule.skills
                      let skill = skills[idx]
                      // if (_this.props.modalStyle.toLowerCase() === 'standard') {
                      //     skill.title = val;
                      // } else {
                      //     skill.tip = val;
                      // }
                      skill.tip = val

                      // skill.title
                      _this.props.dispatch({
                        type: 'CHANGE_MODULE_SKILL',
                        data: {
                          type: 'title',
                          index: idx,
                          content: skill,
                        },
                      })
                    } else {
                      _this.props.dispatch({
                        type: 'CHANGE_MODULE_QUERIES',
                        data: {
                          index: idx,
                          content: val,
                        },
                      })
                    }
                  }}
                  onFocus={() => {
                    console.log(11111112, 111)
                  }}
                  onBlur={() => {}}
                  onSearch={(val) => {
                    if (val.length == 0) {
                      return
                    }
                    if (type != 'query') {
                      let skills = _this.props.homeModule.skills
                      let skill = skills[idx]
                      // if (_this.props.modalStyle.toLowerCase() === 'standard') {
                      //     skill.title = val;
                      // } else {
                      //     skill.tip = val;
                      // }
                      skill.tip = val
                      // = skill.title
                      _this.props.dispatch({
                        type: 'CHANGE_MODULE_SKILL',
                        data: {
                          type: 'title',
                          index: idx,
                          content: skill,
                        },
                      })
                    } else {
                      _this.props.dispatch({
                        type: 'CHANGE_MODULE_QUERIES',
                        data: {
                          index: idx,
                          content: val,
                        },
                      })
                    }
                    let data = val
                    if (data.trim().length > 0) {
                      // let conciseContent = homeModule.templateInfo.conciseContent;
                      // conciseContent.suggestion =data;
                      // this.props.dispatch({
                      //     type: 'CHANGE_INFO',
                      //     data: {type: 'conciseContent',content: conciseContent}
                      // })
                      let data1 = {
                        question_list: JSON.stringify([data]),
                      }
                      searchAnswer(data1)
                        .then((res) => {
                          let answer = res.items[0]
                          if (!answer.existed) {
                            setErrShow(true)
                            setCheckMsg(data)
                          }
                        })
                        .catch((err) => {
                          console.log(err)
                        })
                    } else {
                      return
                    }
                  }}
                  filterOption={
                    (input, option) => true
                    // option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {' '}
                  {suggQuerys &&
                    suggQuerys.map((item) => {
                      return <Option value={item}>{item}</Option>
                    })}
                </Select>

                {type == 'query' ? (
                  <>
                    <Icon
                      icon='guide_voice'
                      className={
                        'icon1 iconV icon_query' +
                        (_value.length > 0 ? ' icon_theme ' : '') +
                        (errShow ? ' icon_error ' : '')
                      }
                    />
                  </>
                ) : (
                  ''
                )}
                <AutoComplete
                  style1={{
                    width: 200,
                    position: 'absolute',
                    right: '30px',
                    zIndex: 10000,
                    marginLeft: 65,
                    // display: 'none'
                  }}
                  dropdownClassName={_this.props.user.userLocaleId == 'zh_CN' ? '' : 'ant-select-dropdown-hidden'}
                  maxLength={onLangLen(_this.props.lang, 'a')}
                  className={type == 'query' ? (prefixSelector ? 'query hasBefore' : ' query ') : ' question'}
                  onBlur={_this.props.checkJson.bind(_this)}
                  // style={{ width: '70%' }}
                  placeholder=''
                  // children={<input maxLength={8}></input>}
                  onKeyDown={(e) => {
                    if (e.keyCode == 8) {
                      // inputTimeout && clearTimeout(inputTimeout);
                    } else if (e.keyCode == 39) {
                      window.inputing = true
                    }
                  }}
                  onKeyUp={(e) => {
                    console.log('keyup', inputR)
                    if (e.keyCode != 39) {
                      return
                    }
                    let val = inputR.current.value
                    window.inputing = true
                    inputTimeout && clearTimeout(inputTimeout)
                    inputTimeout = setTimeout(() => {
                      window.inputing = false
                      if (type != 'query' && type != 'questionExt') {
                        let skills = _this.props.homeModule.skills
                        let skill = skills[idx]
                        // if (_this.props.modalStyle.toLowerCase() === 'standard') {
                        //     skill.title = val;
                        // } else {
                        //     skill.tip = val;
                        // }
                        skill.tip = val
                        // = skill.title
                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_SKILL',
                          data: {
                            type: 'title',
                            index: idx,
                            content: skill,
                          },
                        })
                      } else if (type == 'questionExt') {
                        let questionExt = _this.props.homeModule.questionExt
                        let ques = questionExt[idx]

                        ques.tip = val
                        ques.title = val

                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_QUESTION',
                          data: {
                            type: 'title',
                            index: idx,
                            content: ques,
                          },
                        })
                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_QUESTION',
                          data: {
                            type: 'tip',
                            index: idx,
                            content: ques,
                          },
                        })
                        ///同步旧版数据结构,否则机器人无法使用
                        setTimeout(() => {
                          _this.props.updatejson && _this.props.updatejson()
                        }, 300)

                        // let oldquestionExt = _this.props.homeModule.questionExt;
                        // let query = [];
                        // oldquestionExt.map(item=>{
                        //     query.push(item.title)
                        // })

                        // _this.props.dispatch({
                        //     type: 'UPDATE_MODULE_QUERY',
                        //     data: {
                        //         query
                        //     }
                        // })
                      } else {
                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_QUERIES',
                          data: {
                            index: idx,
                            content: val,
                          },
                        })
                      }
                      let data = val
                      if (data.trim().length > 0) {
                        // let conciseContent = homeModule.templateInfo.conciseContent;
                        // conciseContent.suggestion =data;
                        // this.props.dispatch({
                        //     type: 'CHANGE_INFO',
                        //     data: {type: 'conciseContent',content: conciseContent}
                        // })
                        let data1 = {
                          question_list: JSON.stringify([data]),
                        }
                        searchAnswer(data1)
                          .then((res) => {
                            let answer = res.items[0]
                            if (!answer.existed && data == (type == 'query' ? value : value.tip)) {
                              setErrShow(true)
                              setCheckMsg(data)
                            } else {
                              setErrShow(false)
                              setCheckMsg('')
                            }
                          })
                          .catch((err) => {
                            console.log(err)
                          })
                      } else {
                        setErrShow(false)
                        return
                      }
                    }, 600)
                  }}
                  onChange={(val) => {
                    // console.log(val,'change')
                    window.inputing = true

                    // let val=inputR.current.value;
                    window.inputing = true
                    inputTimeout && clearTimeout(inputTimeout)
                    inputTimeout = setTimeout(() => {
                      window.inputing = false
                      if (type != 'query' && type != 'questionExt') {
                        let skills = _this.props.homeModule.skills
                        let skill = skills[idx]
                        // if (_this.props.modalStyle.toLowerCase() === 'standard') {
                        //     skill.title = val;
                        // } else {
                        //     skill.tip = val;
                        // }
                        skill.tip = val
                        // = skill.title
                        if (_this.props.modalStyle == 'Buttons') {
                          skill.title = val
                        }
                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_SKILL',
                          data: {
                            type: 'title',
                            index: idx,
                            content: skill,
                          },
                        })
                      } else if (type == 'questionExt') {
                        let questionExt = _this.props.homeModule.questionExt
                        let ques = questionExt[idx]

                        ques.tip = val
                        ques.title = val

                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_QUESTION',
                          data: {
                            type: 'title',
                            index: idx,
                            content: ques,
                          },
                        })
                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_QUESTION',
                          data: {
                            type: 'tip',
                            index: idx,
                            content: ques,
                          },
                        })
                        ///同步旧版数据结构,否则机器人无法使用
                        setTimeout(() => {
                          _this.props.updatejson && _this.props.updatejson()
                        }, 300)

                        // let oldquestionExt = _this.props.homeModule.questionExt;
                        // let query = [];
                        // oldquestionExt.map(item=>{
                        //     query.push(item.title)
                        // })

                        // _this.props.dispatch({
                        //     type: 'UPDATE_MODULE_QUERY',
                        //     data: {
                        //         query
                        //     }
                        // })
                      } else {
                        _this.props.dispatch({
                          type: 'CHANGE_MODULE_QUERIES',
                          data: {
                            index: idx,
                            content: val,
                          },
                        })
                      }
                      let data = val
                      if (data.trim().length > 0) {
                        // let conciseContent = homeModule.templateInfo.conciseContent;
                        // conciseContent.suggestion =data;
                        // this.props.dispatch({
                        //     type: 'CHANGE_INFO',
                        //     data: {type: 'conciseContent',content: conciseContent}
                        // })
                        let data1 = {
                          question_list: JSON.stringify([data]),
                        }
                        searchAnswer(data1)
                          .then((res) => {
                            let answer = res.items[0]
                            if (!answer.existed && data == (type == 'query' ? value : value.tip)) {
                              setErrShow(true)
                              setCheckMsg(data)
                            } else {
                              setErrShow(false)
                              setCheckMsg('')
                            }
                          })
                          .catch((err) => {
                            console.log(err)
                          })
                      } else {
                        setErrShow(false)
                        return
                      }
                    }, 600)

                    // val = val.slice(0,_this.state.maxRecLength)
                  }}
                  bordered={false}
                  value1='12323'
                  // defaultValue={type == 'query' ? value : _this.props.modalStyle.toLowerCase() === 'standard' ? value.title : value.tip}
                  defaultValue={type == 'query' ? value : value.tip} //_this.props.modalStyle.toLowerCase() === 'standard' ? value.title :
                  // listHeight='100'
                  dropdownMatchSelectWidth={true}
                  onMouseEnter={(e) => {
                    // console.log(e,e.target)
                    // console.log('enter')
                    setShowTip(true)
                  }}
                  onMouseLeave={(e) => {
                    // console.log('leave',e.target)
                    setTimeout(() => {
                      setShowTip(false)
                    }, 800)
                  }}
                  // getPopupContainer={triggerNode => triggerNode.parentElement.parentElement.parentElement.parentElement.parentElement.parentElement}
                  // getPopupContainer={()=>{document.body}}
                  options={(() => {
                    //  return [{ value: 'text 1' }, { value: 'text 2' }]
                    let res = []
                    suggQuerys &&
                      suggQuerys.map((item) => {
                        //  <Option value={item}>{item}</Option>
                        res.push({ value: item })
                      })
                    return res
                    return showtip && !window.scrolling ? res : []
                  })()}
                  options1={[{ value: 'text 1' }, { value: 'text 2' }]}
                >
                  <input
                    ref={inputR}
                    type='text'
                    attr='auto_input'
                    className='auto-child'
                    maxLength={onLangLen(_this.props.lang, 'a')}
                    style={{
                      border: 'none',
                      background: 'transparent',
                      boxShadow: 'none',
                      height: '100%',
                      outline: 'none',
                      width: '100%',
                    }}
                  />
                </AutoComplete>
              </div>
            </div>
            <div className='askButton'>
              {type != 'query' && type != 'questionExt' && (
                <Upload
                  devUrl={_this.props.devUrl}
                  modalStyle={_this.props.modalStyle}
                  robotType={_this.props.robotType}
                  idx={idx}
                  editIndex={idx}
                  types={_this.props.chooseQueTypes}
                  type={type}
                  lang={_this.props.lang}
                  selectImg={_this.props.selectImg.bind(this)}
                  // newAddSkill={_this.newAddSkill.bind(this)}
                  // newAddQueries={_this.newAddQueries.bind(this)}
                  addStyle='queries'
                ></Upload>
              )}

              {!hideDelRemove && (
                <>
                  <Popover content={__('customHome.modalMsg.deleConf')}>
                    <Icon
                      icon='rd_trash'
                      className=' asktoright1 rd_trash'
                      onClick={() => {
                        console.log('to right click')
                        // onDelQuery(idx)
                        if (type == 'query') {
                          onDelQuery(idx)
                        } else {
                          onDelFun(idx, type)
                        }
                      }}
                    />
                  </Popover>
                  <Popover content={__('customHome.modalMsg.pressOrder')}>
                    <DragHandle type={type} />
                  </Popover>
                  {type != 'query' && type != 'questionExt' && (
                    <Switch
                      size='small'
                      checked={value.display ? true : false}
                      onChange={(checked) => {
                        _this.add(idx, checked)
                      }}
                    />
                  )}
                </>
              )}
            </div>

            {false && _this.props.lang !== 'arabic' ? (
              <QueryList
                type='0'
                className='querySelCon'
                querys={suggQuerys}
                selectQuery={(query) => {
                  // let skill = Object.assign({}, _this.state.skill, { tip: query })
                }}
              ></QueryList>
            ) : (
              ''
            )}
          </div>
          {/* <div>let </div> <span> big</span> */}
        </p>
        {/* //_this.props.modalStyle.toLowerCase() === 'standard' ? value.title : */}
        {false && errShow && checkmsg == (type == 'query' ? value : value.tip) && (
          <div className='errshow'>
            <span className='errtext'>
              <Icon icon='icon_Warning' className='warnIcon' />“{type == 'query' ? value : value.tip}”
              {__('customHome.step.answers')}
              {/* _this.props.modalStyle.toLowerCase() === 'standard' ? value.title : */}
            </span>
            <span className='handle' onClick={setQuestionAnswer.bind(this)}>
              {__('customHome.modalMsg.set')}
              <Icon icon='control_forward_clic_1' />
            </span>
          </div>
        )}
      </div>
    )
  },
)

let SortableItem2 = SortableElement(({ modalStyle, idx, value, onRemove, onDelQuery, onDelFun, _this, type }) => {
  let suggQuerys = []
  let fun = ''
  if (_this.props.homeModule.skills[idx]) {
    suggQuerys = _this.state.recmmendQuesList[_this.props.homeModule.skills[idx].function]
  }
  // _this.state.recmmendQuesList[_this.props.homeModule.skills[idx].function]
  return (
    <>
      <div className='homeout'>
        <div class='box'>
          <Select
            defaultValue='lucy'
            style={{
              width: 120,
              background: '#DAE6FC',
              // height:'28px'
            }}
            suffixIcon={<CaretDownOutlined />}
            // allowClear
          >
            <Option value='lucy1'>Lucy</Option>
          </Select>
          <SmileOutlined className='icon' />

          <div class='cont'>aaaaa</div>
          <Input
            onMouseEnter={(e) => {
              // show[view.index] = true;
              // view.view=true
              // console.log(show,index,"show");
            }}
            onMouseOut={() => {
              // show[view.index] =false;
              // view.view=false
              // console.log(show,index, "show");
            }}
            bordered={false}
            placeholder='Basic usage'
          />
          <span className='rest'>
            <span className='have'>6</span>
            /8
          </span>
        </div>
        <div className='pic'></div>
        {/* <Switch className='switch' />
      <DeleteOutlined className='del' /> */}
        <DragHandle2 />
        <Icon
          icon='rd_trash'
          className='icon asktoright'
          onClick={() => {
            console.log('to right click')
            // onDelQuery(idx)
            if (type == 'query') {
              onDelQuery(idx)
            } else {
              onDelFun(idx)
            }
          }}
        />
      </div>

      {false && (
        <List
          size='small'
          header={''}
          footer={''}
          bordered
          className='list'
          dataSource={data}
          renderItem={(item) => <List.Item>{item}</List.Item>}
        />
      )}
    </>
  )
})

let flag = false
const SortableList = SortableContainer(
  ({ modalStyle, items, onRemove, onDelQuery, onDelFun, _this, type, hideDelRemove, prefixSelector }) => {
    return (
      <ul className={modalStyle != 'Card' ? 'sort_area1' : 'sort_area1'}>
        {items.map((item, index) => {
          return (
            <SortableItem
              key={`item-${index}-${flag}-${items.length}-${_this.props.robot_id}-${modalStyle}-${
                _this.props.homeModule.version + _this.props.homeModule.templateInfo.titleBarContent
              }-${_this.loaded}`}
              _this={_this}
              onDelQuery={onDelQuery}
              modalStyle={modalStyle}
              type={type}
              hideDelRemove={hideDelRemove}
              index={index}
              idx={index}
              value={item}
              onDelFun={onDelFun}
              onRemove={onRemove}
              prefixSelector={prefixSelector}
              robotType={_this.props.robotType}
            />
          )
        })}
      </ul>
    )
  },
)

@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    devUrl = 'http://test-jiedai.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    modalStyle: state._home.homeModule ? state._home.homeModule.templateInfo.templateCode : '',
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    user: state.user,
    cropId: state.user.corp.corpid,
    brand: state.brand || 'ktv',
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  constructor(props) {
    super(props)
    let _defaultPic = ''
    switch (this.props.modalStyle) {
      case 'List':
        _defaultPic = 'module_public/module_skill_home/List1_interview.png'
        break
      case 'Card':
        _defaultPic = 'module_public/module_skill_home/Card_interview.png'
        break
      case 'Standard':
        _defaultPic = 'module_public/module_skill_home/Standard1_interview.png'
        break
      default:
        _defaultPic = 'module_public/module_skill_home/List1_interview.png'
    }
    let ques = recommendQues
    if (parseQuery().robotType && (parseQuery().robotType.includes('mini') || parseQuery().robotType === 'bao_xiao_mi')) {
      ques = recommendQuesMiniIn
    }
    this.state = {
      recmmendQuesList: ques.recommendQues,
      whichChat: '',
      skill: {
        title: '',
        icon: _defaultPic,
        tip: '我来找人',
        function: 'interview',
        display: true,
      },
      queries: ques.allQuesList[0],
      showQueryTips: true,
      showPicTips: false,
      picTips: '',
      skills: [],
      funcConf: ques.funcConfig,
      allQuesList: ques.allQuesList,
      showQueryList: false,
      isShowFuncWordTip: false,
      isShowQueryWordTip: false,
      lang: this.props.lang || 'chs',
      maxFunLength: this.props.lang == 'chs' ? inputMaxCnQ : inputMaxEnQ,
      maxRecLength: this.props.lang == 'chs' ? inputMaxCnA : inputMaxEnA,
      selectIndex: [],
      selectValue: '问路引领',
      getModalData: '',
      updated: false,
    }
  }

  initRobotNum = 0

  componentDidMount() {
    window.getPropsSet = () => {
      console.log(this.props)
    }

    const formData = new FormData()
    formData.append('lang', this.props.curLang.lang_code)
    getWebQuery(formData).then((res) => {
      let webQuery = [],
        appQuery = []
      res.items.map((item, index) => {
        if (item.type == 0) {
          webQuery.push(item.norm_word)
          item.similar_words.map((el, id) => {
            webQuery.push(el)
          })
        } else {
          appQuery.push(item.norm_word)
          item.similar_words.map((el, id) => {
            appQuery.push(el)
          })
        }
      })
      const web_set = new Set(webQuery)
      webQuery = [...web_set]
      const app_set = new Set(appQuery)
      appQuery = [...app_set]
      this.state.recmmendQuesList['web'] = webQuery
      this.state.recmmendQuesList['open_app'] = appQuery
      this.setState(
        {
          recmmendQuesList: this.state.recmmendQuesList,
        },
        () => {
          // console.log(this.state.recmmendQuesList, 'this.state.recmmendQuesList')
        },
      )
    })
  }

  UNSAFE_componentWillReceiveProps(nextProps) {}
  componentWillUnmount() {
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (this.props.lang != prevProps.lang) {
      this.setState({
        maxFunLength: this.props.lang == 'chs' ? inputMaxCnQ : inputMaxEnQ,
        maxRecLength: this.props.lang == 'chs' ? inputMaxCnA : inputMaxEnQ,
      })
    }
    if (this.props.homeModule && this.props.homeModule != prevProps.homeModule && prevProps.homeModule == undefined) {
      // debugger;
      let updated = !this.state.updated
      this.setState({
        skills: this.props.homeModule.skills,
        updated,
      })
    }
    this.loaded = true
    if (JSON.stringify(prevProps.homeModule) != JSON.stringify(this.props.homeModule)) {
      if (sessionStorage.getItem('config_loaded') == 0) {
        this.loaded = !this.loaded
        sessionStorage.setItem('config_loaded', 1)
      }
    }
    if (
      this.props.currentRobotId != undefined &&
      prevProps.currentRobotId != undefined &&
      prevProps.currentRobotId != this.props.currentRobotId
    ) {
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (
      this.props.homeModule != undefined &&
      prevProps.homeModule &&
      prevProps.homeModule == undefined &&
      this.props.homeModule != undefined
    ) {
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          console.log('-----------------', this.props.robots[el].version, versionGap)
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }
  }
  onSortQueryEnd = ({ oldIndex, newIndex }) => {
    console.log('sortqueryend', oldIndex, newIndex)
    flag = !flag
    this.props.dispatch({
      type: 'CHANGE_QUERY_SORT',
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
      },
    })
  }

  onSortQuestionExtEnd = ({ oldIndex, newIndex }) => {
    console.log('sortquestionend', oldIndex, newIndex)
    flag = !flag
    this.props.dispatch({
      type: 'CHANGE_QUESTIONEXT_SORT',
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
      },
    })
  }
  onSortEnd = (data) => {
    let { oldIndex, newIndex } = data
    // if (this.props.modalStyle == "List") {
    // 	oldIndex = oldIndex + 2;
    // 	newIndex = newIndex + 2
    // }else if(this.props.modalStyle == "Standard") {
    // 	oldIndex = oldIndex + 1;
    // 	newIndex = newIndex + 1
    // }else if(this.props.modalStyle == "Simple") {
    // 	oldIndex = oldIndex ;
    // 	newIndex = newIndex
    // }
    console.log('sortend', oldIndex, newIndex, flag)
    flag = !flag
    // debugger;
    this.props.modalStyle.toLowerCase() === 'list' && newIndex < 2 && (newIndex = 2)
    this.props.modalStyle.toLowerCase() === 'standard' && newIndex < 1 && (newIndex = 1)
    this.props.dispatch({
      type: 'CHANGE_SKILL_SORT',
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
      },
    })
  }
  // 删除指导语
  onDelQuery = (idx) => {
    console.log('del query')
    let standardQueries
    this.props.homeModule.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
    })
    console.log('获取query数量' + standardQueries.length)
    if (standardQueries.length <= 4) {
      message.error(__('customHome.modalMsg.keepRecommendedNum', { num: 4 }))
      // modal({
      // 	title: __('customHome.modalMsg.failedToDelete'),
      // 	content: __('customHome.modalMsg.keepRecommendedNum',{num:4}),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除index：', idx)
      this.props.dispatch({
        type: 'REMOVE_HOMEMODULE_QUERY',
        data: {
          index: idx,
        },
      })
    }
  }
  onDelFun = (id, type) => {
    console.log('del fun', id, type)
    let idx = id
    if (type != 'questionExt') {
      let standardFuns = []
      this.props.homeModule.skills.forEach((item) => {
        standardFuns.push(item)
      })
      console.log('获取query数量' + standardFuns.length)

      if (this.props.modalStyle == 'Buttons') {
        if (standardFuns.length <= 1) {
          //|| this.props.modalStyle.toLowerCase() === 'list' && standardFuns.length <= 5
          message.error(__('customHome.modalMsg.keepButtons', { num: 1 }))
          return
        } else {
          standardFuns.splice(idx, 1)
          this.props.dispatch({
            type: 'UPDATE_SKILL',
            data: {
              skills: standardFuns,
            },
          })
        }
      } else {
        if (standardFuns.length <= 3) {
          //|| this.props.modalStyle.toLowerCase() === 'list' && standardFuns.length <= 5
          message.error(__('customHome.modalMsg.keepFunction', { num: this.props.modalStyle == 'List' ? 1 : 3 }))
          return
        } else {
          standardFuns.splice(idx, 1)
          this.props.dispatch({
            type: 'UPDATE_SKILL',
            data: {
              skills: standardFuns,
            },
          })
        }
      }
    } else {
      let standardQuestionExt = []
      this.props.homeModule.questionExt.forEach((item) => {
        standardQuestionExt.push(item)
      })
      console.log('获取questionEXt数量' + standardQuestionExt.length)
      if (standardQuestionExt.length <= 3) {
        //|| this.props.modalStyle.toLowerCase() === 'list' && standardFuns.length <= 5
        message.error(__('customHome.modalMsg.keepFunction', { num: this.props.modalStyle == 'List' ? 1 : 3 }))
        return
      } else {
        standardQuestionExt.splice(idx, 1)
        this.props.dispatch({
          type: 'UPDATE_QUESTIONEXT',
          data: {
            questionExt: standardQuestionExt,
          },
        })
        setTimeout(() => {
          this.props.updatejson && this.props.updatejson()
        }, 400)
      }
    }
  }
  // 删除指导语
  onDelQuestionExt = (idx) => {
    console.log('del questionExt')
    let standardQueries
    this.props.homeModule.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
    })
    console.log('获取query数量' + standardQueries.length)
    if (standardQueries.length <= 4) {
      message.error(__('customHome.modalMsg.keepRecommendedNum', { num: 4 }))
      // modal({
      // 	title: __('customHome.modalMsg.failedToDelete'),
      // 	content: __('customHome.modalMsg.keepRecommendedNum',{num:4}),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除index：', idx)
      this.props.dispatch({
        type: 'REMOVE_HOMEMODULE_QUERY',
        data: {
          index: idx,
        },
      })
    }
  }
  newAddQueries(text) {
    this.props.dispatch({
      type: 'ADD_NEW_QUERIES',
      data: {
        index: 0,
        content: text,
      },
    })
    closeModal()
  }
  newAddQuestionExt(que) {
    this.props.dispatch({
      type: 'ADD_NEW_QUESTIONEXT',
      data: {
        index: 0,
        content: que,
      },
    })
    setTimeout(() => {
      this.props.updatejson && this.props.updatejson()
    }, 400)
  }
  newAddSkill(skill) {
    console.log(skill)
    // debugger
    if (this.props.modalStyle == 'List') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 2,
          skill: skill,
        },
      })
    } else if (this.props.modalStyle == 'Standard') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 1,
          skill: skill,
        },
      })
    } else if (this.props.modalStyle == 'Simple') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 0,
          skill: skill,
        },
      })
    } else {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 0,
          skill: skill,
        },
      })
    }
    closeModal()
  }
  add = (idx, display) => {
    const skills = this.props.homeModule ? this.props.homeModule.skills : []
    if (this.props.modalStyle == 'Buttons') {
      if (skills.filter((el) => el.display).length <= 1 && !display) {
        return message.error(__('customHome.modalMsg.keepButtons', { num: 1 }))
      }
    } else {
      if (skills.filter((el) => el.display).length <= 3 && !display) {
        return message.error(__('customHome.modalMsg.keepNum'))
      }
    }
    // if (skills.filter(el => el.display).length <= 3 && !display) {
    //     return message.error(__('customHome.modalMsg.keepNum'))
    // }
    console.log('添加index：', idx, '；当前display：', display)
    let index = idx
    switch (this.props.modalStyle) {
      case 'List':
        index = idx
        break
      case 'Card':
        index = idx
        break
      case 'Standard':
        index = idx
        break
      case 'Simple':
        index = idx
        break
      case 'agentOS':
        index = idx
        break
      default:
        index = idx
    }
    this.props.dispatch({
      type: ADD_HOMEMODULE_SKILL,
      data: {
        index,
        display,
        modalStyle: this.props.modalStyle,
      },
    })
  }
  render() {
    const robotName = this.state.robotName != undefined && this.state.robotName
    const skills = this.props.homeModule ? this.props.homeModule.skills : []
    let querys = [],
      questionExt = []
    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'Simple' || templateCode == 'agentOS') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }
      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.state.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };
      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.state.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.state.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.state.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.state.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
      this.props.homeModule.queries.map((e) => {
        // console.log(e.name)
        if (e.name == 'standardMain') {
          querys = e.query
        }
      })
      questionExt = this.props.homeModule.questionExt
    }
    let ask_title = ''
    if (this.props.modalStyle == 'Simple' || this.props.modalStyle == 'agentOS') {
      ask_title = __('customHome.btn.Ask') //'可以问我'
    } else if (this.props.modalStyle == 'Standard') {
      ask_title = __('customHome.btn.Card') //'触发指令'
    } else if (this.props.modalStyle == 'List') {
      ask_title = __('customHome.btn.Card') //'触发指令'
    } else if (this.props.modalStyle == 'Buttons') {
      ask_title = __('customHome.btn.Buttons') //'按钮配置'
    } else {
      ask_title = __('customHome.btn.Card') //'卡片配置'
    }
    let _this = this
    const { suggQuerys } = this.state

    return (
      <div className={this.props.className} key={skills.filter((el) => el.display).length}>
        {this.props.modalStyle.toLowerCase() == 'list' && (
          <>
            <CardTitle
              className='card-rgscl'
              title={__('customHome.btn.Card')}
              tips={__('customHome.btn.Card')} //"卡片配置"
              desc={__('customHome.btn.TTS')} //"触发语音及背景图标配置"
            ></CardTitle>
            {this.props.homeModule && (
              <div className='asksort a2'>
                <SortableList
                  modalStyle={this.props.modalStyle}
                  items={skills.slice(0, 2)}
                  axis='xy'
                  onSortStart={() => {}}
                  onSortEnd={() => {}}
                  _this={this}
                  useDragHandle={true}
                  onDelFun={() => {}}
                  onDelQuery={() => {}}
                  type={'question'}
                  hideDelRemove
                  onRemove={() => {}}
                  robotType={this.props.robotType}
                />
              </div>
            )}
          </>
        )}
        {/*   标准的指令开始   */}
        {false &&
          this.props.homeModule.templateInfo.templateCode != 'Simple' &&
          this.props.homeModule.templateInfo.templateCode != 'agentOS' &&
          this.props.homeModule.templateInfo.templateCode != 'Card' &&
          this.props.homeModule.templateInfo.templateCode != 'List' && (
            <>
              {querys && (
                <CardTitle
                  className='askmeinStandard-rgscl'
                  title={__('customHome.btn.Questions')} //'推荐问法'
                  tips={ask_title}
                  desc={__('customHome.btn.TTS')} //'触发语音配置'
                >
                  <Button
                    attr='newadd'
                    size='small'
                    appearance='hollow'
                    label={__('customHome.btn.New')} //"新增"
                    onClick={() => {
                      let queries = []
                      // "standardMain"
                      this.props.homeModule.queries.map((item) => {
                        if (item.name == 'standardMain') {
                          queries = item.query
                        }
                      })
                      if (queries.length < 16) {
                        let text = '你有什么功能'
                        if (_this.props.lang == 'english') {
                          text = 'What can you do'
                        } else if (_this.props.lang == 'arabic') {
                          text = ''
                        }
                        this.newAddQueries(text)
                      } else {
                        message.error(__('customHome.modalMsg.quesrecommendedNum', { num: 16 }))
                      }
                    }}
                  />
                </CardTitle>
              )}
              <SortableList
                modalStyle={this.props.modalStyle}
                items={querys}
                axis='xy'
                onSortStart={() => {}}
                onSortEnd={this.onSortQueryEnd}
                _this={this}
                type='query'
                useDragHandle={true}
                onDelFun={(idx) => this.onDelFun(idx)}
                onDelQuery={(idx) => this.onDelQuery(idx)}
                onRemove={(idx, display) => this.remove(idx, display)}
                prefixSelector
              />
            </>
          )}
        {/*   标准的指令结束   */}

        {/*   标准的可配置指令开始   */}
        {this.props.homeModule.templateInfo.templateCode != 'Simple' &&
          this.props.homeModule.templateInfo.templateCode != 'agentOS' &&
          this.props.homeModule.templateInfo.templateCode != 'Card' &&
          this.props.homeModule.templateInfo.templateCode != 'List' &&
          this.props.homeModule.templateInfo.templateCode != 'Buttons' && (
            <>
              {querys && (
                <CardTitle
                  className='askmeinStandard-rgscl'
                  title={__('customHome.btn.Questions')} //'推荐问法'
                  tips={ask_title}
                  desc={__('customHome.btn.TTS')} //'触发语音配置'
                >
                  <Button
                    attr='addquestionext'
                    size='small'
                    appearance='hollow'
                    label={__('customHome.btn.New')} //"新增"
                    onClick={() => {
                      let questionExt = []
                      // "standardMain"
                      questionExt = this.props.homeModule.questionExt
                      if (questionExt.length < 16) {
                        let text = '你有什么功能'
                        if (_this.props.lang == 'english') {
                          text = 'What can you do'
                        } else if (_this.props.lang == 'arabic') {
                          text = ''
                        }
                        let que = {
                          function: 'chat',
                          title: text,
                          tip: text,
                          label: 'chat1',
                          display: true,
                          icon: '',
                        }

                        this.newAddQuestionExt(que)
                      } else {
                        message.error(__('customHome.modalMsg.quesrecommendedNum', { num: 16 }))
                      }
                    }}
                  />
                </CardTitle>
              )}
              <SortableList
                modalStyle={this.props.modalStyle}
                items={questionExt}
                axis='xy'
                onSortStart={() => {}}
                onSortEnd={this.onSortQuestionExtEnd}
                _this={this}
                type='questionExt'
                useDragHandle={true}
                onDelFun={(idx) => this.onDelFun(idx, 'questionExt')}
                onDelQuery={(idx) => this.onDelQuery(idx)}
                onRemove={(idx, display) => this.remove(idx, display)}
                prefixSelector
              />
            </>
          )}

        {/*   标准的可配置指令结束   */}
        {this.props.modalStyle.toLowerCase() == 'standard' && (
          <>
            <CardTitle
              className='card-rgscl'
              title={__('customHome.btn.Card')} //'卡片配置'
              tips={__('customHome.btn.Card')} //"卡片配置"
              desc={__('customHome.btn.TTS')} //"触发语音及背景图标配置"
            >
              <Button
                size='small'
                appearance='hollow'
                label={__('customHome.btn.New')} //"新增"
                onClick={() => {
                  if (this.props.homeModule.skills.length < 20) {
                    let fun = 'chat'
                    // chat1==问答；chat2==聊天
                    // let fun = e.target.value;
                    let defaultPic = '',
                      labelObj = {}
                    labelObj.label = 'chat1'
                    if (this.props.modalStyle == 'List') {
                      defaultPic = this.state.funcConf[3].list_icon1
                    } else if (this.props.modalStyle == 'Card') {
                      defaultPic = this.state.funcConf[3].card_icon
                    } else if (this.props.modalStyle == 'Standard') {
                      defaultPic = this.state.funcConf[3].standard1_icon
                    } else if (this.props.modalStyle == 'Buttons') {
                      defaultPic = this.state.funcConf[3].buttons_icon
                    }
                    let skill = Object.assign(
                      {},
                      '',
                      {
                        display: true,
                        function: fun,
                        tip: this.state.recmmendQuesList[fun][0],
                        icon: defaultPic,
                        title: this.state.recmmendQuesList[fun][0],
                      },
                      labelObj,
                    )
                    this.newAddSkill(skill)
                  } else {
                    message.error(
                      __('customHome.modalMsg.cardrecommendedNum', { num: this.props.modalStyle == 'List' ? 18 : 20 }),
                    )
                  }
                }}
              />
            </CardTitle>
            {this.props.homeModule && (
              <div className='asksort  a3'>
                <SortableList
                  modalStyle={this.props.modalStyle}
                  items={skills.slice(0, 1)}
                  axis='xy'
                  onSortStart={() => {}}
                  onSortEnd={() => {}}
                  _this={this}
                  useDragHandle={true}
                  onDelFun={() => {}}
                  onDelQuery={() => {}}
                  type={'question'}
                  hideDelRemove
                  onRemove={() => {}}
                />
              </div>
            )}
          </>
        )}
        {this.props.modalStyle.toLowerCase() !== 'standard' && (
          <CardTitle
            className='askmein-rgscl'
            title={this.props.modalStyle.toLowerCase() == 'list' ? __('customHome.btn.Questions') : ask_title}
            tips={__('customHome.btn.setQuestion')} //"配置问法"
            desc={
              this.props.modalStyle.toLowerCase() == 'buttons'
                ? __('customHome.btn.TTS_BUTTONS_BG')
                : __('customHome.btn.TTS')
            } //"触发语音配置"
          >
            <Button
              size='small'
              appearance='hollow'
              type='simpleadd'
              label={__('customHome.btn.New')} //"新增"
              onClick={() => {
                if (this.props.modalStyle.toLowerCase() !== 'simple' && this.props.modalStyle.toLowerCase() !== 'agentos') {
                  if (this.props.homeModule.skills.length < 20) {
                    let fun = 'chat'
                    // chat1==问答；chat2==聊天
                    // let fun = e.target.value;
                    let defaultPic = '',
                      labelObj = {}
                    labelObj.label = 'chat2'

                    // if (e.target.selectedIndex == 3) {
                    //     labelObj.label = 'chat1';

                    // } else if (e.target.selectedIndex == 4) {
                    //     labelObj.label = 'chat2';

                    // } else {

                    // }
                    if (this.props.modalStyle == 'List') {
                      defaultPic = this.state.funcConf[4].list_icon1
                    } else if (this.props.modalStyle == 'Card') {
                      defaultPic = this.state.funcConf[4].card_icon
                    } else if (this.props.modalStyle == 'Standard') {
                      defaultPic = this.state.funcConf[4].standard1_icon
                    } else if (this.props.modalStyle == 'Buttons') {
                      let arr = this.props.homeModule.skills.find((item) => {
                        return item.icon.indexOf('module_skill_home') > -1
                      })
                      if (arr && arr.icon) {
                        let current = arr.icon.replace('/module_public/module_skill_home/', '')
                        let color_choose = [
                          'button_blue.png',
                          'button_dark_blue.png',
                          'button_purple.png',
                          'button_green.png',
                        ]
                        let can_choose = color_choose.filter((item) => item != current)
                        let index = Math.floor(Math.random() * can_choose.length)
                        defaultPic = '/module_public/module_skill_home/' + can_choose[index]
                      } else {
                        defaultPic = this.state.funcConf[4].buttons_icon
                      }
                    }
                    let text = this.state.recmmendQuesList[fun][0]
                    if (_this.props.lang == 'english') {
                      text = 'What can you do'
                    } else if (_this.props.lang == 'arabic') {
                      text = ''
                    }
                    let skill = Object.assign(
                      {},
                      '',
                      { display: true, function: fun, tip: text, icon: defaultPic, title: text },
                      labelObj,
                    )
                    this.newAddSkill(skill)

                    // let queries = [];
                    // this.props.homeModule.queries.map(item => {
                    //     if (item.name == 'standardMain') {
                    //         queries = item.query;
                    //     }
                    // })
                    // if (queries.length < 16) {
                    //     let text = '我来找人';
                    //     this.newAddQueries(text)
                    // } else {
                    //     message.error(__('customHome.modalMsg.recommendedNum', { num: 16 }))
                    // }
                  } else {
                    // message.error(__('customHome.modalMsg.recommendedNum', { num: 20 }))
                    // 列表式提示为指令
                    if (this.props.modalStyle == 'Buttons') {
                      message.error(
                        __(
                          this.props.modalStyle.toLowerCase() == 'list'
                            ? 'customHome.modalMsg.recommendedNum'
                            : 'customHome.modalMsg.buttonsrecommendedNum',
                          { num: this.props.modalStyle == 'List' ? 18 : 20 },
                        ),
                      )
                    } else {
                      message.error(
                        __(
                          this.props.modalStyle.toLowerCase() == 'list'
                            ? 'customHome.modalMsg.recommendedNum'
                            : 'customHome.modalMsg.cardrecommendedNum',
                          { num: this.props.modalStyle == 'List' ? 18 : 20 },
                        ),
                      )
                    }
                  }
                } else {
                  let questionExt = []
                  // "standardMain"
                  questionExt = this.props.homeModule.questionExt
                  if (questionExt.length < 16) {
                    let text = '你有什么功能'
                    if (_this.props.lang == 'english') {
                      text = 'What can you do'
                    } else if (_this.props.lang == 'arabic') {
                      text = ''
                    }
                    let que = {
                      function: 'chat',
                      title: text,
                      tip: text,
                      label: 'chat1',
                      display: true,
                      icon: '',
                    }

                    this.newAddQuestionExt(que)
                  } else {
                    message.error(__('customHome.modalMsg.quesrecommendedNum', { num: 16 }))
                  }
                }
              }}
            />
          </CardTitle>
        )}

        {/* <Input className='abc' addonBefore={selectBefore} suffix={righttip} defaultValue="介绍" /> */}
        {/* <Speach></Speach> */}
        {this.props.homeModule && (
          <div className='asksort a4'>
            {this.props.homeModule.templateInfo.templateCode != 'Simple' && 
             this.props.homeModule.templateInfo.templateCode != 'agentOS' && (
              <SortableList
                modalStyle={this.props.modalStyle}
                items={skills}
                axis='xy'
                onSortStart={() => {
                  // debugger;
                }}
                onSortEnd={this.onSortEnd}
                _this={this}
                useDragHandle={true}
                onDelFun={(idx) => this.onDelFun(idx)}
                onDelQuery={(idx) => this.onDelQuery(idx)}
                type={'question'}
                onRemove={(idx, display) => this.remove(idx, display)}
              />
            )}

            {this.props.homeModule.templateInfo.templateCode == 'Simple' && (
              <SortableList
                modalStyle={this.props.modalStyle}
                items={questionExt}
                axis='xy'
                onSortStart={() => {}}
                onSortEnd1={this.onSortEnd}
                onSortEnd={this.onSortQuestionExtEnd}
                _this={this}
                type='questionExt'
                useDragHandle={true}
                onDelFun={(idx, type) => this.onDelFun(idx, type)}
                onDelQuery={(idx) => this.onDelQuery(idx)}
                onRemove={(idx, display) => this.remove(idx, display)}
                prefixSelector
              />
            )}

            {this.props.homeModule.templateInfo.templateCode == 'agentOS' && (
              <SortableList
                modalStyle={this.props.modalStyle}
                items={questionExt}
                axis='xy'
                onSortStart={() => {}}
                onSortEnd1={this.onSortEnd}
                onSortEnd={this.onSortQuestionExtEnd}
                _this={this}
                type='questionExt'
                useDragHandle={true}
                onDelFun={(idx, type) => this.onDelFun(idx, type)}
                onDelQuery={(idx) => this.onDelQuery(idx)}
                onRemove={(idx, display) => this.remove(idx, display)}
                prefixSelector
              />
            )}
          </div>
        )}
      </div>
    )
  }
}
