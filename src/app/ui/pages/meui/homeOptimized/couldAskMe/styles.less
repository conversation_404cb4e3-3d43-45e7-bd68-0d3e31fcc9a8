@import '~base.less';

.component {
  background: #fff;
  position: relative;
  // overflow: hidden;
  .errshow {
    margin-top: 8px;
    height: 30px;
    line-height: 30px;
    background: #fff1f0;
    border: 1px solid #eb5b56;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    padding: 0 10px;
    border-radius: 4px;
    .warnIcon {
      width: 14px;
      height: 14px;
      color: #eb5b56;
      margin-right: 4px;
    }
    .errtext {
      display: inline-flex;
      align-items: center;
    }
    .handle {
      color: #4881f0;
      vertical-align: middle;
      display: float;
      float: right;
      display: inline-flex;
      align-items: center;
      cursor: pointer;
    }
  }
  .ant-select-selection-search-input {
    margin-left: -12px;
    width: 79% !important;
  }
  .ant-input-affix-wrapper {
    border: none;
  }
  .askButton{
    position: static;
  }

  .ask_sort_info.sort_info{
    display: flex;
    .label-text{
      color: #9b9b9b;
      white-space: nowrap;
      height: 40px;
      line-height: 50px;
    }
    .content-box{
      display: flex;
      .viewAsk-box{
       .viewAsk{
        margin-left: 10px;
       }
      }
    }
  }
}
