import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import { <PERSON>ton, ChooseRobot } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
import Preview from './preview'
import ChooseModal from './preview/chooseModal'
import Progress from './progress'
import PublishRobot from './publishRobot'
import Sort from './action/sort'
import Action from './action'
import QueryItem from './queryItem'
import Setting from './setting'
import { GET_HOME_MODULE, CHANGE_INFO, CHANGE_MODULE_SKILL, ORIGIN_HOME_MODULE } from '@redux/action-types'
import {
  getModuleConfigList, // /api/control/module_config_list
  getModuleConfig,     // /api/control/module_config_detail
  uploadResource,
  publishModule,
  getModuleStatus,
  directPublishModule,
  recoverDefaultStatus,
  searchAnswer,
  addAsrData,
  translateJson, // 翻译 API
  setCommonTranslations, // 设置通用翻译
} from '@api/_home'
import recommendQues from '@api/_home/recommendQues'
import { getRobotStatistics, selectRobot, getAllRobotList, getRobotOpkList, UPDATE_ROBOT_DATA } from '@api/robots'
import { checkReleaseRaw, setLocalStorageItem } from '@utils/tools'
import { message, Spin, Tooltip as AntTooltip } from 'antd'
import { FETCH, DISFETCH } from '@ui/app'
import { webOperator } from '@utils/operatorRobot'
import { isCnHl, isMini, parseQuery } from '@utils/tools'
import _ from 'lodash'
import { config_json_plus_en_us } from './data'

window.webOperator = webOperator
message.config({
  maxCount: 1,
})

@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    devUrl = 'http://test-jiedai.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    user: state.user,
    langDataActive: state.user.langDataActive,
    brand: state.brand || 'ktv',
    langData: state.user.langData,
    commonTranslations: state._home.commonTranslations,
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  state = {
    iconJson: recommendQues.funcConfig,
    currentButton: 0, //3个按钮
    currentIndex: -2, //模版内容
    currentSort: false, //排序j
    changeContent: 'tips', //编辑区显示内容
    showInputTips: false,
    chooseQueTypes: '', //编辑区question类型
    modalStyle: '', //列表式List 卡片式Card 科技范Technical 标准式Standard,
    curScenes: '', //当前场景
    isDefault: false,
    isShowSetting: true,
    uploadUrl: '', //上传icon
    logo: '', //上传logo
    backImg: '', //自定义背景图
    progressNum: 0, //同步进度
    allNum: 0, //总同步数
    chooseList: [], //同步机器人列表
    fiveTimer: false, //进入页面5秒提示
    queriesIndex: -1, //当前选中queries索引
    showWarning: false, //不显示警示
    queryStyle: '', //底部query
    backImgstyle: '', //自定义背景
    queries_idx: 0,
    showLoading: false,
    currentRobotId: null,
    currentRobotSn: null,
    roleStr: '0',
    robotType: 'globall',
    robotName: undefined,
    lang: isCnHl() ? 'zh_CN' : 'en_US',
    overVersion51: false, //是版本高于5.1,
    overVersion57: false, //是版本高于5.7,
    templateInfoBackground: '',
    faceBackground: '',
    templateNeedUpdate: false, // 模板需要更新
    lowOpkVersion: false,
    initEnd: false, //配置数据是否请求完成
    checkedArray: [],
    unCheckArray: [],
    success: [],
    fail: [],
    utime: null,
    activeKey: '1',
    mainLangSourceConfigInfo: {}, // 主语言下可用的源模板信息，键为模板代码 (`'List'`, `'agentOS'`)，值为包含 `config_id` 和 `version` 的对象
    isGenerating: false,             // 初始为 false
  }
  initRobotNum = 0
  scrollfunction(e) {
    if (
      e.target.nodeName.toLowerCase() == 'input' ||
      (e.target.className && e.target.className.indexOf('virtual-list') > -1)
    ) {
      e.stopPropagation()
      e.preventDefault()
    } else {
      let len = document.querySelectorAll('input[attr="auto_input"]').length
      for (let i = 0; i < len; i++) {
        !window.inputing && document.querySelectorAll('input[attr="auto_input"]')[i].blur()
      }
    }
  }
  componentDidMount() {
    //只执行一次。
    sessionStorage.setItem('config_loaded', 1)
    sessionStorage.setItem('config_title_loaded', 1)
    this.props.onRef && this.props.onRef(this)
    this.resetHomeSetting() // 进入页面首先清理上次的配置缓存
    this.getRobotName()
    this.simple_config_json = {}
    this.common_config_json = {}
    
    // 新增：监听iframe的翻译数据请求
    window.addEventListener('message', (e) => {
      if (e.data.command === 'askCommonTranslations') {
        // 发送当前的翻译数据
        this.sendTranslationsToIframe(this.props.commonTranslations || { askMe: '你还可以问我' });
      }
    });
    
    const timer = setInterval(() => {
      // 同时检查 currentRobotId 和 langData 是否都已就绪
      if (this.state.currentRobotId && this.props.langData && this.props.langData.length > 0) {
        clearInterval(timer)
        this.getConfigList() // 获取当前语言配置
        this.checkMainLangConfigStatus(); // 检查主语言是否有已发布的List配置
        this.translateCommonTexts(); // 翻译通用文本
      }
    }, 500)

    // document.querySelector('.module-detail ')&&(document.querySelector('.module-detail ').addEventListener('scroll',this.scrollfunction,true) )
    window.addEventListener('scroll', this.scrollfunction.bind(this), true)

    //5秒关闭提示蒙层
    const fiveTimer = setTimeout(() => {
      clearTimeout(fiveTimer)
      this.setState({
        fiveTimer: false,
        haveCover: this.props.homeModule && this.props.homeModule.templateInfo.haveCover ? true : false,
      })
    }, 5000)
    // 待默认列表取完之后再取opk 的信息
    let opkcheck = setInterval(() => {
      if (Object.keys(this.props.robots).length > 0) {
        clearInterval(opkcheck)
        this.getRobotOpkList()
      }
    }, 900)
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
  }
  getRobotOpkList() {
    return
    let formdata = new FormData()
    formdata.append('is_pad_baseopk', 1)
    getRobotOpkList(formdata).then((res) => {
      res.robot_list.map((val) => {
        let obj = {}
        obj.id = val.robot.robot_uuid
        obj.data = { robot_baseopk_version: val.robot.robot_baseopk_version }
        this.props.dispatch({
          type: 'UPDATE_ROBOT_DATA',
          id: obj.id,
          data: obj.data,
        })
      })
    })
  }

  getQueryVal = (k, search, uri) => {
    if (search == undefined) {
      search = location.search
    }
    var reg = new RegExp('(^|&)' + k + '=([^&]*)(&|$)')
    var s = (uri && '?' + uri.split('?')[1]) || search
    var r = s.slice(1).match(reg)
    if (r != null) {
      return decodeURIComponent(r[2])
    }
    return null
  }

  componentWillUnmount() {
    // this.props.dispatch({
    //     type: 'GET_HOME_MODULE',
    //     data: {
    //         homeModule: null
    //     }
    // })
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (
      this.props.currentRobotId != undefined &&
      prevProps.currentRobotId != undefined &&
      prevProps.currentRobotId != this.props.currentRobotId
    ) {
      // debugger;
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      this.getConfigList()
      this.checkMainLangConfigStatus();
      this.translateCommonTexts(); // 机器人切换时重新翻译
    }
    // 监听语言切换
    if (
      this.props.langDataActive &&
      prevProps.langDataActive &&
      this.props.langDataActive.lang_code !== prevProps.langDataActive.lang_code
    ) {
      this.translateCommonTexts(); // 语言切换时重新翻译
    }
    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }
    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      prevProps.homeModule.templateInfo.bubbleContents &&
      prevProps.homeModule.templateInfo.bubbleContents != this.props.homeModule.templateInfo.bubbleContents
    ) {
      this.setState({ Bubble1: '' })
    }
    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo.background &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      this.state.templateInfoBackground != this.props.homeModule.templateInfo.background
    ) {
      this.setState({ templateInfoBackground: this.props.homeModule.templateInfo.background })
    }
  }

  getConfigList(success) {
    const { langDataActive , user} = this.props
    const isAgentOS = user?.corp?.enable_aios === '1'
    let module_code_list = ['module_skill_home']
    let formData = new FormData()
    formData.append('module_code_list', JSON.stringify(module_code_list))
    formData.append('robot_uuid', this.state.currentRobotId)
    formData.append('config_status', 'publish')

    formData.append('lang', langDataActive.lang_code)

    this.setState({
      showLoading: true,
    })
    getModuleConfigList(formData)
      .then((res) => {
        let config_id = '',
          version = ''
        let robotType = this.getQueryVal('robotType')
        if (res.module_config_list.length != 0) {
          config_id = res.module_config_list[0].config_id
          version = res.module_config_list[0].version
          this.gethomeModule(config_id, version, success)
        } else {
          config_id = Date.parse(new Date()) / 1000
          version = ''
          if (this.isKTV) {
            this.gethomeModule('ktv_simple_id', version, success)
          } else if (robotType === 'bao_mini') { 
            // bao_mini：豹小秘mini + 豹小秘2, 国内机型 
            let default_id = ''
            if(isAgentOS) { // 添加支持agentos的id
              default_id = 'mini_aios_default_id_' + langDataActive.lang_code.toLowerCase()
            } else {
              default_id = 'mini_default_id_' + langDataActive.lang_code.toLowerCase()
            }
            this.gethomeModule(default_id, version, success)
          } else if (robotType === 'bao_xiao_mi' && isAgentOS) { 
            // 25年5月添加agentos模板之前，bao_xiao_mi机型走的是默认default_id，没有和 bao_mini 国内机型一样有特殊的id配置
            // 为了支持agentos，需要走mini_aios_default_id
            let default_id =  'mini_aios_default_id_' + langDataActive.lang_code.toLowerCase()
            this.gethomeModule(default_id, version, success);
          } else if (robotType === 'mini_in') {
            // mini_in： MiniBot + Nova, 海外机型
            let default_id = ''
            if(isAgentOS) { // 添加支持agentos的id
              default_id = 'mini_in_aios_default_id_' + langDataActive.lang_code.toLowerCase()
            } else {
              default_id = 'mini_in_default_id_' + langDataActive.lang_code.toLowerCase()
            }
            this.gethomeModule(default_id, version, success)
          } else if (robotType === 'bxm_plus' || robotType === 'bxm_plus_in') {           
            const { langDataActive } = this.props
            let default_id = 'bxm_plus_default_id_' + langDataActive.lang_code.toLowerCase()
            this.gethomeModule(default_id, version, success)
          } else {
            this.gethomeModule('default_id', version, success)
          }
        }
        this.module_config_list = res.module_config_list
        sessionStorage.setItem('config_id', config_id)
        this.setState({
          config_id,
        })
        sessionStorage.setItem('version', version)
        this.setState({
          showLoading: false,
        })
      })
      .catch((res) => {
        this.setState({
          showLoading: false,
        })
      })
  }

  storeHomeSetting(key, scenes, json) {
    //检车企业id  是否一致
    let _home_setting = localStorage.getItem('home_setting')
    let home_setting = null
    try {
      home_setting = JSON.parse(_home_setting)
    } catch (error) {}
    home_setting = home_setting || { corp_id: '', data: {} }
    if (key == 'List' || key == 'Standard' || key == 'Card' || key == 'Simple' || key == 'agentOS') {
      key = 'common'
    }
    let index = key + '_' + scenes
    home_setting.data[index] = json
    localStorage.setItem('home_setting', JSON.stringify(home_setting))
  }

  getHomeSetting(key, scenes) {
    let _home_setting = localStorage.getItem('home_setting')
    let home_setting = { corp_id: '', data: {} }
    let config = {}
    try {
      home_setting = JSON.parse(_home_setting)
    } catch (error) {}
    if (key == 'List' || key == 'Standard' || key == 'Card' || key == 'Simple' || key == 'agentOS') {
      key = 'common'
    }
    let index = key + '_' + scenes
    if (home_setting.data[index]) {
      config = home_setting.data[index]
    }
    return config
  }

  resetHomeSetting() {
    let home_setting = { corp_id: '', data: {} }
    let _home_setting = JSON.stringify(home_setting)
    localStorage.setItem('home_setting', JSON.stringify(home_setting))
  }

  // 获取首页模板数据 
  gethomeModule(config_id, version, success) {
    const { langDataActive } = this.props
    let robotType = this.getQueryVal('robotType')
    if (!config_id) {
      config_id = sessionStorage.getItem('config_id')
    }
    if (!version) {
      version = sessionStorage.getItem('version')
    }
    let formData = new FormData()
    formData.append('module_code', 'module_skill_home')
    formData.append('config_id', config_id)
    formData.append('version', version)
    formData.set('lang', langDataActive.lang_code)

    this.props.dispatch({
      type: 'CHANGE_HOME_LANG',
      data: this.state.lang,
    })
    this.setState({
      utime: null,
      showLoading: true,
    })

    getModuleConfig(formData)
      .then((res) => {

        let config_json = res.module_config.config_json
        // console.log('获取首页模板数据========', config_json)
        if (typeof config_json === 'string') {
          try {
            config_json = JSON.parse(config_json)
          } catch (e) {}
        }
        if ((robotType === 'bxm_plus' || robotType === 'bxm_plus_in') && _.isEmpty(res.module_config)) {
          let ens = { ...config_json_plus_en_us() }
          let ens1 = { ...ens.templateInfo }
          ens.lang = langDataActive.lang_code
          config_json = ens
        }
        if (config_json.templateInfo.templateCode == 'Simple') {
          this.simple_config_json = JSON.parse(JSON.stringify(config_json))
        } else {
          this.common_config_json = JSON.parse(JSON.stringify(config_json))
        }
        if (config_json.templateInfo.conciseContent == undefined) {
          config_json.templateInfo.conciseContent = {
            title: '猎户风景区欢迎你',
            image: 'module_public/module_skill_home/conciseimage.png',
            suggestion: '我要买门票',
          }
          if (isMini(robotType)) {
            config_json.templateInfo.conciseContent.image =
              'module_public/module_skill_home/bao_mini/miniconciseimage.png'
          }
        }
        if (
          isMini(robotType) &&
          config_json.templateInfo.conciseContent.image == 'module_public/module_skill_home/conciseimage.png'
        ) {
          config_json.templateInfo.conciseContent.image =
            'module_public/module_skill_home/bao_mini/miniconciseimage.png'
        }
        if (config_json.templateInfo.conciseContent.suggestionExt == undefined) {
          config_json.templateInfo.conciseContent.suggestionExt = {
            function: 'chat',
            title: config_json.templateInfo.conciseContent.suggestion,
            tip: config_json.templateInfo.conciseContent.suggestion,
            label: 'chat1',
            display: true,
            icon: '',
          }
        }

        // 添加agentOS内容初始化
        if (config_json.templateInfo.agentosContent == undefined) {
          config_json.templateInfo.agentosContent = {
            title: (__('customHome.agentosContent.title')),
            suggestion: (__('customHome.agentosContent.suggestion')),
            image: "", // 默认图片为空
            background: "module_public/module_skill_home/bao_mini/miniaiosbg.png",
            suggestionExt: {
              title: (__('customHome.agentosContent.suggestionExt.title')),
              tip: (__('customHome.agentosContent.suggestionExt.tip')),
              icon: "",
              display: true,
              function: "chat",
              label: "chat1"
            }
          }
        }

        // 豹大屏默认大屏图片  兼容已经配置的文件
        if (!config_json.templateInfo.bs_background) {
          if (robotType == 'bao_da_ping') {
            config_json.templateInfo.bs_background = 'module_public/module_skill_home/baodaping_1.png'
          }
          if (robotType == 'bxm_plus' || robotType == 'bxm_plus_in') {
            config_json.templateInfo.bs_background = 'module_default/module_skill_home/bxmplus_1.png'
          }
        }

        //给结构打补丁
        if (config_json.queries && !config_json.questionExt) {
          let standardQueries
          let questionExt = []
          config_json.queries.forEach((item) => {
            if (item.name == 'standardMain') {
              standardQueries = item.query
              standardQueries.map((item, index) => {
                let que = {
                  function: 'chat',
                  title: item,
                  tip: item,
                  label: 'chat1',
                  display: true,
                  icon: '',
                }
                questionExt.push(que)
              })
            }
          })
          config_json.questionExt = questionExt
        }
        this.props.dispatch({
          type: 'GET_HOME_MODULE',
          data: {
            homeModule: { ...config_json },
          },
        })

        this.storeHomeSetting(config_json.templateInfo.templateCode, config_json.templateInfo.scenes, config_json)
        if (
          this.isKTV &&
          this.props.homeModule.templateInfo.templateCode != 'Simple' &&
          !this.state.lowOpkVersion &&
          !this.state.isDefault &&
          !(this.state.isShowSetting && this.state.isDefault)
        ) {
          this.setState({
            templateNeedUpdate: true,
          })
          modal({
            title: __('customHome.modalMsg.tip'),
            fullscreen: false,
            content: (
              <div>
                <p>{__('customHome.modalMsg.pageNewChange')}</p>
                <p>{__('customHome.modalMsg.pageSetChange')}</p>
              </div>
            ),
            cancel: false,
            confirm: __('customHome.modalMsg.cureChange'),
            onConfirm: () => {
              this.setState(
                {
                  templateNeedUpdate: false,
                },
                () => {
                  this.choosemodalStyle('Simple', 'KTV', 'chs')
                },
              )
            },
          })
          let lowOpkVersion = this.state.lowOpkVersion
          let isDefault = this.state.isDefault
          window.tipSetData &&
            window.tipSetData({
              templateNeedUpdate: true,
              lowOpkVersion,
              isDefault,
            })
        } else {
          let lowOpkVersion = this.state.lowOpkVersion
          let isDefault = this.state.isDefault
          window.tipSetData &&
            window.tipSetData({
              templateNeedUpdate: false,
              lowOpkVersion,
              isDefault,
            })
        }
        let scenes = config_json.templateInfo.scenes ? config_json.templateInfo.scenes : 'default'
        let curScenes = ''
        let path = ''
        // 修复 选择类型时 切换 简洁式其他的类型，会造成简洁式 背景被重置的问题
        let backUrl = this.props.homeModule.templateInfo.background
        if (backUrl.indexOf('module_public') == -1) {
          this.setState({
            backImg: backUrl,
          })
        }
        // !this.state.SimplebackImg && this.props.homeModule.templateInfo.templateCode == 'Simple' && this.setState({ //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
        //     SimplebackImg: backUrl,
        //     backImg: backUrl
        // })
        let _faceBackground = this.props.homeModule.templateInfo.faceBackground || ''
        if (this.state.faceBackground == '' && _faceBackground.indexOf('module_public') == -1) {
          this.setState({
            //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
            faceBackground: _faceBackground,
          })
        }
        if (scenes == 'default') {
          path = 'default'
        } else {
          path = path + '_default'
        }
        let lang_ext = ''
        if (this.state.lang == 'english') {
          lang_ext = '_en'
        } else if (this.state.lang == 'arabic') {
          lang_ext = '_ar'
        }
        if (res.module_config.config_json_path == 'module_default/module_skill_home/' + path + lang_ext + '.json') {
          this.setState({
            isDefault: true,
          })
        } else {
          this.setState({
            isDefault: false,
          })
        } //是不是默认JSON
        if (this.isKTV) {
          if (this.module_config_list.length == 0) {
            this.setState({
              isDefault: true,
            })
          } else {
            this.setState({
              isDefault: false,
            })
          }
        }
        if (scenes) {
          switch (scenes) {
            // case __('customHome.sceneType.default').toLowerCase():
            case 'default':
              // curScenes = "默认"
              curScenes = __('customHome.sceneType.default')
              break
            // case __('customHome.sceneType.market'):
            case 'market':
              // curScenes = "商场"
              curScenes = __('customHome.sceneType.market')
              break
            // case __('customHome.sceneType.hotel'):
            case 'hotel':
              // curScenes = "酒店"
              curScenes = __('customHome.sceneType.hotel')
              break
            // case __('customHome.sceneType.bank'):
            case 'bank':
              // curScenes = "银行"
              curScenes = __('customHome.sceneType.bank')
              break
            // case __('customHome.sceneType.museum'):
            case 'museum':
              // curScenes = "博物馆"
              curScenes = __('customHome.sceneType.museum')
              break
            // case __('customHome.sceneType.library'):
            case 'library':
              // curScenes = "图书馆"
              curScenes = __('customHome.sceneType.library')
              break
            case 'KTV':
              // curScenes = "图书馆"
              curScenes = 'KTV'
              break
            default:
              curScenes = __('customHome.sceneType.default')
          }
        }
        if (res.module_config.utime) {
          this.setState({
            utime: res.module_config.utime,
          })
        }
        this.setState({
          curScenes: curScenes,
          modalStyle: config_json.templateInfo.templateCode,
          initEnd: true,
          showLoading: false,
        })

        success && success()
      })
      .catch((res) => {
        // console.log('首页模板数据 42', { res })
        this.setState({
          showLoading: false,
        })
      })
  }
  // changeRobot(id) {
  // 	this.props.dispatch(selectRobot(id));
  // 	this.props.dispatch(getRobotStatistics(id));
  // }
  recoverSet() {
    //删除上传的背景图
    this.setState({
      backImg: '',
      queryStyle: '',
      isShowSetting: true,
      faceBackground: '',
      SimplebackImg: '',
    })
    if (sessionStorage.getItem('version') == '') {
      this.getConfigList(() => {
        message.success(__('customHome.modalMsg.successRestMsg'))
        this.changeTab_backImg()
      })
    } else {
      let robotID = [this.props.currentRobotId]
      let formData = new FormData()
      if (this.state.lang == 'english') {
        formData.append('module_code', 'module_skill_home')
        formData.append('lang', 'en_US')
      } else if (this.state.lang == 'arabic') {
        formData.append('module_code', 'module_skill_home_ar')
        formData.append('lang', 'zh_CN')
      } else {
        formData.append('module_code', 'module_skill_home')
        formData.append('lang', 'zh_CN')
      }
      // formData.append('module_code', 'module_skill_home');
      formData.append('config_id', sessionStorage.getItem('config_id'))
      formData.append('version', sessionStorage.getItem('version'))
      formData.append('robot_uuid_list', JSON.stringify(robotID))
      formData.append('status', 'default')
      recoverDefaultStatus(formData)
        .then((res) => {
          this.getConfigList()
          this.changeTab_backImg()
          message.success(__('customHome.modalMsg.successRestMsg'))
        })
        .catch((res) => {
          modal({
            title: __('customHome.modalMsg.failedToReset'),
            content: __('customHome.modalMsg.failedRestMsg'),
            cancel: __('customHome.btn.cancel2'),
            confirm: __('customHome.btn.retry'),
            btnCenter: true,
            onConfirm: () => {
              this.recoverSet()
            },
          })
        })
    }
  }
  getRobotName(id) {
    const robots = this.props.robots,
      roleStr = this.state.roleStr != undefined && this.state.roleStr,
      robotType = this.state.robotType != undefined && this.state.robotType
    let robotName = '',
      current
    if (Object.keys(robots).length == 0 && !this.props.fetchStatus && this.initRobotNum < 8) {
      this.initRobotNum += 1
      return setTimeout(() => {
        this.getRobotName()
      }, 500)
    }
    if (Object.keys(robots).length == 0) {
      robotName = __('customHome.modalMsg.noRobots')
    } else {
      current = id || localStorage.getItem('globallRobotId')
      const firstRobotId = Object.keys(robots)[0]
      if (current || current !== 'all') {
        if (!robots[current]) {
          robotName = robots[firstRobotId] && robots[firstRobotId].robot_name
          current = firstRobotId
          // setLocalStorageItem('globallRobotId', firstRobotId)
          // setLocalStorageItem('localRobotId', firstRobotId)
        } else {
          robotName = robots[current] && robots[current].robot_name
        }
      } else {
        robotName = robots[firstRobotId] && robots[firstRobotId].robot_name
        current = firstRobotId
        // setLocalStorageItem('globallRobotId', firstRobotId)
        // setLocalStorageItem('localRobotId', firstRobotId)
      }
    }
    let version = this.props.robots && this.props.robots[current] && this.props.robots[current].version
    let robot_baseopk_version =
      (this.props.robots && this.props.robots[current] && this.props.robots[current].robot_baseopk_version) || ''
    let baseVersion = '02.0001.200821'
    let lowOpkVersion = checkReleaseRaw(robot_baseopk_version, baseVersion, true) ? false : true
    let overVersion51 = checkReleaseRaw(version, 'V5.1', true)
    let overVersion57 = checkReleaseRaw(version, 'V5.7', true)

    if (this.props.robots && this.props.robots[current]) {
      this.setState({
        currentRobotId: current,
        robotName: robotName,
        overVersion51: overVersion51,
        overVersion57: overVersion57,
        lowOpkVersion,
      }, async () =>{
        this.props.dispatch(selectRobot(current));
        await this.checkMainLangConfigStatus(); // 在 robotId 确认更新后调用,获取主语言已发布的配置
      })
    }else{
      this.setState(
        {
          robotName: robotName,
          overVersion51: overVersion51,
          overVersion57: overVersion57,
          lowOpkVersion,
        },
      )
    }
    window.tipSetData && window.tipSetData({ lowOpkVersion })
    let isDefault = this.state.isDefault
    window.tipSetData &&
      window.tipSetData({
        templateNeedUpdate: false,
        lowOpkVersion,
        isDefault,
      })
    if (this.isKTV && lowOpkVersion) {
      setTimeout(() => {
        modal({
          title: __('customHome.modalMsg.tip'),
          fullscreen: false,
          content: (
            <div>
              <p>当前机器人系统版本太低，不支持配置，请跳转至原后台</p>
              <p>{__('customHome.modalMsg.contactPresale')}</p>
            </div>
          ),
          cancel: false,
          confirm: __('customHome.btn.confirm'),
          onConfirm: () => {},
        })
      }, 900)
    }
  }
  getRobotId(data, unCheckArray) {
    // publish Robot  组件更新机器人的方法
    this.setState(
      {
        robotArray: data,
        unCheckArray: unCheckArray,
      },
      () => {
        let rid = []
        rid = data.map((item) => {
          return item.robot_id
        })
        this.chooseRobots(rid)
      },
    )
  }
  getRobot(cb) {
    // 设置选中的机器人 和待选机器人
    const idInRobot = this.state.checkedArray, //
      _allRobot = JSON.parse(Object.assign(JSON.stringify(this.props.robots)))
    let checkedArray = [],
      unCheckArray = [],
      allRobot = []
    Object.keys(_allRobot).map((robot, val) => {
      let versionGap = checkReleaseRaw(_allRobot[robot].version, 'V5.10', true)
      versionGap = true
      if (versionGap) {
        allRobot.push(_allRobot[robot])
      }
    })
    // debugger;
    if (idInRobot.length == 0) {
      let currRobot = this.props.robots[this.props.currentRobotId]
      if (currRobot) {
        // currRobot.addstatus = true;
        idInRobot.push(currRobot)
      }
    }
    Object.keys(allRobot).map((robot, index) => {
      allRobot[robot].addstatus = false
      // allRobot[robot].state==1&&
      unCheckArray.push(allRobot[robot])
      if (idInRobot.length > 0) {
        idInRobot.map((id, key) => {
          if (allRobot[robot].robot_id === id.robot_id) {
            checkedArray.push(allRobot[index])
            unCheckArray[index].addstatus = true
          }
        })
      }
    })
    if (checkedArray.length == 0) {
      let currRobot = this.props.robots[this.props.robotId]
      if (currRobot) {
        // currRobot.addstatus = true;
        checkedArray.push(currRobot)
      }
    }
    this.setState(
      {
        robotArray: checkedArray,
        checkedArray,
        unCheckArray: unCheckArray,
      },
      () => {
        let rid = [],
          data = checkedArray
        rid = data.map((item) => {
          return item.robot_id
        })
        this.chooseRobots(rid)
        cb && cb()
      },
    )
  }
  updatejson() {
    // UPDATE_MODULE_QUERY
    let questionExt = this.props.homeModule.questionExt
    let query = []
    questionExt.map((item) => {
      console.log(item)
      query.push(item.title)
    })

    this.props.dispatch({
      type: 'UPDATE_MODULE_QUERY',
      data: {
        query,
      },
    })
  }

  checkHomeModule() {
    return new Promise((resolve) => {
      if (JSON.stringify(this.props.homeModule) === JSON.stringify(this.props.originHomeModule)) {
        return resolve(true)
      } else {
        return modal({
          title: __('customHome.modalMsg.tip'),
          fullscreen: false,
          content: __('customHome.modalMsg.changeSetNull'),
          cancel: __('customHome.btn.cancel2'),
          confirm: __('customHome.btn.confirm'),
          btnCenter: true,
          onConfirm: () => {
            return resolve(true)
          },
          onCancel: () => {
            return resolve(false)
          },
        })
      }
    })
  }
  fetchOnlineStatus() {
    this.robotTimer && clearInterval(this.robotTimer)
    this.mergeRobotList(this.props.robots)
    this.robotTimer = setInterval(() => {
      this.mergeRobotList(this.props.robots)
    }, 30 * 1000)
  }
  disFetchOnlineStatus() {
    this.robotTimer && clearInterval(this.robotTimer)
  }
  mergeRobotList(robots) {
    return getAllRobotList({
      robot_prodtype_id: this.getQueryVal('robotType'),
    }).then((res) => {
      let resRobots = {}
      res.robot_list.map((el) => {
        let robotItem = {
          robot_name: el.robot.robot_name,
          robot_id: el.robot.robot_uuid,
          sn: el.robot.robot_sn,
          state: el.robot.online_status,
          version: el.robot.robot_version,
          robot_aios_version: el.robot.robot_aios_version,
        }
        robots[el.robot.robot_uuid] &&
          (robotItem.robot_baseopk_version = robots[el.robot.robot_uuid].robot_baseopk_version)
        resRobots[el.robot.robot_uuid] = robotItem
        if (Object.keys(this.props.robots).length > 0) {
          // 不能直接dispatch ，否则页面的机器人数据会频繁更新
          this.props.robots[el.robot.robot_uuid] &&
            (this.props.robots[el.robot.robot_uuid].state = el.robot.online_status)
        }
      })
      let localRobotId = localStorage.getItem('globallRobotId')
      if (!resRobots[localRobotId]) {
        localRobotId = Object.keys(resRobots)[0]
      }

      if (Object.keys(this.props.robots).length > 0) {
      } else {
        // this.props.dispatch(initRobots(resRobots))
        this.props.dispatch(initRobots(resRobots, localRobotId))
      }
    })
  }

  getQueryVal(k, search, uri) {
    if (search == undefined) {
      search = location.search
    }
    var reg = new RegExp('(^|&)' + k + '=([^&]*)(&|$)')
    var s = (uri && '?' + uri.split('?')[1]) || search
    var r = s.slice(1).match(reg)
    if (r != null) {
      return decodeURIComponent(r[2])
    }
    return null
  }

  // 新增：检查主语言是否有已发布的配置
  checkMainLangConfigStatus = async () => { 
    
    const { langData } = this.props;
    const mainLang = langData && langData.length > 0 ? langData[0] : null;
    const robotId = this.state.currentRobotId;

    // 重置状态
    this.setState({ mainLangSourceConfigInfo: {} });

    if (!mainLang || !robotId) {
      return; // 缺少必要信息，无法检查
    }

    let module_code_list = ['module_skill_home'];
    let listFormData = new FormData();
    listFormData.append('module_code_list', JSON.stringify(module_code_list));
    listFormData.append('robot_uuid', robotId);
    listFormData.append('config_status', 'publish'); // 只关心已发布的
    listFormData.append('lang', mainLang.lang_code); // 使用主语言代码

    try {
      const listRes = await getModuleConfigList(listFormData); 

      if (!listRes.module_config_list || listRes.module_config_list.length === 0) {
        console.log(`主语言 (${mainLang.lang_code}) 没有已发布的配置`);
        return;
      }

      // 遍历列表，检查每个配置的详情
      const foundSources = {};
      for (const configInfo of listRes.module_config_list) { 
        let detailFormData = new FormData();
        detailFormData.append('module_code', 'module_skill_home');
        detailFormData.append('config_id', configInfo.config_id);
        detailFormData.append('version', configInfo.version);
        detailFormData.set('lang', mainLang.lang_code);

        try {
          const detailRes = await getModuleConfig(detailFormData); 
          let config_json = detailRes.module_config.config_json;
          if (typeof config_json === 'string') {
            try {
              config_json = JSON.parse(config_json);
            } catch (e) {
              console.error(`解析配置JSON失败: id=${configInfo.config_id}`, e);
              continue; // 解析失败，跳过这个配置，继续检查下一个
            }
          }

          // 关键判断：找到模板
          const templateCode = config_json?.templateInfo?.templateCode;
          if (templateCode) {
            foundSources[templateCode] = {
              config_id: configInfo.config_id,
              version: configInfo.version
            };
          }
        } catch (detailErr) {
          // 获取单个详情失败，记录错误并继续检查下一个
          console.error(`获取配置详情失败: id=${configInfo.config_id}`, detailErr);
          continue; // 继续检查列表中的下一个配置
        }
      }

      // 更新状态
      this.setState({ mainLangSourceConfigInfo: foundSources });

    } catch (listErr) {
      console.error(`获取主语言 (${mainLang.lang_code}) 已发布配置列表失败:`, listErr);
      // 确保出错时 state 也是空对象
      this.setState({ mainLangSourceConfigInfo: {} });
    }
  }

  // 新增：处理自动生成逻辑
  handleAutoGenerate = async () => {
    const { langData, langDataActive, homeModule } = this.props;
    const { mainLangSourceConfigInfo } = this.state;
    const mainLang = langData && langData.length > 0 ? langData[0] : null;
    const targetLang = langDataActive?.lang_code;
    const currentTemplateCode = homeModule?.templateInfo?.templateCode;

    // 1. 前置条件检查
    if (!mainLang || !targetLang || !mainLangSourceConfigInfo || !currentTemplateCode) {
      message.error("无法获取语言信息或主语言源配置");
      return;
    }
    if (mainLang.lang_code === targetLang) {
        message.warn("当前已是主语言，无需生成");
        return;
    }

    this.setState({ isGenerating: true });
    console.log(`开始自动生成 ${targetLang} 配置，基于 ${mainLang.lang_code} 的配置:`, mainLangSourceConfigInfo);

    try {
      // 2. 获取主语言配置详情
      const sourceConfig = mainLangSourceConfigInfo[currentTemplateCode];
      if (!sourceConfig) {
        message.error(`主语言 (${mainLang.lang_code}) 没有可用的 ${currentTemplateCode} 模板配置`);
        this.setState({ isGenerating: false });
        return;
      }

      let sourceConfigFormData = new FormData();
      sourceConfigFormData.append('module_code', 'module_skill_home');
      sourceConfigFormData.append('config_id', sourceConfig.config_id);
      sourceConfigFormData.append('version', sourceConfig.version);
      sourceConfigFormData.set('lang', mainLang.lang_code);

      const sourceRes = await getModuleConfig(sourceConfigFormData);
      let sourceConfigJson = sourceRes?.module_config?.config_json;
      if (typeof sourceConfigJson === 'string') {
        try { sourceConfigJson = JSON.parse(sourceConfigJson); } catch (e) { throw new Error("解析主语言配置失败"); }
      }
      if (!sourceConfigJson?.templateInfo) { throw new Error("获取的主语言配置无效"); }
      console.log('获取的主语言配置==', sourceConfigJson)
      // 3. 提取待翻译文本
      const textsToTranslate = {};
      let counter = 0; // 用于生成唯一key
      // --- 【标题配置】提取标题 ---
      if (sourceConfigJson.templateInfo.titleBarType === 'text' && sourceConfigJson.templateInfo.titleBarContent) {
        textsToTranslate[`titleBarContent_${counter++}`] = sourceConfigJson.templateInfo.titleBarContent;
      }
      // --- 提取agentOS模式内容(agentOS Mode) ---
      if (sourceConfigJson.templateInfo.templateCode === 'agentOS' && sourceConfigJson.templateInfo.agentosContent) {
        if(sourceConfigJson.templateInfo.agentosContent.title) textsToTranslate[`agentosContent_title_${counter++}`] = sourceConfigJson.templateInfo.agentosContent.title;
        if(sourceConfigJson.templateInfo.agentosContent.suggestionExt && sourceConfigJson.templateInfo.agentosContent.suggestionExt.tip) textsToTranslate[`agentosContent_suggestionExtTip_${counter++}`] = sourceConfigJson.templateInfo.agentosContent.suggestionExt.tip;
      }
      // --- 提取简洁模式内容(Simple Mode) ---
      if (sourceConfigJson.templateInfo.templateCode === 'Simple' && sourceConfigJson.templateInfo.conciseContent) {
        if(sourceConfigJson.templateInfo.conciseContent.title) textsToTranslate[`conciseContent_title_${counter++}`] = sourceConfigJson.templateInfo.conciseContent.title;
        if(sourceConfigJson.templateInfo.conciseContent.suggestionExt && sourceConfigJson.templateInfo.conciseContent.suggestionExt.tip) textsToTranslate[`conciseContent_suggestionExtTip_${counter++}`] = sourceConfigJson.templateInfo.conciseContent.suggestionExt.tip;
      }
      // --- 提取气泡/卡片内容 (如果存在) ---
      if (sourceConfigJson.templateInfo.bubbleContents) {
          if(sourceConfigJson.templateInfo.bubbleContents.title) textsToTranslate[`bubbleContents_title_${counter++}`] = sourceConfigJson.templateInfo.bubbleContents.title;
          if(Array.isArray(sourceConfigJson.templateInfo.bubbleContents.robot)) {
              sourceConfigJson.templateInfo.bubbleContents.robot.forEach((text, idx) => {
                  if(text) textsToTranslate[`bubbleContents_robot_${idx}_${counter++}`] = text;
              });
          }
      }
      // ---【卡片配置】提取卡片标题和提示 ---
      if (Array.isArray(sourceConfigJson.skills)) {
        sourceConfigJson.skills.forEach((skill, index) => {
          if (skill.title) textsToTranslate[`skill_${index}_title_${counter++}`] = skill.title;
          if (skill.tip) textsToTranslate[`skill_${index}_tip_${counter++}`] = skill.tip;
        });
      }
      // --- 【底部引导】提取问题  ---
      if (Array.isArray(sourceConfigJson.queries)) {
        sourceConfigJson.queries.forEach((queryGroup) => {
          // 通常关注 standardMain 和 mainPage
          if ((queryGroup.name === 'standardMain' || queryGroup.name === 'mainPage') && Array.isArray(queryGroup.query)) {
            queryGroup.query.forEach((queryText, index) => {
              if (queryText) textsToTranslate[`query_${queryGroup.name}_${index}_${counter++}`] = queryText;
            });
          }
        });
      }
       // --- 提取底部引导问题扩展 Question Extensions ---
      if (Array.isArray(sourceConfigJson.questionExt)) {
           sourceConfigJson.questionExt.forEach((ext, index) => {
                if (ext.title) textsToTranslate[`questionExt_${index}_title_${counter++}`] = ext.title;
                if (ext.tip) textsToTranslate[`questionExt_${index}_extTip_${counter++}`] = ext.tip;
           });
      }

      if (Object.keys(textsToTranslate).length === 0) {
          message.info("主语言配置中无可翻译文本");
          this.setState({ isGenerating: false });
          return;
      }
      console.log("待翻译文本:", textsToTranslate);

      // 4. 调用翻译 API
      const translateApiData = {
          from_data: JSON.stringify(textsToTranslate),
          target_lang: targetLang,
          lang: targetLang
      };
      const translateRes = await translateJson(translateApiData);
      if (!translateRes?.translate_data) { throw new Error("翻译接口返回数据无效"); }

      const translatedTexts = translateRes.translate_data;
      console.log("翻译结果:", translatedTexts);

      // 5. 合并翻译结果
      let translatedConfigJson = _.cloneDeep(sourceConfigJson);
      translatedConfigJson.lang = targetLang; // 更新语言标记

      Object.keys(textsToTranslate).forEach(originalKey => {
        if (translatedTexts[originalKey] !== undefined) { // / 确保翻译结果存在
          const parts = originalKey.split('_');
          const type = parts[0];
          try {
            if (type === 'titleBarContent') { translatedConfigJson.templateInfo.titleBarContent = translatedTexts[originalKey]; }
            else if (type === 'conciseContent') { 
              if (parts[1] === 'suggestionExtTip') {
                translatedConfigJson.templateInfo.conciseContent.suggestionExt.tip = translatedTexts[originalKey];
              } else {
                translatedConfigJson.templateInfo.conciseContent[parts[1]] = translatedTexts[originalKey]; 
              }
            }
            else if (type === 'agentosContent') { 
              if (parts[1] === 'suggestionExtTip') {
                translatedConfigJson.templateInfo.agentosContent.suggestionExt.tip = translatedTexts[originalKey];
              } else {
                translatedConfigJson.templateInfo.agentosContent[parts[1]] = translatedTexts[originalKey]; 
              }
            }
            else if (type === 'bubbleContents') {
                if (parts[1] === 'title') { translatedConfigJson.templateInfo.bubbleContents.title = translatedTexts[originalKey]; }
                else if (parts[1] === 'robot') {
                    const idx = parseInt(parts[2], 10);
                    if (!isNaN(idx) && translatedConfigJson.templateInfo.bubbleContents.robot?.[idx] !== undefined) { translatedConfigJson.templateInfo.bubbleContents.robot[idx] = translatedTexts[originalKey]; }
                }
            }
            else if (type === 'skill') {
                const index = parseInt(parts[1], 10); const field = parts[2];
                if (!isNaN(index) && translatedConfigJson.skills?.[index]) { translatedConfigJson.skills[index][field] = translatedTexts[originalKey]; }
            }
            else if (type === 'query') {
                const groupName = parts[1]; const index = parseInt(parts[2], 10);
                const queryGroup = translatedConfigJson.queries?.find(qg => qg.name === groupName);
                if (queryGroup && !isNaN(index) && queryGroup.query?.[index] !== undefined) { queryGroup.query[index] = translatedTexts[originalKey]; }
            }
            else if (type === 'questionExt') {
                const index = parseInt(parts[1], 10); const field = parts[2];
                if (!isNaN(index) && translatedConfigJson.questionExt?.[index]) { 
                  if (field === 'extTip') {
                    translatedConfigJson.questionExt[index].tip = translatedTexts[originalKey];
                  } else {
                    translatedConfigJson.questionExt[index][field] = translatedTexts[originalKey];
                  }
                }
            }
          } catch (mergeError) { console.warn(`合并 ${originalKey} 出错:`, mergeError); }
        } else { console.warn(`Key ${originalKey} 未翻译`); }
      });

      // 确保 questionExt 在更新 Redux state 前是数组
      if (!Array.isArray(translatedConfigJson.questionExt)) {
        translatedConfigJson.questionExt = [];
      }

      // 6. 更新 Redux State
      console.log("最终生成的翻译配置:", translatedConfigJson);
      // 使用 GET_HOME_MODULE 更新当前编辑的配置
      this.props.dispatch({ type: GET_HOME_MODULE, data: { homeModule: translatedConfigJson } });
      // 同时更新原始配置，以便"未修改"状态判断从翻译后的版本开始
      this.props.dispatch({ type: ORIGIN_HOME_MODULE, data: { originHomeModule: _.cloneDeep(translatedConfigJson) } });

      message.success(__('customHome.modalMsg.autoGenerateSuccess'));
    } catch (error) {
      console.error("自动生成配置时出错:", error);
      message.error(__('customHome.modalMsg.autoGenerateFailed'));
    } finally {
      this.setState({ isGenerating: false });
    }
  };

  // 新增：翻译通用文本方法
  translateCommonTexts = async () => {
    const { langDataActive } = this.props;
    
    if (!langDataActive || !langDataActive.lang_code) {
      console.warn('语言信息不完整，跳过翻译');
      return;
    }

    // 如果是中文，直接使用默认值
    if (langDataActive.lang_code === 'zh_CN') {
      this.props.dispatch(setCommonTranslations({
        askMe: '你还可以问我'
      }));
      // 发送翻译数据到iframe
      this.sendTranslationsToIframe({ askMe: '你还可以问我' });
      return;
    }

    try {
      // 调用翻译接口
      const translateApiData = {
        from_data: JSON.stringify({ askMe: '你还可以问我' }),
        target_lang: langDataActive.lang_code,
        lang: 'zh_CN'
      };
      
      const translateRes = await translateJson(translateApiData);
      
      if (translateRes && translateRes.translate_data && translateRes.translate_data.askMe) {
        // 更新Redux状态
        this.props.dispatch(setCommonTranslations({
          askMe: translateRes.translate_data.askMe
        }));
        console.log('通用文本翻译完成:', translateRes.translate_data);
        
        // 发送翻译数据到iframe
        this.sendTranslationsToIframe(translateRes.translate_data);
      } else {
        console.warn('翻译结果格式异常，使用默认值');
        this.props.dispatch(setCommonTranslations({
          askMe: '你还可以问我'
        }));
        // 发送默认数据到iframe
        this.sendTranslationsToIframe({ askMe: '你还可以问我' });
      }
    } catch (error) {
      console.error('翻译通用文本失败:', error);
      // 翻译失败时使用默认值
      this.props.dispatch(setCommonTranslations({
        askMe: '你还可以问我'
      }));
      // 发送默认数据到iframe
      this.sendTranslationsToIframe({ askMe: '你还可以问我' });
    }
  };

  // 新增：向iframe发送翻译数据的方法
  sendTranslationsToIframe = (translations) => {
    try {
      // 查找iframe元素
      const iframe = document.querySelector('iframe[src*="miniHome"]') || 
                    document.querySelector('.mini_preview iframe') ||
                    document.querySelector('iframe');
      
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage({
          command: 'commonTranslationsUpdated',
          data: translations
        }, '*');
        console.log('已发送翻译数据到iframe:', translations);
      } else {
        console.warn('未找到iframe，无法发送翻译数据');
      }
    } catch (error) {
      console.error('发送翻译数据到iframe失败:', error);
    }
  };

  render() {
    const robotName = this.state.robotName != undefined && this.state.robotName
    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo && this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'Simple') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'agentOS') {
        backgroundStr = 'module_public/module_skill_home/bao_mini/miniaiosbg.png' 
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }
      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.state.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };
      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.state.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.state.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.state.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.state.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
    }

    let robotType_1 = this.getQueryVal('robotType')
    return (
      <div className={this.props.className}>
        {this.state.showLoading && (
          <div className='loading'>
            <div className='loading_modal'>
              <img src={require('@assets/svg-icon/loading.gif')} />
              <span>{__('customHome.modalMsg.waitMsg')}</span>
            </div>
          </div>
        )}
        {Object.keys(this.props.robots).length === 0 && this.props.fetchStatus && !this.state.showLoading && (
          <div className='noRobot box'>
            <dl>
              <dd>当前企业暂无机器，请点击进行绑定</dd>
              <dt>
                <img
                  src={require('@assets/png/role/noRobot.png')}
                  alt={__('BASIC_STATUS.NO_ROBOTS')}
                  className='noRobot'
                />
              </dt>
            </dl>

            <Button
              label={__('BASIC_STATUS.ADD_ROBOTS')}
              className='addRobotBtn'
              onClick={() => {
                window.open(window.location.origin + `/web/portal#/frame/hmag-person/hmag-person.person_corp`)
              }}
            />
          </div>
        )}

        {((Object.keys(this.props.robots).length === 0 && !this.props.fetchStatus) ||
          (Object.keys(this.props.robots).length > 0 && this.props.fetchStatus)) && (
          <>
            <div className='combox' style={{ display: 'flex', justifyContent: 'flex-end' }}>
              {this.state.utime && (
                <span
                  // className="utime"
                  style={{ display: 'inline', order: '1', marginRight: '400px' }}
                >
                  {__('customHome.modalMsg.lastRelease')}：{this.state.utime.split(' ')[0].split('-').join('/')}
                </span>
              )}
              <div className='btnBox'>
                <p
                  disabled={this.isKTV && this.state.lowOpkVersion}
                  className='tips_button'
                  onClick={() => {
                    this.updatejson()

                    // this.fetch/OnlineStatus()
                    document.dispatchEvent(FETCH)

                    this.getRobot(() => {
                      if (!this.checkJson()) {
                        return
                      }
                      if (this.state.allNum != 0) {
                        closeModal()
                        message.success(__('customHome.modalMsg.syncing'))
                        return
                      }
                      this.setState({
                        activeKey: '1',
                        backImgstyle: '',
                      })
                      modal({
                        title: __('customHome.modalMsg.chooseRobotToSync'),
                        // content1: <Robot chooseRobots={this.props.chooseRobots.bind(this)} />,
                        content: (
                          <PublishRobot
                            // chooseRobots={this.props.chooseRobots.bind(this)}
                            chooseRobots={(arr) => {
                              this.props.chooseRobots(arr)
                            }}
                            checkedArray={this.state.checkedArray}
                            unCheckArray={this.state.unCheckArray}
                            ableFun={(a, b) => {
                              // console.log(a,'disabled');
                              let opkversion = a.robot_baseopk_version
                              if (this.props.homeModule.templateInfo.templateCode == 'Simple') {
                                // 简洁式需要额外判断opk版本
                                let baseVersion = '02.0001.200821'
                                // baseVersion = "0";
                                let overVersion = checkReleaseRaw(opkversion, baseVersion, true)
                                if (overVersion) {
                                  return true
                                } else {
                                  return false
                                }
                              } else if (this.props.homeModule.templateInfo.templateCode == 'Buttons') {
                                // 简洁式需要额外判断opk版本
                                let buttonBaseVersion = '02.0010.20122920'
                                // baseVersion = "0";
                                let buttonOverVersion = checkReleaseRaw(opkversion, buttonBaseVersion, true)
                                if (buttonOverVersion) {
                                  return true
                                } else {
                                  return false
                                }
                              } else {
                                return true
                              }
                              return true
                            }}
                            robotClick={(robot, index) => {
                              let opkversion = robot.robot_baseopk_version
                              let needUpdate = false
                              if (this.props.homeModule.templateInfo.templateCode == 'Simple') {
                                // 简洁式需要额外判断opk版本
                                let baseVersion = '02.0001.200821'
                                // baseVersion = "0";
                                let overVersion = checkReleaseRaw(opkversion, baseVersion, true)
                                if (overVersion) {
                                  needUpdate = false
                                } else {
                                  needUpdate = true
                                }
                              } else if (this.props.homeModule.templateInfo.templateCode == 'Buttons') {
                                // 简洁式需要额外判断opk版本
                                let buttonBaseVersion = '02.0010.20122419'
                                // baseVersion = "0";
                                let buttonOverVersion = checkReleaseRaw(opkversion, buttonBaseVersion, true)
                                if (buttonOverVersion) {
                                  needUpdate = false
                                } else {
                                  needUpdate = true
                                  console.log(needUpdate)
                                }
                              } else {
                                needUpdate = false
                              }
                              if (needUpdate) {
                                // console.log('needUpdate')
                                message.warn(__('customHome.modalMsg.robotlowPlsUpdate')) //robotlowPlsUpdate
                              }
                            }}
                            disableTip={'(' + __('customHome.modalMsg.versionNotIn') + ')'}
                            getRobotId={this.getRobotId.bind(this)}
                          />
                        ),
                        cancel: __('customHome.btn.cancel2'),
                        confirm: __('customHome.btn.confirm'),
                        onConfirm: () => {
                          if (!this.uploadingData) {
                            this.uploadingData = 1
                            this.uploadData()
                            this.addAsrData()
                            setTimeout(() => {
                              this.uploadingData = 0
                            }, 3 * 1e3)
                          } else {
                            console.log('uploading home data')
                            return
                          }
                          document.dispatchEvent(DISFETCH)
                        },
                        // onConfirm:()=>{
                        //     // this.disFetchOnlineStatus();
                        //     document.dispatchEvent(DISFETCH)

                        // }
                      })
                    })
                  }}
                >
                  {__('customHome.btn.publishedAndSave') /*保存并发布*/}
                </p>
                <Button
                  icon='change'
                  appearance='hollow'
                  label={robotName}
                  className='robot-list'
                  onClick={async () => {
                    document.dispatchEvent(FETCH)
                    let res = await this.checkHomeModule()
                    if (!res) {
                      return false
                    }
                    sessionStorage.setItem('config_loaded', 0)
                    sessionStorage.setItem('config_title_loaded', 0)
                    console.log(123123123123123123123123 + 'zzzzzz')
                    this.setState({
                      activeKey: '1',
                    })
                    this.changeTab_backImg(0)
                    modal({
                      title: __('customHome.modalMsg.title4'),
                      fullscreen: false,
                      content: (
                        <ChooseRobot
                          fetchStatus={this.props.fetchStatus}
                          robots={this.props.robots}
                          user={this.props.user}
                          changeRobotData={this.changeRobotData.bind(this)}
                          // robotType={'part'}
                          robotType='all'
                          showVirtualRobot
                        />
                      ),
                      cancel: __('customHome.btn.cancel2'),
                      confirm: __('customHome.btn.confirm'),
                      onConfirm: () => {
                        const sn = this.state.currentRobotSn,
                          id = this.state.currentRobotId
                        if (id != null && id != undefined) {
                          this.setState({
                            checkedArray: [],
                          })
                          this.changeRobot(id)
                        }
                        document.dispatchEvent(DISFETCH)
                      },
                      onCancel: () => {
                        document.dispatchEvent(DISFETCH)
                      },
                    })
                  }}
                />
              </div>
            </div>
            <div className='content'>
              <div className={isMini(robotType_1) ? 'preview mini_preview' : 'preview'}>
                <Preview
                  key={this.state.modalStyle}
                  {...this.state}
                  checkJson={this.checkJson.bind(this)}
                  choosemodalStyle={this.choosemodalStyle.bind(this)}
                  changeModal={this.changeModal.bind(this)}
                  changeCurrentIndex={this.changeCurrentIndex.bind(this)}
                  changeContent={this.changeContent.bind(this)}
                  changeState={(state) => {
                    this.setState(state)
                  }}
                />
              </div>
              <div className='setting'>
                {/* <Sort></Sort> */}

                <Setting
                  {...this.state}
                  mainLangSourceConfigInfo={this.state.mainLangSourceConfigInfo}
                  isGenerating={this.state.isGenerating}
                  handleAutoGenerate={this.handleAutoGenerate}
                  checkJson={this.checkJson.bind(this)}
                  choosemodalStyle={this.choosemodalStyle.bind(this)}
                  bottomQueries={this.bottomQueries.bind(this)}
                  changeState={(state) => {
                    this.setState(state)
                  }}
                  updateBubble1={(Bubble1) => {
                    this.setState({ Bubble1 })
                    console.log('Bubble1', Bubble1)
                  }}
                  types={this.state.chooseQueTypes}
                  changeTab_backImg={this.changeTab_backImg.bind(this)}
                  deleteBackImg={this.deleteBackImg.bind(this)}
                  deleteFaceImg={this.deleteFaceImg.bind(this)}
                  addCover={this.addCover.bind(this)}
                  config_id={this.state.config_id}
                  setIndexState={this.setIndexState.bind(this)}
                  updatejson={this.updatejson.bind(this)}
                  onRef={(e) => (this.SubSetting = e)}
                ></Setting>
              </div>
            </div>
          </>
        )}

        {/* <canvas style={{'display':'none'}} id='mybackCanvas' width='980' height='846' /> */}
      </div>
    )
  }
  // 场景模板修改
  changeModal() {
    if (this.checkJson()) {
      this.setState({
        currentButton: 1,
        currentIndex: -2,
        currentSort: false,
      })
      modal({
        title: false,
        confirm: false,
        cancel: false,
        fullscreen: false,
        content: (
          <ChooseModal
            curScenes={this.state.curScenes}
            lang={this.state.lang}
            modalStyle={this.state.modalStyle}
            choosemodalStyle={this.choosemodalStyle.bind(this)}
          />
        ),
      })
    }
  }
  chooseRobots(arr) {
    console.log(arr)
    this.setState({
      chooseList: arr,
    })
  }
  changeCurrentIndex(num) {
    this.setState({
      currentIndex: num,
      currentButton: 0,
      currentSort: false,
    })
  }
  changeInputTips(flag) {
    this.setState({
      showInputTips: flag,
    })
  }
  changeContent(txt, sty, queriesIndex) {
    console.log(txt, sty, queriesIndex, '~~~~~~')
    if (queriesIndex != -1) {
      this.setState({
        changeContent: txt,
        chooseQueTypes: sty,
        queriesIndex: queriesIndex,
      })
    } else {
      if (sty) {
        this.setState({
          changeContent: txt,
          chooseQueTypes: sty,
        })
      } else {
        this.setState({
          changeContent: txt,
        })
      }
    }
    this.setState({
      showInputTips: false,
    })
  }
  getConfigIdByScenes(curScenes, txt) {
    let robotType_1 = this.getQueryVal('robotType')
    let config_id = 'default_id'
    switch (curScenes) {
      // case "默认":
      case __('customHome.sceneType.default'):
        config_id = 'default_id'
        break
      // case "商场":
      case __('customHome.sceneType.market'):
        config_id = 'market_default_id'
        break
      // case "酒店":
      case __('customHome.sceneType.hotel'):
        config_id = 'hotel_default_id'
        break
      // case "银行":
      case __('customHome.sceneType.bank'):
        config_id = 'bank_default_id'
        break
      case __('customHome.sceneType.museum'):
        // case "博物馆":
        config_id = 'museum_default_id'
        // config_id = "museum_museum_id"
        break
      case __('customHome.sceneType.library'):
        // case "图书馆":
        config_id = 'library_default_id'
        break
      case 'KTV':
        // case "":
        config_id = 'ktv_default_id'
        break
    }
    // 调整：Simple 和 agentOS 都走 simple 相关的 config_id 逻辑
    if (txt == 'Simple' || txt == 'agentOS') {
      config_id = config_id.replace('default', 'simple')
      if (config_id == 'simple_id') {
        if (curScenes != 'KTV') {
          config_id = 'default_simple_id'
        } else {
          config_id = 'ktv_simple_id'
        }
      }
    }

    if (txt == 'Buttons') {
      config_id = config_id.replace('default', 'buttons')
      if (config_id == 'buttons_id') {
        if (curScenes != 'KTV') {
          config_id = 'default_buttons_id'
        } else {
          config_id = 'ktv_buttons_id'
        }
      }
    }
    if (isMini(robotType_1)) {
      config_id = 'mini_' + config_id
    }
    return config_id
  }
  // 模板选择确定
  choosemodalStyle(txt, curScenes, lang) {
    console.log('模板选择choosemodalStyle:', txt, curScenes, lang)
    this.storeHomeSetting(this.state.modalStyle, this.state.curScenes, this.props.homeModule)
    let oldLang = this.state.lang
    this.setState(
      {
        lang,
        backImg: '',
      },
      () => {
        if (lang != oldLang) {
          this.setState(
            {
              faceBackground: '', // 清楚上一个的背景
            },
            () => {
              this.getConfigList(() => {
                this.chooseRobots_success(txt, curScenes, null, 1)
              })
            },
          )
          return
        }
        let config_id = 'default_id'
        // 切换都要场景就要重新获取
        if (curScenes != this.state.curScenes) {
          this.simple_config_json = {}
          this.common_config_json = {}
        }
        config_id = this.getConfigIdByScenes(curScenes, txt)
        let config = this.getHomeSetting(txt, curScenes)
        if (curScenes != this.state.curScenes || Object.keys(config).length == 0) {
          //切换场景 或者本地没有配置的缓存

          if (curScenes != this.state.curScenes) {
            this.setState({
              SimplebackImg: '',
              // backImg: ''
            })
          }
          this.gethomeModule(
            config_id,
            '',
            () => {
              this.chooseRobots_success(txt, curScenes, null, 2)
            },
            lang,
          )
        } else {
          this.props.dispatch({
            type: 'GET_HOME_MODULE',
            data: {
              homeModule: config,
            },
          })
          this.chooseRobots_success(txt, curScenes, config, 3)
        }
      },
    )
  }
  // 模板选择回调函数
  chooseRobots_success(txt, curScenes, config_json, index) {
    console.log('chooseRobots_success ====', txt, curScenes, config_json, index)
    let homeModule = config_json || this.props.homeModule
    let backUrl = 'module_public/module_skill_home/home1.png'
    let config_id = this.getConfigIdByScenes(curScenes, txt)
    let config = this.getHomeSetting(txt, curScenes)
    this.setState({
      isShowSetting: false,
      modalStyle: txt,
      curScenes: curScenes,
      changeContent: 'tips',
      chooseQueTypes: '1',
      currentButton: 0,
      currentSort: false,
      currentIndex: -2,
    })

    homeModule.templateInfo.templateCode = txt
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'templateCode',
        content: homeModule.templateInfo.templateCode,
      },
    })

    if (curScenes == 'KTV') {
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'scenes',
          content: 'KTV',
        },
      })
    }
    
    if (txt == 'List') {
      backUrl = 'module_public/module_skill_home/home1.png'
      for (var j = 0; j <= homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j <= 1) {
              // this.props.homeModule.skills[j].icon = this.state.iconJson[i].list_icon2
              if (this.state.lang === 'zh_CN') {
                if (homeModule.skills[j].label == 'chat2') {
                  homeModule.skills[j].icon = this.state.iconJson[i + 1].list_icon2
                } else {
                  homeModule.skills[j].icon = this.state.iconJson[i].list_icon2
                }
              }
            } else {
              if (homeModule.skills[j].label == 'chat2') {
                setSkillDefaultIcon(homeModule.skills, j, this.state.iconJson[i + 1].list_icon1)
              } else {
                setSkillDefaultIcon(homeModule.skills, j, this.state.iconJson[i].list_icon1)
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                key: 21,
                index: j,
                content: homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Card') {
      backUrl = 'module_public/module_skill_home/home2.png'
      //切换图标
      for (var j = 0; j <= homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (homeModule.skills[j].label == 'chat2') {
              homeModule.skills[j].icon = this.state.iconJson[i + 1].card_icon
            } else {
              homeModule.skills[j].icon = this.state.iconJson[i].card_icon
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 22,
                content: homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Technical') {
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'titleBarType',
          content: 'text',
        },
      })
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'titleBarContent',
          content: __('customHome.warm.callMe'),
        },
      })
      backUrl = 'module_public/module_skill_home/home3.png'
      //切换图标
      // for (var i = 0; i <= this.state.iconJson.length - 2; i++) {
      // 	this.props.homeModule.skills[i].icon = "";
      // 	this.props.dispatch({
      // 		type: 'CHANGE_MODULE_SKILL',
      // 		data: {
      // 			type: 'icon',
      // 			index: i,
      // 			content: this.props.homeModule.skills[i]
      // 		}
      // 	})
      // }
    } else if (txt == 'Standard') {
      if (homeModule.templateInfo.titleBarType != 'text') {
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarType',
            content: 'text',
          },
        })
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarContent',
            content: __('customHome.warm.callMe'),
          },
        })
      }

      backUrl = 'module_public/module_skill_home/home4.png'
      //切换图标
      for (var j = 0; j <= homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j == 0) {
              //this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon;
              if (homeModule.skills[j].label == 'chat2') {
                homeModule.skills[j].icon = this.state.iconJson[i + 1].standard2_icon
              } else {
                homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon
              }
            } else {
              if (homeModule.skills[j].label == 'chat2') {
                homeModule.skills[j].icon = this.state.iconJson[i + 1].standard1_icon
              } else {
                homeModule.skills[j].icon = this.state.iconJson[i].standard1_icon
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 23,
                content: homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Simple') {
      backUrl = 'module_public/module_skill_home/simpleBg.png'
      if (curScenes == 'KTV') {
        backUrl = 'module_public/module_skill_home/simplektvBg.png'
      }
      if (homeModule.templateInfo.titleBarType != 'text') {
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarType',
            content: 'text',
          },
        })
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarContent',
            content: __('customHome.warm.callMe'),
          },
        })
      }

      if (homeModule.templateInfo.conciseContent == undefined)
        [
          this.props.dispatch({
            type: 'CHANGE_INFO',
            data: {
              type: 'conciseContent',
              content: {
                title: '猎户风景区欢迎你',
                image: 'module_public/module_skill_home/conciseimage.png',
                suggestion: '我要买门票',
              },
            },
          }),
        ]
      //切换图标
      for (var j = 0; j <= homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j == 0) {
              //this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon;
              if (homeModule.skills[j].label == 'chat2') {
                homeModule.skills[j].icon = this.state.iconJson[i + 1].standard2_icon
              } else {
                homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon
              }
            } else {
              if (homeModule.skills[j].label == 'chat2') {
                homeModule.skills[j].icon = this.state.iconJson[i + 1].standard1_icon
              } else {
                homeModule.skills[j].icon = this.state.iconJson[i].standard1_icon
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 24,
                content: homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'agentOS') { // 添加 agentOS 的处理逻辑，与 Simple 类似
      backUrl = 'module_public/module_skill_home/bao_mini/miniaiosbg.png' // agentOS 默认背景图，如果 agentosContent.background 为空，可以使用这个
      if (homeModule.templateInfo.titleBarType != 'text') {
        this.props.dispatch({ type: 'CHANGE_INFO', data: { type: 'titleBarType', content: 'text' }})
        this.props.dispatch({ type: 'CHANGE_INFO', data: { type: 'titleBarContent', content: __('customHome.warm.callMe') }})
      }

      // 检查并初始化 agentosContent
      if (homeModule.templateInfo.agentosContent == undefined) {
          this.props.dispatch({
            type: 'CHANGE_INFO',
            data: {
              type: 'agentosContent', 
              content: { 
                title: "AgentOS版本", // 使用 agentosContent 的默认 title
                suggestion: "我想了解下你的功能", // 使用 agentosContent 的默认 suggestion
                image: "module_public/module_skill_home/bao_mini/miniaiosbg.png", // 初始 image，可以根据需要调整或从 agentosContent.image 读取
                background: "module_public/module_skill_home/bao_mini/miniaiosbg.png", // agentOS 特定背景
                suggestionExt: { // 默认 suggestionExt
                  title: "你的亮点功能有哪些",
                  tip: "你的亮点功能有哪些",
                  icon: "",
                  display: true,
                  function: "chat",
                  label: "chat1"
                }
              },
            },
          })
       }

      // 使用与Simple相同的图标处理逻辑 (这部分是针对 skills 的，保持不变)
      // for (var j = 0; j <= homeModule.skills.length - 1; j++) {
      //   for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
      //     if (homeModule.skills[j].function == this.state.iconJson[i].key) {
      //       if (j == 0) {
      //         if (homeModule.skills[j].label == 'chat2') {
      //           homeModule.skills[j].icon = this.state.iconJson[i + 1].standard2_icon
      //         } else {
      //           homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon
      //         }
      //       } else {
      //         if (homeModule.skills[j].label == 'chat2') {
      //           homeModule.skills[j].icon = this.state.iconJson[i + 1].standard1_icon
      //         } else {
      //           homeModule.skills[j].icon = this.state.iconJson[i].standard1_icon
      //         }
      //       }
      //       this.props.dispatch({
      //         type: 'CHANGE_MODULE_SKILL',
      //         data: {
      //           type: 'icon',
      //           index: j,
      //           key: 24,
      //           content: homeModule.skills[j],
      //         },
      //       })
      //       break;
      //     }
      //   }
      // }
    } else if (txt == 'Buttons') {
      backUrl = 'module_public/module_skill_home/home1.png'
      //切换图标
      for (var j = 0; j <= homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (homeModule.skills[j].label == 'chat2') {
              homeModule.skills[j].icon = this.state.iconJson[i + 1].standard1_icon

              homeModule.skills[j].icon = '/orics/down/b016_20201224_ac9a76dd174009331dabbf1470082547'
            } else {
              homeModule.skills[j].icon = this.state.iconJson[i].standard1_icon
              homeModule.skills[j].icon = '/orics/down/b016_20201223_819ebf6c3db83c656facae7bc3711a5d'
            }

            homeModule.skills[j].icon = this.state.iconJson[i].buttons_icon

            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 25,
                content: homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    }

    //切换背景图
    if (this.state.backImg != '') {
      backUrl = this.state.backImg
    }
    
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'background',
        content: backUrl,
      },
    })

    closeModal()
    if (
      this.isKTV &&
      homeModule.templateInfo.templateCode != 'Simple' &&
      !this.state.lowOpkVersion &&
      !this.state.isDefault
    ) {
      this.setState({
        templateNeedUpdate: true,
      })
      modal({
        title: __('customHome.modalMsg.tip'),
        fullscreen: false,
        content: (
          <div>
            <p>{__('customHome.modalMsg.pageNewChange')}</p>
            <p>{__('customHome.modalMsg.pageSetChange')}</p>
          </div>
        ),
        cancel: false,
        confirm: __('customHome.modalMsg.cureChange'),
        onConfirm: () => {
          this.setState(
            {
              templateNeedUpdate: false,
            },
            () => {
              this.choosemodalStyle('Simple', 'KTV', 'chs')
            },
          )
        },
      })
      let lowOpkVersion = this.state.lowOpkVersion
      let isDefault = this.state.isDefault
      window.tipSetData &&
        window.tipSetData({
          templateNeedUpdate: true,
          lowOpkVersion,
          isDefault,
        })
    } else {
      let lowOpkVersion = this.state.lowOpkVersion
      let isDefault = this.state.isDefault
      window.tipSetData &&
        window.tipSetData({
          templateNeedUpdate: false,
          lowOpkVersion,
          isDefault,
        })
    }
  }
  setIndexState(obj) {
    this.setState(obj)
  }
  deleteBackImg() {
    this.setState({
      backImg: '',
    })
    if (this.props.homeModule.templateInfo.templateCode == 'Simple') {
      this.setState({
        SimplebackImg: '',
      })
    }
  }
  deleteFaceImg() {
    this.setState({
      faceBackground: '',
    })
  }
  addAsrData() {
    let questions = [],
      questionList = []
    let json = this.props.homeModule
    var standardQueries = []
    json.queries.forEach((item) => {
      if (item.name == 'standardMain' || item.name == 'mainPage') {
        standardQueries = item.query
        standardQueries.map((item, index) => {
          if (item != '') {
            questions.push(item)
          }
        })
      }
    })
    if (json.templateInfo.templateCode != 'Simple' && json.templateInfo.templateCode != 'agentOS') {
      json.skills.map((item, index) => {
        if (item.tip != '' && item.tip != undefined) {
          questions.push(item.tip)
        }
      })
    } else {
      if (
        json.templateInfo.conciseContent &&
        json.templateInfo.conciseContent.suggestion &&
        json.templateInfo.conciseContent.suggestion.length > 0
      ) {
        questions.push(json.templateInfo.conciseContent.suggestion)
      }
    }

    questions.push(json.templateInfo.titleBarContent)

    // let data = {
    //     question_list: JSON.stringify(new Set(questions))
    // }
    let _questions = questions.filter((e) => {
      if (e) {
        return e
      }
    })

    // isGlobal
    if (_.get(this.props.user, 'detail.enable_intl', 0) === '1') {
      return
    }

    let data = new FormData()
    data.append('type', 'bo_front_page')
    data.append('query_list', JSON.stringify(new Set(_questions)))
    addAsrData(data)
      .then((res) => {})
      .catch()
  }
  direct_uploadData() {
    console.log('直接发布模版')
    let formData = new FormData()
    let config_info_list_json = {
      config_id: sessionStorage.getItem('config_id'),
      version: sessionStorage.getItem('version'),
    }
    let config_info_list_arr = []
    config_info_list_arr.push(config_info_list_json)
    formData.append('module_code', 'module_skill_home')
    formData.append('config_info_list', JSON.stringify(config_info_list_arr))
    formData.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
    directPublishModule(formData)
      .then((res) => {
        this.uploadDataPorgress()
      })
      .catch((res) => {
        console.log(res)
      })
  }
  uploadData() {
    const { langDataActive } = this.props
    const lang = this.state.lang
    const robotType = this.getQueryVal('robotType')
    _czc.push(['_trackEvent', 'home', 'publish'])
    if (this.state.chooseList.length == 0) {
      message.error(__('customHome.modalMsg.selectTheRobotToSync'))
      return
    }
    let formData = new FormData()
    var config_json = this.props.homeModule
    console.log(this.props.homeModule,"config_json");
    
    config_json.lang = langDataActive.lang_code
    formData.append('lang', langDataActive.lang_code)
    var config_json_str = JSON.stringify(config_json)
    var resource_path_list = []
    if (this.props.homeModule.templateInfo.titleBarType == 'Image') {
      resource_path_list.push(this.props.homeModule.templateInfo.titleBarContent) //顶部logo图
    }
    resource_path_list.push(this.props.homeModule.templateInfo.background) //背景图
    if (this.props.homeModule.templateInfo.faceBackground && this.props.homeModule.templateInfo.faceBackground != '') {
      resource_path_list.push(this.props.homeModule.templateInfo.faceBackground)
    } //表情页背景图
    if (
      (robotType === 'bao_da_ping' || robotType === 'bxm_plus' || robotType === 'bxm_plus_in') &&
      this.props.homeModule.templateInfo.bs_background &&
      this.props.homeModule.templateInfo.bs_background != ''
    ) {
      resource_path_list.push(this.props.homeModule.templateInfo.bs_background)
    } //大屏默认背景图背景图

    // Simple 添加 conciseContent.image
    if (
        (this.props.homeModule.templateInfo.templateCode === 'Simple' || this.props.homeModule.templateInfo.templateCode === 'agentOS') &&
        this.props.homeModule.templateInfo.conciseContent &&
        this.props.homeModule.templateInfo.conciseContent.image
    ) {
      resource_path_list.push(this.props.homeModule.templateInfo.conciseContent.image)
    } 
    
    // agentOS 添加 agentosContent.image
    if (
      (this.props.homeModule.templateInfo.templateCode === 'agentOS') &&
      this.props.homeModule.templateInfo.agentosContent &&
      this.props.homeModule.templateInfo.agentosContent.image
    ) {
      resource_path_list.push(this.props.homeModule.templateInfo.agentosContent.image)
    } 


    if (this.state.modalStyle != 'Technical') {
      this.props.homeModule.skills.map((item, index) => {
        resource_path_list.push(item.icon) //skills图
      })
    }
    resource_path_list = resource_path_list.filter((item) => item)

    var resource_path_list_str = JSON.stringify(resource_path_list)
    var public_resource_path_list = []
    var public_resource_path_list_str = JSON.stringify(public_resource_path_list)
    if (this.state.lang == 'english') {
      formData.append('module_code', 'module_skill_home')
    } else if (this.state.lang == 'arabic') {
      formData.append('module_code', 'module_skill_home_ar')
    } else {
      formData.append('module_code', 'module_skill_home')
    }
    formData.append('config_id', sessionStorage.getItem('config_id'))
    formData.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
    formData.append('config_json', config_json_str)
    formData.append('name', 'list')
    formData.append('version', sessionStorage.getItem('version'))
    formData.append('resource_path_list', resource_path_list_str) //资源列表(选填)
    formData.append('public_resource_path_list', public_resource_path_list_str) //公共资源列表(选填)
   
    publishModule(formData)
      .then((res) => {
        sessionStorage.setItem('version', res.version)
        this.uploadDataPorgress()
      })
      .catch((res) => {
        console.log(res)
      })
  }
  uploadDataPorgress() {
    modal({
      title: false,
      confirm: false,
      cancel: false,
      fullscreen: false,
      content: <Progress />,
    })
    document.getElementsByClassName('btn-close')[0].style.display = 'none'
    let _time = 0
    this.interval = setInterval(() => {
      if (_time >= 20) {
        closeModal()
        this.setState(
          {
            allNum: 0,
          },
          () => {
            message.success(__('DISTANT_GREETINGS.SUCCESS_PUBLISHED', { num: this.state.chooseList.length }))
            this.checkAllQuery()
          },
        )
        clearInterval(this.interval)
        this.interval = null
        return
      }
      _time++
      let formData2 = new FormData()
      let mdcode = ''
      if (this.state.lang == 'english') {
        mdcode = 'module_skill_home'
        formData2.append('lang', 'en_US')
      } else if (this.state.lang == 'arabic') {
        mdcode = 'module_skill_home_ar'
        formData2.append('lang', 'zh_CN')
      } else {
        mdcode = 'module_skill_home'
        formData2.append('lang', 'zh_CN')
      }

      formData2.append('module_code', mdcode)
      formData2.append('config_id', sessionStorage.getItem('config_id'))
      formData2.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
      formData2.append('version', sessionStorage.getItem('version'))
      getModuleStatus(formData2)
        .then((res) => {
          this.setState({
            showWarning: false,
            allNum: res.pull_total_num,
            progressNum: res.pull_suc_num,
          })
          if (res.pull_total_num == res.pull_suc_num && res.pull_suc_num != 0) {
            closeModal()
            clearInterval(this.interval)
            message.success(__('DISTANT_GREETINGS.SUCCESS_PUBLISHED', { num: this.state.chooseList.length }))
            this.checkAllQuery(() => {})
            this.setState({
              allNum: 0,
            })
            this.gethomeModule() // 获取当前激活语言的最新配置
            this.checkMainLangConfigStatus() // 重新检查主语言的发布状态
          }
        })
        .catch(() => {
          console.log('获取同步进度失败')
          clearInterval(this.interval)
        })
    }, 2000)
  }
  //
  checkJson(kind) {
    if (kind != 'bottomQueries') {
      this.setState({
        queryStyle: '',
        queries_idx: 0,
      })
      //
    }
    if (JSON.stringify(this.props.robots) == '{}') {
      message.error(__('customHome.modalMsg.noRobotsOfCompany'))

      return
    }
    let json = this.props.homeModule
    let title = json.templateInfo.titleBarContent

    let flag = true
    if (title == undefined || title.trim() == '') {
      if (title == '') {
        message.error(__('customHome.modalMsg.enterTitleName'))
      } else {
        message.error(__('customHome.modalMsg.enterTitleNamePic'))
      }
      flag = false
      return
    }
    var standardQueries = []
    var mainPageQueries = []
    json.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
      if (item.name == 'mainPage') {
        mainPageQueries = item.query
      }
    })
    standardQueries.some((item, index) => {
      if (item.trim() == '') {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))
        flag = false
        return true
      }
    })
    mainPageQueries.some((item, index) => {
      if (item.trim() == '') {
        message.error(__('customHome.modalMsg.enterBtnRecommendedQuestions'))
        flag = false
        return true
      }
    })

    if (this.state.overVersion51 && json.templateInfo.bubbleContents && json.templateInfo.bubbleContents.faceStyle) {
      if (json.templateInfo.bubbleContents.robot && json.templateInfo.bubbleContents.faceStyle == 'Bubble') {
        if (json.templateInfo.bubbleContents.robot.length == 0) {
          message.error(__('customHome.modalMsg.bubbleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.bubbleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
        /*
                let arr  = json.templateInfo.bubbleContents.raw;
                let NullIndex =[]
                if(arr.length>0){
                    arr.forEach(function(item,index){　　　　//item表示数组中的每一项，index标识当前项的下标，arr表示当前数组
                        console.log(item);
                        console.log(index);
                        if(item==""){
                            NullIndex.push(index+1)
                        }
                       
                    });　
                    if(NullIndex.length>0){
                        message.error(__('customHome.modalMsg.bubbleMsg'))
                        flag = false
                        return;
                    }
                }*/
      }
    }
    // 配置英文卡片模式标题，内容不能为空
    if (this.state.overVersion57 && json.templateInfo.bubbleContents && json.templateInfo.bubbleContents.faceStyle) {
      if (json.templateInfo.bubbleContents.robot && json.templateInfo.bubbleContents.faceStyle == 'Card') {
        if (json.templateInfo.bubbleContents.robot.length == 0) {
          message.error(__('customHome.modalMsg.bubbleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.bubbleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
        if (json.templateInfo.bubbleContents.title.trim().length == 0) {
          message.error(__('customHome.modalMsg.cardTitleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.cardTitleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
      }
    }

    json.skills.some((item, index) => {
      if (item.title.trim() == '') {
        // modal({
        // 	content: __('customHome.modalMsg.enterFunctionName'),
        // 	cancel: false,
        // 	confirm: __('customHome.btn.confirm')
        // });
        message.error(__('customHome.modalMsg.enterFunctionName'))
        flag = false
        return true
      }
      if (item.tip.trim() == '' || item.tip == undefined) {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))
        // modal({
        //     content: __('customHome.modalMsg.enterRecommendedQuestions'),
        //     cancel: false,
        //     confirm: __('customHome.btn.confirm')
        // });
        flag = false
        return true
      }
    })
    if (json.templateInfo.templateCode == 'Simple') {
      if (json.templateInfo.conciseContent.title.trim() == '') {
        message.error(__('customHome.modalMsg.enterTitleName'))

        flag = false
        return
      } else if (json.templateInfo.conciseContent.suggestion.trim() == '') {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))

        flag = false
        return
      }
    }
    return flag
  }

  bottomQueries(queries_style, idx) {
    if (queries_style == 'mainPage') {
      this.setState({
        queryStyle: '',
      })
    } else {
      this.setState({
        queryStyle: queries_style,
      })
    }
    this.setState({
      queries_idx: idx,
    })
  }
  changeTab_backImg(j) {
    if (j == 1) {
      this.setState({
        backImgstyle: 'face',
      })
    } else if (j == 2) {
      this.setState({
        backImgstyle: 'bigScreen',
      })
    } else {
      this.setState({
        backImgstyle: '',
      })
    }
  }
  addCover(flag) {
    this.setState({
      // fiveTimer: flag,
      haveCover: flag,
    })
  }
  checkAllQuery(cb) {
    const lang = this.state.lang
    this.setState({
      showLoading: true,
    })
    closeModal()
    let existed = false
    let questions = [],
      questionList = []
    let json = this.props.homeModule
    var standardQueries = []
    json.queries.forEach((item) => {
      if (
        item.name == 'standardMain' &&
        (json.templateInfo.templateCode == 'Standard' || json.templateInfo.templateCode == 'Simple' || json.templateInfo.templateCode == 'agentOS')
      ) {
        standardQueries = item.query
        standardQueries.map((item, index) => {
          if (item != '') {
            questions.push(item)
          }
        })
      }
    })
    if (json.templateInfo.templateCode != 'Simple' && json.templateInfo.templateCode != 'agentOS') {
      json.skills.map((item, index) => {
        if (item.tip != '' && item.tip != undefined) {
          questions.push(item.tip)
        }
      })
    } else {
      if (
        json.templateInfo.conciseContent &&
        json.templateInfo.conciseContent.suggestion &&
        json.templateInfo.conciseContent.suggestion.length > 0
      ) {
        questions.push(json.templateInfo.conciseContent.suggestion)
      }
    }
    let _questions = questions.filter((e) => {
      if (e) {
        return e
      }
    })
    let data = {
      question_list: JSON.stringify(new Set(_questions)),
    }
    searchAnswer(data)
      .then((res) => {
        console.log(res)
        res.items.map((item) => {
          if (!item.existed) {
            existed = true
            questionList.push(item.question)
          }
        })
        this.setState({
          showLoading: false,
        })
        if (existed && lang === 'chs') {
          modal({
            title: __('customHome.modalMsg.noAnswersInHomePage'),
            content: <QueryItem questionList={questionList}></QueryItem>,
            cancel: false,
            // confirm: __('customHome.btn.gotIt'),
            confirm: false,
            // onConfirm:()=>{
            //     cb&&cb()
            // }
          })
        } else {
          cb && cb()
        }
      })
      .catch((e) => {
        // message.error('')
      })
      .finally((e) => {
        this.setState({
          showLoading: false,
        })
      })
  }
  changeRobotData(id, sn) {
    if (this.state.currentRobotId != id) {
      this.simple_config_json = {} //重置缓存的配置
      this.common_config_json = {}
    }
    this.setState({
      currentRobotId: id,
      currentRobotSn: sn,
      templateNeedUpdate: false,
      lowOpkVersion: false,
    })
  }
  changeRobot(id) {
    this.props.dispatch(selectRobot(id))
    this.resetHomeSetting()
    this.getRobotName(id)
  }
}


function setSkillDefaultIcon(skills, index, icon) {
  if (skills && skills.length > index && skills[index].icon !== '') {
    skills[index].icon = icon
  }
}
