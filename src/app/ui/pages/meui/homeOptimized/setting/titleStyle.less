@import '~base.less';

.component {
  .changeTitle {
    ul.nav {
      padding-left: 30px;
      padding-right: 30px;
      margin-bottom: 28px;
      border-bottom: 1px solid #e4e8ea;

      li {
        display: inline-block;
        font-size: 14px;
        color: #555d61;
        letter-spacing: 0;
        margin-right: 30px;
        padding-bottom: 4px;
        cursor: pointer;
      }

      .active {
        color: #3776ef;
        border-bottom: 2px solid #3776ef;
      }
    }

    .line_name {
      display: flex;
      position: relative;
      min-width: 37rem;

      .title {
        line-height: 2.3rem;
        margin-right: 29px;
        color: #3776ef;
      }

      input {
        width: 27rem;
      }

      .count {
        line-height: 2.3rem;
        position: absolute;
        right: 6rem;
        height: 2.3rem;
        color: #3776ef;
      }

      .delete {
        line-height: 2.3rem;
        position: absolute;
        right: 4rem;
        height: 2.3rem;
        color: grey;

        &:hover {
          cursor: pointer;
          color: #3776ef;

          svg {
            fill: #3776ef;
          }
        }
      }

      .bpreview {
        display: none;
        cursor: pointer;
        line-height: 2.3rem;
        position: absolute;
        right: 6rem;
        height: 2.3rem;
        color: grey;

        svg {
          fill: grey;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: 35px;

          &:lang(en) {
            right: 64px;
          }
        }

        &:hover {
          color: #3776ef;

          svg {
            fill: #3776ef;
          }
        }
      }

      &:hover {
        .bpreview {
          display: block;
        }
      }
    }

    div.listFunTitle {
      // margin-bottom: 0px !important;
      input {
        padding-right: 50px;
      }
    }

    div.list {
      margin-bottom: 15px;

      .input_tips {
        font-size: 12px;
        line-height: 20px;
        color: #ff713c;
        margin-bottom: 10px;
        margin-top: 10px;
        display: block;
      }

      .prompt {
        display: block;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        padding: 10px;
        border: solid 1px #91d5ff;
        background-color: #e6f7ff;
        margin-bottom: 26px;

        .prompt_tips {
          color: #1890ff;
          margin-right: 8px;
          vertical-align: middle;
          margin-top: -2px;
        }
      }

      .hide_input_tips {
        visibility: hidden;
      }

      .show_input_tips {
        visibility: visible;
      }

      .chooseStyle {
        font-size: 12px;
        color: #555d61;
        letter-spacing: 0;
        line-height: 16px;

        .chooseCircle {
          display: inline-block;
          width: 12px;
          height: 12px;
          border: 2px solid rgba(85, 195, 251, 0.5);
          border-radius: 100%;
          margin-right: 8px;
        }

        .blue {
          background: #3776ef;
        }

        label {
          margin-right: 40px;
          line-height: 22px;
        }

        input:checked {
          background: #3776ef;
          color: #3776ef;
        }
      }

      .newTitle {
        clear: both;
        display: inline-block;
        position: relative;
        width: 380px;
        input[type='text'] {
          border: 1px solid #e4e8ea;
          width: 100%;
          font-size: 14px;
          padding-right: 58px;
        }

        span {
          display: inline-block;
          width: 60px;
          font-size: 14px;
          text-align: right;
          position: absolute;
          right: 10px;
          top: 7px;
          em {
            font-style: normal;
          }
        }
      }

      .arabic_newTitle {
        input {
          direction: rtl;
        }
      }

      .dropdown {
        background: rgba(161, 170, 178, 0.15);
        border-radius: 4px;

        button {
          line-height: 26px;
          height: 26px;
          font-size: 12px;
          color: #555d61;
          letter-spacing: 0;
          line-height: 16px;
        }

        .txt {
          display: inline-block;
          padding-right: 67px;
        }

        .menu-container {
          .menu {
            width: 100px;
            text-align: center;

            .menu-item {
              font-size: 14px;
              color: #555d61;
            }

            .menu-item:hover {
              color: #3776ef;
              background: #f3f5f6;
            }
          }
        }
      }

      .query-set {
        display: inline-block;
        width: 50%;

        .queryInput {
          width: 100%;
          // padding-left: 14px;
          padding-right: 50px;
        }

        ul {
          position: absolute;
          width: 50%;
          background: #fff;
          max-height: 300px;
          // padding: 14px;
          padding-bottom: 0;
          margin-top: -1px;
          box-shadow: 0 2px 6px 2px rgba(161, 170, 178, 0.15);
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
          overflow-y: scroll;

          li {
            cursor: pointer;
            padding-left: 14px;
            line-height: 36px;

            &:hover {
              background-color: #f3f5f6;
            }
          }
        }

        .scroll-setting::-webkit-scrollbar {
          width: 5px;
          height: 10px;
        }

        .scroll-setting::-webkit-scrollbar-track {
          -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          border-radius: 10px;
          background-color: #b5b1b1;
        }

        .scroll-setting::-webkit-scrollbar-thumb {
          border-radius: 10px;
          -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          background-color: black;
        }
      }

      select.chooseSetting {
        background: rgba(161, 170, 178, 0.15);
        border-radius: 4px;
        border: none;
        outline: none;
        font-size: 12px;
        color: #555d61;
        letter-spacing: 0;
        line-height: 16px;
        padding: 5px 9px;
      }

      select.chooseSetting:hover {
        option {
          background: #fff;
          border: none;
          line-height: 50px;
          height: 50px;
          text-align: center;
        }
      }

      select.chooseQuestion {
        width: 50%;
        border: none;
        border-bottom: 1px solid #e4e8ea;
        outline: none;
        font-size: 16px;
        color: #555d61;
        letter-spacing: 0;
        line-height: 16px;
        padding-bottom: 11px;
      }

      option {
        text-align: center;
      }

      .title {
        font-size: 14px;
        letter-spacing: 0;
        margin-right: 14px;
        float: left;
      }
      .simple_title {
        font-size: 16px;
        color: #3776ef;
        letter-spacing: 0;
        margin-right: 14px;
        // float: left;
        display: block;
        margin-bottom: 10px;
        margin-top: 24px;
      }

      .fon_list_area {
        color: #4a4a4a;
        font-weight: 400;
      }

      .word-len-tips {
        display: inline-block;
        width: 70px;
        color: #3776ef;
        font-size: 0.8rem;
        text-align: right;
        margin-left: -70px;
      }

      .list_area {
        display: inline-block;
        margin-right: 50px;

        span {
          display: block;
        }

        .circle {
          border-radius: 100%;
        }

        .square {
          border-radius: 8px;
        }

        .upload-btn {
          width: 57px;
          height: 57px;
          background: rgba(0, 146, 245, 0.2) !important;
          position: relative;
          margin: 0 auto;
          z-index: 10;

          &:before {
            display: block;
            content: '';
            width: 21px;
            height: 2px;
            background: @color-theme;
            position: absolute;
            left: 17px;
            top: 27px;
          }

          &:after {
            display: block;
            content: '';
            width: 2px;
            height: 21px;
            background: @color-theme;
            position: absolute;
            left: 27px;
            top: 17px;
          }
        }

        .defaultBackImg {
          width: 36px;
          height: 57px;
          border: 3px solid #3776ef;
          border-radius: 8px;
          margin: 0 auto;
          text-align: center;
          background: #000;
          vertical-align: top;
          margin-left: 5px;
        }

        .simpleBackImg {
          width: 58px;
          height: 58px;
          border: 3px solid #3776ef;
          border-radius: 8px;
          margin: 0 auto;
          text-align: center;
          // background:url(http://test-jiedai.ainirobot.com/media/module_public/module_skill_home/conciseimage.png);
          background-size: cover;
          background-repeat: none;
          vertical-align: top;
          margin-left: 5px;
        }

        .defaultImg {
          width: 57px;
          border: 3px solid #3776ef;
          border-radius: 100%;
        }

        .circleImg {
          height: 57px;
          border-radius: 100%;
        }

        .squareImg {
          height: 30px;
          border-radius: 8px;
          margin-bottom: 10px;
        }

        .rectangleImg1 {
          width: 57px;
          height: 40px;
          border-radius: 8px;
          margin-bottom: 8px;
        }

        .rectangleImg2 {
          width: 57px;
          height: 31px;
          border-radius: 8px;
          margin-bottom: 10px;
        }

        .rectangleImg3 {
          width: 46px;
          height: 57px;
          border-radius: 8px;
          margin-bottom: -2px;
        }

        span.uploadInfo {
          font-size: 12px;

          letter-spacing: 0;
          text-align: center;
          margin-top: 3px;
          color: #3776ef;
        }

        span.defaultInfo {
          font-size: 12px;
          // color: #3776ef;
          color: #99a3a8;
          letter-spacing: 0;
          text-align: center;
          margin-top: 3px;
        }

        span.uploadTips {
          font-size: 12px;
          color: #bcc4ca;
        }
      }
      .simple_list_area {
        position: relative;
        margin-right: 32px;
        .simpleIcon_default {
          width: 57px;
          height: 57px;
          border-radius: 8px;
          top: 0px;
          float: left;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      .default_area {
        cursor: pointer;
        text-align: center;
      }

      .upload_area {
        position: relative;

        .upload-btn {
          opacity: 0;
        }

        &:hover {
          .upload-btn {
            opacity: 1;
          }

          .newLogo,
          .newIcon_rectangle1,
          .newIcon_rectangle2,
          .newIcon_rectangle3,
          .newIcon_circle,
          .newIcon_square,
          .simpleIcon_default,
          .newIcon_default {
            opacity: 0;
          }
        }

        .newLogo {
          width: 57px;
          height: 11px;
          float: left;
          position: absolute;
          left: 50%;
          top: 0;
          margin-top: 23px;
          transform: translateX(-50%);
        }

        .newIcon_rectangle1 {
          width: 57px;
          height: 40px;
          border-radius: 8px;
          float: left;
          position: absolute;
          left: 50%;
          top: 0;
          margin-top: 8px;
          transform: translateX(-50%);
        }

        .newIcon_rectangle2 {
          width: 57px;
          height: 31px;
          border-radius: 8px;
          float: left;
          position: absolute;
          left: 50%;
          top: 0;
          margin-top: 13px;
          transform: translateX(-50%);
        }

        .newIcon_rectangle3 {
          width: 46px;
          height: 57px;
          border-radius: 8px;
          top: 0px;
          float: left;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }

        .newIcon_circle {
          width: 57px;
          height: 57px;
          border-radius: 100px;
          top: 0px;
          float: left;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }

        .newIcon_square {
          width: 57px;
          height: 30px;
          border-radius: 8px;
          margin-top: 13.5px;
          top: 0px;
          float: left;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }

        .newIcon_default {
          width: 30px;
          height: 57px;
          border-radius: 8px;
          top: 0px;
          float: left;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      input[type='text'] {
        border: none;
        border-bottom: 1px solid #e4e8ea;
        font-size: 16px;
        color: #555d61;
        line-height: 21px;
        width: 50%;
        outline: none;
        padding: 0.35rem;
      }

      input::placeholder {
        font-size: 16px;
        color: #555d61;
      }
    }
  }
}
