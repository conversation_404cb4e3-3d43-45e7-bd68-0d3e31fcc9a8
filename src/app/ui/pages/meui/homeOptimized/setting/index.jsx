import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import modal, { close as closeModal } from '@utils/modal'
import ChooseModal from '../preview/chooseModal'
import BottomQueries from '../bottomQueries'
import Title from './title'
import { uploadResource } from '@api/_home'
import { checkReleaseRaw, isMini } from '@utils/tools'
import { getImageSize } from '@utils/image-size'
import { message, Tabs, Upload, Button, Popover, Tooltip, Spin } from 'antd'
import { SwapOutlined, InfoCircleOutlined } from '@ant-design/icons'
import CardTitle from '../cardTitle'
import { webOperator } from '@utils/operatorRobot'
import { getQueryVal } from '@utils/tools'
window.webOperator = webOperator
import 'antd/dist/antd.css'
import { CheckCircleOutlined } from '@ant-design/icons'
import ImgCrop from 'antd-img-crop'
const { TabPane } = Tabs
import CouldAskMe from '../couldAskMe'
import HeadImg from '../headImg'
import BackgroundImg from '../backgroundImg'
import LanguageConfig from '../languageConfig'

import BackImg from './expression/backImg'
@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    // console.log('页面：自定义首页', state._home)
    devUrl = 'http://test-jiedai.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    user: state.user,
    brand: state.brand || 'ktv',
    langData: state.user.langData,
    langDataActive: state.user.langDataActive,
    localeId: state.localeId,
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  state = {
    SimplebackImg: '',
    uploadUrl: '',
    faceBackground: '',
    backImg: '',
    headImgLoading: false,
    backImgstyle: '',
    isDefault: false,
    isShowSetting: true,
    lang: 'chs',
    logo: '',
    // faceStyle: this.props.lang === 'english' ?  (this.props.templateInfo.bubbleContents && this.props.templateInfo.bubbleContents.faceStyle === 'Standard' ? this.props.templateInfo.bubbleContents.faceStyle :  'Card') : (this.props.templateInfo.bubbleContents  ? this.props.templateInfo.bubbleContents.faceStyle :  'Bubble'),
  }
  initRobotNum = 0
  componentDidMount() {
    //只执行一次。
    this.props.onRef && this.props.onRef(this)
    window.getPropsSet = () => {
      console.log(this.props)
    }
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1 ? true : false
  }

  UNSAFE_componentWillReceiveProps(nextProps) {}

  jump = (target, classRoll) => {
    // location.href = "#firstAnchor"
    console.log('target', target)

    let idName = '.' + target + '-rgscl'
    let btn = document.querySelector(idName)
    console.log('querySelector====', btn)
    let parentNodeTop = 0
    if (target == 'card') {
      parentNodeTop = btn.parentNode.offsetTop
    }
    if (target == 'askmein') {
      parentNodeTop = btn.parentNode.offsetTop
      // parentNodeTop = parentNodeTop+ btn.parentNode.parentNode.offsetTop
    }
    if (target == 'askmeinStandard') {
      parentNodeTop = btn.parentNode.offsetTop
      // parentNodeTop = parentNodeTop+ btn.parentNode.parentNode.offsetTop
    }

    if (btn) {
      let btnc = document.querySelector('.' + classRoll)
      //btnc.scrollTop=btn.offsetTop-65
      btnc.scroll({
        top: btn.offsetTop - 75 + parentNodeTop,
        behavior: 'smooth',
      })
    }
    this.setState({
      currModule: target,
    })
  }
  componentWillUnmount() {
    this.props.dispatch({
      type: 'GET_HOME_MODULE',
      data: {
        homeModule: null,
      },
    })
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (
      this.props.currentRobotId != undefined &&
      prevProps.currentRobotId != undefined &&
      prevProps.currentRobotId != this.props.currentRobotId
    ) {
      console.log('componentDidUpdate  acaction ', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (
      this.props.homeModule != undefined &&
      prevProps.homeModule &&
      prevProps.homeModule == undefined &&
      this.props.homeModule != undefined
    ) {
      console.log('componentDidUpdate  acaction  props', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          console.log('-----------------', this.props.robots[el].version, versionGap)
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }

    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      prevProps.homeModule.templateInfo.bubbleContents &&
      prevProps.homeModule.templateInfo.bubbleContents != this.props.homeModule.templateInfo.bubbleContents
    ) {
      this.setState({
        Bubble1: '',
      })
    }
    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo.background &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      this.state.templateInfoBackground != this.props.homeModule.templateInfo.background
    ) {
      this.setState({
        templateInfoBackground: this.props.homeModule.templateInfo.background,
      })
    }
  }
  changeModal() {
    console.log('点击 打开modal 1')
    if (this.props.checkJson()) {
      this.props.changeState({
        currentButton: 1,
        currentIndex: -2,
        currentSort: false,
      })
      modal({
        title: false,
        confirm: false,
        cancel: false,
        fullscreen: false,
        content: (
          <ChooseModal
            curScenes={this.props.curScenes}
            modalStyle={this.props.modalStyle}
            choosemodalStyle={this.props.choosemodalStyle}
          />
        ),
      })
    }
  }
  /////////////表情
  addCover(flag) {
    this.props.addCover(flag)
    /*
       this.setState({
           // fiveTimer: flag,
           haveCover: flag
       })*/
  }

  changeTab_backImg(j) {
    if (j == 1) {
      this.setState({
        backImgstyle: 'face',
      })
    } else {
      this.setState({
        backImgstyle: '',
      })
    }
  }

  selectImg(e, type, idx, imgName, callback) {
    const file = !e.target ? e : e.target.files[0]
    let reader = new FileReader()
    var uploadImgWidth = 0,
      uploadImgHeight = 0
    if (!file) {
      return
    }
    if (file.type != 'image/png' && file.type != 'image/jpeg' && file.type != 'image/gif') {
      message.error(__('customHome.pic.uploadFilesFormat', { format: 'png/jpg/jpeg/gif' }))
      return
    }

    //在做图片提示需求时，发现部分图片格式和需求不符，已经校准了部分图片格式，发现部分图片也转换了大小，所以不需要再次判断
    //已经封装了统一获取 图片尺寸 的方法 @utils/image-size , 后续没有加入的图片尺寸，可以加入统一管理
    switch (type) {
      case 'logo':
        uploadImgWidth = 1000
        uploadImgHeight = 200
        break
    }

    if (file.size / 1024 / 1000 > 5) {
      message.error(__('customHome.pic.picSizeLimit2'))
      return
    }
    var _this = this
    reader.onload = function (e) {
      let image = new Image()
      image.onload = function () {
        if (uploadImgWidth != 0 && (image.width != uploadImgWidth || image.height != uploadImgHeight)) {
          console.log(image.width)
          console.log(image.height)

          return message.error(
            __('customHome.pic.uploadpicDimensions') +
              ':' +
              uploadImgWidth +
              '*' +
              uploadImgHeight +
              __('customHome.btn.pixels'),
          )
        } else {
          let formData = new FormData()
          formData.append('resource_file', file)
          formData.append('module_code', 'module_skill_home')
          formData.append('config_id', sessionStorage.getItem('config_id'))
          formData.append('resource_type', 'image')
          formData.append('resource_file', file.name || imgName)
          formData.append('version', '')
          uploadResource(formData)
            .then((res) => {
              let upload_url = res.resource_url
              console.log(upload_url)
              if (type == 'backImg' || type == 'miniBackImg') {
                _this.setState({
                  SimplebackImg: upload_url,
                })
              }
              if (type != 'backImg' && type != 'miniBackImg') {
                _this.setState({
                  uploadUrl: upload_url,
                })
                if (type == 'facebackImg' || type == 'minifacebackImg') {
                  _this.setState({
                    faceBackground: upload_url,
                  })
                }
              } else {
                _this.setState({
                  backImg: upload_url,
                })
              }

              if (type == 'logo') {
                _this.setState({
                  logo: upload_url,
                })
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'titleBarType',
                    content: 'Image',
                  },
                }) //更改json标题类型
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'titleBarContent',
                    content: upload_url,
                  },
                }) //更改json标题内容
              } else if (type == 'backImg' || type == 'miniBackImg') {
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'background',
                    content: upload_url,
                  },
                }) //更改json标题内容
                _this.props.setIndexState({
                  backImg: upload_url,
                })
              } else if (type == 'facebackImg' || type == 'minifacebackImg') {
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    key: 10,
                    type: 'faceBackground',
                    content: upload_url,
                  },
                }) //更改json标题内容
                _this.props.setIndexState({
                  faceBackground: upload_url,
                })
              } else if (type == 'conciseImg') {
                let conciseContent = _this.props.homeModule.templateInfo.conciseContent
                conciseContent.image = upload_url
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'conciseContent',
                    content: conciseContent,
                  },
                })
              } else if (type == 'miniconciseImg') {
                let conciseContent = _this.props.homeModule.templateInfo.conciseContent
                conciseContent.image = upload_url
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'conciseContent',
                    content: conciseContent,
                  },
                })
              } else if (type == 'agentosImg' || type == 'miniagentosImg') {
                let agentosContent = _this.props.homeModule.templateInfo.agentosContent
                if (!agentosContent) agentosContent = {}; 
                agentosContent.image = upload_url;
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'agentosContent',
                    content: agentosContent,
                  },
                });
              } else if (type === 'bs_background') {
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'bs_background',
                    content: upload_url,
                  },
                })
              } else {
                _this.props.homeModule.skills[idx].icon = upload_url
                _this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'icon',
                    key: 37,
                    index: idx,
                    content: _this.props.homeModule.skills[idx],
                  },
                }) //更改skills中icon路径
              }
              message.success(__('customHome.pic.uploadSuccess'))
              
              // 如果提供了可选的回调函数，则调用它
              if (typeof callback === 'function') {
                callback(upload_url);
              }
            })
            .catch((res) => {
              console.log(res)
              message.error(__('customHome.pic.uploadFailed'))
              // modal({
              //     content: __('customHome.pic.uploadFailed'),
              //     cancel: false,
              //     confirm: __('customHome.btn.gotIt')
              // })
            })
        }
      }
      image.src = e.target.result
    }
    reader.readAsDataURL(file)
  }

  deleteBackImg() {
    this.props.deleteBackImg()
    /*this.setState({
            backImg: ""
        })
        if(this.props.homeModule.templateInfo.templateCode=='Simple'){
            this.setState({
                SimplebackImg: ""
            })
        }*/
  }
  callback(val) {
    this.props.changeTab_backImg(val - 1)
    this.props.changeState({
      activeKey: val,
    })
  }

  restBackImg = () => {
    let backgroundImg = ''
    let robotType = getQueryVal('robotType')
    if (this.props.homeModule.templateInfo.templateCode == 'List') {
      if (isMini(robotType)) {
        backgroundImg = 'module_public/module_skill_home/bao_mini/minibg.png'
      } else {
        backgroundImg = 'module_public/module_skill_home/home1.png'
      }
    } else if (this.props.homeModule.templateInfo.templateCode == 'Card') {
      backgroundImg = 'module_public/module_skill_home/home2.png'
    } else if (this.props.homeModule.templateInfo.templateCode == 'Standard') {
      backgroundImg = 'module_public/module_skill_home/home4.png'
    } else if (this.props.homeModule.templateInfo.templateCode == 'Simple') {
      if (!this.isKTV) {
        backgroundImg = 'module_public/module_skill_home/simpleBg.png'
      } else {
        backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
      }
      if (
        this.props.homeModule.templateInfo.scenes == 'KTV' &&
        (this.props.homeModule.templateInfo.templateCode == 'Simple')
      ) {
        backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
      }
    }else if(this.props.homeModule.templateInfo.templateCode == 'agentOS') {
      backgroundImg = 'module_public/module_skill_home/bao_mini/miniaiosbg.png'
    }else if (this.props.homeModule.templateInfo.templateCode == 'Buttons') {
      backgroundImg = 'module_public/module_skill_home/home1.png'
    }
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: { type: 'background', content: backgroundImg },
    }) //更改json标题内容
  }
  onScrollFun(e) {
    let arr = ['now', 'mtitle', 'bottom', 'background', 'card', 'askmein', 'askmeinStandard', 'headPic', 'askme']
    for (var i = 0; i < arr.length; i++) {
      let btn = document.querySelector('.' + arr[i] + '-rgscl')
      if (btn != undefined) {
        let parentTop = 0
        let parentNodeTop = 0
        if (arr[i] == 'card') {
          parentTop = btn.parentNode.offsetTop
          parentNodeTop = btn.offsetTop - 75 + parentTop
        } else if (arr[i] == 'askmein') {
          parentTop = btn.parentNode.offsetTop
          parentNodeTop = btn.offsetTop - 75 + parentTop
        } else if (arr[i] == 'askmeinStandard') {
          parentTop = btn.parentNode.offsetTop
          parentNodeTop = btn.offsetTop - 75 + parentTop
        } else {
          parentNodeTop = btn.offsetTop - 75
        }
        let height = btn.offsetHeight
        let sh = parentNodeTop + height
        if (e.target.scrollTop > parentNodeTop && sh > e.target.scrollTop) {
          this.setState({
            currModule: arr[i],
          })
        }
      }
    }
  }

  // 图片上传之前
  uploadImg = (e) => {
    let _this = this
    let img = e
    let reader = new FileReader()
    let type = 'bs_background'
    reader.readAsDataURL(img)
    reader.onload = function (e) {
      let image = new Image()
      image.onload = function () {
        let mycanvas = document.querySelector('#mybackCanvas2')
        let ctx = mycanvas.getContext('2d')
        ctx.clearRect(0, 0, mycanvas.width, mycanvas.height)
        ctx.drawImage(image, 0, 0, mycanvas.width, mycanvas.height)
        let dataurl = mycanvas.toDataURL('image/png')
        function dataURLtoBlob(dataurl) {
          let arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n)
          while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
          }
          return new Blob([u8arr], { type: mime })
        }
        _this.selectImg(dataURLtoBlob(dataurl), type, '', img.name)
      }
      image.src = e.target.result
    }
    return true
  }

  render() {
    const robotName = this.state.robotName != undefined && this.state.robotName
    const { 
      langDataActive, 
      langData, 
      mainLangSourceConfigInfo,
      isGenerating,
      user,
      homeModule 
    } = this.props;
    const isAgentOS = user?.corp?.enable_aios === '1';
    const mainLang = langData && langData.length > 0 ? langData[0] : null;
    const currentTemplateCode = homeModule?.templateInfo?.templateCode;
    // 决定是否显示自动生成按钮
    const showAutoGenerateButton = Boolean( isAgentOS && mainLang && 
                                 langDataActive && 
                                 langDataActive.lang_code !== mainLang.lang_code && 
                                 currentTemplateCode &&
                                 mainLangSourceConfigInfo && 
                                 mainLangSourceConfigInfo[currentTemplateCode]);
    let robotType = getQueryVal('robotType')
    const is_dp_xm_mini =
      robotType === 'bao_da_ping' ||
      robotType === 'bxm_plus' ||
      robotType === 'bxm_plus_in' ||
      isMini(robotType) ||
      robotType === 'bao_xiao_mi'
    let modules = []
    if (this.props.homeModule) {
      modules = [
        { name: __('customHome.face.currentTemplate'), attr: 'now' },
        { name: __('customHome.btn.title'), attr: 'mtitle' },
        // { name: __('customHome.btn.Card'), attr: 'card' },
        { name: __('customHome.label.bottomGuide'), attr: 'bottom' },
        { name: __('customHome.label.languageConfig'), attr: 'lang_config' },
        { name: __('customHome.pic.backgroundPicture'), attr: 'background' },
      ]
      if (!is_dp_xm_mini) {
        modules = modules.filter((item) => item.attr !== 'lang_config')
      }
      //指令配置  触发指令  就是可以问我
      //标题没有
      //简洁没有指令和卡片
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        modules.splice(2, 0, { name: __('customHome.btn.Card'), attr: 'card' })
        modules.splice(3, 0, { name: __('customHome.btn.Questions'), attr: 'askmein' })
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        modules.splice(2, 0, { name: __('customHome.btn.Card'), attr: 'askmein' })
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        //推荐问法
        modules.splice(2, 0, { name: __('customHome.btn.Questions'), attr: 'askmeinStandard' })
        modules.splice(3, 0, { name: __('customHome.btn.Card'), attr: 'card' })
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'agentOS') {
        backgroundStr = 'module_public/module_skill_home/bao_mini/miniaiosbg.png'
        modules.splice(2, 0, { name: __('customHome.label.headpic'), attr: 'headPic' })
        modules.splice(3, 0, { name: __('customHome.btn.Ask'), attr: 'askme' })
      } else if (templateCode == 'Simple') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
        modules.splice(2, 0, { name: __('customHome.label.headpic'), attr: 'headPic' })
        modules.splice(3, 0, { name: __('customHome.btn.Ask'), attr: 'askme' })
        // 没有卡片、没有指令配置
      } else if (templateCode == 'Buttons') {
        modules.splice(2, 0, { name: __('customHome.btn.Buttons'), attr: 'askmein' })
        backgroundStr = 'module_public/module_skill_home/home2.png'
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }

      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.state.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };

      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.state.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.state.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.state.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.state.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
    }
    let faceModules = []
    if (this.props.homeModule) {
      let faceStyle =
        this.props.lang === 'english'
          ? this.props.homeModule.templateInfo.bubbleContents &&
            this.props.homeModule.templateInfo.bubbleContents.faceStyle === 'Standard'
            ? this.props.homeModule.templateInfo.bubbleContents.faceStyle
            : 'Card'
          : this.props.homeModule.templateInfo.bubbleContents
          ? this.props.homeModule.templateInfo.bubbleContents.faceStyle
          : 'Bubble'
      if ((this.props.overVersion51 && faceStyle == 'Bubble') || (this.props.overVersion57 && faceStyle == 'Card')) {
        faceModules = [
          { name: __('customHome.face.currentTemplate'), attr: 'nowFace' },
          { name: __('customHome.face.copywriterConf'), attr: 'mtitleFace' },
          { name: __('customHome.pic.backgroundPicture'), attr: 'backgroundFace' },
        ]
      } else {
        if (faceStyle == 'Standard') {
          faceModules = [
            { name: __('customHome.face.currentTemplate'), attr: 'nowFace' },
            //  {name:__('customHome.face.copywriterConf'),attr:'mtitleFace'},
            { name: __('customHome.pic.backgroundPicture'), attr: 'backgroundFace' },
          ]
        } else {
          faceModules = [{ name: __('customHome.pic.backgroundPicture'), attr: 'backgroundFace' }]
        }
      }
    }

    if (!this.props.initEnd) return ''
    const sizeRule = getImageSize(robotType, this.props.homeModule?.templateInfo?.templateCode, 0, 'extrascreen')
    return (
      <div
        className={this.props.className}
        key={
          this.props.modalStyle +
          this.props.homeModule.templateInfo.scenes +
          this.props.homeModule.templateInfo.templateCode
        }
      >
        {/* 添加 Loading 覆盖层 */}
        {isGenerating && (
          <div className='loading'>
            <div className='loading_modal'>
              <img src={require('@assets/svg-icon/loading.gif')} alt="Loading..." />
              <span style={{ whiteSpace: 'normal', wordBreak: 'break-all' }}>{__('customHome.modalMsg.autoGenerate')}</span>
            </div>
          </div>
        )}
        <div className={this.props.isDefault && this.props.isShowSetting ? 'card-container dis' : ' card-container'}>
          <Tabs 
            type='card' 
            onChange={this.callback.bind(this)} 
            activeKey={this.props.activeKey}
            tabBarExtraContent={
              showAutoGenerateButton ? (
                <div style={{ marginRight: '20px', display: 'flex', alignItems: 'center' }}>
                  <Tooltip 
                    placement="bottom"
                    title={
                      __('customHome.template_translate_tip').replace(/{(\w+)}/g, (match, key) => {
                        const params = {
                          templateCode: currentTemplateCode,
                          mainLang: mainLang?.name,
                          currentLang: langDataActive?.name
                        };
                        return params[key] || match;
                      })
                    }
                    // title={`点击后将基于主语言(${mainLang?.name})已发布的${currentTemplateCode}模板配置，自动翻译并覆盖当前语言(${langDataActive?.name})页面的${currentTemplateCode}模板文本内容。注意：此操作会覆盖未保存的修改。`}
                  >
                    <Button 
                      shape='round' 
                      onClick={this.props.handleAutoGenerate} 
                      loading={this.props.isGenerating}
                      disabled={this.props.isGenerating}
                    >
                      {__('customHome.AutoCreation')}
                      <InfoCircleOutlined style={{ marginLeft: '5px', color: '#1890ff', cursor: 'pointer' }}/>
                    </Button>
                  </Tooltip>
                </div>
              ) : null
            }
          >
            <TabPane tab={__('customHome.btn.Homepage')} key='1'>
              <div className={isMini(robotType) ? 'index-set index-set-mini' : 'index-set'}>
                <div className='modules'>
                  {modules.map((item) => {
                    return (
                      <div
                        className={this.state.currModule == item.attr ? ' curr modu ' : ' modu '}
                        attr={item.attr}
                        onClick={(e) => {
                          let clickattr = e.target.attributes.attr.nodeValue
                          this.jump(clickattr, 'home-detail-roll')
                        }}
                      >
                        {item.name}
                        {this.state.currModule == item.attr ? (
                          <CheckCircleOutlined className='exp_set_nav_list-icon' />
                        ) : (
                          ''
                        )}
                      </div>
                    )
                  })}
                  {/* <div className="modu">当前</div>
                                        <div className="modu">标题</div>
                                        <div className="modu curr">头图</div>
                                        <div className={"modu "+this.state.currModule==this.state.currModule?' current ':''}  attr='askme' onClick={e=>{
                                            let clickattr = e.target.attributes.attr;
                                            this.jump(clickattr)
                                        }}>可以</div>
                                        <div className="modu">底部</div>*/}
                  {/* <div className="modu">背景</div>  */}
                </div>
                <div className='module-detail home-detail-roll'>
                  <CardTitle className='now-rgscl' title={__('customHome.face.currentTemplate')}></CardTitle>
                  {this.props.modalStyle && (
                    // <Button
                    //   disabled={this.isKTV}
                    //   className='change-temp'
                    //   icon='change'
                    //   appearance='hollow'
                    //   label={`${
                    //     this.props.modalStyle ? __('customHome.SelectStyle.' + this.props.modalStyle.toLowerCase()) : ''
                    //   } (${
                    //     '' + __('customHome.language.' + (this.props.lang === 'zh_CN' ? 'chinese' : this.props.lang))
                    //   })`}
                    //   color='black'
                    //   size='small'
                    //   onClick={this.changeModal.bind(this)}
                    // />
                    <Button
                      shape='round'
                      disabled={this.isKTV}
                      onClick={() => {
                        this.changeModal()
                      }}
                    >
                      <SwapOutlined />
                      {this.props.modalStyle ? __('customHome.SelectStyle.' + this.props.modalStyle.toLowerCase()) : ''}
                      ({langDataActive.name})
                    </Button>
                  )}
                  <Title
                    className='mtitle-rgscl'
                    logo={this.state.logo} //
                    devUrl={this.props.devUrl}
                    lang={this.props.lang}
                    modalStyle={this.props.modalStyle}
                    checkJson={this.props.checkJson.bind(this)}
                    selectImg={this.selectImg.bind(this)}
                    robotType={robotType}
                  />
                  {(this.props.modalStyle == 'Simple' || this.props.modalStyle == 'agentOS') && (
                    <HeadImg
                      lang={this.props.lang}
                      className='headPic-rgscl'
                      selectImg={this.selectImg.bind(this)}
                      headImgLoading={this.state.headImgLoading}
                    />
                  )}
                  <CouldAskMe
                    className={(this.props.modalStyle == 'Simple' || this.props.modalStyle == 'agentOS') ? 'askme-rgscl' : ''}
                    {...this.state}
                    lang={this.props.lang}
                    skills={this.props.homeModule.skills}
                    robot_id={this.props.currentRobotId}
                    robotType={robotType}
                    config_id={this.props.config_id}
                    types={this.state.chooseQueTypes}
                    selectImg={this.selectImg.bind(this)}
                    checkJson={this.props.checkJson.bind(this)}
                    updatejson={this.props.updatejson}
                  ></CouldAskMe>
                  {/* <CouldAskMe
                                      {...this.state}
                                      lang={this.props.lang}
                                      types={this.state.schooseQueTypes}
                                      selectImg={this.selectImg.bind(this)}
                                    ></CouldAskMe> */}
                  <BottomQueries
                    className='bottom-rgscl'
                    queries_idx={this.props.queries_idx}
                    lang={this.props.lang}
                    bottomQueries={this.props.bottomQueries}
                    modalStyle={this.props.modalStyle}
                    checkJson={this.props.checkJson.bind(this)}
                  />
                  {is_dp_xm_mini ? <LanguageConfig lang={this.props.lang} /> : <></>}
                  <BackgroundImg
                    className='background-rgscl'
                    selectImg={this.selectImg.bind(this)}
                    // backImgLoading={this.state.backImgLoading}
                    restBackImg={this.restBackImg}
                    deleteBackImg={this.props.deleteBackImg}
                  />
                </div>
              </div>
            </TabPane>
            <TabPane tab={__('customHome.face.faceConf')} key='2'>
              <div className={isMini(robotType) ? 'index-set index-set-mini' : 'index-set'}>
                <div className='modules'>
                  {faceModules.map((item) => {
                    return (
                      <div
                        className={this.state.currModule == item.attr ? ' curr modu ' : ' modu '}
                        attr={item.attr}
                        onClick={(e) => {
                          let clickattr = e.target.attributes.attr.nodeValue
                          this.jump(clickattr, 'face-detail-roll')
                        }}
                      >
                        {item.name}
                        {this.state.currModule == item.attr ? (
                          <CheckCircleOutlined className='exp_set_nav_list-icon' />
                        ) : (
                          ''
                        )}
                      </div>
                    )
                  })}
                </div>
                <div className='module-detail face-detail-roll'>
                  {this.props.activeKey === '2' ? (
                    <BackImg
                      selectImg={this.selectImg.bind(this)}
                      deleteFaceImg={this.props.deleteFaceImg}
                      isDefault={this.props.isDefault}
                      isShowSetting={this.props.isShowSetting}
                      addCover={this.addCover.bind(this)}
                      deleteBackImg={this.deleteBackImg.bind(this)}
                      setIndexState={this.props.setIndexState}
                      changeTab_backImg={this.changeTab_backImg.bind(this)}
                      devUrl={this.props.devUrl}
                      robot_id={this.props.currentRobotId}
                      updateBubble1={(Bubble1) => {
                        this.props.updateBubble1(Bubble1)
                      }}
                      lang={this.props.langDataActive.lang_code}
                      key={this.props.langDataActive.lang_code}
                      index={1}
                    ></BackImg>
                  ) : null}
                </div>
              </div>
            </TabPane>
            {
              // 只在豹大屏的情况下显示
              robotType === 'bao_da_ping' || robotType === 'bxm_plus' || robotType === 'bxm_plus_in' ? (
                <TabPane tab={__('customHome.bigScreen.tabName')} key='3'>
                  <div className='bigScreenTip'>
                    {__('customHome.bigScreen.resolutionRatio')}
                    {sizeRule.width}*{sizeRule.height}
                  </div>
                  <div className='backImg'>
                    {this.props.homeModule && this.props.homeModule.templateInfo.bs_background && (
                      <img
                        src={`${this.props.devUrl}media/${this.props.homeModule.templateInfo.bs_background}`}
                        className='imgset 4'
                      />
                    )}
                    <span
                      onClick={() => {
                        let content = 'module_public/module_skill_home/baodaping_1.png'
                        if (robotType === 'bxm_plus' || robotType === 'bxm_plus_in') {
                          content = 'module_default/module_skill_home/bxmplus_1.png'
                        }
                        this.props.dispatch({
                          type: 'CHANGE_INFO',
                          data: {
                            type: 'bs_background',
                            content,
                          },
                        })
                      }}
                    >
                      {__('customHome.btn.reset')}
                    </span>
                  </div>
                  <Popover
                    content={
                      <div className='uploadTip'>
                        <div className='tips-container'>
                          <span className='tips-text'>{__('customHome.pic.imageFormat')}：JPG/PNG/JPEG/GIF</span>
                          <span className='tips-text'>
                            {__('customHome.pic.pictureSize')}：{sizeRule.width}px*{sizeRule.height}px
                          </span>
                        </div>
                      </div>
                    }
                  >
                    <div className='changBtn 2'>
                      <ImgCrop
                        quality={1}
                        modalWidth='650px'
                        aspect={sizeRule.width / sizeRule.height}
                        grid='grid'
                        modalTitle={__('customHome.btn.img_cut')}
                        modalOk={__('customHome.btn.confirm')}
                        modalCancel={__('customHome.btn.cancel2')}
                        fillColor={'transparent'}
                        beforeCrop={(file) => {
                          if (file.type === 'image/gif') {
                            let backImg = 'bs_background'
                            this.selectImg(file, backImg, '', '')
                            return false
                          }
                          return true
                        }}
                      >
                        <Upload
                          showUploadList={false}
                          beforeUpload={(e) => {
                            return this.uploadImg(e)
                          }}
                        >
                          <button> {__('CORPUS.UPDATE_PICTURE')}</button>
                        </Upload>
                      </ImgCrop>
                    </div>
                  </Popover>
                  <canvas
                    style={{ display: 'none' }}
                    id='mybackCanvas2'
                    width={sizeRule.width}
                    height={sizeRule.height}
                  />
                </TabPane>
              ) : null
            }
          </Tabs>
        </div>
      </div>
    )
  }
}
