@import '~base.less';

.component {
  background: #fff;
  position: relative;
  overflow: hidden;
  .change-temp {
    min-width: 160px;
    border: 1px solid #555d61;
    svg {
      width: 12px;
      height: 12px;
    }
  }

  .ant-upload-select-picture-card {
    opacity: 1 !important;
    height: 30px;
    width: 100px;
    border-radius: 14px;
    border: 1px solid #4881f0;
    span {
      color: #4881f0;
      line-height: 100%;
    }
    &:hover {
      opacity: 0.7 !important;
    }
  }
  .ant-upload-list {
    width: 100px;
    margin: 0 auto;
  }
  .miniBackImg-1 {
    width: 160px;
    .ant-upload-list {
      width: 160px;
      margin: 0 auto;
    }
  }

  .backImg-1 {
    width: 100px;
    .ant-upload-list {
      width: 100px;
      margin: 0 auto;
    }
  }

  .ant-upload-select-picture-card {
    margin: 0 auto;
  }
  .ant-upload-select-picture-card .ant-upload {
    line-height: 2;
    margin: 0 auto;
  }
  .is-appearance-underline .b9b097-input:focus {
    outline: 0;
    border-color: #3776ef;
  }
  .backImg_new .bubble_mode .icon {
    color: #555d61 !important;
  }

  .ImgCrop_icon_del {
    display: none;
    margin-left: 1.5rem;
    position: relative;
    top: 2px;
    cursor: pointer;
    color: #9b9b9b;
    &:hover {
      opacity: 0.5;
      color: #3776ef;
    }
  }
  .backImg_new {
    max-width: 32rem;
    width: 100%;
    box-sizing: border-box;
    overflow: auto;
    p.backImg_tips {
      font-size: 12px;
      margin-bottom: 10px;
      color: #99a3a8;
    }
    .defaultBackImg-div {
      position: relative;
    }
    .miniBackImg {
      position: relative;
      img {
        width: 160px;
        height: 90px;
      }
      p {
        width: 160px;
        height: 90px;
      }
      .img_span {
        top: 5rem;
        margin-top: -1.1rem;
      }
      .mini_defaultBackImg_new {
        height: 5rem !important;
      }
    }
    .img_span {
      display: block;
      height: 1.1rem;
      width: 5rem;
      font-size: 0.6rem;
      font-weight: 400;
      color: #fff;
      line-height: 1.1rem;
      background: #606060;
      top: 6.9rem;
      opacity: 0.74;
      text-align: center;
      position: absolute;
      cursor: pointer;
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;
      &:hover {
        opacity: 0.9;
      }
    }
    .miniimg_span {
      top: 34.55rem;
      width: 160px;
    }
    .mini_img_span {
      width: 160px;
      top: 3.4rem;
    }
    .changeTitle {
      .cardTitle_en {
        margin-top: 10px;
        input {
          width: 26rem;
        }
      }
    }

    .marked {
      margin-bottom: 28px;
      margin-left: 30px;

      label {
        font-size: 14px;
        color: #3776ef;
      }
    }

    .error {
      margin-top: 74px;
      display: inline-block;
      margin-right: 18px;
      text-align: center;

      p {
        color: #ff5a6e;
        font-size: 15px;
        margin-top: 11px;
      }
    }

    .correct {
      text-align: center;
      display: inline-block;

      p {
        font-size: 15px;
        color: #00cb9d;
        margin-top: 11px;
      }
    }

    .imageTips {
      width: 225px;
      height: 360px;
    }

    .bubble_mode {
      width: 160px;
      height: 30px;
      line-height: 10px;
      .icon {
        color: #555d61;
      }
    }

    .bgtitle {
      margin-top: 5px;
      display: block;
      vertical-align: top;
      color: #4a4a4a;
    }

    .list {
      display: block;
    }

    .face_temp {
      .title {
      }
      .title_h {
        display: inline-block;
        width: 64px;
        height: 22px;
        font-size: 16px;
        font-weight: 500;
        color: #4a4a4a !important;
        line-height: 22px;
        margin-bottom: 10px;
        width: 200px;
      }
      .list-c {
      }
      .list-c-x {
        // max-width: 30rem;
      }
      .con {
        margin-bottom: 20px;
        margin-top: 5px;
        button {
          line-height: 0.65rem;
          display: inline-block;
        }
      }
      .f_set {
        height: 28px;
        margin-top: 15px;
        margin-bottom: 10px;
        // min-width: 33rem;
        .f_set_l {
          width: 26.6rem;
          float: left;
          height: 28px;
          line-height: 28px;
        }
        .f_set_r {
          height: 28px;

          height: 1.4rem;
          width: 3rem;
          float: left;
          .f_set_r_btn {
            line-height: 0.65rem;
            //border: 1px solid #3776ef;
            //color: #3776ef;
            &:hover {
              //border: 1px solid #3776ef;
              //background-color: #fff;
              opacity: 0.7;
            }
          }
        }
      }
      p.marked {
        margin-top: 19px;
        background: #fffbe6;
        border: 1px solid #ffe58f;
        border-radius: 4px;
        white-space: nowrap;
        padding: 10px;
        text-align: left;
        display: inline-block;
        margin-bottom: 0;
        margin-top: 0;

        &:lang(en) {
          margin-left: 0;
          margin-top: 10px;
        }

        .marked_icon {
          color: #faad14;
          margin-right: 8px;
          vertical-align: middle;
          margin-top: -2px;
          margin-left: 8px;
        }

        span {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          letter-spacing: 0;
          white-space: nowrap;
        }
      }
    }

    .arabic_face_temp {
      .line_name {
        input {
          direction: rtl;
          padding-right: 90px;
        }
      }
    }
  }

  .changeQue,
  .changeTitle,
  .backImg_new {
    ul.nav {
      padding-left: 30px;
      padding-right: 30px;
      margin-bottom: 28px;
      border-bottom: 1px solid #e4e8ea;

      li {
        display: inline-block;
        font-size: 14px;
        color: #555d61;
        letter-spacing: 0;
        margin-right: 30px;
        padding-bottom: 4px;
        cursor: pointer;
      }

      .active {
        color: #3776ef;
        border-bottom: 2px solid #3776ef;
      }
    }

    .line_name {
      display: flex;
      align-items: center;
      position: relative;
      padding-right: 20px;
      // min-width: 30rem;

      .title {
        line-height: 2.3rem;
        margin-right: 15px;
        color: #9b9b9b;
        font-size: 14px;
        min-width: 60px;
      }
      .input-pad.is-appearance-underline{
        flex-grow: 1;
      }

      input {
        // width: 25.5rem;
        // flex-grow: 1;

        height: 36px;
        border-radius: 4px;
        border: 1px solid #e1e3e9;
        // position: absolute;
        top: 3px;
      }

      .count {
        line-height: 2.3rem;
        position: absolute;
        right: 3rem;
        height: 2.3rem;
      }

      .delete {
        line-height: 2.3rem;
        // position: absolute;
        // right: 2rem;
        margin-left: 10px;
        height: 2.3rem;
        color: grey;
        color: #3776ef;
        opacity: 0.8;
        &:hover {
          cursor: pointer;
          color: #3776ef;
          opacity: 1;
          svg {
            fill: #3776ef;
          }
        }
      }

      .bpreview {
        display: none;
        cursor: pointer;
        line-height: 2.3rem;
        position: absolute;
        right: 5rem;
        height: 2.3rem;
        color: grey;
        // display: flex;
        align-items: center;

        svg {
          fill: grey;
          margin-right: 3px;
          // position: absolute;
          // top: 50%;
          // transform: translateY(-50%);
          // right: 35px;
          // right: 64px;

          // &:lang(en) {
          //   right: 64px;
          // }

          // &:lang(ja) {
          //   right: 70px;
          // }
        }

        &:hover {
          color: #3776ef;

          svg {
            fill: #3776ef;
          }
        }
      }

      &:hover {
        .bpreview {
          display: flex;
        }
      }
    }
    .cardTitle_en {
      max-width: 33rem;
      overflow: hidden;
      margin-top: 10px;
      input {
        width: 26rem;
      }
      .count {
      }
    }

    div.listFunTitle {
      // margin-bottom: 0px !important;
      input {
        padding-right: 50px;
      }
    }

    div.list {
      margin-bottom: 30px;

      .input_tips {
        font-size: 12px;
        line-height: 20px;
        color: #ff713c;
        margin-bottom: 10px;
        margin-top: 10px;
        display: block;
      }

      .prompt {
        display: block;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        padding: 10px;
        border: solid 1px #91d5ff;
        background-color: #e6f7ff;
        margin-bottom: 26px;

        .prompt_tips {
          color: #1890ff;
          margin-right: 8px;
          vertical-align: middle;
          margin-top: -2px;
        }
      }

      .hide_input_tips {
        visibility: hidden;
      }

      .show_input_tips {
        visibility: visible;
      }

      .chooseStyle {
        font-size: 12px;
        color: #555d61;
        letter-spacing: 0;
        line-height: 16px;

        .chooseCircle {
          display: inline-block;
          width: 12px;
          height: 12px;
          border: 2px solid rgba(85, 195, 251, 0.5);
          border-radius: 100%;
          margin-right: 8px;
        }

        .blue {
          background: #3776ef;
        }

        label {
          margin-right: 40px;
          line-height: 22px;
        }

        input:checked {
          background: #3776ef;
          color: #3776ef;
        }
      }

      .newTitle {
        clear: both;
        display: inline-block;
        border-bottom: 1px solid #e4e8ea;
        width: 50%;

        input[type='text'] {
          border: none;
          width: 80%;
        }

        span {
          float: right;
          color: #3776ef;
          font-size: 16px;
          color: #3776ef;
          text-align: right;

          em {
            font-style: normal;
          }
        }
      }

      .arabic_newTitle {
        input {
          direction: rtl;
        }
      }

      .dropdown {
        background: rgba(161, 170, 178, 0.15);
        border-radius: 4px;

        button {
          line-height: 26px;
          height: 26px;
          font-size: 12px;
          color: #555d61;
          letter-spacing: 0;
          line-height: 16px;
        }

        .txt {
          display: inline-block;
          padding-right: 67px;
        }

        .menu-container {
          .menu {
            width: 100px;
            text-align: center;

            .menu-item {
              font-size: 14px;
              color: #555d61;
            }

            .menu-item:hover {
              color: #3776ef;
              background: #f3f5f6;
            }
          }
        }
      }

      .query-set {
        display: inline-block;
        width: 50%;

        .queryInput {
          width: 100%;
          // padding-left: 14px;
          padding-right: 50px;
        }

        ul {
          position: absolute;
          width: 50%;
          background: #fff;
          max-height: 300px;
          // padding: 14px;
          padding-bottom: 0;
          margin-top: -1px;
          box-shadow: 0 2px 6px 2px rgba(161, 170, 178, 0.15);
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
          overflow-y: scroll;

          li {
            cursor: pointer;
            padding-left: 14px;
            line-height: 36px;

            &:hover {
              background-color: #f3f5f6;
            }
          }
        }

        .scroll-setting::-webkit-scrollbar {
          width: 5px;
          height: 10px;
        }

        .scroll-setting::-webkit-scrollbar-track {
          -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          border-radius: 10px;
          background-color: #b5b1b1;
        }

        .scroll-setting::-webkit-scrollbar-thumb {
          border-radius: 10px;
          -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          background-color: black;
        }
      }

      select.chooseSetting {
        background: rgba(161, 170, 178, 0.15);
        border-radius: 4px;
        border: none;
        outline: none;
        font-size: 12px;
        color: #555d61;
        letter-spacing: 0;
        line-height: 16px;
        padding: 5px 9px;
      }

      select.chooseSetting:hover {
        option {
          background: #fff;
          border: none;
          line-height: 50px;
          height: 50px;
          text-align: center;
        }
      }

      select.chooseQuestion {
        width: 50%;
        border: none;
        border-bottom: 1px solid #e4e8ea;
        outline: none;
        font-size: 16px;
        color: #555d61;
        letter-spacing: 0;
        line-height: 16px;
        padding-bottom: 11px;
      }

      option {
        text-align: center;
      }

      .title {
        font-size: 16px;
        color: #3776ef;
        letter-spacing: 0;
        margin-right: 14px;
        float: left;
      }

      .simple_title {
        font-size: 16px;
        color: #3776ef;
        letter-spacing: 0;
        margin-right: 14px;
        // float: left;
        display: block;
        margin-bottom: 10px;
        margin-top: 24px;
      }

      .fon_list_area {
        color: #4a4a4a;
        font-weight: 400;
      }

      .word-len-tips {
        display: inline-block;
        width: 70px;
        color: #3776ef;
        font-size: 0.8rem;
        text-align: right;
        margin-left: -70px;
      }

      .list_area {
        display: block;
        margin-right: 50px;

        span {
          display: block;
        }

        .circle {
          border-radius: 100%;
        }

        .square {
          border-radius: 8px;
        }

        .upload-btn {
          width: 80px;
          display: inline-block;
          height: 20px;

          /*   width: 57px;
                            height: 57px;
                            background: rgba(0, 146, 245, 0.20) !important;
                            position: relative;
                            margin: 0 auto;
                            z-index: 10;

                            &:before {
                                display: block;
                                content: '';
                                width: 21px;
                                height: 2px;
                                background: @color-theme;
                                position: absolute;
                                left: 17px;
                                top: 27px;
                            }

                            &:after {
                                display: block;
                                content: '';
                                width: 2px;
                                height: 21px;
                                background: @color-theme;
                                position: absolute;
                                left: 27px;
                                top: 17px;
                            }
*/
        }

        .defaultBackImg_new {
          width: 100px;
          height: 160px;
          border-radius: 4px;
          margin: 0 auto;
          text-align: center;
          background: #000;
          vertical-align: top;
          margin-left: 0px;
          margin-top: 5px;
          margin-bottom: 5px;
        }
        .mini_defaultBackImg_new {
          width: 160px;
          height: 90px;
        }
        .default_new_span {
          height: 20px;
          width: 100px;
          border: 1px solid #3776ef;
          text-align: center;
          vertical-align: middle;
        }

        .simpleBackImg {
          width: 58px;
          height: 58px;
          border: 3px solid #3776ef;
          border-radius: 8px;
          margin: 0 auto;
          text-align: center;
          // background:url(http://test-jiedai.ainirobot.com/media/module_public/module_skill_home/conciseimage.png);
          background-size: cover;
          background-repeat: none;
          vertical-align: top;
          margin-left: 5px;
        }

        .defaultImg {
          width: 57px;
          border: 3px solid #3776ef;
          border-radius: 100%;
        }

        .circleImg {
          height: 57px;
          border-radius: 100%;
        }

        .squareImg {
          height: 30px;
          border-radius: 8px;
          margin-bottom: 10px;
        }

        .rectangleImg1 {
          width: 57px;
          height: 40px;
          border-radius: 8px;
          margin-bottom: 8px;
        }

        .rectangleImg2 {
          width: 57px;
          height: 31px;
          border-radius: 8px;
          margin-bottom: 10px;
        }

        .rectangleImg3 {
          width: 46px;
          height: 57px;
          border-radius: 8px;
          margin-bottom: -2px;
        }

        span.uploadInfo {
          font-size: 12px;

          letter-spacing: 0;
          text-align: center;
          margin-top: 3px;
          color: #3776ef;
        }

        span.defaultInfo {
          font-size: 12px;
          // color: #3776ef;
          color: #99a3a8;
          letter-spacing: 0;
          text-align: center;
          margin-top: 3px;
        }

        span.uploadTips {
          font-size: 12px;
          color: #bcc4ca;
        }
      }
      .upload_area-q {
      }
      .simple_list_area {
        position: relative;
        margin-right: 32px;
        .simpleIcon_default {
          width: 57px;
          height: 57px;
          border-radius: 8px;
          top: 0px;
          float: left;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      .default_area {
        cursor: pointer;
        text-align: center;
      }

      .newIcon_default {
        width: 100px;
        height: 160px;
        border-radius: 0.2rem;
        margin-bottom: 5px;
      }
      .minicss {
        width: 160px;
        height: 100px;
        border-radius: 0.2rem;
        margin-bottom: 5px;
      }

      input[type='text'] {
        border: none;
        border-bottom: 1px solid #e4e8ea;
        font-size: 16px;
        color: #555d61;
        line-height: 21px;
        width: 50%;
        outline: none;
        padding-bottom: 11px;
      }

      input::placeholder {
        font-size: 16px;
        color: #555d61;
      }
    }
  }

  .arabic_changeQue {
    div.list {
      position: relative;

      input[type='text'] {
        direction: rtl;
      }

      .word-len-tips {
        margin-left: 0;
        position: absolute;
        display: inline-block;
      }
    }
  }

  .sort-container {
    display: flex;

    .query-sort-container {
      display: flex;
    }
  }

  .bottomsort-container {
    ul.nav {
      padding-left: 30px;
      padding-right: 30px;
      margin-bottom: 28px;
      border-bottom: 1px solid #e4e8ea;

      li {
        display: inline-block;
        font-size: 14px;
        color: #555d61;
        letter-spacing: 0;
        margin-right: 30px;
        padding-bottom: 4px;
        cursor: pointer;
        margin-top: 12px;
      }

      .active {
        color: #3776ef;
        border-bottom: 2px solid #3776ef;
      }
    }

    .no_queries {
      color: #f00;
      font-size: 14px;
      margin-left: 30px;
    }

    .sort_tips {
      font-size: 12px;
      color: #555d61;
      letter-spacing: 0;
      padding-left: 30px;
      padding-right: 30px;
    }

    .sort {
      label {
        margin-right: 26px;
        font-size: 14px;
        color: #979797;
      }
    }

    .sort {
      width: 450px;
    }
  }

  .ver-line {
    width: 1px;
    background-color: #eff1f2;
  }

  .sort {
    width: 340px;
    min-width: 340px;
    padding-left: 30px;
    padding-right: 30px;

    .sort-title {
      font-size: 24px;
      color: #3776ef;
      letter-spacing: 0;
      height: 33px;
      line-height: 33px;
      margin-bottom: 15px;
    }

    .sort_txt {
      display: flex;

      span.txt {
        flex: 1;
        display: inline-block;
        height: 44px;
        background: rgba(85, 195, 251, 0.2);
        border-radius: 4px;
        text-align: center;
        line-height: 44px;
        font-size: 16px;
        color: #3776ef;
        letter-spacing: 0;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      span.visited {
        margin-left: 10px;
      }
    }

    .sort_add {
      height: 44px;
      line-height: 44px;
      background: #3776ef;
      border-radius: 4px;
      margin-top: 10px;
      margin-bottom: 10px;
      font-size: 16px;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
    }

    .sort_area1,
    .sort_area2 {
      position: relative;
      z-index: 9;

      .sort_info {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        width: 100%;
        height: 44px;
        line-height: 44px;
        background: rgba(85, 195, 251, 0.2);
        border-radius: 4px;
        margin-bottom: 10px;
        padding-left: 30px;
        padding-right: 30px;
        position: relative;
        transition: initial;

        .sort_content {
          font-size: 16px;
          color: #3776ef;
          letter-spacing: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 70%;
          display: inline-block;
        }

        .sort_switch {
          width: 33px;
          height: 20px;
          border-style: solid;
          border-color: #fff;
          border-width: 2px;
          border-radius: 15.33px;
          position: absolute;
          right: 63px;
          top: 50%;
          transform: translateY(-50%);

          em {
            width: 16px;
            height: 16px;
            background: #ffffff;
            position: absolute;
            right: 2px;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 50%;
          }

          .switch_on {
            right: 2px;
          }

          .switch_off {
            left: 2px;
          }
        }

        .sort_button {
          display: inline-block;
          margin-top: 17px;
          width: 14px;
          height: 10px;
          // display:inline-block;
          // float:right;
          // margin-top:17px;
          // width: 14px;
          // height: 10px;
          // border-top: 2px solid #3776ef;
          // border-bottom: 2px solid #3776ef;
          // background-color: #3776ef;
          // padding: 2px 0;
          // background-clip:content-box
        }
      }
    }

    .sort_area1 {
      .sort_info {
        color: #3776ef;

        .icon {
          width: 16px;
          height: 16px;
          position: absolute;
          right: 63px;
          top: 50%;
          transform: translateY(-50%);
        }

        .toright {
          right: 5.5rem;
          // display:none;
        }

        .icon_edit {
          width: 16px;
          height: 16px;
          position: absolute;
          right: 100px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }

    .sort_area2 {
      width: 380px;

      .sort_info {
        width: 150px;
        height: 44px;
        line-height: 44px;
        padding-left: 15px;
        padding-right: 15px;
        display: inline-block;
        margin-right: 10px;
        position: relative;

        .sort_content {
          font-size: 14px;
        }

        .sort_switch {
          right: 43px;
        }
      }
    }

    .sort_area1,
    .sort_area2 {
      .gray {
        position: relative;
        background: #eff1f2;

        .sort_content {
          color: #bcc4ca;
        }
      }
    }
  }

  .sort2 {
    width: 580px;

    .sort_add {
      width: 510px;
    }

    .sort_area2 {
      width: auto;

      .sort_info {
        width: 250px;
      }
    }
  }
}
.input-pad {
  input {
    padding-right: 80px;
  }
}
