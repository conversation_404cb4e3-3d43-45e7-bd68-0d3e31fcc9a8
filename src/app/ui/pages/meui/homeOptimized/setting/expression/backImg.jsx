import React from 'react'
import { connect } from 'react-redux'
import classNames from 'classnames'
import { extend } from 'koot'
import { Radio } from 'biz-components'
import { Input } from 'biz-components'
import { Icon } from 'biz-components'
import { Upload, Button, message, Popover } from 'antd'
import { SwapOutlined } from '@ant-design/icons'
import ImgCrop from 'antd-img-crop'
import _ from 'lodash'

import modal, { close as closeModal } from '@utils/modal'
import { getImageSize } from '@utils/image-size'
import {
  checkReleaseRaw,
  isMini,
  helpQaList,
  inputMaxCnBubble,
  inputMaxEnBubble,
  getQueryVal,
  onLangLen,
} from '@utils/tools'
import ChooseFaceTemplate from './chooseFaceTemplate'
import CardTitle from '../../cardTitle'

@connect((state) => {
  return {
    templateInfo: state._home.homeModule.templateInfo,
    robotId: state.robots.current,
    robots: state.robots.robots,
    langDataActive: state.user.langDataActive,
  }
})
@extend({
  styles: require('./index.less'),
})
class BackImg extends React.Component {
  askMe = ''
  helpMe = ''
  cardTitle = ''
  state = {
    tabIndex: 1,
    nav_content: [__('customHome.btn.homepageBackground'), __('customHome.label.expressions')],
    haveCover: this.props.templateInfo.haveCover == undefined ? true : this.props.templateInfo.haveCover,
    version13: false,
    overVersion51: false,
    overVersion57: false,
    sugg1: '',
    style: 'Standard',
    faceStyle: this.props.templateInfo.bubbleContents ? this.props.templateInfo.bubbleContents.faceStyle : 'Bubble',
    rawbubbleContents: [],
    cardTitle: this.props.templateInfo.bubbleContents ? this.props.templateInfo.bubbleContents.title : this.cardTitle,
    placeholder1: __('customHome.qa.askMeQuestions'),
    placeholder2: __('customHome.qa.helpWarm'),
    maxLength:
      this.props.lang == 'zh_CN' || this.props.lang == 'zh_GD' || this.props.lang == 'zh_TW'
        ? inputMaxCnBubble
        : inputMaxEnBubble,
    cardTitleMaxLength:
      this.props.lang == 'zh_CN' || this.props.lang == 'zh_GD' || this.props.lang == 'zh_TW'
        ? inputMaxCnBubble
        : inputMaxEnBubble,
    initDateEnglishWarmarrayTxt: [
      'What can you do?',
      'Where is the restroom?',
      'What’s your name?',
      'Where is the meeting room?',
      'What are you doing here?',
      'How old are you?',
    ],
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    console.log({ props11: this.props })

    if (this.props.lang && nextProps.lang && nextProps.lang != this.props.lang) {
      this.setState({
        lang: nextProps.lang,
      })
    }
    if (this.props.templateInfo && this.props.templateInfo.bubbleContents) {
      if (
        nextProps.templateInfo.bubbleContents &&
        nextProps.templateInfo.bubbleContents != this.props.templateInfo.bubbleContents
      ) {
        //数据有更新
        this.setState({
          faceStyle: nextProps.templateInfo.bubbleContents.faceStyle,
          raw: nextProps.templateInfo.bubbleContents.raw,
          robot: nextProps.templateInfo.bubbleContents.robot,
          title: nextProps.templateInfo.bubbleContents.title,
        })
      } else if (
        nextProps.templateInfo.bubbleContents == undefined &&
        nextProps.templateInfo.bubbleContents != this.props.templateInfo.bubbleContents
      ) {
        // 数据重置
        this.setState(
          {
            faceStyle: this.props.lang === 'en_US' ? 'Card' : 'Bubble',
            raw:
              this.props.lang !== 'en_US' ? [this.askMe, this.helpMe] : ['What can you do?', 'Where is the restroom?'],
            robot:
              this.props.lang !== 'en_US' ? [this.askMe, this.helpMe] : ['What can you do?', 'Where is the restroom?'],
            rawbubbleContents:
              this.props.lang !== 'en_US' ? [this.askMe, this.helpMe] : ['What can you do?', 'Where is the restroom?'],
            title: this.cardTitle,
          },
          () => {
            let rawbubbleContents =
              this.props.lang !== 'en_US' ? [this.askMe, this.helpMe] : ['What can you do?', 'Where is the restroom?']

            this.props.updateBubble1('')
            let bubbleContents = {
              faceStyle: this.props.lang === 'en_US' ? 'Card' : 'Bubble',
              robot: rawbubbleContents.filter((e) => e),
              raw: rawbubbleContents,
              title: this.cardTitle,
            }

            this.props.dispatch({
              type: 'CHANGE_INFO',
              data: {
                type: 'bubbleContents',
                content: bubbleContents,
              },
            })
          },
        )
      }
    }
  }
  componentDidMount() {
    this.props.addCover(this.state.haveCover)
    var localrobotId = this.props.robotId || localStorage.getItem('globallRobotId')
    Object.keys(this.props.robots).map((el) => {
      if (localrobotId == el) {
        let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
        let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
        let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)

        this.setState({
          overVersion51,
          overVersion57,
        })
        console.log(this.props.robots[el].version, versionGap)
        console.log('auto change info')
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'haveCover',
            content: this.state.haveCover,
          },
        })
        if (versionGap) {
          //4.13
          this.setState({
            version13: true,
          })
        } else {
          //非4.13
          this.setState({
            version13: false,
          })
        }
      }
    })

    if (!this.props.templateInfo.bubbleContents) {
      let rawbubbleContents = this.state.rawbubbleContents
      let bubbleContents = {
        faceStyle: this.state.faceStyle,
        robot: rawbubbleContents.filter((e) => e),
        raw: rawbubbleContents,
        title: this.state.cardTitle,
      }

      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'bubbleContents',
          content: bubbleContents,
        },
      })
    }
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1 ? true : false

    // 设置语言
    const { langDataActive, templateInfo } = this.props
    const lang_code = langDataActive.lang_code
    const robotType = getQueryVal('robotType')

    const sole = _.find(helpQaList, { key: lang_code })
    console.log('设置语言', { sole, templateInfo, helpQaList, props: this.props, lang_code: lang_code })
    if (robotType === 'bxm_plus' || robotType === 'bxm_plus_in') {
      if (lang_code === 'en_US') {
        sole.askMe = 'Where is the washroom?'
        sole.helpMe = 'What can you do?'
      } else if (lang_code === 'zh_GD') {
        sole.askMe = '有咩問題都可以問我呀'
        sole.helpMe = '廁所喺邊度?'
      } else if (lang_code === 'zh_TW') {
        sole.askMe = '需要幫忙嗎'
        sole.helpMe = '有問題可以問我'
      }
    }
    this.askMe = sole.askMe
    this.helpMe = sole.helpMe
    this.cardTitle = sole.cardTitle
    //
    let arrRaw = []
    if (templateInfo.bubbleContents) {
      if (templateInfo.bubbleContents.raw.length) {
        arrRaw = templateInfo.bubbleContents.raw
      } else {
        arrRaw = [sole.askMe, sole.helpMe]
      }
    } else {
      arrRaw = [sole.askMe, sole.helpMe]
    }
    console.log('todo11111', { arrRaw, sole, templateInfo })
    let bubbleContents = {
      faceStyle: this.state.faceStyle,
      robot: arrRaw.filter((e) => e.trim()),
      raw: arrRaw,
      title: this.state.cardTitle,
    }

    this.setState(
      {
        rawbubbleContents: arrRaw,
        initDateEnglishWarmarrayTxt: sole.initDateEnglishWarmarrayTxt,
      },
      () => {
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'bubbleContents',
            content: bubbleContents,
          },
        })
      },
    )
  }
  componentDidUpdate(prevProps) {
    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }
  }
  chooseFaceStyle(faceStyle) {
    this.setState(
      {
        faceStyle,
      },
      () => {
        closeModal()
        let rawbubbleContents = this.state.rawbubbleContents
        let bubbleContents = {
          faceStyle,
          robot: rawbubbleContents.filter((e) => e),
          raw: rawbubbleContents,
          title: this.state.cardTitle,
        }

        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'bubbleContents',
            content: bubbleContents,
          },
        })
      },
    )
  }

  render() {
    const { langDataActive } = this.props
    let image_url = this.props.devUrl + 'media/'
    let backgroundImg = ''
    if (this.props.templateInfo.templateCode == 'List') {
      backgroundImg = 'module_public/module_skill_home/home1.png'
    } else if (this.props.templateInfo.templateCode == 'Card') {
      backgroundImg = 'module_public/module_skill_home/home2.png'
    } else if (this.props.templateInfo.templateCode == 'Standard') {
      backgroundImg = 'module_public/module_skill_home/home4.png'
    } else if (this.props.templateInfo.templateCode == 'Simple') {
      if (!this.isKTV) {
        backgroundImg = 'module_public/module_skill_home/simpleBg.png'
      } else {
        backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
      }
      if (this.props.templateInfo.scenes == 'KTV' && this.props.templateInfo.templateCode == 'Simple') {
        backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
      }
    }

    let robotType_1 = getQueryVal('robotType')
    let sizeRule = getImageSize(robotType_1, this.props.templateInfo.templateCode, 0, 'faceBg')
    let w = sizeRule.width // isMini(robotType_1) ? 1920 : 1200
    let h = sizeRule.height // isMini(robotType_1) ? 1080 : 1920
    let type = isMini(robotType_1) ? 'miniBackImg' : 'backImg'
    console.log('page_meui_backImg_render', { state: this.state, props: this.props })
    return (
      <div className={this.props.className}>
        <div className='backImg_new'>
          {this.state.version13 &&
            this.state.tabIndex == 0 &&
            this.props.templateInfo.templateCode == 'Standard' &&
            (!this.props.isShowSetting || !this.props.isDefault) && (
              <div className='marked'>
                <Radio
                  name='sort'
                  label={__('customHome.pic.picWarm')}
                  checked={this.state.haveCover == true}
                  onClick={() => {
                    this.setState(
                      {
                        haveCover: !this.state.haveCover,
                      },
                      () => {
                        this.props.addCover(this.state.haveCover)
                        this.props.dispatch({
                          type: 'CHANGE_INFO',
                          data: {
                            type: 'haveCover',
                            content: this.state.haveCover,
                          },
                        })
                      },
                    )
                  }}
                />
              </div>
            )}

          {/** 背景图片开始 */}

          {/** 背景图片结束 */}
          {this.state.tabIndex == 0 && (
            <div>
              <div className='error'>
                <img src={require('@assets/png/meui/error.png')} className='imageTips' />
                <p>{__('customHome.pic.wrong')}</p>
              </div>
              <div className='correct'>
                <img src={require('@assets/png/meui/correct.png')} className='imageTips' />
                <p>{__('customHome.pic.correct')}</p>
              </div>
            </div>
          )}
          <canvas style={{ display: 'none' }} id='mybackCanvasFace' width={w} height={h} />
          {((this.state.overVersion51 && this.state.tabIndex == 1 && this.props.lang === 'zh_CN') ||
            (this.state.overVersion57 && this.state.tabIndex == 1 && this.props.lang !== 'zh_CN')) && (
            <div className={classNames({ face_temp: true })}>
              <div className='nowFace-rgscl'>
                {this.props.lang === 'zh_CN' && (
                  <CardTitle
                    title={__('customHome.face.currentTemplate')}
                    tips={__('customHome.pic.bubbleWarm')}
                  ></CardTitle>
                )}
                {this.props.lang !== 'zh_CN' && (
                  <CardTitle
                    title={__('customHome.face.currentTemplate')}
                    tips={__('customHome.pic.cardWarm')}
                  ></CardTitle>
                )}
              </div>
              <div className='con'>
                <Button
                  shape='round'
                  onClick={() => {
                    modal({
                      content: (
                        <ChooseFaceTemplate
                          FaceStyle={this.state.faceStyle}
                          chooseFaceStyle={this.chooseFaceStyle.bind(this)}
                          lang={this.props.lang}
                        />
                      ),
                      confirm: false,
                      cancel: false,
                    })
                  }}
                >
                  <SwapOutlined />
                  {this.state.faceStyle == 'Bubble'
                    ? __('customHome.pic.bubble')
                    : this.state.faceStyle == 'Card'
                    ? __('customHome.pic.card')
                    : __('customHome.pic.simple')}
                  ({langDataActive.name})
                </Button>
                {/*提示开始*/}
                {this.props.lang === 'zh_CN' && (
                  <p className='marked'>
                    <Icon icon='Alert_Default_Warning' className='marked_icon' />
                    <span>{__('customHome.pic.bubbleWarm')}</span>
                  </p>
                )}
                {this.props.lang === 'en_US' && (
                  <p className='marked'>
                    <Icon icon='Alert_Default_Warning' className='marked_icon 2' />
                    <span>{__('customHome.pic.cardWarm')}</span>
                  </p>
                )}

                {/*提示结束英文卡片标题开始*/}

                {this.state.overVersion57 && this.state.faceStyle == 'Card' && (
                  <div className='cardTitle line_name cardTitle_en'>
                    <div className='title'>{__('customHome.qa.cardTitle')}</div>
                    <Input
                      type='text'
                      placeholder={this.state.cardTitle}
                      maxLength={onLangLen(this.props.lang, 'bubble')}
                      value={this.state.cardTitle}
                      onChange={(e) => {
                        let txt = e.target.value
                        const rawbubbleContents = this.state.rawbubbleContents
                        let bubbleContents = {
                          faceStyle: this.state.faceStyle,
                          robot: rawbubbleContents,
                          raw: rawbubbleContents,
                          title: txt.trim(),
                        }
                        this.setState(
                          {
                            rawbubbleContents,
                            cardTitle: txt,
                          },
                          () => {
                            this.props.dispatch({
                              type: 'CHANGE_INFO',
                              data: {
                                type: 'bubbleContents',
                                content: bubbleContents,
                              },
                            })
                          },
                        )
                      }}
                      onFocus={() => {
                        this.setState({
                          focusIdx: 'cardTitle',
                        })
                      }}
                      onBlur={() => {
                        this.setState({
                          focusIdx: null,
                        })
                      }}
                    />

                    <span className='count'>{`${this.state.cardTitle.length}/${onLangLen(
                      this.props.lang,
                      'bubble',
                    )}`}</span>
                  </div>
                )}
              </div>
              {(this.state.overVersion51 && this.state.faceStyle == 'Bubble') ||
              (this.state.overVersion57 && this.state.faceStyle == 'Card') ? (
                <div className='list-c'>
                  <div className='mtitleFace-rgscl'>
                    <CardTitle title={__('customHome.face.copywriterConf')}></CardTitle>
                  </div>
                  <div className='list-c-x'>
                    <div className='f_set'>
                      <div className='f_set_l'>{__('customHome.face.qaFrame')}</div>
                      <div className='f_set_r'>
                        <Button
                          size='small'
                          appearance='hollow'
                          onClick={(e) => {
                            let { rawbubbleContents } = this.state
                            if (rawbubbleContents.length >= 6) {
                              message.error(__('customHome.face.qaFrameNum'))
                              return false
                            }
                            rawbubbleContents.push('')
                            this.setState({
                              rawbubbleContents: rawbubbleContents,
                            })
                          }}
                          className='f_set_r_btn'
                        >
                          {__('VIP_MANAGEMENT.NEW')}
                        </Button>
                      </div>
                    </div>

                    {((this.state.overVersion51 && this.state.faceStyle == 'Bubble') ||
                      (this.state.overVersion57 && this.state.faceStyle == 'Card')) &&
                      this.state.rawbubbleContents.map((item, index) => {
                        return (
                          <div className='line_name 2'>
                            <div className='title'>{__('customHome.face.copywriter')}</div>
                            <Input
                              className='input-pad'
                              type='text'
                              key={index}
                              placeholder={
                                this.state.faceStyle == 'Card'
                                  ? item === ''
                                    ? this.state.initDateEnglishWarmarrayTxt[index]
                                    : item
                                  : index % 2 == 0
                                  ? this.state.placeholder1
                                  : this.state.placeholder2
                              }
                              maxLength={onLangLen(this.props.lang, 'bubble')}
                              value={item}
                              onChange={(e) => {
                                let text = e.target.value
                                let rawbubbleContents = this.state.rawbubbleContents
                                rawbubbleContents[index] = text
                                // if (text.length > this.state.maxLength) {
                                //   return false
                                // }
                                if (this.props.lang === 'en_US') {
                                  this.props.updateBubble1(text)
                                } else {
                                  if (index != 1) {
                                    this.props.updateBubble1(text)
                                  }
                                }

                                let bubbleContents = {
                                  faceStyle: this.state.faceStyle,
                                  robot: rawbubbleContents.filter((e) => e.trim()),
                                  raw: rawbubbleContents,
                                  title: this.state.cardTitle,
                                }
                                this.setState(
                                  {
                                    rawbubbleContents,
                                  },
                                  () => {
                                    this.props.dispatch({
                                      type: 'CHANGE_INFO',
                                      data: {
                                        type: 'bubbleContents',
                                        content: bubbleContents,
                                      },
                                    })
                                  },
                                )
                              }}
                              onFocus={() => {
                                this.setState({
                                  focusIdx: index,
                                })
                              }}
                              onBlur={() => {
                                this.setState({
                                  focusIdx: null,
                                })
                              }}
                            />
                            <span className='count'>{`${(item + '').length}/${onLangLen(
                              this.props.lang,
                              'bubble',
                            )}`}</span>
                            <div
                              className='bpreview'
                              onClick={() => {
                                let rawbubbleContents = this.state.rawbubbleContents
                                let text = rawbubbleContents[index]
                                if (this.props.lang !== 'en_US') {
                                  if (index != 1) {
                                    this.props.updateBubble1(text)
                                  }
                                } else {
                                  this.props.updateBubble1(text)
                                }
                              }}
                            >
                              <Icon icon='guide_editor_preview_b' />
                              <span>{__('customHome.btn.preview')}</span>
                            </div>
                            {
                              <Icon
                                icon='rd_trash'
                                className='delete'
                                onClick={() => {
                                  let rawbubbleContents = this.state.rawbubbleContents
                                  if (rawbubbleContents.length <= 1) {
                                    message.error(__('customHome.face.qaFrameDeleNum'))
                                    return false
                                  }
                                  rawbubbleContents.splice(index, 1)
                                  let bubbleContents = {
                                    faceStyle: this.state.faceStyle,
                                    robot: rawbubbleContents.filter((e) => e),
                                    raw: rawbubbleContents,
                                    title: this.state.cardTitle,
                                  }
                                  this.setState(
                                    {
                                      rawbubbleContents,
                                    },
                                    () => {
                                      this.props.dispatch({
                                        type: 'CHANGE_INFO',
                                        data: {
                                          type: 'bubbleContents',
                                          content: bubbleContents,
                                        },
                                      })
                                    },
                                  )
                                }}
                              />
                            }
                          </div>
                        )
                      })}
                  </div>
                </div>
              ) : null}
            </div>
          )}

          {/** 背景新地方 */}

          {this.state.tabIndex == 1 && (
            <div className='bgtitle backgroundFace-rgscl'>
              <CardTitle title={__('customHome.pic.backgroundPicture')}></CardTitle>
            </div>
          )}
          <p className='backImg_tips'>{__('customHome.pic.backgroundWarm2')}</p>
          <div className='list'>
            <p
              className={
                (this.props.templateInfo.background != backgroundImg && this.state.tabIndex == 0) ||
                (this.props.templateInfo.faceBackground &&
                  this.props.templateInfo.faceBackground != '' &&
                  this.state.tabIndex == 1)
                  ? 'list_area upload_area'
                  : 'list_area upload_area-q'
              }
            >
              {this.state.tabIndex == 1 &&
              this.props.templateInfo.faceBackground &&
              this.props.templateInfo.faceBackground != '' ? (
                <div className={isMini(robotType_1) ? 'miniBackImg' : 'defaultBackImg-div 1'}>
                  <img
                    src={image_url + this.props.templateInfo.faceBackground}
                    className={isMini(robotType_1) ? 'newIcon_default minicss' : 'newIcon_default'}
                  />
                  <span
                    onClick={() => {
                      if (this.props.templateInfo.templateCode == 'List') {
                        backgroundImg = 'module_public/module_skill_home/home1.png'
                      } else if (this.props.templateInfo.templateCode == 'Card') {
                        backgroundImg = 'module_public/module_skill_home/home2.png'
                      } else if (this.props.templateInfo.templateCode == 'Standard') {
                        backgroundImg = 'module_public/module_skill_home/home4.png'
                      } else if (this.props.templateInfo.templateCode == 'Simple') {
                        // backgroundImg = "module_public/module_skill_home/simpleBg.png";

                        if (!this.isKTV) {
                          backgroundImg = 'module_public/module_skill_home/simpleBg.png'
                        } else {
                          backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
                        }

                        if (
                          this.props.templateInfo.scenes == 'KTV' &&
                          this.props.templateInfo.templateCode == 'Simple'
                        ) {
                          backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
                        }
                      }
                      if (this.state.tabIndex == 0) {
                        this.props.dispatch({
                          type: 'CHANGE_INFO',
                          data: {
                            type: 'background',
                            content: backgroundImg,
                          },
                        })
                        this.props.deleteBackImg()
                      } else {
                        this.props.dispatch({
                          type: 'CHANGE_INFO',
                          data: {
                            key: 11,
                            type: 'faceBackground',
                            content: '',
                          },
                        })
                        this.props.deleteBackImg()
                        this.props.deleteFaceImg()
                        //this.props.faceBackground();  暂时注释掉  上级组建调用没有绑定这个函数
                      }
                    }}
                    className={isMini(robotType_1) ? 'img_span miniimg_span' : 'img_span'}
                  >
                    {__('customHome.btn.reset')}
                  </span>
                </div>
              ) : (
                <div className={isMini(robotType_1) ? 'defaultBackImg-div miniBackImg' : 'defaultBackImg-div 2'}>
                  <p
                    className={
                      isMini(robotType_1) ? 'defaultBackImg_new mini_defaultBackImg_new' : 'defaultBackImg_new'
                    }
                  ></p>
                  <span className={isMini(robotType_1) ? 'img_span mini_img_span' : 'img_span'}>
                    {__('customHome.btn.reset')}
                  </span>
                </div>
              )}
              <Popover
                content={
                  <div className='uploadTip'>
                    <div className='tips-container'>
                      <span className='tips-text'>{__('customHome.pic.imageFormat')}：JPG/PNG/JPEG</span>
                      <span className='tips-text'>
                        {__('customHome.pic.pictureSize')}：{sizeRule.width}px*{sizeRule.height}px
                      </span>
                    </div>
                  </div>
                }
              >
                <div className={isMini(robotType_1) ? 'miniBackImg-1' : 'backImg-1'}>
                  <ImgCrop
                    quality={1}
                    aspect={w / h}
                    grid
                    modalTitle={__('customHome.btn.img_cut')} //
                    modalWidth='650px'
                    modalOk={__('customHome.btn.confirm')}
                    modalCancel={__('customHome.btn.cancel2')}
                    fillColor={'transparent'}
                    beforeCrop={(file) => {
                      // if (file.type === 'image/gif') {
                      //   let robotType_1 = getQueryVal('robotType')
                      //   let backImg = isMini(robotType_1) ? 'minifacebackImg' : 'facebackImg'
                      //   this.props.selectImg(file, backImg, '')
                      //   return false
                      // }
                      return true
                    }}
                  >
                    <Upload
                      customRequest={() => {}}
                      className='upload-btn square'
                      listType='picture-card'
                      beforeUpload={(e) => {
                        console.log(e)
                        let _this = this
                        let img = e
                        let reader = new FileReader()
                        reader.readAsDataURL(img)
                        reader.onload = function (e) {
                          let image = new Image()
                          image.onload = function () {
                            let mycanvas = document.querySelector('#mybackCanvasFace')
                            let ctx = mycanvas.getContext('2d')
                            ctx.clearRect(0, 0, w, h)
                            ctx.drawImage(image, 0, 0, w, h)
                            let dataurl = mycanvas.toDataURL('image/png')
                            function dataURLtoBlob(dataurl) {
                              let arr = dataurl.split(','),
                                mime = arr[0].match(/:(.*?);/)[1],
                                bstr = atob(arr[1]),
                                n = bstr.length,
                                u8arr = new Uint8Array(n)
                              while (n--) {
                                u8arr[n] = bstr.charCodeAt(n)
                              }
                              return new Blob([u8arr], { type: mime })
                            }
                            // _this.props.selectImg(dataURLtoBlob(dataurl), "backImg", "")
                            if (_this.state.tabIndex == 0) {
                              _this.props.selectImg(dataURLtoBlob(dataurl), type, '')
                            } else {
                              //_this.props.selectImg(dataURLtoBlob(dataurl), 'minifacebackImg', "")
                              let backImg = isMini(robotType_1) ? 'minifacebackImg' : 'facebackImg'
                              _this.props.selectImg(dataURLtoBlob(dataurl), backImg, '')
                            }
                          }
                          image.src = e.target.result
                        }
                        return true
                      }}
                      onChange={(e) => {
                        // console.log(e)
                      }}
                    >
                      {__('CORPUS.UPDATE_PICTURE')}
                    </Upload>
                  </ImgCrop>

                  {this.state.tabIndex == 1 &&
                  this.props.templateInfo.faceBackground &&
                  this.props.templateInfo.faceBackground != '' ? (
                    <Icon
                      onClick={() => {
                        if (this.props.templateInfo.templateCode == 'List') {
                          backgroundImg = 'module_public/module_skill_home/home1.png'
                        } else if (this.props.templateInfo.templateCode == 'Card') {
                          backgroundImg = 'module_public/module_skill_home/home2.png'
                        } else if (this.props.templateInfo.templateCode == 'Standard') {
                          backgroundImg = 'module_public/module_skill_home/home4.png'
                        } else if (this.props.templateInfo.templateCode == 'Simple') {
                          // backgroundImg = "module_public/module_skill_home/simpleBg.png";

                          if (!this.isKTV) {
                            backgroundImg = 'module_public/module_skill_home/simpleBg.png'
                          } else {
                            backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
                          }

                          if (
                            this.props.templateInfo.scenes == 'KTV' &&
                            this.props.templateInfo.templateCode == 'Simple'
                          ) {
                            backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
                          }
                        }
                        if (this.state.tabIndex == 0) {
                          this.props.dispatch({
                            type: 'CHANGE_INFO',
                            data: {
                              type: 'background',
                              content: backgroundImg,
                            },
                          })
                          this.props.deleteBackImg()
                        } else {
                          this.props.dispatch({
                            type: 'CHANGE_INFO',
                            data: {
                              key: 12,
                              type: 'faceBackground',
                              content: '',
                            },
                          })
                          this.props.deleteBackImg()
                          //this.props.faceBackground();  暂时注释掉  上级组建调用没有绑定这个函数
                        }
                      }}
                      className='ImgCrop_icon_del'
                      icon='rd_trash'
                    />
                  ) : (
                    ''
                  )}
                </div>
              </Popover>
            </p>
          </div>
          {/**背景新地方结束 */}
        </div>
      </div>
    )
  }
}

export default BackImg
export { BackImg }
