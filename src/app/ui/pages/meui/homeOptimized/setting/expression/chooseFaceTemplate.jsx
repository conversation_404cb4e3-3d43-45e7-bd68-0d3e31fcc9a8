import React from 'react'
import { extend } from 'koot'
import { Dropdown, MenuItem } from 'biz-components'
import { Icon } from 'biz-components'
import { Radio, Button } from 'biz-components'
import { isMini } from '@utils/tools'
import classNames from 'classnames'

@extend({
  styles: require('./chooseFaceTemplate.less'),
})
class ChooseFaceTemplate extends React.Component {
  state = {
    style: this.props.FaceStyle || 'Standard',
    FaceStyle: this.props.FaceStyle || 'Standard',
  }
  changeStyle(FaceStyle) {
    this.setState({
      FaceStyle,
    })
  }

  getQueryVal = (k, search, uri) => {
    if (search == undefined) {
      search = location.search
    }
    var reg = new RegExp('(^|&)' + k + '=([^&]*)(&|$)')
    var s = (uri && '?' + uri.split('?')[1]) || search
    var r = s.slice(1).match(reg)
    if (r != null) {
      return decodeURIComponent(r[2])
    }
    return null
  }

  onShowImageHello(robotType) {
    console.log({ robotType, props: this.props })
    let imagePath
    if (isMini(robotType) && this.props.lang === 'en_US') {
      imagePath = require('@assets/png/meui/bao_mini_en.png')
    } else if (['bao_xiao_mi', 'bxm_plus_in'].includes(robotType) && this.props.lang === 'en_US') {
      imagePath = require('@assets/png/meui/xiaobao_en.png')
    } else if (isMini(robotType)) {
      imagePath = require('@assets/png/meui/mini_tob1.png')
    } else {
      imagePath = require('@assets/png/meui/qipao2.png')
    }
    return imagePath
  }

  render() {
    let robotType = this.getQueryVal('robotType')
    return (
      <div className={this.props.className}>
        <p>{__('customHome.btn.selectExpressionTemplate')}</p>

        <div className={isMini(robotType) ? 'modal_choose minicss' : 'modal_choose'}>
          {/* {(this.props.lang === 'arabic' || this.props.lang === 'chs') && */}
          <p
            className={classNames('template9', {
              blue: this.state.style == 'List',
            })}
            onClick={() => {
              this.changeStyle('Bubble')
            }}
          >
            <img src={this.onShowImageHello(robotType)} />
            <Radio name='sort' label={__('customHome.pic.bubble')} checked={this.state.FaceStyle == 'Bubble'} />
            {/* {this.props.FaceStyle == "Bubble" && <span>(当前)</span>} */}
          </p>
          {/* } */}
          {/* {this.props.lang === 'english' &&
						<p className={this.state.style == "List" ? "blue" : ""}
							onClick={() => {
								this.changeStyle("Card")
							}}>
							<img src={robotType == "bao_mini" && require('@assets/png/meui/mini_tob2.png') || require('@assets/png/meui/card.png')} />
							<Radio name="sort" label={__('customHome.pic.card')} checked={this.state.FaceStyle == "Card"} />

						</p>
					} */}

          <p
            className={classNames('template8', {
              blue: this.state.style == 'Standard',
            })}
            onClick={() => {
              this.changeStyle('Standard')
            }}
          >
            <img
              src={
                (isMini(robotType) && require('@assets/png/meui/mini_tob2.png')) ||
                require('@assets/png/meui/biaozhun.png')
              }
            />
            <Radio name='sort' label={__('customHome.pic.simple')} checked={this.state.FaceStyle == 'Standard'} />
          </p>
        </div>
        {/** <p className="marked">
						<Icon icon='Alert_Default_Warning' className='marked_icon' />
						<span>{__('customHome.warm.msg6')}</span>
					</p>*/}
        {/* <p className="modal_button"
						onClick={() => {
							console.log(this.props)
							this.props.chooseFaceStyle(this.state.FaceStyle)
						}}
					>{__('customHome.btn.save2')}</p> */}
        <Button
          className='modal_button'
          onClick={() => {
            console.log('模板选择关闭 1', { FaceStyle: this.state.FaceStyle })
            this.props.chooseFaceStyle(this.state.FaceStyle)
          }}
        >
          {__('customHome.btn.save2')}
        </Button>
      </div>
    )
  }
}
export default ChooseFaceTemplate
export { ChooseFaceTemplate }
