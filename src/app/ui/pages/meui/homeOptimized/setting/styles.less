@import '~base.less';

.component {
  position: relative;
  overflow: hidden;

  .card-container {
    background: #fff;
    .ant-tabs-tabpane {
      padding-left: 0;
    }
  }
  .dis {
    pointer-events: none;
    opacity: 0.5;
  }

  .ant-tabs-nav {
    font-size: 18px;
  }

  .card-container p {
    margin: 0;
  }

  .card-container > .ant-tabs-card .ant-tabs-content {
    // height: 120px;
    // margin-top: -16px;
  }

  .card-container > .ant-tabs-card .ant-tabs-content > .ant-tabs-tabpane {
    background: #fff;
    padding: 16px;
    padding-left: 0;
  }

  .card-container > .ant-tabs-card > .ant-tabs-tab {
    border-top-color: #3776ef;
    border-top-width: 2px;
    border-top-style: solid;
  }

  .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {
    border-top-color: #3776ef;
    border-top-width: 2px;
    border-top-style: solid;
  }

  .card-container > .ant-tabs-card > .ant-tabs-nav::before {
    /* display:block; */
  }

  .card-container > .ant-tabs-card .ant-tabs-tab,
  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-tab {
    border-color: transparent;
    background: transparent;
  }

  .card-container > .ant-tabs-card .ant-tabs-tab-active,
  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-tab-active {
    border-color: #fff;
    background: #fff;
    border-top-color: #3776ef;
    border-top-width: 2px;
    border-top-style: solid;
    border-style: solid;
    border-right: solid 1px #f0f0f0;
    border-bottom-style: none;
  }

  .code-box-demo {
    background: #f5f5f5;
    overflow: hidden;
    padding: 24px;
  }

  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-content {
    height: 120px;
    margin-top: -8px;
  }

  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-tab {
    border-color: transparent;
    background: transparent;
  }

  [data-theme='dark'] .code-box-demo {
    background: #000;
  }

  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-content > .ant-tabs-tabpane {
    background: #141414;
  }

  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-tab-active {
    border-color: #141414;
    background: #141414;
  }

  .index-set {
    display: flex;
    flex-direction: row;
    height: calc(100vh - 250px);
    min-height: 450px;
    // overflow-x: scroll;

    .modules {
      min-width: 123px;
      font-size: 14px;

      background: #dce7fd;
      background: rgba(220, 231, 253, 0.2);

      .modu {
        height: 45px;
        line-height: 45px;
        // text-align: center;
        padding-left: 19px;
        cursor: pointer;
        color: rgba(74, 74, 74, 0.6);
      }

      .curr {
        background: #ffffff;
        color: #4a4a4a;
      }
    }

    .exp_set_nav_list-icon {
      color: #3776ef;
      margin-left: 10px;
    }
    .module-detail {
      flex: 1;
      margin-left: 27px;
      overflow-y: scroll;
      height: 100%;
      // min-width: 600px;
      max-width: 33.5rem;
      overflow-x: auto;
      .change-temp {
        min-width: 160px;
        border: 1px solid #555d61;
        svg {
          width: 12px;
          height: 12px;
        }
      }
      button {
        &:disabled {
          border-color: #e1e3e9;
        }
      }
    }
  }
  .index-set-mini {
    flex-direction: column;
    .modules {
      display: flex;
      .modu {
        width: 15%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .backImg {
    width: 100px;
    // height: 160px;
    position: relative;
    background: #d8d8d8;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 9px;
    margin-right: 20px;
    .imgset {
      display: inline-block;
      width: 100px;
      height: auto;
    }
    span {
      display: block;
      height: 22px;
      width: 100%;
      font-size: 12px;
      // font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 22px;
      background: #000000;
      opacity: 0.64;
      text-align: center;
      position: absolute;
      bottom: 0;
      cursor: pointer;
      &:hover {
        opacity: 1;
      }
    }
  }
  .bigScreenTip {
    color: red;
    font-size: 12px;
    height: 40px;
    line-height: 40px;
  }
  .changBtn {
    width: 100px;
    text-align: center;
    button {
      border: none;
      background: #fff;
      width: 88px;
      height: 28px;
      border-radius: 18px;
      // opacity: 0.2;
      border: 1px solid #4881f0;
      color: #4881f0;
      cursor: pointer;
      outline: none;
      line-height: 26px;
      font-size: 12px;
      &:hover {
        // opacity: 1;
        background: #4881f0;
        color: #fff;
      }
    }
  }

  .loading {
    background: rgba(248, 250, 250, .5);
  }
}
