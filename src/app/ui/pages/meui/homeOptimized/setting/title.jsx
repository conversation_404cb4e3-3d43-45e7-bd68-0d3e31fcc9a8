import React, { useRef } from 'react'
import { connect } from 'react-redux'
import { extend, getLocaleId } from 'koot'
import classNames from 'classnames'
import { ButtonSelectFile } from 'biz-components'
import { CHANGE_INFO } from '@redux/action-types'
import { Radio } from 'biz-components'
import { Icon } from 'biz-components'
import { Upload } from 'antd'
import ImgCrop from 'antd-img-crop'
import 'antd/es/modal/style'
import 'antd/es/slider/style'
import CardTitle from '../cardTitle'
import { inputMaxCnTitle, inputMaxEnTitle, onLangLen } from '@src/app/utils/tools'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页标题', state._home)
  return {
    templateInfo: state._home.homeModule.templateInfo,
    homeModule: state._home.homeModule,
  }
})
@extend({
  styles: require('./titleStyle.less'),
})
class Title extends React.Component {
  constructor(props) {
    super(props)
    this.uploadingData = 0
    this.state = {
      titleText: this.props.templateInfo.titleBarType == 'text' ? this.props.templateInfo.titleBarContent : '',
      titleImage: this.props.templateInfo.titleBarType == 'Image' ? this.props.templateInfo.titleBarContent : '',
      TitleStyle: this.props.templateInfo.titleBarType,
      isShowTitleWordTip: false,
      isShowSuggWordTip: false,
      maxFunLength: onLangLen(this.props.lang, 'title'),
      tabIndex: 1,
    }
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
    this.myRef = React.createRef()
  }

  handleChange(title) {
    this.setState({
      TitleStyle: title,
    })
    if (title == 'text') {
      this.props.templateInfo.titleBarContent = this.state.titleText
    } else {
      if (this.props.logo != '') {
        this.props.templateInfo.titleBarContent = this.props.logo
      } else {
        if (this.state.titleImage != '') {
          this.props.templateInfo.titleBarContent = this.state.titleImage
        } else {
          this.props.templateInfo.titleBarContent = 'module_public/module_skill_home/default_title_bg.jpeg'
        }
        //this.props.templateInfo.titleBarContent = this.state.titleImage
      }
    }
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'titleBarType',
        content: title,
      },
    })
  }
  getDerivedStateFromProps(props, state) {
    let a = null
    let b = null
    if (props.lang !== state.lang) {
      let maxFunLength = inputMaxCnTitle
      maxFunLength = props.lang !== 'zh_CN' ? inputMaxEnTitle : inputMaxCnTitle
      a = {
        lang: props.lang,
        maxFunLength,
      }
    }
    if (state.TitleStyle == 'text' && props.templateInfo.titleBarContent !== state.titleText) {
      b = {
        titleText: props.templateInfo.titleBarContent,
      }
    }
    if (a || b) {
      return {
        ...a,
        ...b,
      }
    }
    return
  }
  componentWillReceiveProps(nextProps) {
    // let maxFunLength = inputMaxCnTitle
    // if (this.props.lang && nextProps.lang) {
    //   maxFunLength = nextProps.lang !== 'zh_CN' ? inputMaxEnTitle : inputMaxCnTitle
    //   this.setState({
    //     lang: nextProps.lang,
    //     maxFunLength,
    //   })
    // }
    // if (
    //   this.state.TitleStyle == 'text' &&
    //   this.props.templateInfo.titleBarContent !== nextProps.templateInfo.titleBarContent
    // ) {
    //   this.setState({
    //     titleText: nextProps.templateInfo.titleBarContent,
    //   })
    // }
  }
  componentDidUpdate(prevProps) {
    this.loaded = true
    if (JSON.stringify(prevProps.homeModule) != JSON.stringify(this.props.homeModule)) {
      if (sessionStorage.getItem('config_title_loaded') == 0) {
        this.loaded = !this.loaded
        sessionStorage.setItem('config_title_loaded', 1)
      }
    }
  }
  componentWillReceiveProps(nextProps) {
    // this.setState({
    //   titleText: nextProps.templateInfo.titleBarType == 'text' ? nextProps.templateInfo.titleBarContent : '',
    //   titleImage: nextProps.templateInfo.titleBarType == 'Image' ? nextProps.templateInfo.titleBarContent : '',
    //   TitleStyle: nextProps.templateInfo.titleBarType,
    // })
  }
  componentDidMount() {
    // console.log('init-radio')
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
  }
  render() {
    const templateCode = this.props.templateInfo.templateCode 
    var image_url = this.props.devUrl + 'media/'
    let backgroundImg = ''
    if (templateCode == 'List') {
      backgroundImg = 'module_public/module_skill_home/home1.png'
    } else if (templateCode == 'Card') {
      backgroundImg = 'module_public/module_skill_home/home2.png'
    } else if (templateCode == 'Standard') {
      backgroundImg = 'module_public/module_skill_home/home4.png'
    }
    let simpleImg = 'module_public/module_skill_home/conciseimage.png'
    if (this.isKTV) {
      simpleImg = 'module_public/module_skill_home/concisektv.png'
    }

    if (this.props.templateInfo.scenes == 'KTV' && templateCode == 'Simple') {
      simpleImg = 'module_public/module_skill_home/concisektv.png'
    }
    let inputTimeout
    const maxTitleLength = templateCode === 'agentOS' ? 15 : 11;
    return (
      <div className={this.props.className}>
        <CardTitle title={__('customHome.btn.title')}></CardTitle>
        <div className='changeTitle a3'>
          {templateCode != 'Standard' &&
            templateCode != 'Simple' &&
            templateCode != 'Buttons' && 
            templateCode != 'agentOS' && (
            <div className='list'>
              <span className='title'>{__('customHome.modalMsg.titleType')}</span>
              <div className='chooseStyle 1'>
                <Radio
                  name='sort'
                  label={__('customHome.btn.titletext')}
                  checked={this.state.TitleStyle == 'text'}
                  onClick={() => {
                    this.handleChange('text')
                  }}
                />

                <Radio
                  name='sort'
                  label={__('customHome.btn.titlepic')}
                  checked={this.state.TitleStyle == 'Image'}
                  onClick={() => {
                    this.handleChange('Image')
                  }}
                />
              </div>
            </div>
          )}
          {templateCode == 'Buttons123' && (
            <div className='list'>
              <p className={classNames({ newTitle: true })}>
                <input
                  type='text'
                  maxLength={29}
                  value={this.props.templateInfo.conciseContent.title}
                  onFocus={() => {
                    this.setState({ isShowTitleWordTip: true })
                  }}
                  onBlur={() => {
                    this.setState({ isShowTitleWordTip: false })
                    this.props.checkJson()
                  }}
                  onChange={(e) => {
                    this.setState({
                      titleText: e.target.value,
                    })
                    // this.props.templateInfo.titleBarContent = e.target.value;
                    let conciseContent = this.props.templateInfo.conciseContent
                    conciseContent.title = e.target.value
                    this.props.dispatch({
                      type: 'CHANGE_INFO',
                      data: {
                        type: 'conciseContent',
                        content: conciseContent,
                      },
                    })
                  }}
                />
                {this.state.isShowTitleWordTip ? (
                  <span>
                    <em>{this.props.templateInfo.conciseContent.title.length}</em> / <em>{29}</em>
                  </span>
                ) : (
                  ''
                )}
              </p>
            </div>
          )}
          {this.state.TitleStyle == 'text' && 
           templateCode != 'Simple' && 
           templateCode != 'agentOS' && (
            <div className='list'>
              <p className={classNames({ newTitle: true })}>
                <input
                  d='ff 1'
                  key={`item--${this.props.robot_id}-${this.props.modalStyle}-${this.loaded}`}
                  type='text'
                  maxLength={onLangLen(this.props.lang, 'title')}
                  value={this.props.templateInfo.titleBarType == 'text' ? this.props.templateInfo.titleBarContent : ''}
                  attr='tit'
                  ref={this.myRef}
                  onFocus={() => {
                    this.setState({ isShowTitleWordTip: true })
                  }}
                  onBlur={() => {
                    this.setState({ isShowTitleWordTip: false })
                    this.props.checkJson()
                  }}
                  onKeyUp={(e) => {
                    return
                    console.log('keyup', this.myRef)
                    if (e.keyCode != 39) {
                      return
                    }
                    let val = this.myRef.current.value
                    if (val.length == 0) {
                      this.setState({
                        titleText: val,
                      })
                      this.props.templateInfo.titleBarContent = val
                      this.props.dispatch({
                        type: 'CHANGE_INFO',
                        data: {
                          type: 'titleBarContent',
                          content: val,
                        },
                      })
                    }
                  }}
                  onClick={(e) => {
                    // console.log('oninput')
                  }}
                  onChange={(e) => {
                    //window.inputing=true;
                    // e.persist && e.persist();
                    let val = e.target.value
                    this.setState({
                      titleText: val,
                    })
                    // this.props.templateInfo.titleBarContent = val;
                    this.props.dispatch({
                      type: 'CHANGE_INFO',
                      data: {
                        type: 'titleBarContent',
                        content: val,
                      },
                    })
                    return null

                    inputTimeout && clearTimeout(inputTimeout)
                    inputTimeout = setTimeout(() => {
                      this.setState({
                        titleText: val,
                      })
                      // this.props.templateInfo.titleBarContent = val;
                      this.props.dispatch({
                        type: 'CHANGE_INFO',
                        data: {
                          type: 'titleBarContent',
                          content: val,
                        },
                      })
                      window.inputing = false
                    }, 0)
                  }}
                />
                <span>
                  <em>{this.props.templateInfo.titleBarContent.length}</em> /{' '}
                  <em>{onLangLen(this.props.lang, 'title')}</em>
                </span>
                {this.state.isShowTitleWordTip ? (
                  <span>
                    <em>{this.props.templateInfo.titleBarContent.length}</em> /{' '}
                    <em>{onLangLen(this.props.lang, 'title')}</em>
                  </span>
                ) : (
                  ''
                )}
              </p>
            </div>
          )}
          {templateCode == 'Simple' && (
            <div className='list'>
              <p className={classNames({ newTitle: true, arabic_newTitle: this.props.lang === 'arabic' })}>
                <input
                  type='text'
                  maxLength={maxTitleLength}
                  value={this.props.templateInfo.conciseContent.title}
                  onFocus={() => {
                    this.setState({ isShowTitleWordTip: true })
                  }}
                  onBlur={() => {
                    this.setState({ isShowTitleWordTip: false })
                    this.props.checkJson()
                  }}
                  onChange={(e) => {
                    this.setState({
                      titleText: e.target.value,
                    })
                    // this.props.templateInfo.titleBarContent = e.target.value;
                    let conciseContent = this.props.templateInfo.conciseContent
                    conciseContent.title = e.target.value
                    this.props.dispatch({
                      type: 'CHANGE_INFO',
                      data: {
                        type: 'conciseContent',
                        content: conciseContent,
                      },
                    })
                  }}
                />
                {this.state.isShowTitleWordTip ? (
                  <span>
                    <em>{this.props.templateInfo.conciseContent.title.length}</em> / <em>{maxTitleLength}</em>
                  </span>
                ) : (
                  ''
                )}
              </p>
            </div>
          )}
          {(templateCode == 'agentOS') && (
            <div className='list'>
              <p className={classNames({ newTitle: true, arabic_newTitle: this.props.lang === 'arabic' })}>
                <input
                  type='text'
                  maxLength={maxTitleLength}
                  value={this.props.templateInfo.agentosContent.title}
                  onFocus={() => {
                    this.setState({ isShowTitleWordTip: true })
                  }}
                  onBlur={() => {
                    this.setState({ isShowTitleWordTip: false })
                    this.props.checkJson()
                  }}
                  onChange={(e) => {
                    this.setState({
                      titleText: e.target.value,
                    })
                    let agentosContent = this.props.templateInfo.agentosContent
                    agentosContent.title = e.target.value
                    this.props.dispatch({
                      type: 'CHANGE_INFO',
                      data: {
                        type: 'agentosContent',
                        content: agentosContent,
                      },
                    })
                  }}
                />
                {this.state.isShowTitleWordTip ? (
                  <span>
                    <em>{this.props.templateInfo.agentosContent.title.length}</em> / <em>{maxTitleLength}</em>
                  </span>
                ) : (
                  ''
                )}
              </p>
            </div>
          )}
          {this.state.TitleStyle == 'Image' && (
            <div className='list'>
              <span className='title'>{__('customHome.pic.uploadPhoto')}</span>
              <p
                className={
                  this.props.logo != '' && this.props.logo != undefined ? 'list_area upload_area' : 'list_area'
                }
              >
                <ImgCrop
                  aspect={1000 / 200}
                  grid
                  modalTitle={__('customHome.btn.img_cut')} //
                  modalWidth='650px'
                  modalOk={__('customHome.btn.confirm')}
                  modalCancel={__('customHome.btn.cancel2')}
                  fillColor={'transparent'}
                >
                  <Upload
                    customRequest={() => {}}
                    className='upload-btn square'
                    listType='picture-card'
                    beforeUpload={(e) => {
                      let _this = this
                      let img = e
                      let reader = new FileReader()
                      reader.readAsDataURL(img)
                      reader.onload = function (e) {
                        let image = new Image()
                        image.onload = function () {
                          let mycanvas = document.querySelector('#myCanvasLogo')
                          let ctx = mycanvas.getContext('2d')
                          ctx.clearRect(0, 0, 1000, 200)
                          ctx.drawImage(image, 0, 0, 1000, 200)
                          let dataurl = mycanvas.toDataURL('image/png')
                          function dataURLtoBlob(dataurl) {
                            let arr = dataurl.split(','),
                              mime = arr[0].match(/:(.*?);/)[1],
                              bstr = atob(arr[1]),
                              n = bstr.length,
                              u8arr = new Uint8Array(n)
                            while (n--) {
                              u8arr[n] = bstr.charCodeAt(n)
                            }
                            return new Blob([u8arr], { type: mime })
                          }
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'logo', '')
                        }
                        image.src = e.target.result
                      }
                      return true
                    }}
                    onChange={(e) => {
                      // console.log(e)
                    }}
                  ></Upload>
                </ImgCrop>
                <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
                {this.props.logo != '' && this.props.logo != undefined ? (
                  <img src={image_url + this.props.logo} className='newLogo' />
                ) : (
                  ''
                )}
              </p>
            </div>
          )}
          <canvas style={{ display: 'none' }} id='myCanvas' width='980' height='846' />
          <canvas style={{ display: 'none' }} id='myCanvasLogo' width='1000' height='200' />
        </div>
      </div>
    )
  }
}
export default Title
export { Title }
