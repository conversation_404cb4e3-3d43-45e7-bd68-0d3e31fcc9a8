import React from 'react'
import { extend } from 'koot'
import { Popover } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'

import styles from './index.module.less'

@extend({
  styles,
})
class CardTitle extends React.Component {
  render() {
    return (
      <div
        className={this.props.className + ' card-title'}
        data-custom-props={this.props.customProps}
        data-class-name={this.props['data-class-name']}
      >
        <h3>
          {this.props.title}
          {/* 目前文案没有出，全部隐藏  this.props.tips && <Popover content={this.props.tips}><QuestionCircleOutlined /></Popover>*/}
        </h3>
        <div>
          <p> {this.props.desc}</p>
          <div>{this.props.children}</div>
        </div>
      </div>
    )
  }
}

export default CardTitle
