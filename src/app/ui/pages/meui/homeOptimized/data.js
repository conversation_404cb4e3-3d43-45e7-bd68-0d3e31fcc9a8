export const config_json_plus_en_us = () => ({
  lang: 'en_US',
  queries: [
    {
      name: 'mainPage',
      query: ['You can ask me something', 'You can ask me something'],
      sort: 0,
    },
  ],
  questionExt: [],
  skills: [
    {
      display: true,
      function: 'chat',
      icon: 'module_public/module_skill_home/visit_bg_img.png',
      label: 'chat2',
      tip: "What's your name?",
      title: 'My Name',
    },
    {
      display: true,
      function: 'chat',
      icon: 'module_public/module_skill_home/visit_bg_img.png',
      label: 'chat1',
      tip: 'What can you do?',
      title: 'My Features',
    },
    {
      display: true,
      function: 'chat',
      icon: 'module_public/module_skill_home/List1_chat1.png',
      label: 'chat1',
      tip: 'How to set up homepage?',
      title: 'Home Setup',
    },
    {
      display: true,
      function: 'chat',
      icon: 'module_public/module_skill_home/List1_chat2.png',
      label: 'chat2',
      tip: 'How to configure Q&As?',
      title: 'Customize Q&As',
    },
    {
      display: true,
      function: 'chat',
      icon: 'module_public/module_skill_home/List1_chat2.png',
      label: 'chat2',
      tip: 'How to build a map?',
      title: 'Map Building',
    },
    {
      display: true,
      function: 'chat',
      icon: 'module_public/module_skill_home/List1_chat2.png',
      label: 'chat2',
      tip: 'Introduce your navigation feature',
      title: 'Navigation',
    },
    {
      display: true,
      function: 'chat',
      icon: 'module_public/module_skill_home/List1_chat1.png',
      label: 'chat1',
      tip: 'Introduce your greetings feature',
      title: 'Greetings ',
    },
  ],
  templateInfo: {
    background: 'module_public/module_skill_home/home1.png',
    bs_background: 'module_default/module_skill_home/bxmplus_1.png',
    bubbleContents: {
      faceStyle: 'Bubble',
      raw: ['You can ask me:', 'What can you do?'],
      robot: ['You can ask me:', 'What can you do?'],
      title: '',
    },
    conciseContent: {
      image: 'module_public/module_skill_home/conciseimage.png',
      suggestion: '我要买门票',
      suggestionExt: {
        display: true,
        function: 'chat',
        icon: '',
        label: 'chat1',
        tip: '我要买门票',
        title: '我要买门票',
      },
      title: '猎户风景区欢迎你',
    },
    faceBackground: '',
    haveCover: true,
    lang_config: {
      is_open: false,
      lang_list: [],
    },
    scenes: 'default',
    templateCode: 'List',
    titleBarContent: 'Ask me something',
    titleBarType: 'text',
  },
  version: 6,
})
