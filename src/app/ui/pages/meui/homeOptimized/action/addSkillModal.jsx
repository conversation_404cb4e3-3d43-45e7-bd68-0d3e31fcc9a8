import React from 'react'
import { connect } from 'react-redux'
import classNames from 'classnames'
import { extend } from 'koot'
import { message } from 'antd'
import { Upload } from 'antd'
import ImgCrop from 'antd-img-crop'
import 'antd/es/modal/style'
import 'antd/es/slider/style'

import QueryList from './queryList'
import { uploadResource, getWebQuery } from '@api/_home'
import recommendQues from '@api/_home/recommendQues'
import { inputMaxCnQ, inputMaxCnA, inputMaxEnQ, inputMaxEnA } from '@utils/tools'

@connect((state) => {
  return {
    skills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
    curLang: state.user.curLang,
  }
})
@extend({
  styles: require('./addSkillModal.less'),
})
class AddSkill extends React.Component {
  constructor(props) {
    super(props)
    let _defaultPic = ''
    switch (this.props.modalStyle) {
      case 'List':
        _defaultPic = 'module_public/module_skill_home/List1_interview.png'
        break
      case 'Card':
        _defaultPic = 'module_public/module_skill_home/Card_interview.png'
        break
      case 'Standard':
        _defaultPic = 'module_public/module_skill_home/Standard1_interview.png'
        break
      default:
        _defaultPic = 'module_public/module_skill_home/List1_interview.png'
    }
    var bottom_queries = '',
      bottom_queries_arr = [],
      online_bottom_queries_arr = []
    recommendQues.queries.forEach((item) => {
      if (item.name == this.props.queries_style) {
        bottom_queries_arr = item.query
      }
    })
    this.props.queries.forEach((item) => {
      if (item.name == this.props.queries_style) {
        online_bottom_queries_arr = item.query
      }
    })
    if (this.props.queriesIndex || this.props.queriesIndex == 0) {
      bottom_queries = online_bottom_queries_arr[this.props.queriesIndex]
    } else {
      bottom_queries = bottom_queries_arr[0]
    }
    if (props.lang == 'english') {
      bottom_queries = bottom_queries || 'Add a general guide'
    } else if (props.lang == 'arabic') {
      bottom_queries = ''
    }

    this.state = {
      funcConf: recommendQues.funcConfig,
      recmmendQuesList: this.props.lang == 'english' ? recommendQues.recommendQuesEng : recommendQues.recommendQues,
      allQuesList: recommendQues.allQuesList,
      whichChat: '',
      skill:
        this.props.lang == 'english'
          ? {
              title: '',
              icon: _defaultPic,
              tip: 'Take me to toilet',
              function: 'take_me_there',
              display: true,
            }
          : {
              title: '',
              icon: _defaultPic,
              // tip: __('customHome.qa.appointment'),
              tip: props.lang == 'arabic' ? '' : '我来找人',
              function: 'interview',
              display: true,
            },
      queries: recommendQues.allQuesList[0],
      bottom_queries: bottom_queries,
      bottom_queries_arr: bottom_queries_arr,
      queries_style: this.props.queries_style,
      showQueryList: false,
      showQueryTips: true,
      showPicTips: false,
      picTips: '',
      isShowFuncWordTip: false,
      isShowQueryWordTip: false,
      isShowQueryOnlyWordTip: false,
      lang: this.props.lang || 'chs',
      maxFunLength: this.props.lang == 'english' || this.props.lang == 'arabic' ? inputMaxEnQ : inputMaxCnQ,
      maxRecLength: this.props.lang == 'english' || this.props.lang == 'arabic' ? inputMaxEnA : inputMaxCnA,
    }
  }

  componentDidMount() {
    console.log(this.props.lang, this.state.skill, 'this.props.skill')
    console.log(this.state.funcConf, 'this.state.funcConf')

    const formData = new FormData()
    formData.append('lang', this.props.curLang.lang_code)
    getWebQuery(formData).then((res) => {
      let webQuery = [],
        appQuery = []
      res.items.map((item, index) => {
        if (item.type == 0) {
          webQuery.push(item.norm_word)
          item.similar_words.map((el, id) => {
            webQuery.push(el)
          })
        } else {
          appQuery.push(item.norm_word)
          item.similar_words.map((el, id) => {
            appQuery.push(el)
          })
        }
      })
      const web_set = new Set(webQuery)
      webQuery = [...web_set]
      const app_set = new Set(appQuery)
      appQuery = [...app_set]
      this.state.recmmendQuesList['web'] = webQuery
      this.state.recmmendQuesList['open_app'] = appQuery
      this.setState({
        recmmendQuesList: this.state.recmmendQuesList,
      })
    })
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    let maxFunLength = inputMaxCnQ
    let maxRecLength = inputMaxCnA
    let showQueryList = true
    if (this.props.lang && nextProps.lang) {
      maxFunLength = nextProps.lang == 'english' || nextProps.lang == 'arabic' ? inputMaxEnQ : inputMaxCnQ
      maxRecLength = nextProps.lang == 'english' || nextProps.lang == 'arabic' ? inputMaxEnA : inputMaxCnA
      showQueryList = nextProps.lang == 'english' || nextProps.lang == 'arabic' ? false : true
      this.setState({
        lang: nextProps.lang,
        maxFunLength,
        maxRecLength,
        showQueryList,
      })
    }
  }
  render() {
    var image_url = this.props.devUrl + 'media/'
    let sizeRule = {}
    if (this.props.modalStyle == 'List') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：110px*110px</span>
      var imageStyle = 'defaultImg  circleImg'
      var newIconStyle = 'newIcon_circle'
      sizeRule = { width: 110, height: 110 }
    } else if (this.props.modalStyle == 'Card') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：550px*380px</span>
      var imageStyle = 'defaultImg  rectangleImg1'
      var newIconStyle = 'newIcon_rectangle1'
      sizeRule = { width: 550, height: 380 }
    } else if (this.props.modalStyle == 'Standard') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：420px*230px</span>
      var imageStyle = 'defaultImg  rectangleImg2'
      var newIconStyle = 'newIcon_rectangle2'
      sizeRule = { width: 420, height: 230 }
    }

    var isComplete = false //判断即将新增的skill中数据是否完整
    for (let i in this.state.skill) {
      if (!this.state.skill[i]) {
        isComplete = false
        break
      } else {
        isComplete = true
      }
    }
    console.table(this.state.skill)

    return (
      <div className={this.props.className}>
        <div className='setting'>
          <div className={classNames({ changeQue: true, arabic_changeQue: this.props.lang === 'arabic' })}>
            {!this.props.addStyle || (this.props.addStyle != 'queries' && this.props.addStyle != 'bottom_queries') ? (
              <div className='list'>
                <span className='title'>{__('customHome.label.function')}</span>
                <select
                  className='chooseSetting'
                  value={this.state.skill.label ? this.state.skill.label : this.state.skill.function}
                  onChange={(e) => {
                    let fun = e.target.value == 'chat1' || e.target.value == 'chat2' ? 'chat' : e.target.value
                    // chat1==问答；chat2==聊天
                    let defaultPic = '',
                      labelObj = {}
                    if (e.target.value == 'chat1') {
                      labelObj.label = 'chat1'
                      this.setState({
                        whichChat: 1,
                      })
                    } else if (e.target.value == 'chat2') {
                      labelObj.label = 'chat2'
                      this.setState({
                        whichChat: 2,
                      })
                    } else {
                      if (this.state.skill['label']) {
                        delete this.state.skill['label']
                      }
                      this.setState({
                        whichChat: '',
                      })
                    }
                    if (this.props.modalStyle == 'List') {
                      defaultPic = this.state.funcConf[e.target.selectedIndex].list_icon1
                    } else if (this.props.modalStyle == 'Card') {
                      defaultPic = this.state.funcConf[e.target.selectedIndex].card_icon
                    } else if (this.props.modalStyle == 'Standard') {
                      defaultPic = this.state.funcConf[e.target.selectedIndex].standard1_icon
                    }
                    let skill = Object.assign(
                      {},
                      this.state.skill,
                      { function: fun, tip: this.state.recmmendQuesList[fun][0], icon: defaultPic },
                      labelObj,
                    )
                    this.setState({
                      funcIndex: e.target.selectedIndex,
                      skill: skill,
                    })
                  }}
                >
                  {this.state.funcConf.map((item, index) => {
                    if (this.props.lang == 'english') {
                      if (
                        item.key == 'take_me_there' ||
                        item.label == 'chat1' ||
                        item.label == 'chat2' ||
                        item.key == 'guide'
                      ) {
                        return (
                          <option key={index} value={item.key == 'chat' ? item.label : item.key}>
                            {item.name}
                          </option>
                        )
                      }
                    } else if (this.props.lang == 'arabic') {
                      if (item.label == 'chat1') {
                        return (
                          <option key={index} value={item.key == 'chat' ? item.label : item.key}>
                            {item.name}
                          </option>
                        )
                      }
                    } else {
                      return (
                        <option key={index} value={item.key == 'chat' ? item.label : item.key}>
                          {item.name}
                        </option>
                      )
                    }
                  })}
                </select>
              </div>
            ) : (
              ''
            )}
            {!this.props.addStyle || (this.props.addStyle != 'queries' && this.props.addStyle != 'bottom_queries') ? (
              <div className='list'>
                <span className='title'>{__('customHome.label.icon')}</span>
                <div
                  className={
                    this.state.skill.icon !=
                    'module_public/module_skill_home/' +
                      this.props.modalStyle +
                      (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
                      '_' +
                      this.state.skill.function +
                      this.state.whichChat +
                      '.png'
                      ? 'upload_area list_area'
                      : 'list_area'
                  }
                >
                  <ImgCrop
                    aspect={sizeRule.width / sizeRule.height}
                    grid
                    modalTitle={__('customHome.btn.img_cut')} //
                    modalWidth='650px'
                    modalOk={__('customHome.btn.confirm')}
                    modalCancel={__('customHome.btn.cancel2')}
                    fillColor={'transparent'}
                  >
                    <Upload
                      customRequest={() => {}}
                      className={this.props.modalStyle == 'List' ? 'upload-btn  circle' : 'upload-btn  square'}
                      listType='picture-card'
                      beforeUpload={(e) => {
                        let _this = this
                        let img = e
                        let reader = new FileReader()
                        reader.readAsDataURL(img)
                        reader.onload = function (e) {
                          let image = new Image()
                          image.onload = function () {
                            let mycanvas = document.querySelector('#mySkillCanvas')
                            let ctx = mycanvas.getContext('2d')
                            ctx.clearRect(0, 0, sizeRule.width, sizeRule.height)
                            ctx.drawImage(image, 0, 0, sizeRule.width, sizeRule.height)
                            let dataurl = mycanvas.toDataURL('image/png')
                            function dataURLtoBlob(dataurl) {
                              let arr = dataurl.split(','),
                                mime = arr[0].match(/:(.*?);/)[1],
                                bstr = atob(arr[1]),
                                n = bstr.length,
                                u8arr = new Uint8Array(n)
                              while (n--) {
                                u8arr[n] = bstr.charCodeAt(n)
                              }
                              return new Blob([u8arr], { type: mime })
                            }

                            if (_this.props.modalStyle == 'List') {
                              _this.selectImg(dataURLtoBlob(dataurl), 'list_icon1')
                            } else if (_this.props.modalStyle == 'Card') {
                              _this.selectImg(dataURLtoBlob(dataurl), 'card_icon')
                            } else if (_this.props.modalStyle == 'Standard') {
                              _this.selectImg(dataURLtoBlob(dataurl), 'standard1_icon')
                            }
                          }
                          image.src = e.target.result
                        }
                        return true
                      }}
                      onChange={(e) => {}}
                    ></Upload>
                  </ImgCrop>
                  <canvas
                    style={{ display: 'none' }}
                    id='mySkillCanvas'
                    width={sizeRule.width}
                    height={sizeRule.height}
                  />
                  {this.state.skill.icon !=
                  'module_public/module_skill_home/' +
                    this.props.modalStyle +
                    (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
                    '_' +
                    this.state.skill.function +
                    this.state.whichChat +
                    '.png' ? (
                    <img className={newIconStyle} src={image_url + this.state.skill.icon} alt='' />
                  ) : (
                    ''
                  )}
                  <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
                </div>
                <div
                  className='list_area'
                  onClick={() => {
                    let newSkill = Object.assign({}, this.state.skill, {
                      icon:
                        'module_public/module_skill_home/' +
                        this.props.modalStyle +
                        (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
                        '_' +
                        this.state.skill.function +
                        this.state.whichChat +
                        '.png',
                    })
                    this.setState({
                      skill: newSkill,
                    })
                  }}
                >
                  <img
                    src={
                      image_url +
                      'module_public/module_skill_home/' +
                      this.props.modalStyle +
                      (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
                      '_' +
                      this.state.skill.function +
                      this.state.whichChat +
                      '.png'
                    }
                    className={imageStyle}
                  />
                  <span className='defaultInfo'>{__('customHome.pic.default')}</span>
                </div>
                <div className='list_area'>
                  <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
                  {imageSizeTip}
                  <span className='uploadTips'>
                    2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
                  </span>
                  <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
                </div>
                {this.state.showPicTips ? <span className='pic_tips'>{this.state.picTips}</span> : ''}
              </div>
            ) : (
              ''
            )}
            {!this.props.addStyle || (this.props.addStyle != 'queries' && this.props.addStyle != 'bottom_queries') ? (
              <div className={classNames({ list: true, arabic_list: this.props.lang === 'arabic' })}>
                <span className='title'>{__('customHome.label.name')}</span>
                <input
                  type='text'
                  className={classNames({ arabic_input: this.props.lang === 'arabic' })}
                  placeholder={__('customHome.warm.msg3')}
                  maxLength={this.props.lang === 'chs' ? inputMaxCnQ : inputMaxEnQ}
                  value={this.state.skill.title}
                  onFocus={() => {
                    this.setState({ isShowFuncWordTip: true })
                  }}
                  onBlur={() => {
                    this.setState({ isShowFuncWordTip: false })
                  }}
                  onChange={(e) => {
                    if (this.state.skill.title == e.target.value.replace(/\s+/g, '')) {
                      return
                    }
                    let values = e.target.value.replace(/\s+/g, '')
                    let skill = Object.assign({}, this.state.skill, { title: values })
                    this.setState({
                      skill: skill,
                    })
                  }}
                />
                {this.state.isShowFuncWordTip ? (
                  <span
                    className={classNames({
                      'word-len-tips': true,
                      'arabic-word-len-tips': this.state.lang === 'arabic',
                    })}
                  >{`${this.state.skill.title.length} / ${
                    this.props.lang === 'chs' ? inputMaxCnQ : inputMaxEnQ
                  }`}</span>
                ) : (
                  ''
                )}
              </div>
            ) : (
              ''
            )}
            {!this.props.addStyle || (this.props.addStyle != 'queries' && this.props.addStyle != 'bottom_queries') ? (
              <div className='list'>
                {this.state.showQueryTips ? (
                  <span className='input_tips show_input_tips'>{__('customHome.warm.msg4')}</span>
                ) : (
                  ''
                )}
                <div className={classNames({ arabic_box: this.props.lang === 'arabic' })}>
                  <span className='title'>{__('customHome.label.question')}</span>
                  <div
                    className='query-set'
                    onMouseEnter={() => {
                      this.setState({
                        showQueryList: true,
                      })
                    }}
                    onMouseLeave={() => {
                      setTimeout(() => {
                        this.setState({
                          showQueryList: false,
                        })
                      }, 200)
                    }}
                  >
                    <input
                      className='queryInput'
                      type='text'
                      maxLength={this.state.lang === 'chs' ? inputMaxCnA : inputMaxEnA}
                      value={this.state.skill.tip || ''}
                      placeholder={__('customHome.warm.msg3')}
                      onInput={(e) => {
                        this.setState({
                          showQueryTips: true,
                        })
                      }}
                      onFocus={() => {
                        this.setState({ isShowQueryWordTip: true })
                      }}
                      onBlur={() => {
                        this.setState({ isShowQueryWordTip: false })
                      }}
                      onChange={(e) => {
                        if (this.state.skill.tip == e.target.value.replace(/\s+/g, '')) {
                          return
                        }
                        let skill = Object.assign({}, this.state.skill, { tip: e.target.value })
                        this.setState({
                          skill: skill,
                        })
                      }}
                      name=''
                      id=''
                    />

                    {this.state.showQueryList && this.props.lang !== 'arabic' ? (
                      <QueryList
                        type='0'
                        className='querySelCon'
                        querys={this.state.recmmendQuesList[this.state.skill.function]}
                        selectQuery={(query) => {
                          let skill = Object.assign({}, this.state.skill, { tip: query })
                          this.setState({
                            skill: skill,
                          })
                          this.setState({
                            showQueryTips: true,
                            showQueryList: false,
                          })
                        }}
                      ></QueryList>
                    ) : (
                      ''
                    )}
                  </div>
                  {this.state.isShowQueryWordTip ? (
                    <span className='word-len-tips'>
                      {this.state.skill.tip
                        ? `${this.state.skill.tip.length} / ${this.state.lang === 'chs' ? inputMaxCnA : inputMaxEnA}`
                        : ''}
                    </span>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            ) : (
              ''
            )}
            {!this.props.addStyle || (this.props.addStyle != 'queries' && this.props.addStyle != 'bottom_queries') ? (
              <button
                disabled={!isComplete}
                className={!isComplete ? 'addSkillBtn disBtn' : 'addSkillBtn'}
                onClick={() => {
                  this.props.newAddSkill(this.state.skill)
                }}
              >
                {__('customHome.btn.confirm')}
              </button>
            ) : (
              ''
            )}
            {this.props.addStyle && this.props.addStyle == 'queries' ? (
              <div className='list'>
                {this.state.showQueryTips ? (
                  <span className='input_tips show_input_tips'>{__('customHome.warm.msg4')}</span>
                ) : (
                  ''
                )}
                <div className='box'>
                  <span className='title'>{__('customHome.label.question')}</span>
                  <div
                    className='query-set'
                    onMouseEnter={() => {
                      this.setState({
                        showQueryList: true,
                      })
                    }}
                    onMouseLeave={() => {
                      setTimeout(() => {
                        this.setState({
                          showQueryList: false,
                        })
                      }, 200)
                    }}
                  >
                    <input
                      className='queryInput'
                      type='text'
                      maxLength={this.state.lang === 'chs' ? inputMaxCnA : inputMaxEnA}
                      value={this.state.queries}
                      placeholder={__('customHome.warm.msg3')}
                      onInput={() => {
                        this.setState({
                          showQueryTips: true,
                        })
                      }}
                      onFocus={() => {
                        this.setState({ isShowQueryOnlyWordTip: true })
                      }}
                      onBlur={() => {
                        this.setState({ isShowQueryOnlyWordTip: false })
                      }}
                      onChange={(e) => {
                        if (this.state.queries == e.target.value.replace(/\s+/g, '')) {
                          return
                        }
                        this.setState({
                          queries: e.target.value,
                        })
                      }}
                      name=''
                      id=''
                    />

                    {this.state.showQueryList ? (
                      <QueryList
                        type='1'
                        className='querySelCon'
                        querys={this.state.allQuesList}
                        selectQuery={(query) => {
                          this.setState({
                            queries: query,
                            showQueryTips: true,
                            showQueryList: false,
                          })
                        }}
                      ></QueryList>
                    ) : (
                      ''
                    )}
                  </div>
                  {this.state.isShowQueryOnlyWordTip ? (
                    <span className='word-len-tips'>{`${this.state.queries.length} / ${
                      this.state.lang === 'chs' ? inputMaxCnA : inputMaxEnA
                    }`}</span>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            ) : (
              ''
            )}

            {this.props.addStyle && this.props.addStyle == 'bottom_queries' ? (
              <div className='list'>
                {this.state.showQueryTips ? (
                  <span className='input_tips show_input_tips'>{__('customHome.warm.msg4')}</span>
                ) : (
                  ''
                )}
                <div className='box'>
                  <span className='title'>{__('customHome.label.question')}</span>
                  <div
                    className='query-set'
                    onMouseEnter={() => {
                      this.state.lang === 'chs' &&
                        this.setState({
                          showQueryList: true,
                        })
                    }}
                    onMouseLeave={() => {
                      setTimeout(() => {
                        this.setState({
                          showQueryList: false,
                        })
                      }, 200)
                    }}
                  >
                    <input
                      className='queryInput'
                      type='text'
                      maxLength={this.state.maxRecLength}
                      value={this.state.bottom_queries}
                      placeholder={__('customHome.warm.msg5')}
                      onInput={() => {
                        this.setState({
                          showQueryTips: true,
                        })
                      }}
                      onFocus={() => {
                        this.setState({ isShowQueryOnlyWordTip: true })
                      }}
                      onBlur={() => {
                        this.setState({ isShowQueryOnlyWordTip: false })
                      }}
                      onChange={(e) => {
                        if (this.state.bottom_queries == e.target.value.replace(/\s+/g, '')) {
                          return
                        }
                        this.setState({
                          bottom_queries: e.target.value,
                        })
                      }}
                      name=''
                      id=''
                    />

                    {this.state.showQueryList ? (
                      <QueryList
                        type='1'
                        className='querySelCon'
                        querys={this.state.bottom_queries_arr}
                        selectQuery={(query) => {
                          this.setState({
                            bottom_queries: query,
                            showQueryTips: true,
                            showQueryList: false,
                          })
                        }}
                      ></QueryList>
                    ) : (
                      ''
                    )}
                  </div>
                  {this.state.isShowQueryOnlyWordTip ? (
                    <span className='word-len-tips'>{`${this.state.bottom_queries.length} / ${this.state.maxRecLength}`}</span>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            ) : (
              ''
            )}

            {this.props.addStyle && (this.props.addStyle == 'queries' || this.props.addStyle == 'bottom_queries') ? (
              <button
                className={
                  this.props.addStyle == 'queries'
                    ? this.state.queries == ''
                      ? 'addSkillBtn disBtn'
                      : 'addSkillBtn'
                    : this.state.bottom_queries == ''
                    ? 'addSkillBtn disBtn'
                    : 'addSkillBtn'
                }
                onClick={() => {
                  if (this.props.editQueries) {
                    if (this.state.bottom_queries == '') return
                    this.props.editQueries(this.state.bottom_queries, this.props.queriesIndex)
                  } else {
                    if (this.props.addStyle == 'queries') {
                      if (this.state.queries == '') return
                      this.props.newAddQueries(this.state.queries)
                    } else {
                      if (this.state.bottom_queries == '') return
                      this.props.newAddQueries(this.state.bottom_queries)
                    }
                  }
                }}
              >
                {__('customHome.btn.confirm')}
              </button>
            ) : (
              ''
            )}
          </div>
        </div>
      </div>
    )
  }
  selectImg(e, type) {
    // const file = e.target.files[0];

    const file = type === 'card_icon' || type == 'list_icon1' || type == 'standard1_icon' ? e : e.target.files[0]
    let reader = new FileReader()
    var uploadImgWidth = 0,
      uploadImgHeight = 0
    if (!file) {
      return
    }
    if (file.type != 'image/png' && file.type != 'image/jpeg') {
      this.setState({
        picTips: __('customHome.pic.wrongPictureFormat'),
        showPicTips: true,
      })
      return
    } else {
      this.setState({
        picTips: '',
        showPicTips: false,
      })
    }
    switch (type) {
      case 'card_icon':
        uploadImgWidth = 550
        uploadImgHeight = 380
        break
      case 'list_icon1':
        uploadImgWidth = 110
        uploadImgHeight = 110
        break
      case 'standard1_icon':
        uploadImgWidth = 420
        uploadImgHeight = 230
        break
    }
    if (file.size / 1024 / 1000 > 5) {
      this.setState({
        picTips: __('customHome.pic.picSizeLimit'),
        showPicTips: true,
      })
      return
    } else {
      this.setState({
        picTips: '',
        showPicTips: false,
      })
    }
    var _this = this
    reader.onload = function (e) {
      let image = new Image()
      image.onload = function () {
        if (image.width != uploadImgWidth || image.height != uploadImgHeight) {
          _this.setState({
            picTips: __('customHome.pic.WrongPictureDimensions'),
            showPicTips: true,
          })
        } else {
          _this.setState({
            picTips: '',
            showPicTips: false,
          })
          let formData = new FormData()
          formData.append('resource_file', file)
          formData.append('module_code', 'module_skill_home')
          formData.append('config_id', sessionStorage.getItem('config_id'))
          formData.append('resource_type', 'image')
          formData.append('resource_file', file.name)
          formData.append('version', '')
          uploadResource(formData)
            .then((res) => {
              let upload_url = res.resource_url
              let skill = Object.assign({}, _this.state.skill, { icon: upload_url })
              _this.setState({
                skill: skill,
              })
            })
            .catch(() => {
              return message.error(__('customHome.pic.uploadFailed'))
            })
        }
      }
      image.src = e.target.result
    }
    reader.readAsDataURL(file)
  }
}
export default AddSkill
export { AddSkill }
