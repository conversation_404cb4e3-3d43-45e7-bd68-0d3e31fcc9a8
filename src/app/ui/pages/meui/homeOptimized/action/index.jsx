import React from 'react'
import { connect } from 'react-redux'
import { extend, getLocaleId } from 'koot'
import { Icon } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
// import { CHANGE_INFO } from '@redux/action-types'
import { TitleBlock } from 'biz-components'
// import ChooseModal from './chooseModal'
// import Progress from './progress'
// import List from './list'
// import Card from './card'
// import AI from './AI'
// import Standard from './Standard'
// import Simple from './Simple'
// import Tips from './tips'
// import Question from './question'
// import Title from './title'
// import Sort from './sort'
// import BottomQueries from './bottomQueries'
// import BackImg from './backImg'
// import Robot from './robot'
// import QueryItem from './queryItem'
// import Sort from './action/sort'
import { Button } from 'biz-components'
import { GET_HOME_MODULE, <PERSON>ANGE_INFO, CHANGE_MODULE_SKILL, ORIGIN_HOME_MODULE } from '@redux/action-types'
import {
  getModuleConfigList,
  getModuleConfig,
  uploadResource,
  publishModule,
  getModuleStatus,
  directPublishModule,
  recoverDefaultStatus,
  searchAnswer,
} from '@api/_home'
import recommendQues from '@api/_home/recommendQues'
import { getRobotStatistics, selectRobot, getRobotOpkList, UPDATE_ROBOT_DATA } from '@api/robots'
import { RobotList, ChooseRobot } from 'biz-components'
import { checkReleaseRaw, isCnHl } from '@utils/tools'
import { setLocalStorageItem } from '@utils/tools'
import { message } from 'antd'
import { webOperator } from '@utils/operatorRobot'
window.webOperator = webOperator
@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    // console.log('页面：自定义首页', state._home)
    devUrl = 'http://test-jiedai.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    user: state.user,
    brand: state.brand || 'ktv',
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  state = {
    iconJson: recommendQues.funcConfig,
    currentButton: 0, //3个按钮
    currentIndex: -2, //模版内容
    currentSort: false, //排序j
    changeContent: 'tips', //编辑区显示内容
    showInputTips: false,
    chooseQueTypes: '', //编辑区question类型
    modalStyle: '', //列表式List 卡片式Card 科技范Technical 标准式Standard,
    curScenes: '', //当前场景
    isDefault: false,
    isShowSetting: true,
    uploadUrl: '', //上传icon
    logo: '', //上传logo
    backImg: '', //自定义背景图
    progressNum: 0, //同步进度
    allNum: 0, //总同步数
    chooseList: [], //同步机器人列表
    fiveTimer: true, //进入页面5秒提示
    queriesIndex: -1, //当前选中queries索引
    showWarning: false, //不显示警示
    queryStyle: '', //底部query
    backImgstyle: '', //自定义背景
    queries_idx: 0,
    showLoading: false,
    currentRobotId: null,
    currentRobotSn: null,
    roleStr: '0',
    robotType: 'globall',
    robotName: undefined,
    lang: 'chs',
    overVersion51: false, //是版本高于5.1,
    overVersion57: false, //是版本高于5.7,
    templateInfoBackground: '',
    faceBackground: '',
    templateNeedUpdate: false, // 模板需要更新
    lowOpkVersion: false,
  }
  initRobotNum = 0
  componentDidMount() {
    //只执行一次。
    window.getProps = () => {
      console.log(this.props)
    }
  }
  getRobotOpkList() {
    return
    let formdata = new FormData()
    formdata.append('is_pad_baseopk', 1)
    getRobotOpkList(formdata).then((res) => {
      console.log(res, 'opk')
      res.robot_list.map((val) => {
        let obj = {}
        obj.id = val.robot.robot_uuid
        obj.data = { robot_baseopk_version: val.robot.robot_baseopk_version }

        // console.log(val,val.robot.robot_baseopk_version,'opl',this.props.robots)
        this.props.dispatch({
          type: 'UPDATE_ROBOT_DATA',
          id: obj.id,
          data: obj.data,
        })
      })
    })
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    // 	if (this.props.homeModule&&this.props.homeModule.templateInfo&&this.props.homeModule.templateInfo.bubbleContents && (this.props.homeModule.templateInfo.bubbleContents!=nextProps.homeModule.templateInfo.bubbleContents)&&this.props.homeModule.templateInfo.bubbleContents.faceStyle != "") {
    // 		this.setState({
    // 			Bubble1:this.props.homeModule.templateInfo.bubbleContents.robot[0],
    // 			Bubble2:this.props.homeModule.templateInfo.bubbleContents.robot[1]
    // 		})
    // 	}
    // if(nextProps.homeModule&&nextProps.homeModule.templateInfo&&nextProps.homeModule.templateInfo&&nextProps.homeModule.templateInfo.background&&this.props.homeModule&&this.props.homeModule.templateInfo&&(this.props.homeModule.templateInfo.background!=nextProps.homeModule.templateInfo.background)){
    // 	this.setState({
    // 		templateInfoBackground:nextProps.homeModule.templateInfo.background
    // 	})
    // }
  }
  componentWillUnmount() {
    this.props.dispatch({
      type: 'GET_HOME_MODULE',
      data: {
        homeModule: null,
      },
    })
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (
      this.props.currentRobotId != undefined &&
      prevProps.currentRobotId != undefined &&
      prevProps.currentRobotId != this.props.currentRobotId
    ) {
      console.log('componentDidUpdate  acaction ', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (
      this.props.homeModule != undefined &&
      prevProps.homeModule &&
      prevProps.homeModule == undefined &&
      this.props.homeModule != undefined
    ) {
      console.log('componentDidUpdate  acaction  props', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      // this.getConfigList();
    }

    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          console.log('-----------------', this.props.robots[el].version, versionGap)
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }

    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      prevProps.homeModule.templateInfo.bubbleContents &&
      prevProps.homeModule.templateInfo.bubbleContents != this.props.homeModule.templateInfo.bubbleContents
    ) {
      this.setState({
        Bubble1: '',
      })
    }
    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo.background &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      this.state.templateInfoBackground != this.props.homeModule.templateInfo.background
    ) {
      this.setState({
        templateInfoBackground: this.props.homeModule.templateInfo.background,
      })
    }
  }
  getConfigList(success) {
    const lang = this.state.lang
    console.log('this.props.currentRobotId~~~', this.props.currentRobotId)
    let module_code_list = ['module_skill_home']
    // if(this.state.lang=='english'){
    // 	module_code_list = ['module_skill_home_en']
    // }
    if (this.state.lang == 'arabic') {
      module_code_list = ['module_skill_home_ar']
    }

    let formData = new FormData()
    formData.append('module_code_list', JSON.stringify(module_code_list))
    formData.append('robot_uuid', this.props.currentRobotId)
    // formData.append('robot_uuid', localStorage.getItem("globallRobotId"));
    formData.append('config_status', 'publish')
    if (lang === 'arabic') {
      // formData.set('lang','ar_SA')
      formData.set('lang', 'zh_CN')
    }
    if (lang === 'english') {
      formData.set('lang', 'en_US')
    }
    if (lang === 'chs') {
      formData.set('lang', 'zh_CN')
    }

    getModuleConfigList(formData)
      .then((res) => {
        console.log('获取config_list', res)
        let config_id = '',
          version = ''
        if (res.module_config_list.length != 0) {
          config_id = res.module_config_list[0].config_id
          version = res.module_config_list[0].version
          this.gethomeModule(config_id, version, success)
        } else {
          config_id = Date.parse(new Date()) / 1000
          version = ''
          if (this.isKTV) {
            this.gethomeModule('ktv_simple_id', version, success)
          } else {
            this.gethomeModule('default_id', version, success)
          }
        }
        this.module_config_list = res.module_config_list
        sessionStorage.setItem('config_id', config_id)
        sessionStorage.setItem('version', version)
      })
      .catch((res) => {})
  }

  gethomeModule(config_id, version, success, lang) {
    let formData = new FormData()
    if (lang == 'english' || this.state.lang == 'english') {
      formData.append('module_code', 'module_skill_home')
      // formData.append('module_code', 'module_skill_home_en');
    } else if (lang == 'arabic' || this.state.lang == 'arabic') {
      formData.append('module_code', 'module_skill_home_ar')
    } else {
      formData.append('module_code', 'module_skill_home')
    }

    formData.append('config_id', config_id)
    formData.append('version', version)
    // console.info(formData.has('lang'))
    // debugger
    if (lang === 'arabic' || this.state.lang == 'arabic') {
      // formData.set('lang','ar_SA')
      formData.set('lang', 'zh_CN')
    }
    if (lang === 'english' || this.state.lang == 'english') {
      formData.set('lang', 'en_US')
    }
    if (lang === 'chs' || this.state.lang == 'chs') {
      formData.set('lang', 'zh_CN')
    }

    console.log('首页模板数据 5')
    getModuleConfig(formData)
      .then((res) => {
        let config_json = res.module_config.config_json
        if (typeof config_json === 'string') {
          try {
            config_json = JSON.parse(config_json)
          } catch (e) {}
        }
        console.log(config_json)
        if (config_json.templateInfo.templateCode == 'Simple') {
          this.simple_config_json = JSON.parse(JSON.stringify(config_json))
        } else {
          this.common_config_json = JSON.parse(JSON.stringify(config_json))
        }
        if (config_json.templateInfo.conciseContent == undefined) {
          config_json.templateInfo.conciseContent = {
            title: '猎户风景区欢迎你',
            image: 'module_public/module_skill_home/conciseimage.png',
            suggestion: '我要买门票',
          }
        }
        console.log('get_home_module 11')
        this.props.dispatch({
          type: 'GET_HOME_MODULE',
          data: {
            homeModule: config_json,
          },
        })
        if (
          this.isKTV &&
          this.props.homeModule.templateInfo.templateCode != 'Simple' &&
          !this.state.lowOpkVersion &&
          !this.state.isDefault &&
          !(this.state.isShowSetting && this.state.isDefault)
        ) {
          this.setState({
            templateNeedUpdate: true,
          })
          modal({
            title: __('customHome.modalMsg.tip'),
            fullscreen: false,
            content: (
              <div>
                <p>{__('customHome.modalMsg.pageNewChange')}</p>
                <p>{__('customHome.modalMsg.pageSetChange')}</p>
              </div>
            ),
            cancel: false,
            confirm: __('customHome.modalMsg.cureChange'),
            onConfirm: () => {
              this.setState(
                {
                  templateNeedUpdate: false,
                },
                () => {
                  this.choosemodalStyle('Simple', 'KTV', 'chs')
                },
              )
            },
          })
          let lowOpkVersion = this.state.lowOpkVersion
          let isDefault = this.state.isDefault
          window.tipSetData({
            templateNeedUpdate: true,
            lowOpkVersion,
            isDefault,
          })
        } else {
          let lowOpkVersion = this.state.lowOpkVersion
          let isDefault = this.state.isDefault
          window.tipSetData({
            templateNeedUpdate: false,
            lowOpkVersion,
            isDefault,
          })
        }
        let scenes = config_json.templateInfo.scenes ? config_json.templateInfo.scenes : 'default'
        let curScenes = ''
        let path = ''
        // 修复 选择类型时 切换 简洁式其他的类型，会造成简洁式 背景被重置的问题
        let backUrl = this.props.homeModule.templateInfo.background
        !this.state.SimplebackImg &&
          this.props.homeModule.templateInfo.templateCode == 'Simple' &&
          this.setState({
            //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
            SimplebackImg: backUrl,
            backImg: backUrl,
          })

        let _faceBackground = this.props.homeModule.templateInfo.faceBackground
        this.state.faceBackground == '' &&
          this.setState({
            //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
            faceBackground: _faceBackground,
          })
        if (scenes == 'default') {
          path = 'default'
        } else {
          path = path + '_default'
        }
        let lang_ext = ''
        if (this.state.lang == 'english') {
          lang_ext = '_en'
        } else if (this.state.lang == 'arabic') {
          lang_ext = '_ar'
        }
        if (res.module_config.config_json_path == 'module_default/module_skill_home/' + path + lang_ext + '.json') {
          this.setState({
            isDefault: true,
          })
        } else {
          this.setState({
            isDefault: false,
          })
        } //是不是默认JSON
        if (this.isKTV) {
          if (this.module_config_list.length == 0) {
            this.setState({
              isDefault: true,
            })
          } else {
            this.setState({
              isDefault: false,
            })
          }
        }

        if (scenes) {
          switch (scenes) {
            // case __('customHome.sceneType.default').toLowerCase():
            case 'default':
              // curScenes = "默认"
              curScenes = __('customHome.sceneType.default')
              console.log(curScenes)
              break
            // case __('customHome.sceneType.market'):
            case 'market':
              // curScenes = "商场"
              curScenes = __('customHome.sceneType.market')
              break
            // case __('customHome.sceneType.hotel'):
            case 'hotel':
              // curScenes = "酒店"
              curScenes = __('customHome.sceneType.hotel')
              break
            // case __('customHome.sceneType.bank'):
            case 'bank':
              // curScenes = "银行"
              curScenes = __('customHome.sceneType.bank')
              break
            // case __('customHome.sceneType.museum'):
            case 'museum':
              // curScenes = "博物馆"
              curScenes = __('customHome.sceneType.museum')
              break
            // case __('customHome.sceneType.library'):
            case 'library':
              // curScenes = "图书馆"
              curScenes = __('customHome.sceneType.library')
              break
            case 'KTV':
              // curScenes = "图书馆"
              curScenes = 'KTV'
              break
            default:
              curScenes = __('customHome.sceneType.default')
          }
        }
        this.setState({
          curScenes: curScenes,
          modalStyle: config_json.templateInfo.templateCode,
        })
        success && success()
      })
      .catch((res) => {
        console.log(res)
      })
  }
  // changeRobot(id) {
  // 	this.props.dispatch(selectRobot(id));
  // 	this.props.dispatch(getRobotStatistics(id));
  // }
  recoverSet() {
    //删除上传的背景图
    this.setState({
      backImg: '',
      queryStyle: '',
      isShowSetting: true,
      faceBackground: '',
      SimplebackImg: '',
    })
    console.log('恢复出厂设置')
    if (sessionStorage.getItem('version') == '') {
      this.getConfigList(() => {
        message.success(__('customHome.modalMsg.successRestMsg'))
        // modal({
        //     title: __('customHome.modalMsg.successfullyReset'),
        //     content: __('customHome.modalMsg.successRestMsg'),
        //     cancel: false,
        //     confirm: __('customHome.btn.gotIt'),
        // });
        this.changeTab_backImg()
      })
    } else {
      let robotID = [this.props.currentRobotId]
      let formData = new FormData()
      if (this.state.lang == 'english') {
        formData.append('module_code', 'module_skill_home')
        formData.append('lang', 'en_US')
      } else if (this.state.lang == 'arabic') {
        formData.append('module_code', 'module_skill_home_ar')
        formData.append('lang', 'zh_CN')
      } else {
        formData.append('module_code', 'module_skill_home')
        formData.append('lang', 'zh_CN')
      }
      // formData.append('module_code', 'module_skill_home');
      formData.append('config_id', sessionStorage.getItem('config_id'))
      formData.append('version', sessionStorage.getItem('version'))
      formData.append('robot_uuid_list', JSON.stringify(robotID))
      formData.append('status', 'default')
      recoverDefaultStatus(formData)
        .then((res) => {
          this.getConfigList()
          this.changeTab_backImg()
          message.success(__('customHome.modalMsg.successRestMsg'))
          // modal({
          //     title: __('customHome.modalMsg.successfullyReset'),
          //     content: __('customHome.modalMsg.successRestMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.gotIt'),
          // });
        })
        .catch((res) => {
          modal({
            title: __('customHome.modalMsg.failedToReset'),
            content: __('customHome.modalMsg.failedRestMsg'),
            cancel: __('customHome.btn.cancel2'),
            confirm: __('customHome.btn.retry'),
            btnCenter: true,
            onConfirm: () => {
              this.recoverSet()
            },
          })
        })
    }
  }
  getRobotName() {
    const robots = this.props.robots,
      roleStr = this.state.roleStr != undefined && this.state.roleStr,
      robotType = this.state.robotType != undefined && this.state.robotType
    let robotName = '',
      current
    if (Object.keys(robots).length == 0 && this.initRobotNum < 25) {
      this.initRobotNum += 1
      return setTimeout(() => {
        this.getRobotName()
      }, 500)
    }

    if (Object.keys(robots).length == 0) {
      robotName = __('customHome.modalMsg.noRobots')
    } else {
      if (robotType == 'local') {
        current = localStorage.getItem('localRobotId')
      } else {
        // current = localStorage.getItem('globallRobotId')
        if (roleStr == '0' || roleStr == '1') {
          current = localStorage.getItem('receptionRobotId')
        }
        if (roleStr == '2') {
          current = localStorage.getItem('docentRobotId')
        }
      }

      const firstRobotId = Object.keys(robots)[0]
      if (current == 'all') {
        const robotType = this.state.robotType
        const localRobotId =
          robotType == 'local' ? localStorage.getItem('localRobotId') : localStorage.getItem('globallRobotId')

        if (localRobotId != undefined || localRobotId != null) {
          robotName = robots[localRobotId] && robots[localRobotId].robot_name
          current = localRobotId
          //this.props.dispatch(selectRobot(localRobotId));
        } else {
          robotName = robots[firstRobotId] && robots[firstRobotId].robot_name
          //this.props.dispatch(selectRobot(firstRobotId));
          current = firstRobotId
        }
      } else {
        robotName = robots[current] && robots[current].robot_name
        console.log('当前机器人robotName@@@@', robotName)
        if (robotName == undefined || robotName == null) {
          robotName = robots[firstRobotId] && robots[firstRobotId].robot_name
          console.log('当前机器人robotName!!!!', robotName)
          setLocalStorageItem('globallRobotId', firstRobotId)
          setLocalStorageItem('localRobotId', firstRobotId)
          //this.props.dispatch(selectRobot(firstRobotId));
        } else {
          //this.props.dispatch(selectRobot(current));
          // current = current
        }
      }
    }
    let version = this.props.robots && this.props.robots[current] && this.props.robots[current].version
    let robot_baseopk_version =
      (this.props.robots && this.props.robots[current] && this.props.robots[current].robot_baseopk_version) || ''
    let baseVersion = '02.0001.200821'
    let lowOpkVersion = checkReleaseRaw(robot_baseopk_version, baseVersion, true) ? false : true
    let overVersion51 = checkReleaseRaw(version, 'V5.1', true)
    let overVersion57 = checkReleaseRaw(version, 'V5.7', true)
    console.log('机器人版本51', overVersion51)
    this.setState(
      {
        robotName: robotName,
        overVersion51: overVersion51,
        overVersion57: overVersion57,
        lowOpkVersion,
      },
      () => {
        this.props.dispatch(selectRobot(current))
      },
    )
    window.tipSetData && window.tipSetData({ lowOpkVersion })
    let isDefault = this.state.isDefault
    window.tipSetData({
      templateNeedUpdate: false,
      lowOpkVersion,
      isDefault,
    })
    if (this.isKTV && lowOpkVersion) {
      setTimeout(() => {
        modal({
          title: __('customHome.modalMsg.tip'),
          fullscreen: false,
          content: (
            <div>
              <p>当前机器人系统版本太低，不支持配置，请跳转至原后台</p>
              <p>{__('customHome.modalMsg.contactPresale')}</p>
            </div>
          ),
          cancel: false,
          confirm: __('customHome.btn.confirm'),
          onConfirm: () => {},
        })
      }, 900)
    }
  }
  render() {
    const robotName = this.state.robotName != undefined && this.state.robotName

    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'Simple') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }

      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.state.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };

      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.state.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.state.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.state.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.state.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
    }
    return (
      <div className={this.props.className}>
        {this.state.showLoading && (
          <div className='loading'>
            <div className='loading_modal'>
              <img src={require('@assets/svg-icon/loading.gif')} />
              <span>{__('customHome.modalMsg.waitMsg')}</span>
            </div>
          </div>
        )}
        <TitleBlock title={__('customHome.title')} component='h1'>
          {isCnHl() && (
            <div className='tip'>
              <Icon icon='help' className='icon helpIcon blue' />
              <a
                target='_blank'
                href='https://bbs.ainirobot.com/forum.php?mod=viewthread&tid=176&from=jiedai'
                className='helpBtn'
              >
                {__('customHome.btn.help')}
              </a>
            </div>
          )}
        </TitleBlock>
        <div className='combox'>
          <div className='btnBox'>
            <input
              type='text'
              value={this.state.htitle}
              onChange={(e) => {
                this.setState({ htitle: e.target.value })
              }}
            />
            <Button
              icon=''
              appearance='hollow'
              label={'更新title'}
              className='robot-list'
              onClick={() => {
                this.props.homeModule.templateInfo.titleBarContent = this.state.htitle
                this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'titleBarContent',
                    content: this.props.homeModule.templateInfo.titleBarContent,
                  },
                })
              }}
            />
          </div>
        </div>

        <div className='content'>
          <div className='preview'></div>
          <div className='setting'>{/* <Sort></Sort> */}</div>
        </div>
      </div>
    )
  }
  // 场景模板修改
  changeModal() {
    if (this.checkJson()) {
      this.setState({
        currentButton: 1,
        currentIndex: -2,
        currentSort: false,
      })
      modal({
        title: false,
        confirm: false,
        cancel: false,
        fullscreen: false,
        content: (
          <ChooseModal
            curScenes={this.state.curScenes}
            lang={this.state.lang}
            modalStyle={this.state.modalStyle}
            choosemodalStyle={this.choosemodalStyle.bind(this)}
          />
        ),
      })
    }
  }
  chooseRobots(arr) {
    console.log(arr)
    this.setState({
      chooseList: arr,
    })
  }
  changeCurrentIndex(num) {
    this.setState({
      currentIndex: num,
      currentButton: 0,
      currentSort: false,
    })
  }
  changeInputTips(flag) {
    this.setState({
      showInputTips: flag,
    })
  }
  changeContent(txt, sty, queriesIndex) {
    console.log(txt, sty, queriesIndex, '~~~~~~')
    if (queriesIndex != -1) {
      this.setState({
        changeContent: txt,
        chooseQueTypes: sty,
        queriesIndex: queriesIndex,
      })
    } else {
      if (sty) {
        this.setState({
          changeContent: txt,
          chooseQueTypes: sty,
        })
      } else {
        this.setState({
          changeContent: txt,
        })
      }
    }
    this.setState({
      showInputTips: false,
    })
  }
  // 模板选择确定
  choosemodalStyle(txt, curScenes, lang) {
    // console.log('qy-txt',txt);
    // console.log('qy-curScenes',curScenes);
    // console.log('qy-lang',lang);
    let oldLang = this.state.lang
    this.setState(
      {
        lang,
        backImg: '',
      },
      () => {
        if (lang != oldLang) {
          this.getConfigList(() => {
            this.chooseRobots_success(txt, curScenes)
          })
          return
        }
        let config_id = 'default_id'
        // 切换都要场景就要重新获取
        if (curScenes != this.state.curScenes) {
          this.simple_config_json = {}
          this.common_config_json = {}
        }
        if (
          curScenes != this.state.curScenes ||
          (txt == 'Simple' && Object.keys(this.simple_config_json).length == 0) ||
          (txt != 'Simple' && Object.keys(this.common_config_json).length == 0)
        ) {
          //切换场景
          switch (curScenes) {
            // case "默认":
            case __('customHome.sceneType.default'):
              config_id = 'default_id'
              break
            // case "商场":
            case __('customHome.sceneType.market'):
              config_id = 'market_default_id'
              break
            // case "酒店":
            case __('customHome.sceneType.hotel'):
              config_id = 'hotel_default_id'
              break
            // case "银行":
            case __('customHome.sceneType.bank'):
              config_id = 'bank_default_id'
              break
            case __('customHome.sceneType.museum'):
              // case "博物馆":
              config_id = 'museum_default_id'
              // config_id = "museum_museum_id"
              break
            case __('customHome.sceneType.library'):
              // case "图书馆":
              config_id = 'library_default_id'
              break
            case 'KTV':
              // case "":
              config_id = 'ktv_default_id'
              break
          }
          if (curScenes != this.state.curScenes) {
            this.setState({
              SimplebackImg: '',
              backImg: '',
            })
          }

          if (txt == 'Simple') {
            config_id = config_id.replace('default', 'simple')
            if (config_id == 'simple_id') {
              if (curScenes != 'KTV') {
                config_id = 'default_simple_id'
              } else {
                config_id = 'ktv_simple_id'
              }
            }
          }
          this.gethomeModule(
            config_id,
            '',
            () => {
              this.chooseRobots_success(txt, curScenes)
            },
            lang,
          )
        } else if (
          txt == 'Simple' &&
          curScenes == this.state.curScenes &&
          Object.keys(this.simple_config_json).length > 0
        ) {
          let config_json = this.simple_config_json
          console.log('get_home_module 12')
          this.props.dispatch({
            type: 'GET_HOME_MODULE',
            data: {
              homeModule: config_json,
            },
          })
          this.chooseRobots_success(txt, curScenes)
        } else if (
          txt != 'Simple' &&
          curScenes == this.state.curScenes &&
          Object.keys(this.common_config_json).length > 0
        ) {
          let config_json = this.common_config_json
          console.log('get_home_module 13')
          this.props.dispatch({
            type: 'GET_HOME_MODULE',
            data: {
              homeModule: config_json,
            },
          })
          this.chooseRobots_success(txt, curScenes)
        } else {
          this.chooseRobots_success(txt, curScenes)
        }
      },
    )
  }
  // 模板选择回调函数
  chooseRobots_success(txt, curScenes) {
    let backUrl = 'module_public/module_skill_home/home1.png'
    this.setState({
      isShowSetting: false,
      modalStyle: txt,
      curScenes: curScenes,
      changeContent: 'tips',
      chooseQueTypes: '1',
      currentButton: 0,
      currentSort: false,
      currentIndex: -2,
    })

    this.props.homeModule.templateInfo.templateCode = txt
    // debugger
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'templateCode',
        content: this.props.homeModule.templateInfo.templateCode,
      },
    })

    if (curScenes == 'KTV') {
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'scenes',
          content: 'KTV',
        },
      })
    }
    if (txt == 'List') {
      backUrl = 'module_public/module_skill_home/home1.png'
      for (var j = 0; j <= this.props.homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (this.props.homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j <= 1) {
              //this.props.homeModule.skills[j].icon = this.state.iconJson[i].list_icon2
              if (this.state.lang != 'chs') {
                // return;
              } else {
                if (this.props.homeModule.skills[j].label == 'chat2') {
                  this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].list_icon2
                } else {
                  this.props.homeModule.skills[j].icon = this.state.iconJson[i].list_icon2
                }
              }
            } else {
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].list_icon1
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].list_icon1
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 26,
                content: this.props.homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Card') {
      backUrl = 'module_public/module_skill_home/home2.png'
      //切换图标
      for (var j = 0; j <= this.props.homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (this.props.homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (this.props.homeModule.skills[j].label == 'chat2') {
              this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].card_icon
            } else {
              this.props.homeModule.skills[j].icon = this.state.iconJson[i].card_icon
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 27,
                content: this.props.homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Technical') {
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'titleBarType',
          content: 'text',
        },
      })
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'titleBarContent',
          content: __('customHome.warm.callMe'),
        },
      })
      backUrl = 'module_public/module_skill_home/home3.png'
      //切换图标
      // for (var i = 0; i <= this.state.iconJson.length - 2; i++) {
      // 	this.props.homeModule.skills[i].icon = "";
      // 	this.props.dispatch({
      // 		type: 'CHANGE_MODULE_SKILL',
      // 		data: {
      // 			type: 'icon',
      // 			index: i,
      // 			content: this.props.homeModule.skills[i]
      // 		}
      // 	})
      // }
    } else if (txt == 'Standard') {
      if (this.props.homeModule.templateInfo.titleBarType != 'text') {
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarType',
            content: 'text',
          },
        })
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarContent',
            content: __('customHome.warm.callMe'),
          },
        })
      }

      backUrl = 'module_public/module_skill_home/home4.png'
      //切换图标
      for (var j = 0; j <= this.props.homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (this.props.homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j == 0) {
              //this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon;
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].standard2_icon
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon
              }
            } else {
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].standard1_icon
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard1_icon
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 28,
                content: this.props.homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Simple') {
      backUrl = 'module_public/module_skill_home/simpleBg.png'
      if (this.props.homeModule.templateInfo.titleBarType != 'text') {
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarType',
            content: 'text',
          },
        })
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarContent',
            content: __('customHome.warm.callMe'),
          },
        })
      }

      if (this.props.homeModule.templateInfo.conciseContent == undefined)
        [
          this.props.dispatch({
            type: 'CHANGE_INFO',
            data: {
              type: 'conciseContent',
              content: {
                title: '猎户风景区欢迎你',
                image: 'module_public/module_skill_home/conciseimage.png',
                suggestion: '我要买门票',
              },
            },
          }),
        ]

      // backUrl = "module_public/module_skill_home/home4.png";
      //切换图标
      for (var j = 0; j <= this.props.homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (this.props.homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j == 0) {
              //this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon;
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].standard2_icon
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon
              }
            } else {
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].standard1_icon
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard1_icon
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 29,
                content: this.props.homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    }

    // 有配置，就是用配置里的背景 // 简约式除外
    if (this.props.homeModule.templateInfo.background && this.props.homeModule.templateInfo.background.length > 44) {
      backUrl = this.props.homeModule.templateInfo.background
      this.state.backImg == '' &&
        this.setState({
          //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
          backImg: backUrl,
        })
    }
    if (this.props.homeModule.templateInfo.templateCode == 'Simple' && this.state.SimplebackImg) {
      backUrl = this.state.SimplebackImg
    }

    // 有配置，就是用配置里的faceBackground背景 // 简约式除外
    if (this.state.faceBackground && this.state.faceBackground.length > 0) {
      let _faceBackground = this.state.faceBackground || this.props.homeModule.templateInfo.faceBackground
      this.state.faceBackground == '' &&
        this.setState({
          //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
          faceBackground: _faceBackground,
        })

      if (_faceBackground) {
        console.log('改背景 3')
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            key: 8,
            type: 'faceBackground',
            content: _faceBackground,
          },
        })
      }
    }

    //切换背景图
    if (this.state.backImg != '') {
      backUrl = this.state.backImg
    }
    if (this.props.homeModule.templateInfo.templateCode == 'Simple' && this.state.SimplebackImg) {
      backUrl = this.state.SimplebackImg
    }
    console.log('改背景 8')
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'background',
        content: backUrl,
      },
    })

    closeModal()
    if (
      this.isKTV &&
      this.props.homeModule.templateInfo.templateCode != 'Simple' &&
      !this.state.lowOpkVersion &&
      !this.state.isDefault
    ) {
      this.setState({
        templateNeedUpdate: true,
      })
      modal({
        title: __('customHome.modalMsg.tip'),
        fullscreen: false,
        content: (
          <div>
            <p>{__('customHome.modalMsg.pageNewChange')}</p>
            <p>{__('customHome.modalMsg.pageSetChange')}</p>
          </div>
        ),
        cancel: false,
        confirm: __('customHome.modalMsg.cureChange'),
        onConfirm: () => {
          this.setState(
            {
              templateNeedUpdate: false,
            },
            () => {
              this.choosemodalStyle('Simple', 'KTV', 'chs')
            },
          )
        },
      })
      let lowOpkVersion = this.state.lowOpkVersion
      let isDefault = this.state.isDefault
      window.tipSetData({
        templateNeedUpdate: true,
        lowOpkVersion,
        isDefault,
      })
    } else {
      let lowOpkVersion = this.state.lowOpkVersion
      let isDefault = this.state.isDefault
      window.tipSetData({
        templateNeedUpdate: false,
        lowOpkVersion,
        isDefault,
      })
    }
  }
  deleteBackImg() {
    this.setState({
      backImg: '',
    })
    if (this.props.homeModule.templateInfo.templateCode == 'Simple') {
      this.setState({
        SimplebackImg: '',
      })
    }
  }
  deleteFaceImg() {
    this.setState({
      faceBackground: '',
    })
  }
  direct_uploadData() {
    console.log('直接发布模版')
    let formData = new FormData()
    let config_info_list_json = {
      config_id: sessionStorage.getItem('config_id'),
      version: sessionStorage.getItem('version'),
    }
    let config_info_list_arr = []
    config_info_list_arr.push(config_info_list_json)
    formData.append('module_code', 'module_skill_home')
    formData.append('config_info_list', JSON.stringify(config_info_list_arr))
    formData.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
    directPublishModule(formData)
      .then((res) => {
        console.log('保存并发布模块11')
        this.uploadDataPorgress()
      })
      .catch((res) => {
        console.log(res)
      })
  }
  uploadData() {
    const lang = this.state.lang
    _czc.push(['_trackEvent', 'home', 'publish'])
    if (this.state.chooseList.length == 0) {
      message.error(__('customHome.modalMsg.selectTheRobotToSync'))
      // modal({
      //     content: __('customHome.modalMsg.selectTheRobotToSync'),
      //     cancel: false,
      //     confirm: __('customHome.btn.gotIt'),
      // });
      return
    }
    // console.log(this.props.originHomeModule);
    // console.log(this.props.homeModule)
    // if(JSON.stringify(this.props.homeModule) == JSON.stringify(this.props.originHomeModule) && sessionStorage.getItem("version")!=""){
    // 	console.log('same');
    // 	this.direct_uploadData();
    // 	return;
    // }

    console.log(this.props.homeModule)
    let formData = new FormData()
    var config_json = this.props.homeModule
    if (lang === 'chs') {
      config_json.lang = 'zh_CN'
      formData.append('lang', 'zh_CN')
    }
    if (lang === 'english') {
      config_json.lang = 'en_US'
      formData.append('lang', 'en_US')
    }
    if (lang === 'arabic') {
      config_json.lang = 'ar_SA'
      // formData.append('lang', 'ar_SA');
      formData.append('lang', 'zh_CN')
    }

    console.log(config_json)
    var config_json_str = JSON.stringify(config_json)
    var resource_path_list = []
    if (this.props.homeModule.templateInfo.titleBarType == 'Image') {
      resource_path_list.push(this.props.homeModule.templateInfo.titleBarContent) //顶部logo图
    }
    resource_path_list.push(this.props.homeModule.templateInfo.background) //背景图
    if (this.props.homeModule.templateInfo.faceBackground && this.props.homeModule.templateInfo.faceBackground != '') {
      resource_path_list.push(this.props.homeModule.templateInfo.faceBackground)
    } //表情页背景图
    if (this.props.homeModule.templateInfo.conciseContent && this.props.homeModule.templateInfo.conciseContent != '') {
      resource_path_list.push(this.props.homeModule.templateInfo.conciseContent.image)
    } // 简洁式
    if (this.state.modalStyle != 'Technical') {
      this.props.homeModule.skills.map((item, index) => {
        resource_path_list.push(item.icon) //skills图
      })
    }
    console.log('图片数据- 4')
    console.log(resource_path_list)
    var resource_path_list_str = JSON.stringify(resource_path_list)
    var public_resource_path_list = []
    var public_resource_path_list_str = JSON.stringify(public_resource_path_list)
    if (this.state.lang == 'english') {
      // formData.append('module_code', 'module_skill_home_en');
      formData.append('module_code', 'module_skill_home')
    } else if (this.state.lang == 'arabic') {
      formData.append('module_code', 'module_skill_home_ar')
    } else {
      formData.append('module_code', 'module_skill_home')
    }
    formData.append('config_id', sessionStorage.getItem('config_id'))
    formData.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
    formData.append('config_json', config_json_str)
    formData.append('name', 'list')
    formData.append('version', sessionStorage.getItem('version'))
    formData.append('resource_path_list', resource_path_list_str) //资源列表(选填)
    formData.append('public_resource_path_list', public_resource_path_list_str) //公共资源列表(选填)
    publishModule(formData)
      .then((res) => {
        console.log('保存并发布模块12')
        console.log(res)
        sessionStorage.setItem('version', res.version)
        this.uploadDataPorgress()
      })
      .catch((res) => {
        console.log(res)
      })
  }
  uploadDataPorgress() {
    console.log('获取同步进度')
    modal({
      title: false,
      confirm: false,
      cancel: false,
      fullscreen: false,
      content: <Progress />,
    })
    document.getElementsByClassName('btn-close')[0].style.display = 'none'
    let _time = 0
    this.interval = setInterval(() => {
      if (_time >= 20) {
        closeModal()
        this.setState(
          {
            allNum: 0,
          },
          () => {
            // message.error( __('customHome.modalMsg.syncingTimeout'))
            message.success(__('DISTANT_GREETINGS.SUCCESS_PUBLISHED', { num: this.state.chooseList.length }))
            // modal({
            //     content: __('customHome.modalMsg.syncingTimeout'),
            //     cancel: false,
            //     confirm: __('customHome.btn.gotIt'),
            // });
          },
        )
        clearInterval(this.interval)
        this.interval = null
        return
      }
      _time++
      let formData2 = new FormData()
      let mdcode = ''
      if (this.state.lang == 'english') {
        mdcode = 'module_skill_home'
        formData2.append('lang', 'en_US')
      } else if (this.state.lang == 'arabic') {
        mdcode = 'module_skill_home_ar'
        formData2.append('lang', 'zh_CN')
      } else {
        mdcode = 'module_skill_home'
        formData2.append('lang', 'zh_CN')
      }

      formData2.append('module_code', mdcode)
      formData2.append('config_id', sessionStorage.getItem('config_id'))
      formData2.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
      formData2.append('version', sessionStorage.getItem('version'))
      getModuleStatus(formData2)
        .then((res) => {
          console.log(res)
          this.setState({
            showWarning: false,
            allNum: res.pull_total_num,
            progressNum: res.pull_suc_num,
          })
          if (res.pull_total_num == res.pull_suc_num && res.pull_suc_num != 0) {
            closeModal()
            clearInterval(this.interval)
            message.success(__('DISTANT_GREETINGS.SUCCESS_PUBLISHED', { num: this.state.chooseList.length }))
            // modal({
            //     content: __('customHome.modalMsg.syncingSuccessful'),
            //     cancel: false,
            //     confirm: __('customHome.btn.gotIt'),
            // });
            this.setState({
              allNum: 0,
            })
          }
        })
        .catch((res) => {
          console.log('获取同步进度失败')
          clearInterval(this.interval)
        })
    }, 2000)
    // setTimeout(function() {//拿不到this
    // 	console.log("1分钟")
    // 	clearInterval(this.interval);
    // 	modal({
    // 		content: '同步成功：'+this.state.progressNum+"台",
    // 		cancel: false,
    // 		confirm:  __('customHome.btn.gotIt'),
    // 	});
    // },1*60*1000);//1分钟执行一次
  }
  selectImg(e, type, idx) {
    console.log(e)
    console.log(type)
    console.log(idx)

    const file = !e.target ? e : e.target.files[0]
    let reader = new FileReader()
    var uploadImgWidth = 0,
      uploadImgHeight = 0
    if (!file) {
      return
    }
    if (file.type != 'image/png' && file.type != 'image/jpeg') {
      message.error(__('customHome.pic.uploadFilesFormat', { format: 'png/jpg/jpeg' }))
      // modal({
      //     content: __('customHome.pic.uploadFilesFormat', { format: 'png/jpg/jpeg' }),
      //     cancel: false,
      //     confirm: __('customHome.btn.gotIt')
      // })
      return
    }
    switch (type) {
      case 'backImg':
        uploadImgWidth = 1200
        uploadImgHeight = 1920
        break
      case 'facebackImg':
        uploadImgWidth = 1200
        uploadImgHeight = 1920
        break
      case 'logo':
        uploadImgWidth = 1000
        uploadImgHeight = 200
        break
      case 'card_icon':
        uploadImgWidth = 550
        uploadImgHeight = 380
        break
      case 'list_icon1':
        uploadImgWidth = 490
        uploadImgHeight = 260
        break
      case 'list_icon2':
        uploadImgWidth = 110
        uploadImgHeight = 110
        break
      case 'standard1_icon':
        uploadImgWidth = 420
        uploadImgHeight = 230
        break
      case 'standard2_icon':
        uploadImgWidth = 392
        uploadImgHeight = 480
        break
      case 'conciseImg':
        uploadImgWidth = 980
        uploadImgHeight = 846
    }

    if (file.size / 1024 / 1000 > 5) {
      message.error(__('customHome.pic.picSizeLimit2'))
      // modal({
      //     content: __('customHome.pic.picSizeLimit2'),
      //     cancel: false,
      //     confirm: __('customHome.btn.gotIt')
      // })
      return
    }
    var _this = this
    reader.onload = function (e) {
      let image = new Image()
      image.onload = function () {
        if (image.width != uploadImgWidth || image.height != uploadImgHeight) {
          console.log(image.width)
          console.log(image.height)

          return message.error(
            __('customHome.pic.uploadpicDimensions') +
              ':' +
              uploadImgWidth +
              '*' +
              uploadImgHeight +
              __('customHome.btn.pixels'),
          )
          // return modal({
          //     title: __('customHome.pic.WrongPictureDimensions'),
          //     content: __('customHome.pic.uploadpicDimensions') + ':' + uploadImgWidth + '*' + uploadImgHeight + __('customHome.btn.pixels'),
          //     cancel: false,
          //     confirm: __('customHome.btn.gotIt')
          // })
        } else {
          let formData = new FormData()
          formData.append('resource_file', file)
          formData.append('module_code', 'module_skill_home')
          formData.append('config_id', sessionStorage.getItem('config_id'))
          formData.append('resource_type', 'image')
          formData.append('resource_file', file.name)
          formData.append('version', '')
          uploadResource(formData)
            .then((res) => {
              console.log('上传图片')
              let upload_url = res.resource_url
              console.log(upload_url)
              if (type == 'backImg') {
                _this.setState({
                  SimplebackImg: upload_url,
                })
              }
              if (type != 'backImg') {
                _this.setState({
                  uploadUrl: upload_url,
                })
                if (type == 'facebackImg') {
                  _this.setState({
                    faceBackground: upload_url,
                  })
                }
              } else {
                _this.setState({
                  backImg: upload_url,
                })
              }

              if (type == 'logo') {
                _this.setState({
                  logo: upload_url,
                })
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'titleBarType',
                    content: 'Image',
                  },
                }) //更改json标题类型
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'titleBarContent',
                    content: upload_url,
                  },
                }) //更改json标题内容
              } else if (type == 'backImg') {
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'background',
                    content: upload_url,
                  },
                }) //更改json标题内容
              } else if (type == 'facebackImg') {
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    key: 9,
                    type: 'faceBackground',
                    content: upload_url,
                  },
                }) //更改json标题内容
              } else if (type == 'conciseImg') {
                let conciseContent = _this.props.homeModule.templateInfo.conciseContent
                conciseContent.image = upload_url
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'conciseContent',
                    content: conciseContent,
                  },
                })
              } else {
                _this.props.homeModule.skills[idx].icon = upload_url
                _this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'icon',
                    index: idx,
                    key: 30,
                    content: _this.props.homeModule.skills[idx],
                  },
                }) //更改skills中icon路径
              }
              message.success(__('customHome.pic.uploadSuccess'))
              // modal({
              //     content: __('customHome.pic.uploadSuccess'),
              //     cancel: false,
              // });
            })
            .catch((res) => {
              console.log(res)
              message.error(__('customHome.pic.uploadFailed'))
              // modal({
              //     content: __('customHome.pic.uploadFailed'),
              //     cancel: false,
              //     confirm: __('customHome.btn.gotIt')
              // })
            })
        }
      }
      image.src = e.target.result
    }
    reader.readAsDataURL(file)
  }
  //
  checkJson(kind) {
    if (kind != 'bottomQueries') {
      this.setState({
        queryStyle: '',
        backImgstyle: '',
        queries_idx: 0,
      })
    }
    if (JSON.stringify(this.props.robots) == '{}') {
      message.error(__('customHome.modalMsg.noRobotsOfCompany'))
      // modal({
      //     content: __('customHome.modalMsg.noRobotsOfCompany'),
      //     cancel: false,
      //     confirm: __('customHome.btn.confirm')
      // });
      return
    }
    let json = this.props.homeModule
    //console.log(json);
    let title = json.templateInfo.titleBarContent
    let flag = true
    if (title.trim() == '') {
      message.error(__('customHome.modalMsg.enterTitleName'))
      // modal({
      //     content: __('customHome.modalMsg.enterTitleName'),
      //     cancel: false,
      //     confirm: __('customHome.btn.confirm')
      // });
      flag = false
      return
    }
    var standardQueries = []
    json.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
    })
    standardQueries.map((item, index) => {
      // if (item == "") {
      if (item.trim() == '') {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))
        // modal({
        //     content: __('customHome.modalMsg.enterRecommendedQuestions'),
        //     cancel: false,
        //     confirm: __('customHome.btn.confirm')
        // });
        flag = false
        return
      }
    })
    if (this.state.overVersion51 && json.templateInfo.bubbleContents && json.templateInfo.bubbleContents.faceStyle) {
      if (json.templateInfo.bubbleContents.robot && json.templateInfo.bubbleContents.faceStyle == 'Bubble') {
        if (json.templateInfo.bubbleContents.robot.length == 0) {
          message.error(__('customHome.modalMsg.bubbleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.bubbleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
      }
    }
    // 配置英文卡片模式标题，内容不能为空
    if (this.state.overVersion57 && json.templateInfo.bubbleContents && json.templateInfo.bubbleContents.faceStyle) {
      if (json.templateInfo.bubbleContents.robot && json.templateInfo.bubbleContents.faceStyle == 'Card') {
        if (json.templateInfo.bubbleContents.robot.length == 0) {
          message.error(__('customHome.modalMsg.bubbleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.bubbleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
        if (json.templateInfo.bubbleContents.title.trim().length == 0) {
          message.error(__('customHome.modalMsg.cardTitleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.cardTitleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
      }
    }

    json.skills.map((item, index) => {
      // if (item.title.trim() == "") {
      // 	modal({
      // 		content: __('customHome.modalMsg.enterFunctionName'),
      // 		cancel: false,
      // 		confirm: __('customHome.btn.confirm')
      // 	});
      // 	flag = false
      // 	return;
      // }
      if (item.tip.trim() == '' || item.tip == undefined) {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))
        // modal({
        //     content: __('customHome.modalMsg.enterRecommendedQuestions'),
        //     cancel: false,
        //     confirm: __('customHome.btn.confirm')
        // });
        flag = false
        return
      }
    })

    if (json.templateInfo.templateCode == 'Simple') {
      if (json.templateInfo.conciseContent.title.trim() == '') {
        message.error(__('customHome.modalMsg.enterTitleName'))

        flag = false
        return
      } else if (json.templateInfo.conciseContent.suggestion.trim() == '') {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))

        flag = false
        return
      }
    }
    return flag
  }

  bottomQueries(queries_style, idx) {
    if (queries_style == 'mainPage') {
      this.setState({
        queryStyle: '',
      })
    } else {
      this.setState({
        queryStyle: queries_style,
      })
    }
    this.setState({
      queries_idx: idx,
    })
  }
  changeTab_backImg(j) {
    if (j == 1) {
      this.setState({
        backImgstyle: 'face',
      })
    } else {
      this.setState({
        backImgstyle: '',
      })
    }
  }
  addCover(flag) {
    this.setState({
      // fiveTimer: flag,
      haveCover: flag,
    })
  }
  checkAllQuery(cb) {
    this.setState({
      showLoading: true,
    })
    closeModal()
    let existed = false
    let questions = [],
      questionList = []
    let json = this.props.homeModule
    var standardQueries = []
    json.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
        standardQueries.map((item, index) => {
          if (item != '') {
            questions.push(item)
          }
        })
      }
    })
    if (json.templateInfo.templateCode != 'Simple') {
      json.skills.map((item, index) => {
        if (item.tip != '' && item.tip != undefined) {
          questions.push(item.tip)
        }
      })
    }

    let data = {
      question_list: JSON.stringify(new Set(questions)),
    }
    searchAnswer(data)
      .then((res) => {
        console.log(res)
        res.items.map((item) => {
          if (!item.existed) {
            existed = true
            questionList.push(item.question)
          }
        })
        this.setState({
          showLoading: false,
        })
        console.log(questionList)
        if (existed) {
          modal({
            title: __('customHome.modalMsg.noAnswersInHomePage'),
            content: <QueryItem questionList={questionList}></QueryItem>,
            cancel: false,
            confirm: false,
          })
        } else {
          cb && cb()
        }
      })
      .catch({})
  }
  changeRobotData(id, sn) {
    if (this.state.currentRobotId != id) {
      this.simple_config_json = {} //重置缓存的配置
      this.common_config_json = {}
    }
    this.setState({
      currentRobotId: id,
      currentRobotSn: sn,
      templateNeedUpdate: false,
      lowOpkVersion: false,
    })
  }
  changeRobot(id) {
    this.props.dispatch(selectRobot(id))
    console.log('机器人id@@@@', id)
    const robotType = this.state.robotType,
      roleStr = this.state.roleStr != undefined && this.state.roleStr
    if (robotType == 'local') {
      setLocalStorageItem('localRobotId', id)
    } else {
      setLocalStorageItem('localRobotId', id)
      setLocalStorageItem('globallRobotId', id)
      if (roleStr == '0' || roleStr == '1') {
        setLocalStorageItem('receptionRobotId', id)
      }
      if (roleStr == '2') {
        setLocalStorageItem('docentRobotId', id)
      }
    }
    this.getRobotName(id)
  }
}
