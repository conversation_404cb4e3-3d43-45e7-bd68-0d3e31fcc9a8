@import '~base.less';

.component {
  // background: #f6f9f9;
  position: relative;
  overflow: hidden;
  min-height: 36rem;
  .ant-upload-select-picture-card {
    display: block !important;
    opacity: 0;
    width: 2.85rem;
    height: 2.85rem;
  }
  .ant-upload-list-picture-card-container {
    display: none;
  }
  input::-webkit-input-placeholder,
  textarea::-webkit-input-placeholder {
    color: #ccd2d6 !important;
    font-size: 12px;
  }

  input {
    -webkit-appearance: none;
  }

  button {
    // border: none;
    outline: none;
    -webkit-highlight: none;
  }

  p {
    margin: 0;
    padding: 0;
  }

  ul,
  li {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  // .tip_cover {
  //     background-color: rgba(85, 195, 251, 0.50);
  //     border-radius: 8px;
  // }  1102

  .have_cover {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    // margin-left:-4px;
  }

  .dib {
    display: inline-block;
  }
  .dbp {
    display: none;
  }

  .tip_empty {
    background: none;
    border-radius: 0px;
  }

  .loading {
    position: fixed;
    z-index: 999999;
    left: 0rem;
    width: 100%;
    pointer-events: all;
    background: rgba(248, 250, 250, 0.9);
    height: 100%;
    top: 0px;

    .loading_modal {
      position: relative;
      top: 50%;
      transform: translate(0%, -50%);
      text-align: center;
      margin: 0 auto;
      width: 257px;
      height: 126px;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.21);
      background-color: #ffffff;
      border-radius: 4px;
      &:lang(en) {
        width: 330px;
      }

      img {
        width: 60px;
        height: 60px;
        margin-top: 18px;
      }

      span {
        color: #979797;
        font-size: 16px;
        margin-top: 13px;
        display: block;
        white-space: nowrap;
      }
    }
  }

  .click_background {
    background: #3776ef !important;
    color: #fff !important;
  }
  .combox {
    //     display: flex;
    // -webkit-align-items: center;
    // align-items: center;
    // -webkit-justify-content: space-between;
    // justify-content: space-between;
    height: 3rem;
    background: #fff;
    // margin-bottom: .5rem;
    padding: 1rem;
    position: relative;
  }
  .btnBox {
    position: absolute;
    right: 0;
    right: 32px;
    top: 0;
    .utime {
      position: absolute;
      right: 380px;
      top: 1rem;
      display: inline-block;
      width: 200px;
      height: 22px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #9b9b9b;
      line-height: 22px;
    }
    .tips_button {
      width: 7.6rem;
      padding: 0.65rem 0;
      font-size: 0.8rem;
      color: #fff;
      letter-spacing: 0;
      background: #3776ef;
      border-radius: 5rem;
      text-align: center;
      margin: 0 auto;
      text-indent: 1.25rem;
      position: relative;
      cursor: pointer;
      // float: right;

      position: absolute;
      right: 0;
      margin-top: 6px;
      ::after {
        content: '';
        display: block;
        width: 1.2rem;
        height: 1.2rem;
        position: absolute;
        left: 1.2rem;
        top: 50%;
        margin-top: -0.6rem;
        background: url('@assets/map/card_issue.svg') no-repeat;
        background-size: 1.2rem 1.2rem;
      }
    }
  }

  .robot-list {
    // float: right;
    position: relative;
    // margin-top: -6px;
    border: 1px solid #3776ef;
    border-radius: 25px;
    white-space: nowrap;
    padding: 0px 17px;
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-top: 8px;
    // right:153px;
    right: 8.65rem;

    .icon {
      width: 14px;
      height: 14px;
    }
  }

  .progress_modal {
    .progress_title {
      padding: 38px 30px;
    }
  }

  .home-title-bar {
    float: right;
    margin-top: 12px;

    .progress {
      font-size: 16px;
      color: #3776ef;
      letter-spacing: 0;
      padding: 8px 17px;
      display: inline-block;
      margin-right: 40px;
      float: right;
    }

    .warning {
      display: inline-block;
      font-size: 14px;
      line-height: 14px;
      color: #ffffff;
      padding: 10px 14px;
      background: #ff713c;
      border-radius: 15px;
      margin-right: 40px;
      float: right;
      margin-top: 5px;
    }

    .success_setting {
      border-radius: 25px;
      font-size: 18px;
      letter-spacing: 0;
      line-height: 24px;
      padding: 6px 17px;
      cursor: pointer;
      float: right;
      box-sizing: border-box;
      color: #ffffff;
      background: #3776ef;
      // display: none;

      &:hover {
        margin-top: -3px;
      }

      &:lang(en) {
        line-height: initial;
      }
    }

    .cancel_setting {
      margin-left: 40px;
      background: #fff;
      color: #3776ef;
      border: 1px solid #3776ef;
    }

    .recover_setting {
      background: #ffffff;
      border: 1px solid #3776ef;
      color: #3776ef;
      margin: 0 40px;
    }
  }

  .content {
    display: flex;
    height: calc(100vh - 80px);
    min-height: 600px;
    position: relative;
    top: 10px;
    overflow: hidden;
    min-height: 30.85rem;

    .preview,
    .setting {
      background: #ffffff;
      // box-shadow: 0 20px 20px 0 rgba(161, 170, 178, 0.15);
      border-radius: 8px;
      height: 100%;
    }

    .preview {
      width: 395px;
      margin-right: 20px;
      padding: 10px;
    }
    // for-mini
    .mini_preview {
      width: 598px;
      height: 345px;
    }

    .setting::-webkit-scrollbar {
      display: none;
    }

    .setting {
      position: relative;
      flex: 1;
      // max-width: 800px;
      padding: 40px 25px;
      overflow-y: scroll;
    }
  }
  .buttons-swiper-box {
    height: 500px;
    height: 21.3rem;
    overflow: hidden;
    p {
      text-align: center;
    }
    .simple-swiper-container-ver {
      height: 10rem;
    }
  }
  .noRobot {
    padding: 22px 20px 20px 20px;
    margin-top: 150px;
    dl {
      display: flex;
      align-content: center;
      justify-content: center;
      flex-direction: column;
      dd {
        font-size: 18px;
        color: #3776ef;
        letter-spacing: 0;
        text-align: center;
        margin-bottom: 14px;
        margin-left: 0;
      }
      dt {
        display: flex;
        align-content: center;
        justify-content: center;
        img {
          width: 674px;
          height: 240px;
          margin: 0;
          padding: 0;
        }
      }
    }
    .addRobotBtn {
      width: 222px;
      height: 44px;
      font-size: 18px;
      margin: 0 auto;
    }
  }
}
