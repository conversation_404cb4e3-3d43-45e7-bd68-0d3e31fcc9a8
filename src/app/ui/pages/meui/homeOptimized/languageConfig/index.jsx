import React, { Component } from 'react'
import { extend } from 'koot'
import styles from './index.module.less'
import { Switch, Checkbox } from 'antd'
import { connect } from 'react-redux'
import _ from 'lodash'
import 'antd/es/modal/style'
import 'antd/es/slider/style'

const CheckboxGroup = Checkbox.Group
const cn_langs = ['zh_CN', 'en_US', 'zh_GD', 'zh_TW']

@connect((state) => {
  return {
    homeModule: state._home.homeModule,
    curLang: state.user.curLang,
    user: state.user,
    cropId: state.user.detail.corp_uuid,
  }
})
@extend({
  styles,
})
class LanguageConfig extends Component {
  constructor(props) {
    super(props)
    const { user, homeModule } = this.props
    const { lang_config = {} } = homeModule.templateInfo || {}
    const allLangList = this.attributeConversion(
      user.detail.isCnData
        ? user.langData.filter((item) => cn_langs.findIndex((el) => el == item.lang_code) !== -1)
        : user.langData,
      true,
    )
    const lang_list = (lang_config.lang_list || []).filter((item) =>
      allLangList.some((el) => el.value == item.lang_code),
    )
    this.state = {
      allLangList,
      indeterminate: !!lang_list.length && lang_list.length < allLangList.length,
      checkedLanguages: lang_list,
      checkedLangCodes: lang_list.map((item) => item.lang_code),
      checkAll: !!lang_list.length && lang_list.length == allLangList.length,
      is_open: lang_config && lang_config.is_open ? lang_config.is_open : false,
    }
  }

  componentDidMount() {
    const { homeModule } = this.props
    const { checkedLanguages } = this.state
    const { lang_config = {} } = homeModule.templateInfo || {}
    const lang_list = lang_config.lang_list || []
    if (
      checkedLanguages.length != lang_list.length ||
      lang_list.every((item, i) => item.lang_code == checkedLanguages[i].lang_code)
    ) {
      this.updataHomeModuleLangConfig()
    }
  }

  attributeConversion = (list, isOut) => {
    return list.map((item) => {
      if (isOut) {
        return {
          label: item.name,
          value: item.lang_code,
        }
      }
      return {
        name: item.label,
        lang_code: item.value,
      }
    })
  }

  //同步语言数据列表
  getCheckedLangData = (code_list) => {
    const { allLangList } = this.state
    return code_list.map((item) => {
      const lang = allLangList.find((el) => el.value == item)
      return {
        lang_code: lang.value,
        lang_name: lang.label,
      }
    })
  }

  shouldComponentUpdate(nextProps, nextState) {
    const newTemplateInfo = nextProps.homeModule.templateInfo || {}
    const templateInfo = this.props.homeModule.templateInfo || {}
    // console.log('内存', { lang: this.props.lang != nextProps.lang, is: !_.isEqual(templateInfo, newTemplateInfo) })
    if (this.props.lang != nextProps.lang || !_.isEqual(templateInfo, newTemplateInfo)) {
      this.updateState(newTemplateInfo)
      return true
    }
    if (
      this.state.is_open != nextState.is_open ||
      nextState.checkedLangCodes.length != this.state.checkedLangCodes.length ||
      nextState.checkedLangCodes.every((item, i) => item == this.state.checkedLangCodes[i])
    ) {
      return true
    }
  }

  //判断并更新数据
  updateState = (newTemplateInfo) => {
    const { is_open, checkedLanguages, allLangList } = this.state

    const lang_config = newTemplateInfo.lang_config || {}
    const lang_list = (lang_config.lang_list || []).filter((item) =>
      allLangList.some((el) => el.value == item.lang_code),
    )
    const is_update_is_open = !!lang_config.is_open == is_open
    const is_update_lang_list =
      lang_list.length == checkedLanguages.length &&
      lang_list.every((item, i) => item.lang_code == checkedLanguages[i].lang_code)
    if (!is_update_is_open || !is_update_lang_list) {
      this.setState(
        {
          is_open: lang_config.is_open,
          checkedLanguages: lang_list || [],
          //indeterminate: !!lang_list.length && lang_list.length < allLangList.length,
          checkedLangCodes: lang_list.map((item) => item.lang_code),
          //checkAll: !!lang_list.length && lang_list.length == allLangList.length,
        },
        () => {
          this.updataHomeModuleLangConfig
        },
      )
    }
    return true
  }

  onChange = (list) => {
    this.setState(
      {
        //checkAll: !!list.length && list.length == allLangList.length,
        //indeterminate: !!list.length && list.length < allLangList.length,
        checkedLangCodes: list,
        checkedLanguages: this.getCheckedLangData(list),
      },
      () => {
        this.updataHomeModuleLangConfig()
      },
    )
  }

  //全选改变时
  onCheckAllChange = (e) => {
    const { allLangList } = this.state
    this.setState(
      {
        checkAll: e.target.checked,
        indeterminate: false,
        checkedLanguages: e.target.checked ? allLangList : [],
        checkedLangCodes: e.target.checked ? allLangList.map((item) => item.value) : [],
      },
      this.updataHomeModuleLangConfig,
    )
  }

  switchOnChange = (is_open) => {
    this.setState(
      {
        is_open,
      },
      () => {
        this.updataHomeModuleLangConfig()
      },
    )
  }

  updataHomeModuleLangConfig = () => {
    const { is_open, checkedLanguages } = this.state
    const lang_config = {
      is_open,
      lang_list: checkedLanguages,
    }
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'lang_config',
        content: lang_config,
      },
    })
  }

  render() {
    const {
      allLangList,
      // checkAll,
      // indeterminate,
      checkedLangCodes,
      is_open,
    } = this.state
    return (
      <div className={this.props.className + ' lang_config-rgscl'}>
        <div>
          <div className='header'>
            <span className='title'>{__('customHome.label.languageConfig')}</span>
            <Switch
              disabled={allLangList.length < 2}
              onChange={this.switchOnChange}
              checked={is_open && allLangList.length >= 2}
            />
          </div>
          {is_open && allLangList.length >= 2 ? (
            <div className='checkboxs'>
              {/* <Checkbox indeterminate={indeterminate} onChange={this.onCheckAllChange} checked={checkAll}>
            {__('customHome.btn.selectAll')}
            </Checkbox> */}
              <CheckboxGroup
                options={allLangList}
                defaultValue={checkedLangCodes}
                value={checkedLangCodes}
                onChange={this.onChange}
              />
            </div>
          ) : (
            <></>
          )}
        </div>
      </div>
    )
  }
}

export default LanguageConfig
