import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import { Icon } from 'biz-components'
import { copy } from 'iclipboard'

@connect((state) => {
  if (__DEV__) {
  }

  return {
    cropId: state.user.detail.corp_uuid,
    currentGroup: state._newReplies.current,
    curRobot: state._newReplies.curRobot,
    robots: state.robots.robots,
  }
})
@extend({
  styles: require('./queryItem.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      msg: null,
    }
  }
  componentDidMount() {}
  copyText(txt) {
    if (copy(txt)) {
      // this.showMessage("*复制成功")
      this.showMessage('*' + __('customHome.modalMsg.copySuccess'))
    } else {
      // this.showMessage("*复制失败")
      this.showMessage('*' + __('customHome.modalMsg.copyFailed'))
    }
  }
  showMessage(txt) {
    this.setState(
      {
        msg: txt,
      },
      () => {
        let timer = setTimeout(() => {
          clearTimeout(timer)
          this.setState({
            msg: null,
          })
        }, 3000)
      },
    )
  }
  render() {
    return (
      <div className={this.props.className}>
        <span className='prompt'>
          <Icon icon='Alert_Default_Info' className='prompt_icon' />
          {/* 警示：当前首页存在 {this.props.questionList.length} 个没有答案的推荐问法，建议立即优化 */}
          {__('customHome.modalMsg.noAnswersWarm', { questionListNum: this.props.questionList.length })}
        </span>
        <span className='tips'>{this.state.msg}</span>
        <div className='querylist-item'>
          {this.props.questionList.length > 0 &&
            this.props.questionList.map((item, index) => {
              return (
                <p className='item'>
                  <span>{index + 1 + '.' + item}</span>
                  <em
                    onClick={() => {
                      this.copyText(item)
                    }}
                  >
                    {__('customHome.btn.copy')}
                  </em>
                </p>
              )
            })}
        </div>
        {/* <div className="bottom">
                <p className="modal_button">
                <span onClick={() => { window.open("/web/portal/#/frame/hmag-qa/hmag-qa.qa_lib/") }}>{__('customHome.warm.addAnswers')}</span>
                </p>
                <p className="modal_button">
                <span onClick={() => {  }}>{'继续发布'}</span>
                </p>
                </div> */}
        <p className='modal_button'>
          <span
            onClick={() => {
              window.open('/web/portal/#/frame/hmag-qa/hmag-qa.qa_lib/')
            }}
          >
            {__('customHome.warm.addAnswers')}
          </span>
        </p>
      </div>
    )
  }
}
