import React from 'react'
import { connect } from 'react-redux'
import { extend, getLocaleId } from 'koot'
import { Icon } from 'biz-components'
import List from './template/list'
import Card from './template/card'
import AI from './template/AI'
import Standard from './template/Standard'
import Simple from './template/Simple'
import ButtonsTep from './template/Buttons'
import { isMini } from '@utils/tools'
import classNames from 'classnames'

@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    devUrl = 'http://dev-jiedai.ainirobot.com/'
  }

  if (state._home && state._home.homeModule) {
    let homeModule = JSON.stringify(state._home.homeModule)
    let respid = new Date() - 0
    window.frames[0] && window.frames[0].postMessage({ command: 'homeModuleUpdated', respid, detail: homeModule }, '*')
  }

  return {
    homeModule: state._home.homeModule,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    devUrl: devUrl,
    curLang: state.user.curLang,
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  state = {}
  initRobotNum = 0
  componentDidMount() {
    //只执行一次。
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
    let _this = this

    window.addEventListener(
      'message',
      function (e) {
        if (e.data.command == 'askhomeModule') {
          let homeModule = JSON.stringify(_this.props.homeModule || {})
          let respid = new Date() - 0
          window.frames[0] &&
            window.frames[0].postMessage({ command: 'homeModuleUpdated', respid, detail: homeModule }, '*')
        }
      },
      true,
    )
  }

  componentWillUnmount() {} //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (JSON.stringify(prevProps.homeModule) != JSON.stringify(this.props.homeModule)) {
    }
  }

  getQueryVal = (k, search, uri) => {
    if (search == undefined) {
      search = location.search
    }
    var reg = new RegExp('(^|&)' + k + '=([^&]*)(&|$)')
    var s = (uri && '?' + uri.split('?')[1]) || search
    var r = s.slice(1).match(reg)
    if (r != null) {
      return decodeURIComponent(r[2])
    }
    return null
  }

  // showmodalpreview=()=>{

  // }

  render() {
    const robotName = this.props.robotName != undefined && this.props.robotName
    let bgImage = ''

    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'Simple') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'agentOS') {
        backgroundStr = 'module_public/module_skill_home/bao_mini/miniaiosbg.png'
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }

      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.props.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };

      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      // 豹大屏的图片路径拼接
      if (this.props.homeModule.templateInfo.bs_background && this.props.homeModule.templateInfo.bs_background != '') {
        var bs_background = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.bs_background + ')',
        }
        bgImage = this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.bs_background
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.props.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.props.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.props.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.props.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
    }

    let robotType = this.getQueryVal('robotType')
    let miniUrl = `${location.origin}${location.pathname}?hl=zh&robotType=bao_mini&#/minihome`
    return (
      <div className={this.props.className} style={{ height: '100%', overflowY: 'auto', overflowX: 'hide' }}>
        {/* {this.props.showLoading &&
                    <div className="loading">
                        <div className="loading_modal">
                            <img src={require('@assets/svg-icon/loading.gif')} />
                            <span>{__('customHome.modalMsg.waitMsg')}</span>
                        </div>
                    </div>
                } */}

        <div className={isMini(robotType) ? 'content_preview mini_content_preview' : 'content_preview 1'}>
          {/* {robotType=='bao_mini'&&<div className="preBtn" onClick={this.showmodalpreview}>
                    <Icon icon='guide_edit_preview' style={{verticalAlign:'middle',marginRight:'2px'}}/>
                        <span style={{verticalAlign:'middle'}}>预览</span>
                    </div>} */}
          <div>
            <div className='previewCon' style={styles}>
              {this.props.queryStyle != '' && (
                <img className='query_bg' src={require('@assets/png/meui/home_' + this.props.queryStyle + '_bg.jpg')} />
              )}
              {this.props.backImgstyle != '' && this.props.backImgstyle == 'face' && (
                <div
                  style={facebackground}
                  className={isMini(robotType) ? 'faceBackground mini_faceBackground' : 'faceBackground'}
                >
                  <img
                    className={
                      (this.props.overVersion51 && faceStyle == 'Bubble') || faceStyle === 'Card' ? 'moveup' : ''
                    }
                    src={require('@assets/png/meui/home4_eyes.png')}
                  />
                  {/* 只有一条的时候 */}
                  {this.props.overVersion51 && faceStyle == 'Bubble' && Bubble1 && !Bubble2 && (
                    <img
                      className={Bubble1.length > 6 ? 'bubble1m_two' : 'bubble1m_one'}
                      alt=''
                      src={
                        Bubble1.length > 6
                          ? require('@assets/png/meui/bubble1big.png')
                          : require('@assets/png/meui/bubble1.png')
                      }
                    />
                  )}
                  {this.props.overVersion51 && faceStyle == 'Bubble' && Bubble1 && !Bubble2 && (
                    <div className={Bubble1.length > 6 ? 'bubbleText1m_two' : 'bubbleText1m_one'}> {`${Bubble1}`}</div>
                  )}

                  {Bubble1Big && <div className='bubbleText1'> {`“${Bubble1Big}”`}</div>}

                  {!isMini(robotType) && this.props.overVersion51 && faceStyle == 'Bubble' && Bubble1 && Bubble2 && (
                    <img
                      className={Bubble1.length > 6 ? 'bubble1_two' : 'bubble1_one'}
                      alt=''
                      src={
                        Bubble1.length > 6
                          ? require('@assets/png/meui/bubble1big.png')
                          : require('@assets/png/meui/bubble1.png')
                      }
                    />
                  )}
                  {isMini(robotType) && this.props.overVersion51 && faceStyle == 'Bubble' && Bubble1 && Bubble2 && (
                    <img
                      className={Bubble1.length > 6 ? 'bubble1_two' : 'bubble1_one'}
                      alt=''
                      src={
                        Bubble1.length > 6
                          ? require('@assets/png/meui/miniL2.png')
                          : require('@assets/png/meui/miniL1.png')
                      }
                    />
                  )}

                  {this.props.overVersion51 && faceStyle == 'Bubble' && Bubble1 && Bubble2 && Bubble1.length <= 6 && (
                    <div className='bubbleText1_one'> {`${Bubble1}`}</div>
                  )}
                  {this.props.overVersion51 && faceStyle == 'Bubble' && Bubble1 && Bubble2 && Bubble1.length > 6 && (
                    <div className='bubbleText1_two 3'> {`${Bubble1}`}</div>
                  )}

                  {Bubble1Big && <div className='bubbleText1'> {`“${Bubble1Big}”`}</div>}

                  {this.props.Bubble1big && (
                    <img className='bubble1big' alt='' src={require('@assets/png/meui/bubble1big.png')} />
                  )}

                  {!isMini(robotType) && this.props.overVersion51 && faceStyle == 'Bubble' && Bubble2 && (
                    <img
                      className={Bubble2.length > 6 ? 'bubble2_two' : 'bubble2_one'}
                      alt=''
                      src={
                        Bubble2.length > 6
                          ? require('@assets/png/meui/bubble2big.png')
                          : require('@assets/png/meui/bubble2.png')
                      }
                    />
                  )}
                  {isMini(robotType) && this.props.overVersion51 && faceStyle == 'Bubble' && Bubble2 && (
                    <img
                      className={Bubble2.length > 6 ? 'bubble2_two' : 'bubble2_one'}
                      alt=''
                      src={
                        Bubble2.length > 6
                          ? require('@assets/png/meui/miniR2.png')
                          : require('@assets/png/meui/miniR1.png')
                      }
                    />
                  )}

                  {this.props.overVersion51 && faceStyle == 'Bubble' && Bubble2 && Bubble2.length <= 6 && (
                    <div className='bubbleText2_one'> {`${Bubble2}`}</div>
                  )}
                  {this.props.overVersion51 && faceStyle == 'Bubble' && Bubble2 && Bubble2.length > 6 && (
                    <div className='bubbleText2_two'> {`${Bubble2}`}</div>
                  )}

                  {this.props.Bubble2big && (
                    <img className='bubble2big' alt='' src={require('@assets/png/meui/bubble2big.png')} />
                  )}

                  {/*张维青卡片英文模式  */}
                  {this.props.overVersion57 && faceStyle == 'Card' && this.props.lang === 'english' && (
                    <div className='cardContent'>
                      <div className='title'>
                        {/* <Icon icon='icon_title' className='icon' /> */}
                        <Icon icon='icon_title' className='icon' />
                        {this.props.homeModule.templateInfo.bubbleContents.title}
                      </div>
                      {/* <img className={this.props.homeModule.templateInfo.bubbleContents.title.length > 6 ? 'title_two' : 'title_one'} alt="" src={this.props.homeModule.templateInfo.bubbleContents.title.length > 6 ? require('@assets/png/meui/card_title.png') : require('@assets/png/meui/card_title.png')} /> */}
                      <img className='title_two' alt='' src={require('@assets/png/meui/card_title.png')} />
                      <img className='txt_one' alt='' src={require('@assets/png/meui/card_bg.png')} />
                      {/* {Bubble1 && <img className={Bubble1.length > 6 ? 'txt_two' : 'txt_one'} alt="" src={Bubble1.length > 6 ? require('@assets/png/meui/card_bg.png') : require('@assets/png/meui/card_bg.png')} />} */}

                      <div className='txt'>{Bubble1 && `${Bubble1}`}</div>
                    </div>
                  )}

                  <div className='compare1'></div>
                  <div className='compare2'></div>
                  <div className='one_m'></div>
                </div>
              )}
              {this.props.backImgstyle != '' && this.props.backImgstyle == 'bigScreen' && (
                <div className='bs_background'>
                  <img src={bgImage} />
                </div>
              )}
              {this.props.queryStyle == '' && this.props.backImgstyle == '' && (
                <div className='previewCon_height'>
                  {this.props.isDefault && this.props.isShowSetting && (
                    <div className='startSetting_tips'>
                      <div className='startSetting_modal'>
                        <Icon icon='icon_Warning' className='prompt_icon' />
                        <p className='title'>{__('customHome.toast.msg1')}</p>
                        <p className='button'>
                          <span
                            onClick={() => {
                              this.props.changeState({
                                isShowSetting: false,
                              })
                              if (!this.isKTV) {
                                this.props.changeModal()
                              } else {
                                this.props.changeState(
                                  {
                                    templateNeedUpdate: false,
                                  },
                                  () => {
                                    this.props.choosemodalStyle('Simple', 'KTV', 'chs')
                                  },
                                )

                                let isDefault = false
                                // window.tipSetData({
                                //     templateNeedUpdate: false,
                                //     // lowOpkVersion,
                                //     isDefault
                                // })
                              }
                            }}
                          >
                            {__('customHome.btn.startSetup')}
                          </span>
                        </p>
                      </div>
                    </div>
                  )}
                  {this.isKTV && this.props.lowOpkVersion && (
                    <div className='startSetting_tips'>
                      <div className='startSetting_modal lowopkversion'>
                        <Icon icon='icon_Warning' className='prompt_icon' />
                        <p className='title'>{"'" + __('customHome.modalMsg.lowVersion') + "'"}</p>
                        <p className='title'>{"'" + __('customHome.modalMsg.contactPresale') + "'"}</p>
                      </div>
                    </div>
                  )}
                  {this.isKTV &&
                    this.props.templateNeedUpdate &&
                    !this.props.lowOpkVersion &&
                    !this.props.isDefault && (
                      <div className='startSetting_tips'>
                        <div className='startSetting_modal templateNeedUpdate'>
                          <Icon icon='icon_Warning' className='prompt_icon' />
                          <p className='title'>{"'" + __('customHome.modalMsg.pageNewChange') + "'"}</p>
                          <p className='title'>{"'" + __('customHome.modalMsg.setChangeLeave') + "'"}</p>
                          <p className='button'>
                            <span
                              onClick={() => {
                                this.props.changeState(
                                  {
                                    templateNeedUpdate: false,
                                  },
                                  () => {
                                    this.props.choosemodalStyle('Simple', 'KTV', 'chs')
                                  },
                                )
                                let isDefault = false
                                // window.tipSetData({
                                //     templateNeedUpdate: false,
                                //     // lowOpkVersion,
                                //     isDefault
                                // })
                              }}
                            >
                              {__('customHome.modalMsg.cureChange')}
                            </span>
                          </p>
                        </div>
                      </div>
                    )}
                  {(!isMini(robotType) && this.props.modalStyle == 'List') || this.props.modalStyle == 'list' ? (
                    <List
                      fiveTimer={this.props.fiveTimer}
                      backImg={this.props.backImg}
                      devUrl={this.props.devUrl}
                      lang={this.props.lang}
                      currentSort={this.props.currentSort}
                      currentIndex={this.props.currentIndex}
                      changeCurrentIndex={this.props.changeCurrentIndex}
                      changeContent={this.props.changeContent}
                      checkJson={this.props.checkJson}
                    />
                  ) : (
                    ''
                  )}

                  {!isMini(robotType) && this.props.modalStyle == 'Card' && (
                    <Card
                      fiveTimer={this.props.fiveTimer}
                      backImg={this.props.backImg}
                      devUrl={this.props.devUrl}
                      currentSort={this.props.currentSort}
                      currentIndex={this.props.currentIndex}
                      changeCurrentIndex={this.props.changeCurrentIndex}
                      changeContent={this.props.changeContent}
                      checkJson={this.props.checkJson}
                    />
                  )}
                  {/* mini机型预览页面是src/app/ui/pages/meui/miniHome/index.jsx,不是当前文件夹下的template */}
                  {isMini(robotType) && this.props.modalStyle && <iframe className='viewiframe' src={miniUrl}></iframe>} 

                  {this.props.modalStyle == 'Technical' && (
                    <AI
                      fiveTimer={this.props.fiveTimer}
                      backImg={this.props.backImg}
                      devUrl={this.props.devUrl}
                      currentSort={this.props.currentSort}
                      currentIndex={this.props.currentIndex}
                      changeCurrentIndex={this.props.changeCurrentIndex}
                      changeContent={this.props.changeContent}
                      checkJson={this.props.checkJson}
                    />
                  )}

                  {!isMini(robotType) && this.props.modalStyle == 'Standard' && this.props.queryStyle == '' && (
                    <Standard
                      fiveTimer={this.props.fiveTimer}
                      haveCover={this.props.haveCover}
                      backImg={this.props.backImg}
                      devUrl={this.props.devUrl}
                      lang={this.props.lang}
                      currentSort={this.props.currentSort}
                      currentIndex={this.props.currentIndex}
                      changeCurrentIndex={this.props.changeCurrentIndex}
                      changeContent={this.props.changeContent}
                      checkJson={this.props.checkJson}
                      className='standard-con'
                    />
                  )}
                  {!isMini(robotType) && this.props.modalStyle == 'Simple' && this.props.queryStyle == '' && (
                    <Simple
                      fiveTimer={this.props.fiveTimer}
                      haveCover={this.props.haveCover}
                      backImg={this.props.backImg}
                      devUrl={this.props.devUrl}
                      lang={this.props.lang}
                      currentSort={this.props.currentSort}
                      currentIndex={this.props.currentIndex}
                      changeCurrentIndex={this.props.changeCurrentIndex}
                      changeContent={this.props.changeContent}
                      checkJson={this.props.checkJson}
                      className='standard-con'
                    />
                  )}
                  {!isMini(robotType) && this.props.modalStyle == 'Buttons' && (
                    <ButtonsTep
                      fiveTimer={this.props.fiveTimer}
                      haveCover={this.props.haveCover}
                      backImg={this.props.backImg}
                      devUrl={this.props.devUrl}
                      lang={this.props.lang}
                      currentSort={this.props.currentSort}
                      currentIndex={this.props.currentIndex}
                      changeCurrentIndex={this.props.changeCurrentIndex}
                      changeContent={this.props.changeContent}
                      checkJson={this.props.checkJson}
                      className='standard-con'
                    ></ButtonsTep>
                  )}
                </div>
              )}
              {this.props.homeModule && this.props.backImgstyle == '' ? (
                <div className={this.props.currentButton === -1 ? 'bottom_cover tip_cover' : 'bottom_cover'}></div>
              ) : (
                ''
              )}
              {this.props.homeModule && this.props.backImgstyle == '' ? (
                // <div className={this.props.currentButton == -1 ? "bottom_queries" : "bottom_queries"}>
                <div
                  key={this.props.currentButton}
                  // className={this.props.currentButton === -1 ? 'bottom_queries tip_cover' : 'bottom_queries'}
                  className={classNames({
                    bottom_queries: true,
                    tip_cover: this.props.currentButton === -1,
                    'right-view': this.props?.lang === 'ar_SA',
                  })}
                >
                  <div
                    className='bottom_queries_con'
                    onClick={() => {
                      if (this.props.checkJson('bottomQueries')) {
                        this.props.changeState({
                          currentButton: -1,
                          currentIndex: -2,
                          currentSort: false,
                        })
                        this.props.changeContent('bottomQueries')
                      }
                    }}
                  >
                    {isMini(robotType) ? (
                      <div className='mini_recommend_robot'>
                        <img className='recommend_robot_chat_icon' src={require('@assets/png/meui/recommend_robot_img_mini.png')} alt='' />
                        {this.props?.lang === 'ar_SA' ? (
                          <img
                            className='chat_bubbles rg'
                            src={require('@assets/png/meui/chat_bubbles_rg.png')}
                            alt=''
                          />
                        ) : (
                          <img
                            className='chat_bubbles lf'
                            src={require('@assets/png/meui/chat_bubbles_lf.png')}
                            alt=''
                          />
                        )}
                      </div>
                    ) : (
                      <img src={require('@assets/png/meui/recommend_robot_img.png')} />
                    )}
                    {['zh_CN', 'zh_GD', 'ja_JP', 'ko_KR'].includes(this.props.lang) ? (
                      <span>
                        {bottom_queries_con.length > 15 ? bottom_queries_con.substr(0, 15) + '...' : bottom_queries_con}
                      </span>
                    ) : ['ar_SA'].includes(this.props.lang) ? (
                      <span>
                        <p>
                          {bottom_queries_con.length > 30
                            ? '...' + bottom_queries_con.substr(0, 30)
                            : bottom_queries_con}
                        </p>
                      </span>
                    ) : (
                      // (this.props.lang == 'english' || this.props.lang == 'arabic') && (
                      <span>
                        {bottom_queries_con.length > 28 ? bottom_queries_con.substr(0, 28) + '...' : bottom_queries_con}
                      </span>
                      // )
                    )}
                  </div>
                </div>
              ) : (
                ''
              )}
              {/*this.props.homeModule && this.props.modalStyle == 'Card' && this.props.changeContent != "backImg" ?
                                <img src={require('@assets/png/meui/home1_query_bg_img.png')} className="shadow" />
                                : ""*/}
            </div>
          </div>
          {/* <div className="minipreview">
                        {/* mini预览 */}

          {/* </div> */}
        </div>
      </div>
    )
  }
}
