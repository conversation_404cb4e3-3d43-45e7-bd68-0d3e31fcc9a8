import React from 'react'
import { connect } from 'react-redux'
import { extend, getLocaleId } from 'koot'
import { Dropdown, MenuItem } from 'biz-components'
import { Icon } from 'biz-components'
import { Radio, Button } from 'biz-components'
import _ from 'lodash'
import { Popover, message, Select } from 'antd'
import { checkReleaseRaw, isMini } from '@utils/tools'
import classNames from 'classnames'
const { Option } = Select

@connect((state) => {
  return {
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    langData: state.user.langData,
    langDataActive: state.user.langDataActive,
    user: state.user,
  }
})
@extend({
  styles: require('./chooseModal.less'),
})
class ChooseModal extends React.Component {
  state = {
    style: null,
    curScenes: this.props.curScenes == 'KTV' ? 'KTV' : this.props.curScenes.toLowerCase(),
    allScenes: [
      __('customHome.sceneType.default'),
      __('customHome.sceneType.market'),
      __('customHome.sceneType.museum'),
      __('customHome.sceneType.bank'),
      __('customHome.sceneType.hotel'),
      __('customHome.sceneType.library'),
    ],
    languages: [__('customHome.language.chinese'), __('customHome.language.english')],
    langActive: {},
  }
  componentDidMount() {
    console.log('preview_chooseModal_did', { props: this.props })
    const { langDataActive } = this.props
    // console.log(66,langDataActive)
    this.setState({
      langActive: langDataActive,
      style: this.props.modalStyle,
    })

    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
    if (this.isKTV) {
    }
    let allScenes = this.state.allScenes
    allScenes.push('KTV')
    this.setState({
      allScenes,
    })
  }

  getQueryVal = (k, search, uri) => {
    if (search == undefined) {
      search = location.search
    }
    var reg = new RegExp('(^|&)' + k + '=([^&]*)(&|$)')
    var s = (uri && '?' + uri.split('?')[1]) || search
    var r = s.slice(1).match(reg)
    if (r != null) {
      return decodeURIComponent(r[2])
    }
    return null
  }

  changeStyle(txt) {
    this.setState({
      style: txt,
    })
  }

  render() {
    const { langData, user } = this.props
    const { langActive } = this.state
    const lang_code = _.get(langActive, 'lang_code', '')
    let opk_version = true /// 默认禁用 简洁式
    let buttons_opk_version = true // 默认禁用按钮式
    let mini_opk_verion = true // mini 默认禁用 标准式 卡片式 简洁式
    let currentRobotId = this.props.currentRobotId
    let robots = this.props.robots
    let baseVersion = '02.0001.200821'
    let buttonsVersion = '02.0010.20122920'
    let miniVersion = '03.0001.20122819' // mini  设备不小于这个版本可以使用标准式 卡片式 简洁式
    let robotType = this.getQueryVal('robotType')
    // baseVersion = "0";
    let minitemp = true
    const isAgentOS = user?.corp?.enable_aios === '1'
    const agentRobotVersion = robots[currentRobotId]?.robot_aios_version
    const agentVersionOver = checkReleaseRaw(agentRobotVersion, '1.1.0', true)

    if (robots[currentRobotId] && robots[currentRobotId].robot_baseopk_version) {
      let opkversion = robots[currentRobotId].robot_baseopk_version
      let overVersion = checkReleaseRaw(opkversion, baseVersion, true)
      let buttonsOverVersion = checkReleaseRaw(opkversion, buttonsVersion, true)
      let miniOverversion = checkReleaseRaw(opkversion, miniVersion, true)
      console.log('版本', { overVersion, opkversion, baseVersion })
      if (overVersion) {
        opk_version = false
      } else {
        opk_version = true
      }

      if (buttonsOverVersion) {
        buttons_opk_version = false
      } else {
        buttons_opk_version = true
      }
      if (miniOverversion) {
        mini_opk_verion = false
      } else {
        mini_opk_verion = true
      }
    }
    let langDataTrim = langData

    console.log('modal_preview_render', {
      props: this.props,
      state: this.state,
      robotType: this.robotType,
    })
    return (
      <div className={this.props.className}>
        <div className='modal_content'>
          <p className='modal_title'>{__('customHome.label.template')}</p>
          <p className='list1ext list1 3'>
            <span className='small_title'>{__('customHome.label.language')}</span>
            <Select
              style={{ width: 120 }}
              value={langActive.lang_code}
              onChange={(k) => {
                const sole = _.find(langDataTrim, { lang_code: k })
                const prevStyle = this.state.style 
                this.setState({
                  langActive: sole,
                })
                  // 检查当前样式在新语言下是否支持
                if (sole.lang_code !== 'zh_CN') {
                  if (prevStyle !== 'List' && prevStyle !== 'agentOS') {
                    this.setState({
                      style: 'List'
                    })
                  }
                }
              }}
            >
              {langDataTrim.map((u) => (
                <Option key={u.lang_code} value={u.lang_code}>
                  {u.name}
                </Option>
              ))}
            </Select>

            {lang_code == 'zh_CN' && !isMini(robotType) && (
              <>
                <span className='small_title'>{__('customHome.warm.scenario')}</span>
                <Dropdown className='list_dropdown' label={this.state.curScenes}>
                  {this.state.allScenes.map((item, idx) => {
                    return (
                      <MenuItem
                        className={item.toLowerCase() == this.state.curScenes ? 'cur' : ''}
                        children={item}
                        key={idx}
                        onClick={() => {
                          this.setState(
                            {
                              curScenes: item,
                            },
                            () => {
                              console.log(item.toLowerCase())
                            },
                          )
                        }}
                      />
                    )
                  })}
                </Dropdown>
              </>
            )}
          </p>

          {lang_code == 'zh_CN' && (
            <p className='list2'>
              <span className='small_title'>{__('customHome.SelectStyle.title')}</span>
            </p>
          )}
          {/* 豹小秘场景选择 */}
          {lang_code == 'zh_CN' && !isMini(robotType) && (
            <div className='modal_choose'>
              {this.robotType != 'bao_xiao_di' && (
                <p
                  className={classNames('template- 1', {
                    blue: this.state.style == 'Standard',
                  })}
                  onClick={() => {
                    this.changeStyle('Standard')
                  }}
                >
                  <img src={require('@assets/png/meui/bg_4_s.png')} />
                  <Radio
                    name='sort'
                    label={__('customHome.SelectStyle.standard')}
                    checked={this.state.style == 'Standard'}
                  />
                  {/* {this.props.modalStyle == 'Standard' && <span>({__('customHome.current')})</span>} */}
                </p>
              )}
              {this.robotType != 'bao_xiao_di' && (
                <p
                  className={classNames('template- 2', {
                    blue: this.state.style == 'List',
                  })}
                  onClick={() => {
                    this.changeStyle('List')
                  }}
                >
                  <img src={require('@assets/png/meui/bg_1_s.png')} />
                  <Radio name='sort' label={__('customHome.SelectStyle.list')} checked={this.state.style == 'List'} />
                  {/* {this.props.modalStyle == 'List' && <span>({__('customHome.current')})</span>} */}
                </p>
              )}
              {robotType != 'bao_xiao_di' && (
                <p
                  className={classNames('template- 3', {
                    blue: this.state.style == 'Card',
                  })}
                  onClick={() => {
                    this.changeStyle('Card')
                  }}
                >
                  <img src={require('@assets/png/meui/bg_2_s.png')} />
                  <Radio name='sort' label={__('customHome.SelectStyle.card')} checked={this.state.style == 'Card'} />
                  {/* {this.props.modalStyle == 'Card' && <span>({__('customHome.current')})</span>} */}
                </p>
              )}

              {opk_version ? (
                <Popover content={'需升级机器版本'} title='Title'>
                  {!['bao_da_ping'].includes(robotType) && (
                    <p
                      disabled={opk_version}
                      className={classNames('template- 4', {
                        blue: this.state.style == 'Simple',
                      })}
                      onClick={() => {
                        this.changeStyle('Simple')
                      }}
                    >
                      <img src={require('@assets/png/meui/simple.png')} />
                      {!opk_version && (
                        <Radio
                          name='sort'
                          attr={opk_version}
                          disabled={opk_version}
                          label={
                            __('customHome.SelectStyle.simple') + '(' + __('customHome.modalMsg.currVersionNotIn') + ')'
                          }
                          checked={this.state.style == 'Simple'}
                        />
                      )}
                      {opk_version && <span className='lowver'>{__('customHome.modalMsg.currVersionNotIn')}</span>}
                      {/* {this.props.modalStyle == 'Simple' && <span>({__('customHome.current')})</span>} */}
                    </p>
                  )}
                </Popover>
              ) : (
                <p
                  disabled={opk_version}
                  className={classNames('template- 5', {
                    blue: this.state.style == 'Simple',
                  })}
                  onClick={() => {
                    this.changeStyle('Simple')
                  }}
                >
                  <img src={require('@assets/png/meui/simple.png')} />
                  {
                    <Radio
                      name='sort'
                      attr={opk_version}
                      disabled={opk_version}
                      label={__('customHome.SelectStyle.simple')}
                      checked={this.state.style == 'Simple'}
                    />
                  }
                  {/* {this.props.modalStyle == 'Simple' && <span>({__('customHome.current')})</span>} */}
                </p>
              )}

              {!['bao_da_ping', 'bao_xiao_di'].includes(robotType) ? (
                <p
                  className={classNames('template- 6', {
                    blue: this.state.style == 'Buttons',
                    p_dis: buttons_opk_version,
                  })}
                  onClick={() => {
                    if (buttons_opk_version) {
                      message.warn(__('customHome.modalMsg.robotlowPlsUpdate'))
                    } else {
                      this.changeStyle('Buttons')
                    }
                  }}
                >
                  <img src={require('@assets/png/meui/bg_buttons_s.png')} />
                  {buttons_opk_version && <span className='lowver'>{__('customHome.modalMsg.currVersionNotIn')}</span>}
                  {!buttons_opk_version && (
                    <Radio
                      name='sort'
                      label={__('customHome.SelectStyle.buttons')}
                      checked={this.state.style == 'Buttons'}
                    />
                  )}
                  {/* {this.props.modalStyle == 'Buttons' && <span>({__('customHome.current')})</span>} */}
                </p>
              ) : null}
            </div>
          )}
          {/* mini 场景模板选择开始 */}
          {lang_code === 'zh_CN' && isMini(robotType) && (
            <div className='modal_choose minicss'>
              <p
                className={classNames('template- 7', {
                  blue: this.state.style == 'List',
                  template5: true,
                })}
                onClick={() => {
                  this.changeStyle('List')
                }}
              >
                <img src={require('@assets/png/meui/mini_tob_home.png')} />
                <Radio name='sort' label={__('customHome.SelectStyle.list')} checked={this.state.style == 'List'} />
                {/* {this.props.modalStyle == 'List' && <span>({__('customHome.current')})</span>} */}
              </p>

              {minitemp && this.robotType != 'bao_xiao_di' && lang_code != 'en_US' && (
                <p
                  className={classNames('template- 8', {
                    blue: this.state.style == 'Standard',
                    p_dis: mini_opk_verion,
                  })}
                  onClick={() => {
                    this.changeStyle('Standard')
                  }}
                >
                  <img src={require('@assets/png/meui/mini_bg_4_s.png')} />
                  {/* <Radio name="sort" label={__('customHome.SelectStyle.standard')} checked={this.state.style == "Standard"} /> */}

                  {mini_opk_verion && <span className='lowver'>{__('customHome.modalMsg.currVersionNotIn')}</span>}
                  {!mini_opk_verion && (
                    <Radio
                      name='sort'
                      label={__('customHome.SelectStyle.standard')}
                      checked={this.state.style == 'Standard'}
                    />
                  )}

                  {/* {this.props.modalStyle == 'Standard' && <span>({__('customHome.current')})</span>} */}
                </p>
              )}

              {minitemp && this.robotType != 'bao_xiao_di' && lang_code != 'en_US' && (
                <p
                  className={classNames('template- 9', {
                    blue: this.state.style == 'Card',
                    p_dis: mini_opk_verion,
                  })}
                  onClick={() => {
                    this.changeStyle('Card')
                  }}
                >
                  <img src={require('@assets/png/meui/mini_bg_2_s.png')} />
                  {/* <Radio name="sort" label={__('customHome.SelectStyle.card')} checked={this.state.style == "Card"} /> */}
                  {mini_opk_verion && <span className='lowver'>{__('customHome.modalMsg.currVersionNotIn')}</span>}
                  {!mini_opk_verion && (
                    <Radio name='sort' label={__('customHome.SelectStyle.card')} checked={this.state.style == 'Card'} />
                  )}
                  {/* {this.props.modalStyle == 'Card' && <span>({__('customHome.current')})</span>} */}
                </p>
              )}
              {true && minitemp && (
                // <Popover content={'需升级机器版本'} title="Title"></Popover>
                <p
                  disabled={opk_version}
                  className={classNames('template- 10', {
                    blue: this.state.style == 'Simple',
                    p_dis: mini_opk_verion,
                  })}
                  onClick={() => {
                    this.changeStyle('Simple')
                  }}
                >
                  <img src={require('@assets/png/meui/mini_simple.png')} />
                  {mini_opk_verion && <span className='lowver'>{__('customHome.modalMsg.currVersionNotIn')}</span>}
                  {!mini_opk_verion && (
                    <Radio
                      name='sort'
                      label={__('customHome.SelectStyle.simple')}
                      checked={this.state.style == 'Simple'}
                    />
                  )}

                  {/* {this.props.modalStyle == 'Simple' && <span>({__('customHome.current')})</span>} */}
                </p>
              )}

              {/* agentOS 样式选项 */}
              {minitemp && isAgentOS && agentVersionOver && (
                 <p
                   className={classNames('template- agentos-mini', { 
                     blue: this.state.style == 'agentOS',
                   })}
                   onClick={() => {
                     this.changeStyle('agentOS')
                   }}
                 >
                   <img src={require('@assets/png/meui/agentos/agentos_mockup.png')} />
                   <Radio
                     name='sort'
                     label={'agentOS'} 
                     checked={this.state.style == 'agentOS'}
                   />
                 </p>
              )}
            </div>
          )}
          {/* mini场景模板选择结束 */}
          {lang_code !== 'zh_CN' && (
            <div className={classNames({ modal_choose: true, minicss: isMini(robotType) })}>
              <p
                className={classNames('template- 11', {
                  blue: this.state.style == 'List',
                })}
                onClick={() => {
                  this.changeStyle('List')
                }}
              >
                {!isMini(robotType) && <img src={require('@assets/png/meui/bg_1_s.png')} />}
                {isMini(robotType) && <img src={require('@assets/png/meui/mini_tob_home.png')} />}
                <Radio name='sort' label={__('customHome.SelectStyle.list')} checked={this.state.style == 'List'} />
                {/* {this.props.modalStyle == 'List' && <span>({__('customHome.current')})</span>} */}
              </p>

              {/* agentOS 样式选项 (mini)*/}
              {minitemp && isAgentOS && agentVersionOver && (
                 <p
                   className={classNames('template- agentos-mini', { 
                     blue: this.state.style == 'agentOS',
                   })}
                   onClick={() => {
                     this.changeStyle('agentOS')
                   }}
                 >
                   <img src={require('@assets/png/meui/agentos/agentos_mockup.png')} />
                   <Radio
                     name='sort'
                     label={'agentOS'} 
                     checked={this.state.style == 'agentOS'}
                   />
                 </p>
              )}
            </div>
          )}

          {/* 英文模板结束 */}
          {lang_code == 'arabic' && (
            <div className={classNames({ modal_choose: true, minicss: isMini(robotType) })}>
              {!isMini(robotType) ? (
                <p
                  className={classNames('template- 12', {
                    blue: this.state.style == 'List',
                  })}
                  onClick={() => {
                    this.changeStyle('List')
                  }}
                >
                  {!isMini(robotType) && <img src={require('@assets/png/meui/bg_1_s_ar.png')} />}
                  {isMini(robotType) && <img src={require('@assets/png/meui/mini_tob_home.png')} />}
                  <Radio name='sort' label={__('customHome.SelectStyle.list')} checked={this.state.style == 'List'} />
                  {/* {this.props.modalStyle == 'List' && <span>({__('customHome.current')})</span>} */}
                </p>
              ) : (
                <div style={{ height: '50px' }}></div>
              )}
            </div>
          )}

          <Button
            className='modal_button'
            onClick={() => {
              console.log('模板选择 ', { state: this.state, lang_code })
              const { style } = this.state
              if (!style) {
                message.warn(__('customHome.step.one'))
                return
              }
              this.props.dispatch({
                type: 'SET_LANG_DATA',
                data: {
                  langDataActive: langActive,
                  curLang: langActive,
                },
              })
              this.props.choosemodalStyle(this.state.style, this.state.curScenes, lang_code)
            }}
          >
            {__('customHome.btn.save2')}
          </Button>
        </div>
      </div>
    )
  }
}
export default ChooseModal
export { ChooseModal }
