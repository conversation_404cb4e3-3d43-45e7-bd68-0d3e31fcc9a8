import React from 'react'
import { connect } from 'react-redux'
import { GET_EDIT_LIST } from '@redux/action-types'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页卡片', state._home)
  return {
    showSkills: state._home.homeModule.skills,
    templateInfo: state._home.homeModule.templateInfo,
  }
})
class Card extends React.Component {
  render() {
    var list = []
    var image_url = this.props.devUrl + 'media/'
    this.props.showSkills.map((item, index) => {
      if (item.display) {
        list.push(
          <div className={this.props.currentIndex == index ? 'tip_cover' : ''}>
            <p
              style={{ backgroundImage: 'url(' + image_url + item.icon + ')' }}
              key={index}
              onClick={() => {
                if (this.props.checkJson()) {
                  this.props.changeCurrentIndex(index)
                  this.props.changeContent('question', 'card_icon')
                  this.props.dispatch({
                    type: GET_EDIT_LIST,
                    data: {
                      index: index,
                    },
                  })
                }
              }}
            >
              <span className='card_name' title={item.title}>
                {item.title.length > 8 ? item.title.substr(0, 7) + '...' : item.title}
              </span>
              <span className='card_info' title={item.tip ? item.tip : ''}>
                {item.tip ? (item.tip.length > 8 ? item.tip.substr(0, 7) + '...' : item.tip) : ''}
              </span>
            </p>
          </div>,
        )
      }
    })
    if (this.props.templateInfo.background == 'module_public/module_skill_home/home2.png') {
      var styles = {}
    } else {
      var styles = { backgroundImage: 'url(' + image_url + this.props.templateInfo.background + ')' }
    }
    return (
      <div className={this.props.className}>
        <div className='card_state'>
          <div className={this.props.currentIndex == -1 || this.props.fiveTimer ? 'card_top tip_cover' : 'card_top'}>
            {this.props.templateInfo.titleBarType == 'text' ? (
              <p
                className='card_title'
                onClick={() => {
                  if (this.props.checkJson()) {
                    this.props.changeCurrentIndex(-1)
                    this.props.changeContent('title')
                  }
                }}
              >
                <em className='title_icon'></em>
                {this.props.templateInfo.titleBarContent.length > 12
                  ? this.props.templateInfo.titleBarContent.substr(0, 11) + '...'
                  : this.props.templateInfo.titleBarContent}
              </p>
            ) : (
              <img
                className='card_Icon'
                src={image_url + this.props.templateInfo.titleBarContent}
                onClick={() => {
                  if (this.props.checkJson()) {
                    this.props.changeCurrentIndex(-1)
                    this.props.changeContent('title')
                  }
                }}
              />
            )}
          </div>
          <div className={this.props.currentSort || this.props.fiveTimer ? 'card_button tip_cover' : 'card_button'}>
            {list}
          </div>
        </div>
      </div>
    )
  }
}
export default Card
export { Card }
