import React from 'react'
import { connect } from 'react-redux'
import Swiper from 'react-id-swiper'
import { GET_EDIT_LIST } from '@redux/action-types'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页列表', state._home)
  return {
    showSkills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
    templateInfo: state._home.homeModule.templateInfo,
  }
})
class Simple extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      translateX: 0,
      translateY: 0,
      standardQueries: [],
      lang: this.props.lang || 'chs',
    }
    this.wordMax = 4
    this.moving = false
    this.lastX = null
    this.lastY = null
    window.onmouseup = (e) => this.onMouseUp(e)
    window.onMouseDown = (e) => this.onMouseDown(e)
    window.onmousemove = (e) => this.onMouseMove(e)
  }

  onMouseDown(e) {
    e.stopPropagation()
    e.preventDefault()
    this.moving = true
  }

  onMouseUp() {
    this.moving = false
    this.lastX = null
    this.lastY = null
  }

  onMouseMove(e) {
    this.moving && this.onMove(e)
  }

  onMove(e) {
    if (this.lastX && this.lastY) {
      let dx = e.clientX - this.lastX
      let dy = e.clientY - this.lastY
      let fl = (this.props.showSkills.length / 2 + 1) * 150 - 360
      if (this.state.translateX + dx >= -fl && this.state.translateX + dx <= 0) {
        this.setState({ translateX: this.state.translateX + dx })
      }
    }
    this.lastX = e.clientX
    this.lastY = e.clientY
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.lang && nextProps.lang && nextProps.lang != this.props.lang) {
      this.setState({
        lang: nextProps.lang,
      })
    }
  }
  isEng(word) {
    if (word.length == 0) {
      return ''
    }
    let word0 = word[0]
    let reg = /[a-z|A-Z]/
    let eng = reg.test(word0)
    this.wordMax = eng ? 8 : 4
    return eng
  }
  render() {
    let eng = this.isEng(this.props.showSkills[0].title)
    var image_url = this.props.devUrl + 'media/'
    var standardQueries = []
    this.props.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
    })
    //queries
    var queries_result_all = [],
      queries_result = [],
      simile_queries = []
    // for (let i = 0; i <= standardQueries.length / 4; i++) {

    //     queries_result.push(
    //         <div className="standard_area 1212" key={i}>
    //             {
    //                 standardQueries[4 * i] != undefined &&
    //                 <p title={standardQueries[4 * i]}
    //                     className={this.props.currentIndex == (4 * i) ? "tip_cover" : ""}
    //                     onClick={() => { this.clickQueries(4 * i, standardQueries[4 * i]) }}
    //                 >
    //                     <span>{'“' + (standardQueries[4 * i].length > 8 ? standardQueries[4 * i].substr(0, 7) + '...' : standardQueries[4 * i]) + '”'}</span>
    //                 </p>
    //             }
    //             {
    //                 standardQueries[4 * i + 1] != undefined &&
    //                 <p title={standardQueries[4 * i + 1]}
    //                     className={this.props.currentIndex == (4 * i + 1) ? "tip_cover" : ""}
    //                     onClick={() => { this.clickQueries(4 * i + 1, standardQueries[4 * i + 1]) }}
    //                 >
    //                     <span>{'“' + (standardQueries[4 * i + 1].length > 8 ? standardQueries[4 * i + 1].substr(0, 7) + "..." : standardQueries[4 * i + 1]) + '”'}</span>
    //                 </p>
    //             }
    //             {
    //                 standardQueries[4 * i + 2] != undefined &&
    //                 <p title={standardQueries[4 * i + 2]}
    //                     className={this.props.currentIndex == (4 * i + 2) ? "tip_cover" : ""}
    //                     onClick={() => { this.clickQueries(4 * i + 2, standardQueries[4 * i + 2]) }}
    //                 >
    //                     <span>{'“' + (standardQueries[4 * i + 2].length > 8 ? standardQueries[4 * i + 2].substr(0, 7) + "..." : standardQueries[4 * i + 2]) + '”'}</span>
    //                 </p>
    //             }
    //             {
    //                 standardQueries[4 * i + 3] != undefined &&
    //                 <p title={standardQueries[4 * i + 3]}
    //                     className={this.props.currentIndex == (4 * i + 3) ? "tip_cover" : ""}
    //                     onClick={() => { this.clickQueries(4 * i + 3, standardQueries[4 * i + 3]) }}
    //                 >
    //                     <span>{'“' + (standardQueries[4 * i + 3].length > 8 ? standardQueries[4 * i + 3].substr(0, 7) + "..." : standardQueries[4 * i + 3]) + '”'}</span>
    //                 </p>
    //             }
    //         </div>
    //     )
    //     if (standardQueries.length <= 4) {
    //         if (i == 0) {
    //             queries_result_all.push(
    //                 <div>{queries_result}</div>
    //             )
    //             queries_result = [];
    //         }
    //     } else if (standardQueries.length > 4 && standardQueries.length <= 8) {
    //         if (i == 1) {
    //             queries_result_all.push(
    //                 <div>{queries_result}</div>
    //             )
    //             queries_result = [];
    //         }
    //     } else if (standardQueries.length > 8 && standardQueries.length <= 12) {
    //         if (i == 1 || i == 2) {
    //             queries_result_all.push(
    //                 <div>{queries_result}</div>
    //             )
    //             queries_result = [];
    //         }
    //     } else if (standardQueries.length > 12 && standardQueries.length <= 16) {
    //         if (i == 1 || i == 3) {
    //             queries_result_all.push(
    //                 <div>{queries_result}</div>
    //             )
    //             queries_result = [];
    //         }
    //     }

    // }

    for (let i = 0; i <= standardQueries.length; i++) {
      standardQueries[i] != undefined &&
        simile_queries.push(
          <div className={'standard_area 3434 sa' + (i % 3)} key={i}>
            {standardQueries[i] != undefined && (
              <p
                title={standardQueries[i]}
                className={this.props.currentIndex == i ? 'tip_cover' : ''}
                onClick={() => {
                  this.clickQueries(i, standardQueries[i])
                }}
              >
                <span>
                  {'“' +
                    (standardQueries[i].length > 8 ? standardQueries[i].substr(0, 7) + '...' : standardQueries[i]) +
                    '”'}
                </span>
              </p>
            )}
          </div>,
        )

      queries_result_all.push(<div>{simile_queries}</div>)
      simile_queries = []
      // if (standardQueries.length <= 4) {
      //     if (i == 0) {

      //         simile_queries = [];
      //     }
      // }
    }

    //skills
    var skills_result = []
    if (this.props.showSkills[0].display) {
      skills_result.push(
        <p
          title={this.props.showSkills[0].title}
          className={this.props.currentIndex == 'skills0' ? 'tip_cover firstContent' : 'firstContent'}
          onClick={() => {
            this.clickSkills('skills0', 0)
          }}
        >
          {this.state.lang == 'chs' && (
            <span style={{ backgroundImage: 'url(' + image_url + this.props.showSkills[0].icon + ')' }}>
              {this.props.showSkills[0].title.length > this.wordMax
                ? this.props.showSkills[0].title.substr(0, this.wordMax)
                : this.props.showSkills[0].title}
            </span>
          )}
          {this.state.lang == 'chs' && (
            <span className='FCline2'>
              {this.props.showSkills[0].title.length > this.wordMax &&
                this.props.showSkills[0].title.substr(this.wordMax)}
            </span>
          )}
          {this.state.lang == 'eng' && (
            <span style={{ backgroundImage: 'url(' + image_url + this.props.showSkills[0].icon + ')' }}>
              {this.props.showSkills[0].title.length > 9
                ? this.props.showSkills[0].title.substr(0, 8) + '...'
                : this.props.showSkills[0].title}
            </span>
          )}
          {this.state.lang == 'eng' && <span className='FCline2'>{}</span>}
        </p>,
      )
    }

    var newSkills = this.props.showSkills.slice(1)
    for (let i = 0; i <= newSkills.length / 2; i++) {
      skills_result.push(
        <div className='standard_area2' key={'skills' + i} ref={this.saveRef}>
          {newSkills[2 * i] && newSkills[2 * i].display && (
            <p
              title={newSkills[2 * i].title}
              className={this.props.currentIndex == 'skills' + (2 * i + 1) ? 'tip_cover' : ''}
              onClick={() => {
                this.clickSkills('skills' + (2 * i + 1), 2 * i + 1)
              }}
            >
              <span style={{ backgroundImage: 'url(' + image_url + newSkills[2 * i].icon + ')' }}>
                {newSkills[2 * i].title.length > 5
                  ? newSkills[2 * i].title.substr(0, 4) + '...'
                  : newSkills[2 * i].title}
              </span>
            </p>
          )}
          {newSkills[2 * i + 1] && newSkills[2 * i + 1].display && (
            <p
              title={newSkills[2 * i + 1].title}
              className={this.props.currentIndex == 'skills' + (2 * i + 2) ? 'tip_cover' : ''}
              onClick={() => {
                this.clickSkills('skills' + (2 * i + 2), 2 * i + 2)
              }}
            >
              <span style={{ backgroundImage: 'url(' + image_url + newSkills[2 * i + 1].icon + ')' }}>
                {newSkills[2 * i + 1].title.length > 5
                  ? newSkills[2 * i + 1].title.substr(0, 4) + '...'
                  : newSkills[2 * i + 1].title}
              </span>
            </p>
          )}
        </div>,
      )
    }
    let params = {
      slidesPerView: 'auto',
      centeredSlides: false,
      spaceBetween: 10,
      grabCursor: true,
      freeMode: true,
      pagination: {
        el: '.swiper-pagination',
        // type: 'bullets',
        type: 'none',
        clickable: true,
      },
      // activeIndex:0,
      observer: true,
    }
    // params = {observer:true,}

    if (this.props.templateInfo.background == 'module_public/module_skill_home/home4.png') {
      var styles = {}
    } else {
      var styles = { backgroundImage: 'url(' + image_url + this.props.templateInfo.background + ')' }
    }
    var styles = { backgroundImage: `  url( ${image_url}${this.props.templateInfo.conciseContent.image})` }
    let fl = (this.props.showSkills.length / 2 + 1) * 140
    return (
      <div className={this.props.className}>
        <div className='standard_state'>
          <img src={require('@assets/png/meui/home4_eyes.png')} className='simple_standard_eyes' />
          <p
            className={
              this.props.haveCover
                ? 'simple_standard_title have_cover tac'
                : this.props.currentIndex == -1 || this.props.fiveTimer
                ? 'simple_standard_title tip_cover tac'
                : 'simple_standard_title tac'
            }
            title={this.props.templateInfo.titleBarContent}
            onClick={() => {
              if (this.props.checkJson()) {
                this.props.changeCurrentIndex(-1)
                this.props.changeContent('title')
              }
            }}
          >
            {this.props.templateInfo.conciseContent.title.length > 11
              ? this.props.templateInfo.conciseContent.title.substr(0, 10) + '...'
              : this.props.templateInfo.conciseContent.title}
          </p>
          <div
            className='ConciseImage'
            onClick={() => {
              if (this.props.checkJson()) {
                this.props.changeCurrentIndex(-1)
                this.props.changeContent('title')
              }
            }}
          >
            <div style={styles} src={require('@assets/png/meui/conciseimage.png')} className='ConciseImg'></div>
          </div>
          <div className='simple_title'>
            <em className='title_icon'></em>你还可以问我
          </div>
          <div
            className={
              this.props.haveCover
                ? 'simple_standard_queries have_cover1'
                : this.props.fiveTimer || this.props.currentSort
                ? 'simple_standard_queries tip_cover'
                : 'simple_standard_queries'
            }
          >
            <Swiper containerClass='simple-swiper-container' {...params}>
              {queries_result_all}
            </Swiper>
            <div className='simpleQu'>{/* {queries_result_all} */}</div>
          </div>
          <div
            className1={
              this.props.haveCover
                ? 'standard_skills add_cover'
                : this.props.fiveTimer || this.props.currentSort
                ? 'standard_skills tip_cover'
                : 'standard_skills'
            }
            className='dpn'
            onMouseDown={(e) => this.onMouseDown(e)}
            style={{
              transform: `translateX(${this.state.translateX}px)translateY(${this.state.translateY}px)`,
              width: `${fl}px`,
            }}
          >
            {skills_result}
          </div>
        </div>
      </div>
    )
  }
  clickQueries(i, txt) {
    if (this.props.checkJson()) {
      this.props.changeCurrentIndex(i)
      this.props.changeContent('question', 'standard1', i)
    }
  }
  clickSkills(currentI, i) {
    if (this.props.checkJson()) {
      this.props.changeCurrentIndex(currentI)
      if (i == 0) {
        this.props.changeContent('question', 'standard2_icon')
      } else {
        this.props.changeContent('question', 'standard1_icon')
      }
      this.props.dispatch({
        type: GET_EDIT_LIST,
        data: {
          index: i,
        },
      })
    }
  }
}
export default Simple
export { Simple }
