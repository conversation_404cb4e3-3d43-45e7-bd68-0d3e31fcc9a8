@import '~base.less';

.component {
  p {
    margin: 0;
    padding: 0;
  }
  .modal_content {
    .modal_title {
      font-size: 24px;
      color: #555d61;
      letter-spacing: 0;
      text-align: center;
      margin-bottom: 36px;
    }
    .list1,
    .list2 {
      text-align: left;
      .small_title {
        width: 70px;
        color: #3776ef;
        font-size: 16px;
        margin-right: 18px;
      }
    }
    .list1ext {
      margin-bottom: 14px;
    }
    .list2 {
      margin-top: 24px;
      margin-bottom: 8px;
    }
    .list1 {
      .list_dropdown {
        display: inline-block;
        cursor: pointer;
        width: 112px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border: solid 1px #3776ef;
        font-size: 14px;
        color: #9aa3a8;
        border-radius: 4px;
        margin-right: 20px;
      }
      .label {
        font-size: 14px;
        &:after {
          right: 6px !important;
          color: @color-theme;
          width: 12px;
          height: 12px;
          background: url('@assets/svg-icon/more-theme.svg') no-repeat 50% 50% / contain;
        }
      }
      .menu-container {
        left: 0px;
        top: 7px;
        position: relative;
        width: 100%;
        .menu {
          max-height: 300px;
          overflow-y: scroll;
          border-radius: 0;
          ::-webkit-scrollbar {
            width: 6px;
            height: 119px;
          }

          /* 滚动槽 */
          ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.06);
            // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            border-radius: 10px;
          }

          /* 滚动条滑块 */
          ::-webkit-scrollbar-thumb {
            height: 67px;
            border-radius: 10px;
            background: @color-theme;
            // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
          }

          ::-webkit-scrollbar-thumb:window-inactive {
            background: @color-theme;
          }
        }
        .menu-item {
          height: 44px;
          line-height: 44px;
          padding: 0 44px 0 16px;
          cursor: pointer;
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 14px;
          color: #555d61;
          position: relative;
          &:hover,
          &.cur {
            background: rgba(0, 146, 245, 0.1);
          }
          &.cur {
            cursor: default;
            &:after {
              display: block;
              content: '';
              position: absolute;
              width: 20px;
              height: 20px;
              right: 20px;
              top: 10px;
              background: url('@assets/svg-icon/clear_blue.svg') no-repeat center right;
              background-size: 28px;
              right: 0;
            }
          }
        }
      }
    }
    .modal_choose {
      max-height: 63vh;
      overflow-y: auto;
      display: flex;
      p {
        flex: 1;
        text-align: center;
        // max-width:162px;
        img {
          display: block;
          margin: 0 auto;
          margin-bottom: 6px;
          width: 162px;
          height: 260px;
        }
        label,
        span {
          font-size: 12px;
          color: #555d61;
          letter-spacing: 0;
          line-height: 16px;
          vertical-align: middle;
        }
      }
      .p_dis {
        opacity: 0.5;
      }
      .blue {
        label,
        span {
          color: #3776ef;
        }
      }
      p:nth-child(even) {
        margin-left: 12.5px;
        margin-right: 12.5px;
      }
    }
    .minicss {
      width: 600px;
      /* flex-direction: row; */
      justify-content: space-between;
      flex-wrap: wrap;
      p {
        img {
          width: 260px;
          height: 162px;
          border-radius: 7px;
        }
        label {
          // margin-bottom:8px;
        }
        span {
          display: inline-block;
          // margin-bottom: 8px;
        }
      }
      &.modal_choose {
        p {
          margin-top: 5px;
        }
      }
      .p_dis {
        pointer-events: none;
        opacity: 0.5;
      }
    }
    p.marked {
      margin-top: 19px;
      background: #fffbe6;
      border: 1px solid #ffe58f;
      border-radius: 4px;
      white-space: nowrap;
      padding: 10px;
      text-align: left;
      .marked_icon {
        color: #faad14;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -2px;
        margin-left: 8px;
      }
      span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        letter-spacing: 0;
        white-space: nowrap;
      }
    }
    .modal_button {
      // border-top: 1px solid #E1E7EA;
      // text-align: center;
      // padding-top:19px;
      // font-size: 15px;
      // color: #3776ef;
      // margin-top:20px;
      cursor: pointer;
      margin: 20px auto 0;
    }

  }

  .progress_modal {
    .progress_title {
      font-size: 20px;
      color: #555d61;
      letter-spacing: 0;
      text-align: center;
      line-height: 26px;
      margin-bottom: 26px;
    }
    .percentBg {
      height: 5px;
      background: #e1e7ea;
      width: 350px;
      border-radius: 0.15625rem;
    }
    .percentNow {
      height: 10px;
      margin-top: -7.5px;
      width: 20px;
      background: #1d5fdc;
      border-radius: 0.3125rem;
      animation: percent 40s forwards;
      -webkit-animation: percent 40s forwards;
      animation-timing-function: ease;
      -webkit-animation-timing-function: ease;
    }
    @keyframes percent {
      from {
        width: 0;
      }
      to {
        width: 100%;
      }
    }

    @-webkit-keyframes percent {
      from {
        width: 0;
      }
      to {
        width: 100%;
      }
    }
  }
}
