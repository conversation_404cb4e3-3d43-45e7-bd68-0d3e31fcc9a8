import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import classNames from 'classnames'
import { Icon } from 'biz-components'
import Loader from 'biz-components'
import { replace as historyReplace, push } from '@utils/history'
import modal, { close as closeModal } from '@utils/modal'
import 'rc-pagination/dist/rc-pagination.min.css'
import 'rc-select/assets/index.css'

@connect((state) => {
  let devUrl = ''
  if (__DEV__) {
    devUrl = 'http://test-jiedai.ainirobot.com/'
  }
  return {
    robots: state.robots.robots,
    devUrl: devUrl,
  }
})
@extend({
  styles: require('./publishRobot.less'),
})
class PublishRobot extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      loading: true,
      checkedArray: this.props.checkedArray,
      unCheckArray: this.props.unCheckArray,
      searchRobot: '',
      date: new Date().valueOf(),
    }
  }
  componentDidMount() {
    // debugger
  }
  UNSAFE_componentWillUpdate() {}

  render() {
    return (
      <div className={this.props.className}>
        <div className='publishContain'>
          <h3 className='title'></h3>
          <div className='searchBox'>
            <div className='search'>
              <input
                type='text'
                placeholder={__('customHome.btn.search')}
                disabled={this.state.firstShow}
                value={this.state.searchRobot}
                onChange={(e) => {
                  console.log(e.target.value)
                  const val = e.target.value.trim()
                  this.setState(
                    {
                      searchRobot: val,
                    },
                    () => {
                      if (val.length == 0) {
                        this.onSearch()
                      }
                    },
                  )
                }}
                onKeyDown={(e) => {
                  if (e.keyCode == 13) {
                    this.onSearch()
                  }
                }}
              />
              {this.state.searchRobot.length > 0 ? (
                <Icon
                  icon='monitor_x'
                  className='searchIcon'
                  onClick={() => {
                    this.setState(
                      {
                        searchRobot: '',
                      },
                      () => {
                        this.onSearch()
                      },
                    )
                  }}
                />
              ) : (
                <Icon
                  icon='rd_zoomin'
                  className='searchIcon'
                  onClick={() => {
                    this.onSearch()
                  }}
                />
              )}
            </div>
          </div>
          <ul className='sort'>
            {/* <li>未选择的机器人</li>
                        <li>已选择的机器人</li> */}
            <li>
              {__('robot.robotsNotSelected')}
              <span
                className={classNames({ disabled: this.state.searchRobot.length > 0 })}
                onClick={this.checkAll.bind(this)}
              >
                {this.state.checkAll ? __('AI_RECOMMEND.DIALOG.UNSELECT_ALL') : __('AI_RECOMMEND.DIALOG.SELECT_ALL')}
              </span>
            </li>
            <li>{__('company.selectedRobots')}</li>
          </ul>
          <div className='containBox'>
            <ul className='robotBox list' key={this.state.date}>
              {this.state.unCheckArray.length > 0 ? (
                this.state.unCheckArray.map((robot, index) => (
                  <li
                    key={robot.robot_id}
                    onClick={() => {
                      this.props.robotClick && this.props.robotClick(robot, index)
                    }}
                  >
                    <span className={classNames({ online: robot.state == 1 })} title={robot.robot_name}>
                      {robot.robot_name + (this.props.ableFun(robot, index) ? '' : this.props.disableTip || '')}
                    </span>
                    {this.props.ableFun(robot, index) &&
                      (robot.addstatus === undefined || robot.addstatus == false ? (
                        <Icon icon='rd_add' className='icon' onClick={this.addCheckArray.bind(this, robot, index)} />
                      ) : (
                        <span className='status disabled'>{__('Q&A.Q&As.SELECTED')}</span>
                      ))}
                  </li>
                ))
              ) : (
                <li>{__('company.none')}</li>
              )}
            </ul>
            <div className='arrow'>
              <Icon icon='control_forward_clic_1' className='icon' />
            </div>
            <ul className='checkRobot list'>
              {this.state.checkedArray.length > 0 ? (
                this.state.checkedArray.map((robot, index) => (
                  <li key={robot.sn}>
                    <span className={classNames({ online: robot.state == 1 })} title={robot.robot_name}>
                      {robot.robot_name}
                    </span>
                    <Icon
                      icon='monitor_x'
                      className='icon del'
                      onClick={this.delRobot.bind(this, index, robot.robot_id)}
                    />
                  </li>
                ))
              ) : (
                <li>{__('company.none')}</li>
              )}
            </ul>
          </div>
          {this.props.showtip && (
            <p className='marked'>
              <Icon icon='Alert_Default_Warning' className='marked_icon' />
              <span onClick1={(e) => {}}>{this.props.showtip.message}</span>
            </p>
          )}
        </div>
      </div>
    )
  }

  //删除所选机器人
  delRobot(num, robot_id) {
    const keyWord = this.state.searchRobot.trim()
    if (keyWord.length > 0) {
      console.log('搜索不为空')
      this.props.checkedArray.map((robot, index) => {
        if (robot.robot_id === robot_id) {
          this.props.checkedArray.splice(index, 1)
        }
      })
      this.state.checkedArray.map((robot, index) => {
        if (robot.robot_id === robot_id) {
          this.state.checkedArray.splice(index, 1)
        }
      })
      this.state.unCheckArray.map((robot, index) => {
        if (robot.robot_id === robot_id) {
          robot.addstatus = false
        }
      })
      this.props.unCheckArray.map((robot, index) => {
        if (robot.robot_id === robot_id) {
          robot.addstatus = false
        }
      })
      this.setState(
        {
          checkedArray: this.state.checkedArray,
          unCheckArray: this.state.unCheckArray,
          // unCheckArray: this.props.unCheckArray
        },
        () => {
          this.props.getRobotId(this.state.checkedArray, this.state.unCheckArray)
        },
      )
    } else {
      console.log('搜索为空')

      this.state.checkedArray.splice(num, 1)
      this.props.unCheckArray.map((robot) => {
        if (robot.robot_id === robot_id) {
          console.log(robot, '删除的this.props.unCheckArray')
          robot.addstatus = false
        }
      })

      this.setState(
        {
          checkedArray: this.state.checkedArray,
          unCheckArray: this.props.unCheckArray,
        },
        () => {
          this.props.getRobotId(this.state.checkedArray, this.state.unCheckArray)
        },
      )
    }
  }
  //添加到已选中
  addCheckArray(robot, num) {
    const keyWord = this.state.searchRobot.trim()
    let checkedArray = this.state.checkedArray,
      unCheckArray = this.state.unCheckArray
    checkedArray.push(robot)
    this.state.unCheckArray[num].addstatus = true
    this.setState(
      {
        checkedArray: checkedArray,
        unCheckArray: unCheckArray,
      },
      () => {
        if (keyWord.length > 0) {
          let unCheckArrays = JSON.parse(JSON.stringify(this.props.unCheckArray))
          unCheckArrays[num].addstatus = true
          this.props.getRobotId(this.state.checkedArray, unCheckArrays)
        } else {
          this.props.getRobotId(this.state.checkedArray, unCheckArray)
        }
      },
    )
  }
  //搜索
  onSearch() {
    const keyWord = this.state.searchRobot.trim()
    this.props.unCheckArray.map((robot) => {
      this.state.checkedArray.map((checkRobot) => {
        if (robot.robot_id === checkRobot.robot_id) {
          robot.addstatus = true
        }
      })
    })
    if (keyWord.length < 1) {
      console.log('搜索！！！')
      this.setState(
        {
          checkedArray: this.state.checkedArray,
          // checkedArray: this.props.checkedArray,
          unCheckArray: this.props.unCheckArray,
          date: new Date().valueOf(),
        },
        () => {
          // this.props.getRobotId(this.props.checkedArray,this.props.unCheckArray)
          this.props.getRobotId(this.state.checkedArray, this.props.unCheckArray)
        },
      )
    } else {
      let newChecked = [],
        newUnchecked = [],
        checkedArray = this.state.checkedArray,
        unCheckArray = JSON.parse(JSON.stringify(this.props.unCheckArray))

      // console.log(this.state.checkedArray,'!!!this.state.checkedArray')
      unCheckArray.map((item) => {
        const flag = item.robot_name.toLowerCase().includes(keyWord.toLowerCase())
        if (flag) {
          newUnchecked.push(item)
        }
      })
      if (newUnchecked.length < 0) {
        this.setState({})
      }
      this.setState(
        {
          // checkedArray: newChecked,
          checkedArray: this.state.checkedArray,
          unCheckArray: newUnchecked,
          date: new Date().valueOf(),
        },
        () => {
          console.log(this.state.checkedArray, '选中')
          console.log('未选中', this.state.unCheckArray)
          // this.props.getRobotId(newChecked,newUnchecked)
          this.props.getRobotId(this.state.checkedArray, newUnchecked)
        },
      )
    }
  }

  //全选 //取消全选
  checkAll() {
    let checkAll = this.state.checkAll //false为全选，true为取消全选
    let unCheckArray = this.state.unCheckArray,
      newCheckArray = []
    if (checkAll) {
      unCheckArray.map((robot) => {
        robot.addstatus = false
      })

      this.setState(
        {
          checkedArray: [],
          unCheckArray: unCheckArray,
          checkAll: false,
        },
        () => {
          this.props.getRobotId(this.state.checkedArray, this.state.unCheckArray)
        },
      )
    } else {
      unCheckArray.map((robot) => {
        robot.addstatus = true
      })
      newCheckArray = JSON.parse(Object.assign(JSON.stringify(unCheckArray)))
      let _newCheckArray = []
      newCheckArray.map((robot) => {
        if (robot.state == 1) {
        }

        if (this.props.ableFun(robot)) {
          _newCheckArray.push(robot)
        }
      })
      this.setState(
        {
          checkedArray: _newCheckArray,
          unCheckArray: unCheckArray,
          checkAll: true,
        },
        () => {
          this.props.getRobotId(this.state.checkedArray, this.state.unCheckArray)
        },
      )
    }
  }
}
export default PublishRobot
