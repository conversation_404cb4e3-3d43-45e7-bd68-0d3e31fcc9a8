import React from 'react'
import { Icon } from 'biz-components'
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc'
import { connect } from 'react-redux'
import { Button, Dropdown, MenuItem } from 'biz-components'
import { message, Input, AutoComplete, Popover } from 'antd'

import recommendQues from '@api/_home/recommendQues'
import {
  REMOVE_HOMEMODULE_SKILL,
  ADD_HOMEMODULE_SKILL,
  CHANGE_QUERY_SORT,
  REMOVE_HOMEMODULE_QUERY,
  EDIT_QUERIES,
} from '@redux/action-types'
import { checkReleaseRaw, inputMaxCnFooter, inputMaxEnFooter, soleHelpQa, onLangLen } from '@utils/tools'
import CardTitle from '../cardTitle'

const nav_fun = ['mainPage', 'chat', 'query_locations', 'weather', 'calendar', 'dance', 'web', 'video']
let nav_content = [
  __('customHome.functionList.homepage'),
  __('customHome.functionList.chat2'),
  __('customHome.functionList.guide2'),
  __('customHome.functionList.weather'),
  __('customHome.functionList.calendar'),
  __('customHome.functionList.dance'),
  __('customHome.functionList.miniApp2'),
  __('customHome.functionList.vidwoSing'),
]
let righttip = (value, num) => {
  return (
    <div>
      <span>{value.title ? value.title.length : value.length}</span> <span>/{num}</span>{' '}
    </div>
  )
}
let DragHandle_query = SortableHandle(() => (
  <Popover content={__('customHome.modalMsg.pressOrder')}>
    <Icon icon='move2' className='sort_button_del ask_sort_button_del drag_button ' />
  </Popover>
))
let SortableItem_query = SortableElement(
  ({ idx, value, onDelQuery, lang, editQueries, checkValue, modalStyle, queries, _this }) => {
    let inputTimeout = null
    let value2 = value
    return (
      <div className='sort_parent'>
        <p className='ask_sort_info sort_info 1' style={{display: 'flex'}}>
          <div style={{ display: 'inline-block', color: '#9b9b9b', whiteSpace: 'nowrap', height: '40px', lineHeight:'50px'  }}>
            {__('customHome.face.copywriter')}
          </div>
          <div className='viewAsk 1' style={{ position: 'relative', marginLeft: '10px' }}>
            <Input
              readOnly
              className='abc noevent'
              maxLength={onLangLen(lang, 'footer')}
              suffix={righttip(value, onLangLen(lang, 'footer'))}
              onChange={(e) => {}}
            />
            <AutoComplete
              style1={{
                width: 200,
                position: 'absolute',
                right: '30px',
                zIndex: 10000,
                marginLeft: 65,
              }}
              className='query'
              placeholder=''
              maxLength={onLangLen(lang, 'footer')}
              onKeyDown={(e) => {
                if (e.keyCode == 8) {
                  // inputTimeout && clearTimeout(inputTimeout);
                }
              }}
              onChange={(e) => {
                inputTimeout && clearTimeout(inputTimeout)
                console.log('更新了了', { e })
                value2 = e
                // inputTimeout = setTimeout(() => {
                editQueries(e, idx)
                // }, 300)
              }}
              onBlur={(e) => checkValue(e)}
              bordered={false}
              value={value2}
              // defaultValue={JSON.stringify(queries) + value ? value : ''}
              getPopupContainer={(triggerNode) =>
                triggerNode.parentElement.parentElement.parentElement.parentElement.parentElement
              }
              options={(() => {
                //  return [{ value: 'text 1' }, { value: 'text 2' }]
                let res = []
                queries.map((item) => {
                  //  <Option value={item}>{item}</Option>
                  res.push({ value: item })
                })
                return res
              })()}
              options1={[{ value: 'text 1' }, { value: 'text 2' }]}
            >
              <input
                type='text'
                attr='auto_input'
                className='auto-child'
                maxLength={onLangLen(lang, 'footer')}
                style={{
                  border: 'none',
                  background: 'transparent',
                  boxShadow: 'none',
                  height: '100%',
                  outline: 'none',
                  width: '100%',
                }}
              />
            </AutoComplete>
          </div>
          <div className='askButton' style={{position: 'static'}}>
            <Popover content={__('customHome.modalMsg.deleConf')}>
              <Icon
                icon='rd_trash'
                className='icon1 asktoright1 rd_trash'
                onClick={() => {
                  onDelQuery(idx)
                }}
              />
            </Popover>
            <DragHandle_query />
          </div>
        </p>
      </div>
    )
  },
)

let SortableList_query = SortableContainer(
  ({ items, onDelQuery, lang, editQueries, checkValue, modalStyle, queries, _this }) => {
    return (
      <ul className='sort_area1'>
        {items.map((item, index) => {
          return (
            <SortableItem_query
              key={`item-${index}`}
              index={index}
              idx={index}
              value={item}
              lang={lang}
              onDelQuery={onDelQuery}
              editQueries={editQueries}
              checkValue={checkValue}
              modalStyle={modalStyle}
              queries={queries}
              _this={_this}
            />
          )
        })}
      </ul>
    )
  },
)
@connect((state) => {
  return {
    homeModule: state._home.homeModule,
    skills: state._home.homeModule ? state._home.homeModule.skills : [],
    queries: state._home.homeModule ? state._home.homeModule.queries : [],
    robotId: state.robots.current,
    robots: state.robots.robots,
    langDataActive: state.user.langDataActive,
  }
})
class BottomQueries extends React.Component {
  state = {
    localQueries: recommendQues.queries,
    tabIndex: 0,
    tempaletType: 0,
    queries_style: 'mainPage',
    nav_fun: ['mainPage'],
    nav_content: [__('customHome.functionList.homepage')],
    versionFlag: false,
    lang: this.props.lang || 'chs',
    sortFlag: false,
  }
  componentDidMount() {
    this.pushSort()
    this.setTemplateType()
    Object.keys(this.props.robots).map((el) => {
      if (this.props.robotId == el) {
        let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.11', true)
        this.setState({
          versionFlag: versionGap ? true : false, //4.11 //非4.11
          nav_fun: nav_fun[0],
          nav_content: nav_content[0],
        })
      }
    })
  }
  componentDidUpdate(prevProps) {
    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.11', true)
          this.setState({
            versionFlag: versionGap ? true : false,
          })
        }
      })
    }
  }

  getDerivedStateFromProps(props, state) {
    if (props.lang !== state.lang) {
      return {
        lang: props.lang,
      }
    }
    return null
  }

  checkValue = (data) => {
    if (data.target.value.trim().length == 0) {
      return message.error(__('customHome.modalMsg.cannotBeNull'))
    }
  }

  render() {
    let standardQueries = this.props.queries.filter((el) => el.name === 'mainPage')[0].query
    if (!this.props.homeModule) return ''
    let queriesList = [
      'You can say to me:: xxxxxx',
      'Here is the global boot, please be sure to configure',
      'It will also rotate, please be sure to configure multiple',
    ]
    const soleOne = soleHelpQa(this.props.langDataActive.lang_code)
    if (soleOne) {
      queriesList = [soleOne.sayToMes, soleOne.globalBoot, soleOne.multiple]
    }
    return (
      <div className={'bottomsort-container ' + this.props.className}>
        <CardTitle
          title={__('customHome.label.bottomGuide')}
          tips={__('customHome.label.editGuide')}
          desc={__('customHome.label.editGuide')}
        >
          <>
            {this.state.versionFlag && (
              <div className='sort' style={{ marginRight: '10px' }}>
                <Dropdown
                  className='list_dropdown'
                  labelButton={{
                    appearance: 'hollow',
                    size: 'small',
                  }}
                  label={
                    __('customHome.btn.display') +
                    '：' +
                    (this.state.tempaletType == 0 ? __('customHome.btn.sequence') : __('customHome.btn.random'))
                  }
                >
                  <MenuItem
                    children={__('customHome.btn.sequence')}
                    key='0'
                    onClick={() => {
                      this.editSort(0, standardQueries)
                    }}
                  />
                  <MenuItem
                    children={__('customHome.btn.random')}
                    key='1'
                    onClick={() => {
                      this.editSort(1, standardQueries)
                    }}
                  />
                </Dropdown>
              </div>
            )}
            <Button
              size='small'
              appearance='hollow'
              label={__('customHome.btn.New')}
              onClick={() => {
                if (standardQueries.length < 20) {
                  let text = this.state.localQueries.filter((el) => el.name === 'mainPage')[0].query[0]
                  if (this.props.lang == 'english') {
                    text = 'You can ask me something'
                  } else if (this.props.lang == 'arabic') {
                    text = ' '
                  }
                  this.newAddQueries(text)
                } else {
                  message.error(__('customHome.modalMsg.recommendedNum', { num: 20 }))
                }
              }}
            />
          </>
        </CardTitle>

        {standardQueries.length != 0 ? (
          <div className='bottomquery-sort-container' key={standardQueries.length + this.state.sortFlag}>
            <div className='asksort a1'>
              <SortableList_query
                items={standardQueries}
                lang={this.props.lang}
                axis='y'
                onSortEnd={this.onSortQueryEnd}
                useDragHandle={true}
                onDelQuery={(idx) => this.onDelQuery(idx)}
                editQueries={this.editQueries.bind(this)}
                checkValue={this.checkValue}
                modalStyle={this.props.modalStyle}
                scenes={this.props.homeModule.templateInfo.scenes}
                queries={queriesList}
                _this={this}
              />
            </div>
          </div>
        ) : (
          ''
        )}
        {standardQueries && standardQueries.length == 0 ? (
          <div className='no_queries'>
            <p>{__('customHome.modaMsg.restMsg')}</p>
          </div>
        ) : (
          ''
        )}
      </div>
    )
  }
  pushSort() {
    this.props.queries.forEach((item) => {
      if (item.sort == undefined) {
        this.props.dispatch({
          type: 'MODIFY_QUERIES_SORT',
          data: {
            content: item.query,
            bottomQueries: item.name,
            sort: 0,
          },
        })
      }
    })
  }
  editSort(idx, content) {
    // console.log(content, '排序content');
    this.setState({
      tempaletType: idx,
    })
    this.props.dispatch({
      type: 'MODIFY_QUERIES_SORT',
      data: {
        content: content,
        bottomQueries: this.state.queries_style,
        sort: idx,
      },
    })
  }
  newAddQueries(text) {
    this.props.dispatch({
      type: 'ADD_NEW_QUERIES',
      data: {
        index: 0,
        content: text,
        bottomQueries: this.state.queries_style,
      },
    })
    this.props.bottomQueries(this.state.queries_style, 0)
  }
  setTemplateType() {
    let sort = 0
    this.props.queries.forEach((item) => {
      if (item.name == this.state.queries_style) {
        // console.log(item.sort)
        if (item.sort == undefined) {
          sort = 0
        } else {
          sort = item.sort
        }
      }
    })
    this.setState({
      tempaletType: sort,
    })
  }
  changeTab(index) {
    let sort = 0
    this.props.queries.forEach((item) => {
      if (item.name == this.state.nav_fun[index]) {
        // console.log(item.sort)
        if (item.sort == undefined) {
          sort = 0
        } else {
          sort = item.sort
        }
      }
    })
    this.setState({
      tabIndex: index,
      queries_style: this.state.nav_fun[index],
      tempaletType: sort,
    })
    this.props.bottomQueries(this.state.nav_fun[index], 0)
  }
  remove = (idx, display) => {
    let showSkillNum = 0
    this.props.skills.forEach((item) => {
      if (item.display) {
        showSkillNum = showSkillNum + 1
      }
    })
    // console.log("显示skills数量" + showSkillNum);
    if (showSkillNum <= 3) {
      message.error(__('customHome.modalMsg.keepNum'))
      // modal({
      // 	title: __('customHome.modalMsg.failedToHide'),
      // 	content: __('customHome.modalMsg.keepNum'),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      // console.log("移除index：", idx, '；当前display：', display);
      let index = idx
      switch (this.props.modalStyle) {
        case 'List':
          index = idx + 2
          break
        case 'Card':
          index = idx
          break
        case 'Standard':
          index = idx + 1
          break
        default:
          index = idx
      }
      this.props.dispatch({
        type: REMOVE_HOMEMODULE_SKILL,
        data: {
          index: index,
          display: !display,
        },
      })
    }
  }
  add = (idx, display) => {
    // console.log("添加index：", idx, '；当前display：', display);
    let index = idx
    switch (this.props.modalStyle) {
      case 'List':
        index = idx + 2
        break
      case 'Card':
        index = idx
        break
      case 'Standard':
        index = idx + 1
        break
      default:
        index = idx
    }
    this.props.dispatch({
      type: ADD_HOMEMODULE_SKILL,
      data: {
        index: index,
        display: !display,
        modalStyle: this.props.modalStyle,
      },
    })
  }
  editQueries(text, indexs) {
    this.props.dispatch({
      type: EDIT_QUERIES,
      data: {
        index: indexs,
        content: text,
        bottomQueries: this.state.queries_style,
      },
    })
    this.props.bottomQueries(this.state.queries_style, indexs)
    // closeModal();
  }
  onDelQuery = (idx) => {
    let standardQueries = this.props.queries.filter((el) => el.name === 'mainPage')[0].query
    if (standardQueries.length <= 1) {
      message.error(__('customHome.modalMsg.keepRecommendedNum', { num: 1 }))
      return
    } else {
      // console.log("移除queryies index：", idx);
      this.props.dispatch({
        type: REMOVE_HOMEMODULE_QUERY,
        data: {
          index: idx,
          bottomQueries: this.state.queries_style,
        },
      })
      // console.log(this.props.queries)
      this.props.bottomQueries(this.state.queries_style, 0)
    }
  }
  onSortQueryEnd = ({ oldIndex, newIndex }) => {
    this.props.dispatch({
      type: CHANGE_QUERY_SORT,
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
        bottomQueries: this.state.queries_style,
      },
    })
    this.setState({
      sortFlag: !this.state.sortFlag,
    })
    this.props.bottomQueries(this.state.queries_style, 0)
  }
}

export default BottomQueries
export { BottomQueries }
