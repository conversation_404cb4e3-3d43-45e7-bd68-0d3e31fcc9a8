import React, { Component, Fragment } from 'react'
import { extend } from 'koot'
import styles from './index.module.less'
import { Upload, Popover } from 'antd'
import ImgCrop from 'antd-img-crop'
import { connect } from 'react-redux'
import 'antd/es/modal/style'
import 'antd/es/slider/style'
import { isMini, getQueryVal } from '@utils/tools'
import { getImageSize } from '@utils/image-size'

import { ApiBase } from '@utils/request'
@connect((state) => {
  let devUrl = '/media/'
  if (__DEV__) {
    devUrl = `${ApiBase}media/`
  }
  return {
    homeModule: state._home.homeModule,
    // originHomeModule: state._home.originHomeModule,
    // isModuleChange: state._home.isModuleChange,
    // currentRobotId: state.robots.current,
    // robots: state.robots.robots,
    // fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    // user: state.user,
    // brand: state.brand || 'ktv',
    cropId: state.user.detail.corp_uuid,
  }
})
@extend({
  styles,
})
class BackgroundImg extends Component {
  // 判断是否使用 agentOS 模板
  isAgentOSTemplate = () => {
    const { homeModule } = this.props;
    return homeModule && homeModule.templateInfo && homeModule.templateInfo.templateCode === 'agentOS';
  }

  // 恢复默认背景
  reset = () => {
    this.props.restBackImg()
    this.props.deleteBackImg()
  }

  // 图片上传之前
  uploadImg = (e) => {
    let robotType_1 = getQueryVal('robotType')
    let _this = this
    let img = e
    let reader = new FileReader()
    let sizeRule = this.getSize()
    let w = sizeRule.width //isMini(robotType_1) ? 1920 : 1200
    let h = sizeRule.height //isMini(robotType_1) ? 1080 : 1920
    let type = (isMini(robotType_1) && 'miniBackImg') || 'backImg'
    reader.readAsDataURL(img)
    reader.onload = function (e) {
      let image = new Image()
      image.onload = function () {
        let mycanvas = document.querySelector('#mybackCanvas1')
        let ctx = mycanvas.getContext('2d')
        ctx.clearRect(0, 0, w, h)
        ctx.drawImage(image, 0, 0, w, h)
        let dataurl = mycanvas.toDataURL('image/png')
        function dataURLtoBlob(dataurl) {
          let arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n)
          while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
          }
          return new Blob([u8arr], { type: mime })
        }
        
        // 如果是 agentOS 模板，我们也需要更新 agentosContent.background
        // 在常规背景更新后，我们也需要更新 agentosContent.background
        if (_this.isAgentOSTemplate()) {
          _this.props.selectImg(dataURLtoBlob(dataurl), type, '', '', function(upload_url) {
            // 更新 agentosContent.background 为相同的背景图片
            const { homeModule } = _this.props;
            if (homeModule.templateInfo.agentosContent) {
              const agentosContent = { ...homeModule.templateInfo.agentosContent };
              agentosContent.background = upload_url;
              
              _this.props.dispatch({
                type: 'CHANGE_INFO',
                data: {
                  type: 'agentosContent',
                  content: agentosContent,
                },
              });
            }
          });
        } else {
          // 常规情况，非 agentOS 模板
          _this.props.selectImg(dataURLtoBlob(dataurl), type, '');
        }
      }
      image.src = e.target.result
    }
    return true
  }

  render() {
    const { homeModule, devUrl } = this.props
    let robotType_1 = getQueryVal('robotType')
    const isMiniRobot = isMini(robotType_1)
    // let w = isMiniRobot ? 1920 : 1200
    // let h = isMiniRobot ? 1080 : 1920
    let templateCode = homeModule.templateInfo.templateCode
    let sizeRule = this.getSize()
    return (
      <div className={this.props.className}>
        <div className='head' id='backgroundrg'>
          <span className='tit'>{__('customHome.pic.backgroundPicture')}</span>
          {/**  背景图片 示例 */}
          {(isMini(robotType_1) && (
            <Popover
              content={
                <div style={{ display: 'flex', width: '410px' }}>
                  {templateCode == 'List' && (
                    <>
                      {' '}
                      <div style={{ marginRight: '10px' }}>
                        <img src={require('@assets/png/meui/mini_tob_home_err.png')} style={{ width: '200px' }} />
                        <p style={{ textAlign: 'center' }}>{__('customHome.pic.wrong')}</p>
                      </div>
                      <div>
                        <img src={require('@assets/png/meui/mini_tob_home.png')} style={{ width: '200px' }} />
                        <p style={{ textAlign: 'center' }}>{__('customHome.pic.correct')}</p>
                      </div>
                    </>
                  )}
                  {templateCode == 'Standard' && (
                    <>
                      {' '}
                      <div style={{ marginRight: '10px' }}>
                        <img
                          src={require('@assets/png/meui/standard_mini_tob_home_err.png')}
                          style={{ width: '200px' }}
                        />
                        <p style={{ textAlign: 'center' }}>{__('customHome.pic.wrong')}</p>
                      </div>
                      <div>
                        <img src={require('@assets/png/meui/standard_mini_tob_home.png')} style={{ width: '200px' }} />
                        <p style={{ textAlign: 'center' }}>{__('customHome.pic.correct')}</p>
                      </div>
                    </>
                  )}
                  {(templateCode == 'Simple' || templateCode == 'agentOS') && (
                    <>
                      {' '}
                      <div style={{ marginRight: '10px' }}>
                        <img
                          src={require('@assets/png/meui/simple_mini_tob_home_err.png')}
                          style={{ width: '200px' }}
                        />
                        <p style={{ textAlign: 'center' }}>{__('customHome.pic.wrong')}</p>
                      </div>
                      <div>
                        <img src={require('@assets/png/meui/simple_mini_tob_home.png')} style={{ width: '200px' }} />
                        <p style={{ textAlign: 'center' }}>{__('customHome.pic.correct')}</p>
                      </div>
                    </>
                  )}
                  {templateCode == 'Card' && (
                    <>
                      {' '}
                      <div style={{ marginRight: '10px' }}>
                        <img src={require('@assets/png/meui/card_mini_tob_home_err.png')} style={{ width: '200px' }} />
                        <p style={{ textAlign: 'center' }}>{__('customHome.pic.wrong')}</p>
                      </div>
                      <div>
                        <img src={require('@assets/png/meui/card_mini_tob_home.png')} style={{ width: '200px' }} />
                        <p style={{ textAlign: 'center' }}>{__('customHome.pic.correct')}</p>
                      </div>
                    </>
                  )}
                </div>
              }
            >
              <span className='tip'>{__('Q&A.Q&As.EXAMPLE').substring(0, 7)}</span>
            </Popover>
          )) || (
            <Popover
              content={
                <div style={{ display: 'flex', width: '410px' }}>
                  <div style={{ marginRight: '10px' }}>
                    <img src={require('@assets/png/meui/error.png')} style={{ width: '200px' }} />
                    <p style={{ textAlign: 'center' }}>{__('customHome.pic.wrong')}</p>
                  </div>
                  <div>
                    <img src={require('@assets/png/meui/correct.png')} style={{ width: '200px' }} />
                    <p style={{ textAlign: 'center' }}>{__('customHome.pic.correct')}</p>
                  </div>
                </div>
              }
            >
              <span className='tip'>{__('Q&A.Q&As.EXAMPLE').substring(0, 7)}</span>
            </Popover>
          )}
        </div>
        {/* 背景图片 */}
        {(isMini(robotType_1) && (
          <div className='mini_imgmodal'>
            <div className='backImg'>
              {homeModule && homeModule.templateInfo.background && (
                <img src={`${devUrl}${homeModule.templateInfo.background}`} className='imgset 1' />
              )}
              <span onClick={this.reset}>{__('customHome.btn.reset')}</span>
            </div>
          </div>
        )) || (
          <div className='imgmodal'>
            <div className='backImg'>
              {homeModule && homeModule.templateInfo.background && (
                <img src={`${devUrl}${homeModule.templateInfo.background}`} className='imgset 2' />
              )}
              <span onClick={this.reset}>{__('customHome.btn.reset')}</span>
            </div>
          </div>
        )}
        <Popover
          content={
            <div className='uploadTip' style={{}}>
              <div className='tips-container'>
                <span className='tips-text'>{__('customHome.pic.imageFormat')}：JPG/PNG/JPEG/GIF</span>
                <span className='tips-text'>
                  {__('customHome.pic.pictureSize')}：{sizeRule.width}px*{sizeRule.height}px
                </span>
              </div>
            </div>
          }
        >
          <div className={isMiniRobot ? 'mini_changBtn' : 'changBtn 1'}>
            <ImgCrop
              quality={1}
              modalWidth='650px'
              aspect={sizeRule.width / sizeRule.height}
              grid='grid'
              modalTitle={__('customHome.btn.img_cut')}
              modalOk={__('customHome.btn.confirm')}
              modalCancel={__('customHome.btn.cancel2')}
              fillColor={'transparent'}
              beforeCrop={(file) => {
                if (file.type === 'image/gif') {
                  let robotType_1 = getQueryVal('robotType')
                  let type = (isMini(robotType_1) && 'miniBackImg') || 'backImg'
                  this.props.selectImg(file, type, '')
                  return false
                }
                return true
              }}
            >
              <Upload
                showUploadList={false}
                beforeUpload={(e) => {
                  return this.uploadImg(e)
                }}
              >
                <button> {__('CORPUS.UPDATE_PICTURE')}</button>
              </Upload>
            </ImgCrop>
          </div>
        </Popover>
        <canvas style={{ display: 'none' }} id='mybackCanvas1' width={sizeRule.width} height={sizeRule.height} />
      </div>
    )
  }

  getSize = () => {
    const { homeModule } = this.props
    let robotType_1 = getQueryVal('robotType')
    let templateCode = homeModule.templateInfo.templateCode
    let sizeRule = getImageSize(robotType_1, templateCode, 0, 'bg')
    return sizeRule
  }
}

export default BackgroundImg
