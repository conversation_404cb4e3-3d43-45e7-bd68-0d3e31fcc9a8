@import '~base.less';

.component {
  // background: red;
  margin-top: 34px;
  .head {
    margin-bottom: 9px;
    .tit {
      height: 22px;
      font-size: 16px;
      font-weight: 500;
      color: #4a4a4a;
      line-height: 22px;
      margin-right: 10px;
      vertical-align: middle;
      margin: 40px 0 8px 0 !important;
      font-weight: bold;
      line-height: 20px;
      .anticon {
        margin-left: 5px;
      }
    }
    .icon {
      width: 14px;
      height: 14px;
      margin-right: 10px;
      vertical-align: middle;
    }
    .tip {
      height: 20px;
      font-size: 14px;
      // font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4881f0;
      line-height: 20px;
      vertical-align: middle;
      margin-left: 10px;
      cursor: pointer;
    }
  }

  .imgmodal {
    display: flex;
    .backImg {
      width: 100px;
      height: 160px;
      position: relative;
      background: #d8d8d8;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 9px;
      margin-right: 20px;
      .imgset {
        // width: 100px;
        height: 100%;
      }
      span {
        display: block;
        height: 22px;
        width: 100%;
        font-size: 12px;
        // font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 22px;
        background: #000000;
        opacity: 0.64;
        text-align: center;
        position: absolute;
        bottom: 0;
        cursor: pointer;
        &:hover {
          opacity: 1;
        }
      }
    }
  }
  .mini_imgmodal {
    display: flex;
    .backImg {
      width: 160px;
      height: 90px;
      position: relative;
      background: #d8d8d8;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 9px;
      margin-right: 20px;
      .imgset {
        // width: 100px;
        height: 100%;
        width: 100%;
      }
      span {
        display: block;
        height: 22px;
        width: 100%;
        font-size: 12px;
        // font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 22px;
        background: #000000;
        opacity: 0.64;
        text-align: center;
        position: absolute;
        bottom: 0;
        cursor: pointer;
        &:hover {
          opacity: 1;
        }
      }
    }
  }
  .changBtn {
    width: 100px;
    text-align: center;
    button {
      border: none;
      background: #fff;
      width: 88px;
      height: 28px;
      border-radius: 18px;
      // opacity: 0.2;
      border: 1px solid #4881f0;
      color: #4881f0;
      cursor: pointer;
      outline: none;
      &:hover {
        // opacity: 1;
        background: #4881f0;
        color: #fff;
      }
    }
  }
  .mini_changBtn {
    width: 160px;
    text-align: center;
    button {
      border: none;
      background: #fff;
      width: 88px;
      height: 28px;
      border-radius: 18px;
      // opacity: 0.2;
      border: 1px solid #4881f0;
      color: #4881f0;
      cursor: pointer;
      outline: none;
      &:hover {
        // opacity: 1;
        background: #4881f0;
        color: #fff;
      }
    }
  }

  .example {
    display: flex;
    background: red;
    .error {
      margin-top: 74px;
      display: inline-block;
      margin-right: 18px;
      text-align: center;

      p {
        color: #ff5a6e;
        font-size: 15px;
        margin-top: 11px;
      }
    }
  }
}
