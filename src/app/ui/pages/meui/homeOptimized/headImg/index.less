@import '~base.less';

.component {
  // background: red;
  // margin-bottom: 10px;
  // .head{
  //     margin-bottom: 9px;
  //     .tit{
  //         height: 22px;
  //         font-size: 16px;
  //         font-weight: 500;
  //         color: #4A4A4A;
  //         line-height: 22px;
  //         margin-right:10px;
  //         vertical-align: middle;
  //     }
  //     .icon{
  //         width:14px;
  //         height: 14px;
  //         margin-right:10px;
  //         vertical-align: middle;
  //     }
  // }
  .container {
    display: flex;
    .viewAsk-write {
      display: inline-block;
      position: absolute;
      top: 4px;
      color: #9b9b9b;
    }
    .forIMg {
      width: 300px;
      .tit {
      }
      .imgmodal {
        display: flex;
        .headImg {
          width: 100px;
          height: 100px;
          border-radius: 4px;
          overflow: hidden;
          background: #ddd;
          margin: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          .imgset {
            // width: 100px;
            height: 100%;
          }
        }

        .miniheadImg {
          width: 200px;
          height: 100px;
          border-radius: 4px;
          overflow: hidden;
          background: #ddd;
          margin: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          .imgset {
            // width: 100px;
            height: 100%;
            width: 100%;
          }
        }
      }

      .uploadImg {
        // text-align: center;
        display: flex;
        position: relative;
        align-items: center;
        gap: 10px;

        button {
          min-width: 100px;
          max-width: 120px;
          height: 28px;
          border-radius: 14px;
          // opacity: 0.2;
          border: 1px solid #4881f0;
          outline: none;
          margin: 0 10px;
          vertical-align: middle;
          color: #4881f0;
          background: #fff;
          padding: 0 8px;
          font-size: 12px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex-shrink: 0;
          &:hover {
            // color:  #4881F0;
            // opacity: 1;
            background: #4881f0;
            color: #fff;
          }
        }
        .icon {
          vertical-align: middle;
          display: inline-block;
          width: 16px;
          height: 16px;
          cursor: pointer;
          flex-shrink: 0;
          
          &:hover {
            color: #4881f0;
          }
        }
      }
    }
    .forQuery {
      .tit {
      }
      .viewAsk {
        height: 38px;
        position: relative;
        .ant-select-selector {
          background: #dae6fc;
          border: none;
          box-shadow: none;
        }
        .select {
          width: 108px !important;
          vertical-align: middle;
          position: absolute;
          top: 2px;
          left: 2px;
          height: 28px;
          width: 88px;
          text-align: center;
        }
        .icon {
          vertical-align: middle;
          position: absolute;
          left: 120px;
          top: 5px;
        }
        .abc {
          .ant-select-selection-search-input {
            height: 36px !important;
          }
        }

        .ant-input {
          position: absolute;
          outline: none !important;
          border: none !important;
          box-shadow: none !important;
          top: 4px;
          left: 160px;
          // background: red;
          max-width: 200px;
        }
        .ant-select-selector {
          border-radius: 0;
        }
        .number_1 {
          // position:absolute;
          width: 40px;
          // background: blue;
          z-index: 1;
          height: 38px;
          line-height: 38px;
          text-align: center;
        }
      }

      .errshow {
        margin-top: 8px;
        height: 30px;
        line-height: 30px;
        background: #fff1f0;
        border: 1px solid #eb5b56;
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        padding: 0 10px;
        border-radius: 4px;
        .warnIcon {
          width: 14px;
          height: 14px;
          color: #eb5b56;
          margin-right: 4px;
        }
        .errtext {
          display: inline-flex;
          align-items: center;
        }
        .handle {
          color: #4881f0;
          vertical-align: middle;
          display: float;
          float: right;
          display: inline-flex;
          align-items: center;
          cursor: pointer;
        }
      }
    }
  }
  .ant-alert {
    margin-top: 8px;
    border-radius: 0;
  }
  .answer {
    background: red;
  }
  .err {
    color: #eb5b56;
  }
}
