import React, { Component, Fragment } from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import styles from './index.less'
import { Icon, Dropdown, MenuItem } from 'biz-components'
import { Input, Select, Upload, Alert, message, AutoComplete, Popover } from 'antd'
import { SettingOutlined } from '@ant-design/icons'
import ImgCrop from 'antd-img-crop'
import CardTitle from '../cardTitle'
import { Loader } from 'biz-components'
const { Option } = Select
import { recommendQues, recommendQuesMiniIn } from '@api/_home/recommendQues'
import { CHANGE_INFO } from '@redux/action-types'
import { searchAnswer, qaKewordAdd } from '@api/_home'
import modal, { close as closeModal } from '@src/app/utils/modal'
import Answer from './answerModal'
import 'antd/es/modal/style'
import 'antd/es/slider/style'
import { qaAdd } from '@api/_webApp'
import { isMini, parseQuery } from '@utils/tools'
import { getImageSize } from '@utils/image-size'
import { ApiBase } from '@utils/request'
@connect((state) => {
  let devUrl = '/media/'
  if (__DEV__) {
    devUrl = `${ApiBase}media/`
  }
  return {
    homeModule: state._home.homeModule,
    devUrl: devUrl,
    curLang: state.user.curLang,
    cropId: state.user.detail.corp_uuid,
    currentRobotId: state.robots.current,
  }
})
@extend({
  styles,
})
class HeadImg extends Component {
  // 基于模板获取当前内容
  getTargetContent = () => {
    const { homeModule } = this.props;
    console.log(homeModule,"homeModule");
    
    if (homeModule && homeModule.templateInfo) {
      if (homeModule.templateInfo.templateCode === 'agentOS') {
        // 确保agentosContent存在
        // 尽管它应该在index.jsx中由gethomeModule初始化
        return homeModule.templateInfo.agentosContent || { suggestion: '', image: '', suggestionExt: {} };
      }
      // Simple或其他模板默认使用conciseContent
      return homeModule.templateInfo.conciseContent || { suggestion: '', image: '', suggestionExt: {} };
    }
    // 如果homeModule或templateInfo不可用，返回空对象
    return { suggestion: '', image: '', suggestionExt: {} };
  };

  // 获取默认配置，simple是conciseContent，agentos是agentosContent
  getTargetContentType = () => {
    const { homeModule } = this.props;
    if (homeModule && homeModule.templateInfo && homeModule.templateInfo.templateCode === 'agentOS') {
      return 'agentosContent';
    }
    return 'conciseContent';
  };

  _getQuesSource = () => {
    const robotType = parseQuery().robotType; 
    if (robotType && (robotType.includes('mini') || robotType === 'bao_xiao_mi')) {
      return recommendQuesMiniIn;
    }
    return recommendQues;
  }
  
  quesSource = this._getQuesSource();

  state = {
    showerr: false,
    recmmendQuesList: this.quesSource.recommendQues,
    selectIndex: 0,
    selectValue: this.quesSource.funcConfig.length > 0 ? (this.quesSource.funcConfig[0].label || this.quesSource.funcConfig[0].key) : '',
    funcConf: this.quesSource.funcConfig,
    suggQuerys: this.quesSource.funcConfig.length > 0 && this.quesSource.recommendQues[this.quesSource.funcConfig[0]['key']] 
                  ? this.quesSource.recommendQues[this.quesSource.funcConfig[0]['key']] 
                  : [],
    maxRecLength: 8,
    getModalData: null,
    getTextData: '', 
  };

  getQueryVal = (k, search, uri) => {
    if (search == undefined) {
      search = location.search
    }
    var reg = new RegExp('(^|&)' + k + '=([^&]*)(&|$)')
    var s = (uri && '?' + uri.split('?')[1]) || search
    var r = s.slice(1).match(reg)
    if (r != null) {
      return decodeURIComponent(r[2])
    }
    return null
  }

  // 图片上传之前
  uploadImg = (e) => {
    let _this = this
    let img = e
    let reader = new FileReader()
    reader.readAsDataURL(img)
    reader.onload = function (e) {
      let image = new Image()
      image.onload = function () {
        let mycanvas = document.querySelector('#mybackCanvas')
        let ctx = mycanvas.getContext('2d')

        ctx.clearRect(0, 0, mycanvas.width, mycanvas.height)
        ctx.drawImage(image, 0, 0, mycanvas.width, mycanvas.height)
        let dataurl = mycanvas.toDataURL('image/png')
        function dataURLtoBlob(dataurl) {
          let arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n)
          while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
          }
          return new Blob([u8arr], { type: mime })
        }
        let image_type;
        const { templateCode } = _this.props.homeModule.templateInfo;
        const isMiniDevice = isMini(_this.robotType);
        if (templateCode === 'agentOS') { 
          // AgentOS模板：，mini设备使用miniagentosImg，否则使用agentosImg
          image_type = isMiniDevice ? 'miniagentosImg' : 'agentosImg';
        } else {
          // 其他模板即Simple模板：mini设备使用miniconciseImg，否则使用conciseImg
          image_type = isMiniDevice ? 'miniconciseImg' : 'conciseImg';
        }
        _this.props.selectImg(dataURLtoBlob(dataurl), image_type, '')
      }
      image.src = e.target.result
    }
    return true
  }

  componentDidMount() {
    const currentContent = this.getTargetContent();
    let initialTextData = '';
    if (currentContent && currentContent.suggestionExt && currentContent.suggestionExt.tip !== undefined) {
        initialTextData = currentContent.suggestionExt.tip;
    }
    
    this.quesSource = this._getQuesSource(); 
    this.robotType = parseQuery().robotType; 

    let suggestionExt = currentContent.suggestionExt;
    if (!suggestionExt || typeof suggestionExt !== 'object') {
        suggestionExt = { function: 'chat', label: 'chat1' };
    }

    let activeFnKeyOrLabel = suggestionExt.function === 'chat' ? suggestionExt.label : suggestionExt.function;
    
    const funcExists = this.quesSource.funcConfig.some(f => (f.key === activeFnKeyOrLabel || f.label === activeFnKeyOrLabel));
    let currentSuggQuerys = [];

    if (!funcExists && this.quesSource.funcConfig.length > 0) {
        const firstFunc = this.quesSource.funcConfig[0];
        activeFnKeyOrLabel = firstFunc.label || firstFunc.key;
    }
    
    const suggQueryKey = suggestionExt.function === 'chat' ? 'chat' : activeFnKeyOrLabel;
    currentSuggQuerys = this.quesSource.recommendQues[suggQueryKey] || [];

    this.setState({
      getTextData: initialTextData,
      funcConf: this.quesSource.funcConfig,
      recmmendQuesList: this.quesSource.recommendQues,
      selectValue: activeFnKeyOrLabel, 
      suggQuerys: currentSuggQuerys,
    });
  }

  componentDidUpdate(prevProps) {
    const currentContent = this.getTargetContent();
    const prevContentNode = prevProps.homeModule && prevProps.homeModule.templateInfo && 
                           (prevProps.homeModule.templateInfo.templateCode === 'agentOS' ? prevProps.homeModule.templateInfo.agentosContent : prevProps.homeModule.templateInfo.conciseContent);
    const currentContentNode = this.props.homeModule && this.props.homeModule.templateInfo &&
                              (this.props.homeModule.templateInfo.templateCode === 'agentOS' ? this.props.homeModule.templateInfo.agentosContent : this.props.homeModule.templateInfo.conciseContent);

    if (JSON.stringify(prevContentNode) !== JSON.stringify(currentContentNode)) {
      let newTextData = '';
      if (currentContent && currentContent.suggestionExt && currentContent.suggestionExt.tip !== undefined) {
        newTextData = currentContent.suggestionExt.tip;
      }
      this.setState({
          getTextData: newTextData,
        }
      );
    }

    if (this.props.currentRobotId !== prevProps.currentRobotId && this.props.homeModule) {
      this.quesSource = this._getQuesSource(); 
      this.robotType = parseQuery().robotType; 

      let suggestionExt = currentContent.suggestionExt;
      if (!suggestionExt || typeof suggestionExt !== 'object') {
          suggestionExt = { function: 'chat', label: 'chat1' };
      }
      
      let activeFnKeyOrLabel = suggestionExt.function === 'chat' ? suggestionExt.label : suggestionExt.function;
      const funcExists = this.quesSource.funcConfig.some(f => (f.key === activeFnKeyOrLabel || f.label === activeFnKeyOrLabel));
      let currentSuggQuerys = [];

      if (!funcExists && this.quesSource.funcConfig.length > 0) {
          const firstFunc = this.quesSource.funcConfig[0];
          activeFnKeyOrLabel = firstFunc.label || firstFunc.key;
      }
      
      const suggQueryKey = suggestionExt.function === 'chat' ? 'chat' : activeFnKeyOrLabel;
      currentSuggQuerys = this.quesSource.recommendQues[suggQueryKey] || [];
      
      this.setState({
        funcConf: this.quesSource.funcConfig,
        recmmendQuesList: this.quesSource.recommendQues,
        selectValue: activeFnKeyOrLabel,
        suggQuerys: currentSuggQuerys,
      });
    }
  }

  // 检查问题是否具备答案
  checkQuestionAnswer = (val) => {
    let _this = this
    let data = val != undefined ? val : this.state.getTextData
    // if(data.length>=0){ }
    let currentContent = { ...this.getTargetContent() }; 

    currentContent.suggestion = data
    if (!currentContent.suggestionExt) currentContent.suggestionExt = {};
    currentContent.suggestionExt.title = data
    currentContent.suggestionExt.tip = data

    let data1 = {
      question_list: JSON.stringify([data]),
    }
    if (data.length != 0) {
      searchAnswer(data1)
        .then((res) => {
          let answer = res.items[0]
          // console.log(111111234,!answer.existed)
          if (!answer.existed && this.state.getTextData.length > 0) {
            _this.setState({
              showerr: true,
            })
          } else {
            _this.setState({
              showerr: false,
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    } else {
      _this.setState({
        showerr: false,
      })
    }
  }

  // 删除头图img
  deleteHeadImg = () => {
    const { homeModule } = this.props
    let currentContent = { ...this.getTargetContent() }; // Clone to modify
    let contentType = this.getTargetContentType();
    let defaultImage = 'module_public/module_skill_home/conciseimage.png';

    if (homeModule.templateInfo.templateCode === 'agentOS') {
      defaultImage = ''; // agentOS默认图片为空
    } else { // 对于Simple模板
      if (this.isKTV) {
        defaultImage = 'module_public/module_skill_home/concisektv.png';
      }
      if (homeModule.templateInfo.scenes == 'KTV' && homeModule.templateInfo.templateCode == 'Simple') {
        defaultImage = 'module_public/module_skill_home/concisektv.png';
      }
      if (isMini(this.robotType) && homeModule.templateInfo.templateCode == 'Simple') {
        defaultImage = 'module_public/module_skill_home/bao_mini/miniconciseimage.png';
      }
    }
    currentContent.image = defaultImage;

    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: contentType,
        content: currentContent,
      },
    })
  }

  setQuestionAnswer = () => {
    let _this = this
    const { homeModule, cropId } = _this.props
    const { selectValue, getTextData, getModalData } = _this.state
    let type = 'query'
    let value = getTextData || this.getTargetContent().suggestionExt.tip;
    modal({
      title: __('customHome.modalMsg.set'),
      content: <Answer query={value} getText={this.changAnswer} fun={selectValue} />,
    })
      .then(() => {
        const { getModalData, selectValue: currentFunLabelOrKey } = _this.state;
        const actualFunctionKey = _this.state.funcConf.find(f => f.label === currentFunLabelOrKey || f.key === currentFunLabelOrKey)?.key || currentFunLabelOrKey;

        if (actualFunctionKey == 'web' || actualFunctionKey == 'open_app') {
          // 功能网址和app
          const formData = new FormData()
          let name = value
          let norm_word = name
          let similar_words = new Array(name)
          formData.append('name', name)
          formData.append('norm_word', norm_word)
          formData.append('similar_words', similar_words)
          if (actualFunctionKey == 'web') {
            // web
            formData.append('type', 0)
            formData.append('app_url', getModalData.webCom)
          } else {
            let param_json = JSON.stringify({
              class_name: getModalData.appLess,
              params: [
                {
                  key: getModalData.keyparamer,
                  value: getModalData.valueparamer,
                },
              ],
            })
            formData.append('app_url', getModalData.appName)
            formData.append('type', 1)
            formData.append('param_json', param_json)
          }
          qaAdd(formData)
            .then((res) => {
              message.success('添加成功')
              _this.setState({
                showerr: false,
              })
            })
            .catch((err) => {
              switch (err.code) {
                case 406:
                  message.error('名称已存在')
                  break
                case 407:
                  message.error('标准问法已存在')
                  break
                case 408:
                  message.error('AppURL已存在')
                  break
                case 409:
                  message.error('URL审核不通过')
                  break
                case 410:
                  message.error('轻应用模板ID无效')
                  break
                default:
                  message.error(err.msg)
              }
            })
        } else {
          // 提交问答
          const formData = new FormData()
          let data = {
            media_type: '0',
            media_info: '[]',
            query: JSON.stringify([value]),
            answer: JSON.stringify([getModalData.data]),
            enterprise_id: _this.props.cropId,
            group_id_list: '[]',
            group_id: '',
            intent: 'orion_baike',
            keyword: '[]',
          }
          formData.append('data', JSON.stringify([data]))
          formData.append('lang', _this.props.curLang.lang_code)
          qaKewordAdd(formData)
            .then((res) => {
              // console.log(1111111111444333,res);
              message.success('添加成功')
              _this.setState({
                showerr: false,
              })
            })
            .catch((err) => {
              // console.log(err)
              message.error(err.msg)
            })
        }
      })
      .catch((err) => {
        console.log(err)
      })
  }
  changAnswer = (data) => {
    this.setState({
      getModalData: data,
    })
  }

  render() {
    const { suggQuerys, selectValue, maxRecLength, getTextData, showerr, funcConf } = this.state
    const { devUrl, homeModule, headImgLoading } = this.props
    const templateCode = homeModule.templateInfo.templateCode;
    const currentContent = this.getTargetContent(); // 获取当前内容基于模板
    const contentType = this.getTargetContentType(); // 获取内容类型字符串用于分派

    const appear = {
      // aspect: `1200/1920`,
      grid: 'grid',
      modalTitle: __('customHome.btn.img_cut'),
      modalWidth: '650px',
      modalOk: __('customHome.btn.confirm'),
      modalCancel: __('customHome.btn.cancel2'),
    }

    const prefixSelector = (
      <div
        style={{
          width: '140px',
          // color:showerr&&'#EB5B56'||'',
        }}
      >
        <Select
          value={selectValue}
          className='select'
          onChange={(val) => {
            let newContent = { ...this.getTargetContent() }; 
            if(!newContent.suggestionExt || typeof newContent.suggestionExt !== 'object') newContent.suggestionExt = {}; 

            const selectedFuncDetail = this.state.funcConf.find(f => f.key === val || f.label === val);
            let actualFuncKeyForQues = '';
            let funcToStore = '';
            let labelToStore = '';

            if (selectedFuncDetail) {
              actualFuncKeyForQues = selectedFuncDetail.key;
              funcToStore = selectedFuncDetail.key;
              labelToStore = selectedFuncDetail.label || selectedFuncDetail.key;
            }
            
            this.setState({
              selectValue: val, 
              suggQuerys: this.state.recmmendQuesList[actualFuncKeyForQues] || [],
            });
            
            newContent.suggestionExt.function = funcToStore;
            newContent.suggestionExt.label = labelToStore;
            this.props.dispatch({
              type: 'CHANGE_INFO',
              data: { type: contentType, content: newContent },
            });
          }}
        >
          {funcConf.map((item, index) => {
            if ((item.name === 'Chat' || item.name === '聊天') && this.props.lang === 'arabic') {
              return null;
            }
            return (
              <Option key={index} value={item.label || item.key}> 
                {item.name}
              </Option>
            )
          })}
        </Select>
        <Icon
          icon='guide_voice'
          className='icon'
          style={{
            color: (showerr && '#EB5B56') || (!getTextData || getTextData === '' && '#c0c0c1') || '#3776ef',
          }}
        />
      </div>
    )

    let inputTimeout
    let robotType = this.getQueryVal('robotType')
    //robotType,modalStyle,idx,iconType
    let sizeRule = getImageSize(this.robotType, homeModule.templateInfo.templateCode, 0, 'big')

    return (
      <div className={this.props.className}>
        <CardTitle
          title={__('customHome.label.headpic')}
          tips='配置问法'
          // desc="触发语音配置"
        >
          {' '}
        </CardTitle>
        <div className='container'>
          <div className='forIMg'>
            <span className='tit'>①&nbsp;{__('customHome.label.Picture')}</span>
            <div className='imgmodal'>
              <div className={isMini(this.robotType) ? 'miniheadImg' : 'headImg'}>
                {!headImgLoading && currentContent.image && (
                  <img src={`${devUrl}${currentContent.image}`} className='imgset 3' />
                )}
                {headImgLoading && <Loader />}
              </div>
            </div>
            <div className='uploadImg '>
              <Popover
                content={
                  <div className='uploadTip'>
                    <div className='tips-container'>
                      <span className='tips-text'>{__('customHome.pic.imageFormat')}：JPG/PNG/JPEG</span>
                      <span className='tips-text'>
                        {__('customHome.pic.pictureSize')}：{sizeRule.width}px*{sizeRule.height}px
                      </span>
                    </div>
                  </div>
                }
              >
                <div>
                  <ImgCrop
                    {...appear}
                    // aspect={980/846}
                    aspect={sizeRule.width / sizeRule.height}
                    fillColor={'transparent'}
                  >
                    <Upload
                      customRequest={() => {}}
                      showUploadList={false}
                      beforeUpload={(e) => {
                        return this.uploadImg(e)
                      }}
                    >
                      <button>{__('BATCH_IMPORT_OR_EXPORT.UPLOAD_AGAIN')}</button>
                    </Upload>
                  </ImgCrop>
                </div>
              </Popover>

              <Icon icon='list_delete' className='icon' onClick={this.deleteHeadImg} />
            </div>
          </div>
          <div className='forQuery'>
            <span className='tit'>②&nbsp;{__('customHome.btn.TTS')}</span>
            <p
              className='viewAsk 6'
              // style={{borderColor:showerr&&'#EB5B56'||''}}
            >
              {prefixSelector}
              <AutoComplete
                style1={{ width: 200, position: 'absolute', right: '30px', zIndex: 10000, marginLeft: 65 }}
                // maxLength={maxRecLength}
                className='abc'
                placeholder=''
                onKeyUp={(e) => {
                  window.inputing = true
                  inputTimeout && clearTimeout(inputTimeout)
                  inputTimeout = setTimeout((e) => {
                    window.inputing = false
                  }, 600)
                }}
                onChange={(val) => {
                  window.inputing = true
                  this.setState({ getTextData: val }, () => {})
                  this.checkQuestionAnswer(val)
                }}
                // defaultValue={''}
                value={getTextData}
                // value={this.props.homeModule.templateInfo.conciseContent.suggestion}
                bordered={false}
                options={(() => {
                  let res = []
                  suggQuerys &&
                    suggQuerys.map((item) => {
                      res.push({ value: item })
                    })
                  return res
                })()}
                options1={[{ value: 'text 1' }, { value: 'text 2' }]}
              >
                <input
                  // value={getTextData}
                  type='text'
                  attr='auto_input'
                  onBlur={(e) => {
                    let data = this.state.getTextData
                    let newContent = { ...this.getTargetContent() }; 
                    if(!newContent.suggestionExt || typeof newContent.suggestionExt !== 'object') newContent.suggestionExt = {}; 

                    newContent.suggestion = data
                    newContent.suggestionExt.title = data
                    newContent.suggestionExt.tip = data
                    this.props.dispatch({
                      type: 'CHANGE_INFO',
                      data: { type: contentType, content: newContent },
                    })
                  }}
                  className='auto-child'
                  maxLength={maxRecLength}
                  style={{
                    border: 'none',
                    background: 'transparent',
                    boxShadow: 'none',
                    height: '100%',
                    outline: 'none',
                    width: '100%',
                  }}
                />
              </AutoComplete>
              <span className='number_1'>
                <a href='javascript:;'>{getTextData ? getTextData.length : 0}</a>/8
              </span>
            </p>
            {showerr && (
              <p className='errshow'>
                <span className='errtext'>
                  <Icon icon='icon_Warning' className='warnIcon' />"
                  {currentContent.suggestion}"{__('customHome.step.answers')}
                  {/*this.props&&this.props.lang== 'english'?__('customHome.step.answers')+"""+(homeModule&&homeModule.templateInfo.conciseContent.suggestion)+""":"""+(homeModule&&homeModule.templateInfo.conciseContent.suggestion)+"""+__('customHome.step.answers')*/}
                </span>
                <span className='handle' onClick={this.setQuestionAnswer}>
                  {__('customHome.modalMsg.set')}
                  <Icon icon='control_forward_clic_1' />
                </span>
              </p>
            )}
          </div>
        </div>
        <canvas style={{ display: 'none' }} id='mybackCanvas' width={sizeRule.width} height={sizeRule.height} />
      </div>
    )
  }
}

export default HeadImg
