import React, { Component, Fragment } from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import styles from './index.less'
import { Icon, Dropdown, MenuItem } from 'biz-components'
import { Input, Select, Upload, Alert } from 'antd'
import { SettingOutlined } from '@ant-design/icons'
import ImgCrop from 'antd-img-crop'
// import CardTitle from '../cardTitle';
import { Loader } from 'biz-components'
const { Option } = Select
import { CHANGE_INFO } from '@redux/action-types'
import { searchAnswer } from '@api/_home'
import modal, { close as closeModal } from '@src/app/utils/modal'

let righttip = (value) => {
  return (
    <div>
      <span>{value.title ? value.title.length : value.length}</span> <span>/8</span>{' '}
    </div>
  )
}
@connect((state) => {
  let devUrl = '/media/'
  if (__DEV__) {
    // console.log('页面：自定义首页', state._home)
    devUrl = 'http://test-jiedai.ainirobot.com/media/'
  }
  return {
    homeModule: state._home.homeModule,
    // originHomeModule: state._home.originHomeModule,
    // isModuleChange: state._home.isModuleChange,
    // currentRobotId: state.robots.current,
    // robots: state.robots.robots,
    // fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    // curLang: state.user.curLang,
    // user: state.user,
    // brand: state.brand || 'ktv',
  }
})
@extend({
  styles,
})
class Answer extends Component {
  state = {
    data: '',
  }

  componentDidUpdate(prevProps) {}

  changeData = (e) => {
    let text = e.target.value
    if (text.length <= 500) {
      this.setState({
        data: text,
      })
      this.props.getText(text)
    }
  }

  render() {
    return (
      <div className={this.props.className}>
        <div className='head'>
          <span className='qa'>{__('Q&A.Q&As.Q')}</span>
          <span className='tit'>{this.props.query}</span>
        </div>
        <div className='cont'>
          <span className='an'>{__('Q&A.Q&As.A')}</span>
          <textarea
            className='textArea'
            name=''
            id=''
            cols={60}
            rows='10'
            value={this.state.data}
            onChange={this.changeData}
          ></textarea>
          <span className='set'>{this.state.data.length}/500</span>
        </div>
        <div className='foot'>
          <Alert
            message={__('customHome.modalMsg.configureOrPictures')}
            type='warning'
            showIcon
            style={{ width: '320px' }}
          />
        </div>
      </div>
    )
  }
}

export default Answer
