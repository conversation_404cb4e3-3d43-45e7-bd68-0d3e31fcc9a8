@import '~base.less';

.component {
  // background: red;
  width: 600px;
  padding: 0 30px;
  .head {
    margin-bottom: 15px;
    .qa {
      display: inline-flex;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: rgba(55, 118, 239, 0.2);
      color: #3776ef;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
    }
    .tit {
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #4a4a4a;
      line-height: 22px;
    }
  }
  .cont {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    position: relative;
    .tit {
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #4a4a4a;
      line-height: 22px;
    }
    .an {
      display: inline-flex;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #3776ef;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
    }
    .textArea {
      outline: none;
      border-radius: 4px;
      border: 1px solid #ddd;
      box-shadow: none;
      padding-bottom: 20px;
      &:focus {
        border: 1px solid #ddd !important;
        outline: none !important;
      }
    }
    .set {
      display: block;
      width: 40px;
      position: absolute;
      right: 1rem;
      bottom: 1px;
      // width:calc(100% - 42px);
      background: #fff;
      text-align: right;
      border-radius: 0 0 4px 4px;
    }
  }
  .foot {
    padding-left: 38px;
  }
}
