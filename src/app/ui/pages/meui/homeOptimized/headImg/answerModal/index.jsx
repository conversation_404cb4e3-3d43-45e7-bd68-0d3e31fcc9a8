import React, { Component, Fragment } from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import styles from './index.less'
import { Icon, Dropdown, MenuItem } from 'biz-components'
import { Input, Select, Upload, Alert } from 'antd'
import { SettingOutlined } from '@ant-design/icons'
import ImgCrop from 'antd-img-crop'
// import CardTitle from '../cardTitle';
import { Loader } from 'biz-components'
const { Option } = Select
import { CHANGE_INFO } from '@redux/action-types'
import { searchAnswer } from '@api/_home'
import modal, { close as closeModal } from '@src/app/utils/modal'

let righttip = (value) => {
  return (
    <div>
      <span>{value.title ? value.title.length : value.length}</span> <span>/8</span>{' '}
    </div>
  )
}
@connect((state) => {
  let devUrl = '/media/'
  if (__DEV__) {
    // console.log('页面：自定义首页', state._home)
    devUrl = 'http://test-jiedai.ainirobot.com/media/'
  }
  return {
    homeModule: state._home.homeModule,
    // originHomeModule: state._home.originHomeModule,
    // isModuleChange: state._home.isModuleChange,
    // currentRobotId: state.robots.current,
    // robots: state.robots.robots,
    // fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    // curLang: state.user.curLang,
    // user: state.user,
    // brand: state.brand || 'ktv',
  }
})
@extend({
  styles,
})
class Answer extends Component {
  state = {
    data: '',
    webFunName: '',
    webCom: '',
    appName: '',
    appLess: '',
    keyparamer: '',
    valueparamer: '',
  }

  componentDidUpdate(prevProps) {}

  changeData = (e) => {
    let text = e.target.value
    if (text.length <= 500) {
      this.setState(
        {
          data: text,
        },
        () => {
          this.sendData()
        },
      )
    }
  }

  sendData = () => {
    const { data, webFunName, webCom, appName, appLess, keyparamer, valueparamer } = this.state
    let sendData = {
      data,
      webFunName,
      webCom,
      appName,
      appLess,
      keyparamer,
      valueparamer,
    }
    this.props.getText(sendData)
  }

  render() {
    const { fun } = this.props
    const { data, webFunName, webCom, appName, appLess, keyparamer, valueparamer } = this.state

    return (
      <div className={this.props.className}>
        <div className='head'>
          <span className='qa'>{__('Q&A.Q&As.Q')}</span>
          <span className='tit'>{this.props.query}</span>
        </div>
        {fun == 'web' && (
          <Fragment>
            <div className='cont'>
              <span className='an'>{__('Q&A.Q&As.A')}</span>
              <div>
                <span className='tit' style={{ display: 'block', marginBottom: '5px' }}>
                  {__('customHome.modalMsg.functionName')}
                </span>
                <Input
                  style={{ width: '400px' }}
                  placeholder={__('customHome.modalMsg.enterFunction')}
                  value={webFunName}
                  onChange={(e) =>
                    this.setState({ webFunName: e.target.value }, () => {
                      this.sendData()
                    })
                  }
                />

                <span className='tit' style={{ display: 'block', margin: '10px 0 5px', width: '100%' }}>
                  {__('customHome.modalMsg.functionUrl')}
                </span>
                <Input
                  placeholder={__('customHome.modalMsg.enterUrl')}
                  value={webCom}
                  onChange={(e) =>
                    this.setState({ webCom: e.target.value }, () => {
                      this.sendData()
                    })
                  }
                />
              </div>
            </div>
            <div className='foot'>
              <Alert
                message={__('customHome.modalMsg.pleaseCheck')}
                type='warning'
                showIcon
                style={{ width: '320px' }}
              />
            </div>
          </Fragment>
        )}
        {fun == 'open_app' && (
          <Fragment>
            <div className='cont'>
              <span className='an'>{__('Q&A.Q&As.A')}</span>
              <div>
                <span className='tit' style={{ display: 'block', marginBottom: '5px' }}>
                  {__('customHome.modalMsg.packageName')}
                </span>
                <Input
                  style={{ width: '400px' }}
                  placeholder={__('customHome.modalMsg.enterPackageName')}
                  value={appName}
                  onChange={(e) =>
                    this.setState({ appName: e.target.value }, () => {
                      this.sendData()
                    })
                  }
                />
                <span className='tit' style={{ display: 'block', margin: '10px 0 5px', width: '100%' }}>
                  {__('customHome.modalMsg.className')}
                </span>
                <Input
                  placeholder={__('customHome.modalMsg.enterClassName')}
                  value={appLess}
                  onChange={(e) =>
                    this.setState({ appLess: e.target.value }, () => {
                      this.sendData()
                    })
                  }
                />
                <span className='tit' style={{ display: 'block', margin: '10px 0 5px', width: '100%' }}>
                  Key
                </span>
                <Input
                  placeholder={__('customHome.modalMsg.enterParaKey')}
                  value={keyparamer}
                  onChange={(e) =>
                    this.setState({ keyparamer: e.target.value }, () => {
                      this.sendData()
                    })
                  }
                />
                <span className='tit' style={{ display: 'block', margin: '10px 0 5px', width: '100%' }}>
                  Value
                </span>
                <Input
                  placeholder={__('customHome.modalMsg.enterParaVal')}
                  value={valueparamer}
                  onChange={(e) =>
                    this.setState({ valueparamer: e.target.value }, () => {
                      this.sendData()
                    })
                  }
                />
              </div>
            </div>
            <div className='foot'>
              <Alert message={__('customHome.modalMsg.selectAPP')} type='warning' showIcon style={{ width: '320px' }} />
            </div>
          </Fragment>
        )}
        {fun != 'web' && fun != 'open_app' && (
          <Fragment>
            <div className='cont'>
              <span className='an'>{__('Q&A.Q&As.A')}</span>
              <textarea
                className='textArea'
                name=''
                id=''
                cols={60}
                rows='10'
                value={data}
                onChange={this.changeData}
              ></textarea>
              <span className='set'>{data.length}/500</span>
            </div>
            <div className='foot'>
              <Alert
                message={__('customHome.modalMsg.configureOrPictures')}
                type='warning'
                showIcon
                style={{ width: '320px' }}
              />
            </div>
          </Fragment>
        )}
      </div>
    )
  }
}

export default Answer
