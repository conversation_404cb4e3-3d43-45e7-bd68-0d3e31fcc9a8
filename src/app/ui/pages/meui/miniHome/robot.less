@import '~base.less';

.component {
  ul {
    height: 220px;
    overflow-y: scroll;
    margin: 0;

    li {
      list-style: none;
      height: 44px;
      text-align: left;
      display: flex;
      align-items: center;
      .label {
        margin-left: 10px;
        font-size: 18px;
        width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &.current {
        color: @color-theme;
      }
    }
    // li:nth-child(1){
    //     margin-left:-20px;
    //     .circle{
    //         border-radius: 100%;
    //     }
    // }
  }
}
