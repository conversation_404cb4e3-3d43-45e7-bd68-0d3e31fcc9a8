import React from 'react'
import classNames from 'classnames'
import { connect } from 'react-redux'
import { GET_EDIT_LIST } from '@redux/action-types'
import { throws } from 'assert'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页列表', state._home)
  return {
    // showSkills: state._home.homeModule?state._home.homeModule.skills:[],
    // templateInfo: state._home.homeModule?state._home.homeModule.templateInfo:[]
  }
})
//
class List extends React.Component {
  state = {
    lang: this.props.lang || 'chs',
    // lbg: this.props.showSkills[0].icon,
    // rbg: this.props.showSkills[1].icon
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.lang && nextProps.lang && nextProps.lang != this.props.lang) {
      this.setState({
        lang: nextProps.lang,
        // lbg: nextProps.showSkills[0].icon,
        // rbg: nextProps.showSkills[1].icon
      })
    }
  }

  getQueryVal = (k, search, uri) => {
    if (search == undefined) {
      search = location.search
    }
    var reg = new RegExp('(^|&)' + k + '=([^&]*)(&|$)')
    var s = (uri && '?' + uri.split('?')[1]) || search
    var r = s.slice(1).match(reg)
    if (r != null) {
      return decodeURIComponent(r[2])
    }
    return null
  }

  render() {
    var image_url = this.props.devUrl + 'media/'
    let robotType = this.getQueryVal('robotType') || 'bao_mini'

    return (
      <div className={this.props.className}>
        <div className={robotType == 'bao_mini' ? 'list_state mini_list_state' : 'list_state'}>
          {this.props.templateInfo && (
            <div className={this.props.currentIndex == -1 || this.props.fiveTimer ? 'tip_cover list_top' : 'list_top'}>
              {this.props.templateInfo && this.props.templateInfo.titleBarType == 'text' ? (
                <p
                  className={classNames({
                    list_title: true,
                    arabic_list_title: this.state.lang === 'arabic',
                  })}
                  onClick={() => {
                    if (this.props.checkJson()) {
                      this.props.changeCurrentIndex(-1)
                      this.props.changeContent('title')
                    }
                  }}
                >
                  <em className='title_icon'></em>
                  {this.props.templateInfo.titleBarContent}
                </p>
              ) : (
                <img
                  className='list_Icon'
                  src={image_url + this.props.templateInfo.titleBarContent}
                  onClick={() => {
                    if (this.props.checkJson()) {
                      this.props.changeCurrentIndex(-1)
                      this.props.changeContent('title')
                    }
                  }}
                />
              )}
            </div>
          )}
          {this.props.showSkills && this.props.showSkills.length > 0 && (
            <div className={robotType == 'bao_mini' ? 'mini-list mini_list_list' : ''}>
              <div className={this.props.fiveTimer ? 'tip_cover list_button' : 'list_button'}>
                {this.props.showSkills[0].display && (
                  <div className={this.props.currentIndex == 0 ? 'tip_cover' : ''}>
                    <p
                      attr={this.props.showSkills[0].icon}
                      style={{
                        backgroundImage: 'url(' + image_url + this.props.showSkills[0].icon + ')',
                      }}
                      onClick={() => {
                        if (this.props.checkJson()) {
                          this.props.changeCurrentIndex(0)
                          this.props.changeContent('question', 'list_icon1')
                          this.props.dispatch({
                            type: GET_EDIT_LIST,
                            data: {
                              index: 0,
                            },
                          })
                        }
                      }}
                    >
                      <span
                        style={{
                          textOverflow: 'ellipsis',
                          overflow: 'hidden',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {this.props.showSkills[0].title}
                      </span>
                      {!this.props.showSkills[0].tip && <span></span>}
                      {this.props.showSkills[0].tip && (
                        <span class='chsspan' title={this.props.showSkills[0].tip}>
                          {this.props.showSkills[0].tip}
                        </span>
                      )}
                    </p>
                  </div>
                )}
                {this.props.showSkills[1].display && (
                  <div className={this.props.currentIndex == 1 ? 'tip_cover' : ''}>
                    <p
                      style={{
                        backgroundImage: 'url(' + image_url + this.props.showSkills[1].icon + ')',
                      }}
                      onClick={() => {
                        if (this.props.checkJson()) {
                          this.props.changeCurrentIndex(1)
                          this.props.changeContent('question', 'list_icon1')
                          this.props.dispatch({
                            type: GET_EDIT_LIST,
                            data: {
                              index: 1,
                            },
                          })
                        }
                      }}
                    >
                      <span
                        style={{
                          textOverflow: 'ellipsis',
                          overflow: 'hidden',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {this.props.showSkills[1].title}
                      </span>

                      {!this.props.showSkills[1].tip && <span></span>}
                      {this.props.showSkills[1].tip && (
                        <span class='chsspan' title={this.props.showSkills[1].tip}>
                          {this.props.showSkills[1].tip}
                        </span>
                      )}
                    </p>
                  </div>
                )}
              </div>
              <div
                className={classNames({
                  list_content: true,
                  tip_cover: this.props.currentSort || this.props.fiveTimer,
                  arabic_list_content: this.state.lang === 'arabic',
                  mini_list_content: robotType == 'bao_mini',
                })}
              >
                {this.props.showSkills.slice(2).map((item, i) => {
                  if (item.display) {
                    return (
                      <p
                        className={this.props.currentIndex == i + 2 ? 'tip_cover' : ''}
                        key={i}
                        onClick={() => {
                          if (this.props.checkJson()) {
                            this.props.changeCurrentIndex(i + 2)
                            this.props.changeContent('question', 'list_icon2')
                            this.props.dispatch({
                              type: GET_EDIT_LIST,
                              data: {
                                index: i + 2,
                              },
                            })
                          }
                        }}
                      >
                        <img className='icon' src={image_url + item.icon} />
                        {this.state.lang == 'chs' && (
                          <span className='title'>
                            {item.title.length > 14 ? item.title.substr(0, 13) + '...' : item.title}
                          </span>
                        )}
                        {this.state.lang == 'chs' && (
                          <span className='info' style={{ WebkitBoxOrient: 'vertical' }}>
                            {item.tip}
                          </span>
                        )}
                        {(this.state.lang == 'english' || this.state.lang === 'arabic') && (
                          <span className='title'>
                            {item.title.length > 30 ? item.title.substr(0, 32) + '...' : item.title}
                          </span>
                        )}
                        {(this.state.lang == 'english' || this.state.lang === 'arabic') && (
                          <span className='info'>
                            {'“' + (item.tip.length > 30 ? item.tip.substr(0, 32) + '...' : item.tip) + '”'}
                          </span>
                        )}
                      </p>
                    )
                  }
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }
}
export default List
export { List }
