@import '~base.less';

.component {
  position: relative;
  height: 100vh;
  .custome-home-container {
    display: flex;
    height: 100%;
    .custome-home-preview {
      height: 100%;
      .custome-home-preview-fixed {
        height: 100%;
        position: fixed;
        top: 0;
        left: 30px;
      }
      &.horizontal,
      .horizontal {
        width: 700px;
      }
      &.vertical,
      .vertical {
        width: 400px;
      }
    }
    .custome-home-info {
      min-height: 100%;
      height: max-content;
      flex: 1;
      background: #eee;
    }
  }
}
