import React from 'react'
import { extend } from 'koot'

import styles from './index.module.less'
import BackImg from '../../home/<USER>'
import { RightOutlined, QuestionCircleOutlined, CheckCircleOutlined } from '@ant-design/icons'

// Component Class ============================================================

@extend({
  /*
    // 下例均为简易写法
    // 更详细的释义和高级写法，请查阅文档
    // https://koot.js.org/#/react

    connect: (state) => {
        return {}
    },

    // 修改页面 title 和 meta 标签
    // pageinfo 也可为 function
    pageinfo: {
        title: '页面标题',
        metas: [
            { description: '页面描述' }
        ]
    },
    */
  styles,
})
class ExpressionSetting extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      exp_set_nav_list_active: false,
    }
  }
  selectNavList = (e) => {
    console.log(e.target.getAttribute('data-index'))
  }
  render() {
    return (
      <div className={this.props.className}>
        <div className='exp_set_nav'>
          <div className='exp_set_nav_list' data-index='1'>
            当前模版
          </div>
          <div onClick={this.selectNavList} data-index='2' className='exp_set_nav_list exp_set_nav_list_active'>
            文案配置 <CheckCircleOutlined className='exp_set_nav_list-icon' />
          </div>
          <div className='exp_set_nav_list' data-index='3'>
            背景图片
          </div>
        </div>
        <div className='exp_set_con'>
          {/** 
                    <div className="exp_set_con_list">
                        <div  className="exp_set_con_list_t">
                        当前模版 <QuestionCircleOutlined className="exp_set_con_list_t_icon" />
                        </div>
                        <div className="exp_set_con_list_con">
=====
                        </div>
                    </div>
                    <div  className="exp_set_con_list">
                        <div className="exp_set_con_list_t">
                        文案配置<QuestionCircleOutlined className="exp_set_con_list_t_icon"  />
                        </div>
                        <div className="exp_set_con_list_con">
ddd
                        </div>
                    </div>
                    <div  className="exp_set_con_list">
                        <div className="exp_set_con_list_t">
                        背景图片<QuestionCircleOutlined className="exp_set_con_list_t_icon"  />
                        </div>
                        <div className="exp_set_con_list_con">
ddd
                        </div>
                    </div>*/}

          {/**<BackImg></BackImg>   */}
        </div>
      </div>
    )
  }
}

export default ExpressionSetting
