@import '~base.less';

.component {
  display: flex;
  justify-content: flex-start;
  .exp_set_nav {
    width: 130px;
    height: 100px;
    .exp_set_nav_list {
      width: 123px;
      height: 45px;
      background: #dce7fd;
      font-size: 14px;
      color: #4a4a4a;
      line-height: 45px;
      padding-left: 15px;
      cursor: pointer;
      .exp_set_nav_list-icon {
        width: 16px;
        height: 16px;
        color: #3776ef;
        //color: red;
        padding-left: 10px;
      }
    }
    .exp_set_nav_list_active {
      background: #fff;
    }
  }
  .exp_set_con {
    min-width: 500px;
    height: 100px;
    .exp_set_con_list {
      .exp_set_con_list_t {
        height: 28px;
        font-weight: 500;
        color: #4a4a4a;
        line-height: 28px;
        font-size: 16px !important;
        .exp_set_con_list_t_icon {
          padding-left: 5px;
        }
      }
      .exp_set_con_list_con {
        height: 200px;
      }
    }
  }
}
