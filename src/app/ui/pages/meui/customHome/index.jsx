import React from 'react'
import { extend } from 'koot'
import Preview from './preview'

import styles from './index.module.less'
import ExpressionSetting from './expressionSetting'
// Component Class ============================================================

@extend({
  /*
    // 下例均为简易写法
    // 更详细的释义和高级写法，请查阅文档
    // https://koot.js.org/#/react

    connect: (state) => {
        return {}
    },

    // 修改页面 title 和 meta 标签
    // pageinfo 也可为 function
    pageinfo: {
        title: '页面标题',
        metas: [
            { description: '页面描述' }
        ]
    },

    // 同构数据
    data: (state, renderProps, dispatch) => {
        return dispatch({
            type: "ACTION",
            payload: {}
        });
    },

    // 控制组件的 SSR 行为
    // 仅作用于 SSR 项目
    ssr: true,
    */
  styles,
})
class CustomHome extends React.Component {
  render() {
    return (
      <div className={this.props.className}>
        <div className='custome-home-container'>
          {/* vertical 竖屏 horizontal 横屏 */}
          <div className='custome-home-preview vertical'>
            <div className='custome-home-preview-fixed vertical'>
              <Preview />
            </div>
          </div>
          <div className='custome-home-info'>
            <ExpressionSetting></ExpressionSetting>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
            <div>配置信息</div>
          </div>
        </div>
      </div>
    )
  }
}

export default CustomHome
