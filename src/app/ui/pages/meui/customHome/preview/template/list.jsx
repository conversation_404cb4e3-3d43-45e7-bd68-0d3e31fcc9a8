import React from 'react'
import classNames from 'classnames'
import { connect } from 'react-redux'
import { GET_EDIT_LIST } from '@redux/action-types'
import { inputMaxCnA, inputMaxEnA } from '@utils/tools'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页列表', state._home)
  return {
    showSkills: state._home.homeModule.skills,
    templateInfo: state._home.homeModule.templateInfo,
  }
})
//
class List extends React.Component {
  state = {
    lang: this.props.lang || 'chs',
    lbg: this.props.showSkills[0].icon,
    rbg: this.props.showSkills[1].icon,
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.lang && nextProps.lang && nextProps.lang != this.props.lang) {
      this.setState({
        lang: nextProps.lang,
        lbg: nextProps.showSkills[0].icon,
        rbg: nextProps.showSkills[1].icon,
      })
    }
  }
  render() {
    var image_url = this.props.devUrl + 'media/'
    // if(this.props.templateInfo.background=="module_public/module_skill_home/home1.png"){
    // 	var styles={}
    // }else{
    // 	var styles={"backgroundImage":"url("+image_url+this.props.templateInfo.background+")"};
    // }
    return (
      <div className={this.props.className}>
        <div className='list_state'>
          <div className={this.props.currentIndex == -1 || this.props.fiveTimer ? 'tip_cover list_top' : 'list_top'}>
            {this.props.templateInfo.titleBarType == 'text' ? (
              <p
                className={classNames({
                  list_title: true,
                })}
                onClick={() => {
                  if (this.props.checkJson()) {
                    this.props.changeCurrentIndex(-1)
                    this.props.changeContent('title')
                  }
                }}
              >
                <em className='title_icon'></em>
                {this.state.lang === 'zh_CN' &&
                  (this.props.templateInfo.titleBarContent.length > 12
                    ? this.props.templateInfo.titleBarContent.substr(0, inputMaxCnA) + '...'
                    : this.props.templateInfo.titleBarContent)}
                {this.state.lang !== 'zh_CN' &&
                  (this.props.templateInfo.titleBarContent.length > 26
                    ? this.props.templateInfo.titleBarContent.substr(0, inputMaxEnA) + '...'
                    : this.props.templateInfo.titleBarContent)}
              </p>
            ) : (
              <img
                className='list_Icon'
                src={image_url + this.props.templateInfo.titleBarContent}
                onClick={() => {
                  if (this.props.checkJson()) {
                    this.props.changeCurrentIndex(-1)
                    this.props.changeContent('title')
                  }
                }}
              />
            )}
          </div>
          <div className={this.props.fiveTimer ? 'tip_cover list_button' : 'list_button'}>
            {this.props.showSkills[0].display && (
              <div className={this.props.currentIndex == 0 ? 'tip_cover' : ''}>
                <p
                  attr={this.props.showSkills[0].icon}
                  style={{ backgroundImage: 'url(' + image_url + this.props.showSkills[0].icon + ')' }}
                  onClick={() => {
                    if (this.props.checkJson()) {
                      this.props.changeCurrentIndex(0)
                      this.props.changeContent('question', 'list_icon1')
                      this.props.dispatch({
                        type: GET_EDIT_LIST,
                        data: {
                          index: 0,
                        },
                      })
                    }
                  }}
                >
                  {this.state.lang === 'zh_CN' && (
                    <span>
                      {this.props.showSkills[0].title.length > 6
                        ? this.props.showSkills[0].title.substr(0, 5) + '...'
                        : this.props.showSkills[0].title}
                    </span>
                  )}
                  {this.state.lang !== 'zh_CN' && (
                    <span>
                      {this.props.showSkills[0].title.length > 20
                        ? this.props.showSkills[0].title.substr(0, 20) + '...'
                        : this.props.showSkills[0].title}
                    </span>
                  )}
                  {!this.props.showSkills[0].tip && <span></span>}
                  {this.state.lang === 'zh_CN' && this.props.showSkills[0].tip && (
                    <span class='chsspan' title={this.props.showSkills[0].tip}>
                      {'“' +
                        (this.props.showSkills[0].tip && this.props.showSkills[0].tip.length > 6
                          ? this.props.showSkills[0].tip.substr(0, 5) + '...'
                          : this.props.showSkills[0].tip || '') +
                        '”'}
                    </span>
                  )}
                  {this.state.lang !== 'zh_CN' && this.props.showSkills[0].tip && (
                    <span class='engspan' title={this.props.showSkills[0].tip}>
                      {'“' +
                        (this.props.showSkills[0].tip && this.props.showSkills[0].tip.length > 17
                          ? this.props.showSkills[0].tip.substr(0, 17) + '...'
                          : this.props.showSkills[0].tip || '') +
                        '”'}
                    </span>
                  )}
                </p>
              </div>
            )}
            {this.props.showSkills[1].display && (
              <div className={this.props.currentIndex == 1 ? 'tip_cover' : ''}>
                <p
                  style={{ backgroundImage: 'url(' + image_url + this.props.showSkills[1].icon + ')' }}
                  onClick={() => {
                    if (this.props.checkJson()) {
                      this.props.changeCurrentIndex(1)
                      this.props.changeContent('question', 'list_icon1')
                      this.props.dispatch({
                        type: GET_EDIT_LIST,
                        data: {
                          index: 1,
                        },
                      })
                    }
                  }}
                >
                  {this.state.lang === 'zh_CN' && (
                    <span>
                      {this.props.showSkills[1].title.length > 6
                        ? this.props.showSkills[1].title.substr(0, 5) + '...'
                        : this.props.showSkills[1].title}
                    </span>
                  )}
                  {this.state.lang !== 'zh_CN' && (
                    <span>
                      {this.props.showSkills[1].title.length > 20
                        ? this.props.showSkills[1].title.substr(0, 20) + '...'
                        : this.props.showSkills[1].title}
                    </span>
                  )}

                  {!this.props.showSkills[1].tip && <span></span>}
                  {this.state.lang === 'zh_CN' && this.props.showSkills[1].tip && (
                    <span class='chsspan' title={this.props.showSkills[1].tip}>
                      {'“' +
                        (this.props.showSkills[1].tip && this.props.showSkills[1].tip.length > 6
                          ? this.props.showSkills[1].tip.substr(0, 5) + '...'
                          : this.props.showSkills[1].tip || '') +
                        '”'}
                    </span>
                  )}
                  {this.state.lang !== 'zh_CN' && this.props.showSkills[1].tip && (
                    <span class='engspan' title={this.props.showSkills[1].tip}>
                      {'“' +
                        (this.props.showSkills[1].tip && this.props.showSkills[1].tip.length > 17
                          ? this.props.showSkills[1].tip.substr(0, 17) + '...'
                          : this.props.showSkills[1].tip || '') +
                        '”'}
                    </span>
                  )}
                </p>
              </div>
            )}
          </div>
          {/* <div className={this.props.currentSort || this.props.fiveTimer ? "list_content tip_cover":"list_content"}> */}
          <div
            className={classNames({
              list_content: true,
              tip_cover: this.props.currentSort || this.props.fiveTimer,
            })}
          >
            {this.props.showSkills.slice(2).map((item, i) => {
              if (item.display) {
                return (
                  <p
                    className={this.props.currentIndex == i + 2 ? 'tip_cover' : ''}
                    key={i}
                    onClick={() => {
                      if (this.props.checkJson()) {
                        this.props.changeCurrentIndex(i + 2)
                        this.props.changeContent('question', 'list_icon2')
                        this.props.dispatch({
                          type: GET_EDIT_LIST,
                          data: {
                            index: i + 2,
                          },
                        })
                      }
                    }}
                  >
                    <img className='icon' src={image_url + item.icon} />
                    {this.state.lang === 'zh_CN' && (
                      <span className='title'>
                        {item.title.length > 14 ? item.title.substr(0, 13) + '...' : item.title}
                      </span>
                    )}
                    {this.state.lang === 'zh_CN' && (
                      <span className='info'>
                        {'“' +
                          (item.tip !== undefined && item.tip.length > 14 ? item.tip.substr(0, 13) + '...' : item.tip) +
                          '”'}
                      </span>
                    )}
                    {this.state.lang !== 'zh_CN' && (
                      <span className='title'>
                        {item.title.length > 30 ? item.title.substr(0, 32) + '...' : item.title}
                      </span>
                    )}
                    {this.state.lang !== 'zh_CN' && (
                      <span className='info'>
                        {'“' + (item.tip.length > 30 ? item.tip.substr(0, 32) + '...' : item.tip) + '”'}
                      </span>
                    )}
                  </p>
                )
              }
            })}
          </div>
        </div>
      </div>
    )
  }
}
export default List
export { List }
