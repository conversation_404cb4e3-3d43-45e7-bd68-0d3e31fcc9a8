import React from 'react'
import { connect } from 'react-redux'
import { GET_EDIT_LIST } from '@redux/action-types'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页列表', state._home.homeModule.skills)
  return {
    homeModule: state._home.homeModule.skills,
    templateInfo: state._home.homeModule.templateInfo,
  }
})
class AI extends React.Component {
  render() {
    var list = []
    var image_url = this.props.devUrl + 'media/'
    this.props.homeModule.map((item, index) => {
      if (item.display) {
        let styles_arr = ['AI_info']
        if (index % 2 == 0) {
          styles_arr.push('AI_big')
        } else {
          styles_arr.push('AI_small')
        }
        if (this.props.currentIndex == index || this.props.fiveTimer) {
          styles_arr.push('tip_cover')
        }
        let styles_str = styles_arr.join(' ')
        list.push(
          <p
            key={index}
            className={styles_str}
            onClick={() => {
              if (this.props.checkJson()) {
                this.props.changeCurrentIndex(index)
                this.props.changeContent('question', 'Technical')
                this.props.dispatch({
                  type: GET_EDIT_LIST,
                  data: {
                    index: index,
                  },
                })
              }
            }}
          >
            {item.tip}
          </p>,
        )
      }
    })
    return (
      <div className={this.props.className}>
        <div className='AI_state'>
          <p className='AI_title'>{__('customHome.modalMsg.sayToMe')}:</p>
          <div className={this.props.currentSort ? 'tip_cover' : ''}>{list}</div>
        </div>
      </div>
    )
  }
}
export default AI
export { AI }
