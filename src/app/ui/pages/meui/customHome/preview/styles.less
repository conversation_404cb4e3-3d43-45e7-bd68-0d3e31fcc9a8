@import '~base.less';

.component {
  background: #f6f9f9;
  position: relative;
  overflow: hidden;
  .ant-upload-select-picture-card {
    display: block !important;
    opacity: 0;
    width: 2.85rem;
    height: 2.85rem;
  }
  .ant-upload-list-picture-card-container {
    display: none;
  }
  input::-webkit-input-placeholder,
  textarea::-webkit-input-placeholder {
    color: #ccd2d6 !important;
    font-size: 12px;
  }

  input {
    -webkit-appearance: none;
  }

  button {
    border: none;
    outline: none;
    -webkit-highlight: none;
  }

  p {
    margin: 0;
    padding: 0;
  }

  ul,
  li {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  .tip_cover {
    background-color: rgba(85, 195, 251, 0.5);
    border-radius: 8px;
  }

  .have_cover {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    // margin-left:-4px;
  }

  .dib {
    display: inline-block;
  }
  .dbp {
    display: none;
  }

  .tip_empty {
    background: none;
    border-radius: 0px;
  }

  .loading {
    position: fixed;
    z-index: 999999;
    left: 0rem;
    width: 100%;
    pointer-events: all;
    background: rgba(248, 250, 250, 0.9);
    height: 100%;
    top: 0px;

    .loading_modal {
      position: relative;
      top: 50%;
      transform: translate(0%, -50%);
      text-align: center;
      margin: 0 auto;
      width: 257px;
      height: 126px;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.21);
      background-color: #ffffff;
      border-radius: 4px;
      &:lang(en) {
        width: 330px;
      }

      img {
        width: 60px;
        height: 60px;
        margin-top: 18px;
      }

      span {
        color: #979797;
        font-size: 16px;
        margin-top: 13px;
        display: block;
        white-space: nowrap;
      }
    }
  }

  .click_background {
    background: #3776ef !important;
    color: #fff !important;
  }
  .combox {
    //     display: flex;
    // -webkit-align-items: center;
    // align-items: center;
    // -webkit-justify-content: space-between;
    // justify-content: space-between;
    height: 3rem;
    background: #fff;
    // margin-bottom: .5rem;
    padding: 1rem;
    position: relative;
  }
  .btnBox {
    position: absolute;
    right: 0;
    right: 32px;
    top: 0;
  }

  .robot-list {
    float: right;
    // margin-top: -6px;
    border: 1px solid #3776ef;
    border-radius: 25px;
    white-space: nowrap;
    padding: 0px 17px;
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-top: 8px;

    .icon {
      width: 14px;
      height: 14px;
    }
  }

  .progress_modal {
    .progress_title {
      padding: 38px 30px;
    }
  }

  .home-title-bar {
    float: right;
    margin-top: 12px;

    .progress {
      font-size: 16px;
      color: #3776ef;
      letter-spacing: 0;
      padding: 8px 17px;
      display: inline-block;
      margin-right: 40px;
      float: right;
    }

    .warning {
      display: inline-block;
      font-size: 14px;
      line-height: 14px;
      color: #ffffff;
      padding: 10px 14px;
      background: #ff713c;
      border-radius: 15px;
      margin-right: 40px;
      float: right;
      margin-top: 5px;
    }

    .success_setting {
      border-radius: 25px;
      font-size: 18px;
      letter-spacing: 0;
      line-height: 24px;
      padding: 6px 17px;
      cursor: pointer;
      float: right;
      box-sizing: border-box;
      color: #ffffff;
      background: #3776ef;
      // display: none;

      &:hover {
        margin-top: -3px;
      }

      &:lang(en) {
        line-height: initial;
      }
    }

    .cancel_setting {
      margin-left: 40px;
      background: #fff;
      color: #3776ef;
      border: 1px solid #3776ef;
    }

    .recover_setting {
      background: #ffffff;
      border: 1px solid #3776ef;
      color: #3776ef;
      margin: 0 40px;
    }
  }

  .content {
    display: flex;
    height: 617px;
    position: relative;
    overflow: hidden;

    .preview {
      width: 100%;
      .previewCon {
        width: 100%;
        overflow-y: scroll;
        overflow-x: hidden;
        -ms-overflow-style: none; //IE 10+
        overflow: -moz-scrollbars-none; //Firefox
        background-size: 100%;
        background-repeat: no-repeat;

        .previewCon_height {
          // height: 600px;
          // background: url(@assets/png/meui/simpleview.png) no-repeat;
          // background-size: cover;
        }

        .startSetting_tips {
          background: rgba(0, 0, 0, 0.6);
          position: absolute;
          height: 600px;
          width: 100%;
          z-index: 20;

          .startSetting_modal {
            width: 260px;
            height: 230px;
            background-color: #ffffff;
            border-radius: 4px;
            position: relative;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            &:lang(en) {
              height: 250px;
            }

            .prompt_icon {
              width: 64.8px;
              height: 64.8px;
              color: #ff713c;
              margin-top: 41px;
              margin-bottom: 14px;
              margin-left: 97px;
              vertical-align: middle;
            }

            .title {
              font-size: 14px;
              color: #555d61;
              text-align: center;
              margin: 0 auto;
              margin-bottom: 14px;

              &:lang(en) {
                margin: 0 26px 24px 26px;
              }
            }

            .button {
              padding-top: 16px;
              border-top: 1px solid #e1e7ea;
              margin: 0 auto;
              text-align: center;

              span {
                display: inline-block;
                width: 100px;
                height: 32px;
                font-size: 14px;
                color: #ffffff;
                background: #3776ef;
                border-top: 1px solid #e1e7ea;
                text-align: center;
                border-radius: 16px;
                margin: 0 auto;
                line-height: 32px;
                cursor: pointer;

                &:hover {
                  margin-top: -3px;
                }
              }
            }
          }
          .lowOpkVersion {
            height: 13.5rem;
          }
          .templateNeedUpdate {
            height: 13.5rem;
          }
        }

        .query_bg {
          width: 100%;
          height: 100%;
          vertical-align: top;
        }

        .faceBackground {
          width: 375px;
          height: 600px;
          background-color: #000;
          background-size: cover;
          position: relative;

          .bubble1 {
            position: absolute;
            top: 61%;
            height: auto;
          }

          .bubble1m {
            position: absolute;
            height: auto;
            top: 74%;
          }

          .bubble1m_one {
            position: absolute;
            height: auto;
            top: 74%;
            width: 344px;
          }

          .bubble1m_two {
            position: absolute;
            height: auto;
            top: 74%;
            width: 344px;
          }

          .bubble1_one {
            position: absolute;
            height: auto;

            left: 48%;
            top: 62%;
            width: 344px;
          }

          .bubble1_two {
            position: absolute;
            height: auto;
            top: 63%;
            width: 344px;
            left: 48%;
          }

          .bubbleText1 {
            position: absolute;
            width: 13.75rem;
            font-size: 30px;
            font-weight: bolder;
            line-height: 30px;
            color: #fff;
            text-align: left;
            padding-left: 30px;
            padding-right: 30px;
            line-height: 2rem;
            margin-left: 50%;
            transform: translateX(-50%);
            top: 48%;
          }

          .bubbleText1_one {
            position: absolute;
            width: 13.75rem;
            font-size: 40px;
            font-weight: 550;
            // font-weight: bolder;
            line-height: 1.1;
            color: #fff;
            text-align: center;

            // line-height: 2rem;
            margin-left: 50%;
            transform: translateX(-50%);
            top: 53%;
            word-wrap: break-word;
          }

          .bubbleText1_two {
            position: absolute;
            width: 13.75rem;
            index: '0';
            // font-weight: bolder;
            font-weight: 550;
            line-height: 30px;
            color: #fff;
            text-align: left;

            line-height: 1.3;
            margin-left: 54%;
            transform: translateX(-50%);
            top: 48%;
            top: 50%;
            font-size: 40px;
            word-wrap: break-word;
          }

          .bubbleText2 {
            position: absolute;
            width: 13.75rem;
            font-size: 1.5rem;
            font-weight: bolder;
            line-height: 1.5rem;
            color: #fff;
            text-align: right;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
            line-height: 2rem;
            margin-left: 50%;
            transform: translateX(-50%);
            top: 74%;
          }

          .bubbleText2_one {
            position: absolute;
            width: 13.75rem;
            font-size: 40px;
            // font-weight: bolder;
            line-height: 1.3;
            color: #fff;
            text-align: center;
            // padding-left: 1.5rem;
            // padding-right: 1.5rem;
            // line-height: 2rem;
            margin-left: 50%;
            transform: translateX(-50%);
            top: 82%;
            font-weight: 550;
          }

          .bubbleText2_two {
            position: absolute;
            width: 13.75rem;
            font-size: 40px;
            // font-weight: bolder;
            line-height: 1.3;
            color: #fff;
            text-align: left;

            margin-left: 57%;
            transform: translateX(-50%);
            top: 74%;
            top: 76%;
            font-weight: 550;
          }

          .bubbleText1m {
            position: absolute;
            width: 13.75rem;
            font-size: 41px;
            top: 60%;
            line-height: 1.2;

            font-weight: bolder;

            color: #fff;
            text-align: left;
            padding-left: 30px;
            padding-right: 30px;
            margin-left: 50%;
            transform: translateX(-50%);
            font-weight: 550;
          }

          .bubbleText1m_one {
            position: absolute;
            width: 13.75rem;
            font-size: 44px;
            line-height: 1;

            top: 65%;
            left: 50%;
            color: #fff;
            text-align: center;

            transform: translateX(-50%);
            font-weight: 550;
          }

          .bubbleText1m_two {
            position: absolute;
            width: 13.75rem;
            font-size: 30px;

            color: #fff;
            text-align: left;
            // padding-left: 30px;
            // padding-right: 30px;

            margin-left: 55%;
            transform: translateX(-50%);
            font-size: 40px;

            line-height: 1.2;
            font-weight: 550;

            top: 61%;
          }

          .bubble2 {
            position: absolute;
            top: 86%;
            color: #fff;
            text-align: center;
            height: auto;
          }

          .bubble2_one {
            position: absolute;
            top: 91%;
            color: #fff;
            text-align: center;
            height: auto;
            width: 344px;
            left: 52%;
          }

          .bubble2_two {
            position: absolute;

            color: #fff;
            text-align: center;
            height: auto;
            width: 344px;
            top: 88%;
            top: 90%;
            left: 52%;
          }

          .bubble2m {
            position: absolute;
            top: 86%;
            color: #fff;
            text-align: center;
            height: auto;
          }

          .bubble2m_one {
            position: absolute;
            top: 86%;
            color: #fff;
            text-align: center;
            height: auto;
          }

          .bubble2m_two {
            position: absolute;
            top: 86%;
            color: #fff;
            text-align: center;
            height: auto;
          }

          .bubble1Big {
            position: absolute;
            top: 60%;
          }

          .bubble2Big {
            position: absolute;
            top: 86%;
          }
          .cardContent {
            position: absolute;
            height: auto;
            top: 43%;
            width: 17.2rem;
            left: 50%;
            transform: translate(-50%, -50%);
            .title {
              width: 339px;
              // min-height: 54px;
              // padding-top: 20px;
              // background: url(@assets/png/meui/card_title.png) no-repeat;
              // background-size: contain;
              // margin: 0 auto;
              color: #fff;
              font-size: 25px;
              padding: 0 20px 0 18px;
              word-break: break-word;
              position: absolute;
              top: 22px;
              left: 0;
              // transform: translatex(-50%);
              z-index: 2;
              // border-top-left-radius: 19px;
              // border-top-right-radius: 19px;
              // background: linear-gradient(to right,#1269FF,#118AFF,#00E7FF);
              // background: linear-gradient(to left,rgba(18, 139, 255, 0.59),rgba(17, 138, 255, 0.6),rgba(0, 64, 255, 0),rgba(0, 231, 255, 0.4),rgba(0, 182, 255, 0.3),rgba(0, 79, 255, 0));
              .icon {
                margin-right: 12px;
                vertical-align: middle;
              }
            }
            .txt {
              width: 339px;
              // height: 250px;
              font-size: 34px;
              line-height: 47px;
              color: #fff;
              padding: 0 19px 20px 18px;
              word-break: break-word;
              position: absolute;
              top: 82px;
              left: 0;

              z-index: 2;
              // transform: translatex(-50%);
              // margin: 0 auto;
              // background: url(@assets/png/meui/card_bg.png) no-repeat;

              // background: linear-gradient(to right,#1269FF,#118AFF,#00E7FF,#00B6FF);
            }
            .title_one,
            .title_two {
              width: 339px;
              height: 82px !important;
              position: absolute;
              height: auto;
              top: 0;
              left: 50%;
              transform: translatex(-50%);
            }
            .txt_one,
            .txt_two {
              width: 339px;
              height: 251px;
              position: absolute;
              height: auto;
              top: 82px;
              left: 50%;
              transform: translatex(-50%);
            }
          }

          .compare1 {
            display: none;
            position: absolute;
            width: 100%;

            height: 100%;
            opacity: 0.5;
            top: 0;
            background: url(@assets/png/meui/oneline.png) no-repeat;
            background-size: contain;
          }

          .compare2 {
            display: none;
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.5;
            top: 0;
            background: url(@assets/png/meui/twoline.png) no-repeat;
            background-size: contain;
          }

          .one_m {
            display: none;
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.5;
            top: 0;
            background: url(@assets/png/meui/one_m.jpg) no-repeat;
            background-size: contain;
          }

          .one_m_two {
            display: none;
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.5;
            top: 0;
            background: url(@assets/png/meui/one_m_two.jpg) no-repeat;
            background-size: contain;
          }

          .one_m_xp {
            display: none;
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.5;
            top: 0;
            background: url(@assets/png/meui/xiaoping.png) no-repeat;
            background-size: contain;
          }

          .one_m_xp2 {
            display: none;
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.5;
            top: 0;
            background: url(@assets/png/meui/xiaoping2.jpg) no-repeat;
            background-size: contain;
          }

          img {
            width: 375px;
            height: 183px;
            position: relative;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin-top: -20px;
          }

          .moveup {
            top: 25%;
            top: 29%;
          }
        }

        .shadow {
          width: 375px;
          height: 80px;
          position: absolute;
          bottom: 10px;
        }

        .bottom_queries {
          margin-bottom: 0;
          margin-left: 10px;
          margin-right: 27px;
          padding: 6px;
          position: relative;
          margin-top: -60px;
          z-index: 10;

          .bottom_queries_con {
            height: 60px;
            cursor: pointer;

            // background:#fff;
            img {
              width: 74px;
              display: block;
              position: absolute;
              left: 0px;
              bottom: 10px;
            }

            span {
              width: 100%;
              height: 40px;
              line-height: 40px;
              display: inline-block;
              font-size: 15.62px;
              color: #fff;
              letter-spacing: 0.39px;
              vertical-align: middle;
              text-align: left;
              padding-left: 24px;
              white-space: nowrap;
              background: url(@assets/png/meui/recommend_bubble_img.png) no-repeat;
              background-size: contain;
              position: absolute;
              bottom: 24px;
              left: 56px;
            }
          }
        }
      }

      .previewCon::-webkit-scrollbar,
      .standard-con::-webkit-scrollbar {
        display: none; //Safari and Chrome
      }

      .standard_state {
        padding: 10px 40px;

        .standard_eyes {
          width: 131.3px;
          height: 58.4px;
          margin-left: -20px;
          margin-bottom: 15px;
        }
        .simple_standard_eyes {
          width: 177px;
          height: 85px;
          margin-left: -20px;
          margin-bottom: 15px;
        }

        .standard_title {
          font-size: 25px;
          color: #ffffff;
          line-height: 32.5px;
          cursor: pointer;
          text-align: left;
          white-space: nowrap;
          padding-top: 5px;
          padding-bottom: 5px;
          margin-top: -5px;
          margin-bottom: 29px;
          margin-left: -10px;
          white-space: nowrap;

          .title_icon {
            vertical-align: middle;
            display: inline-block;
            width: 17.5px;
            height: 24px;
            margin-right: 3px;
            background-image: url('@assets/png/meui/home_mic_img.png');
            background-size: 17.5px 24px;
            background-repeat: no-repeat;
            margin-top: -4px;
          }
        }

        .simple_standard_title {
          font-size: 25px;
          color: #ffffff;
          line-height: 32.5px;
          cursor: pointer;
          text-align: left;
          white-space: nowrap;
          padding-top: 5px;
          padding-bottom: 5px;
          margin-top: -5px;
          margin-bottom: 29px;
          // margin-left: -10px;
          white-space: nowrap;

          .title_icon {
            vertical-align: middle;
            display: inline-block;
            width: 17.5px;
            height: 24px;
            margin-right: 3px;
            background-image: url('@assets/png/meui/home_mic_img.png');
            background-size: 17.5px 24px;
            background-repeat: no-repeat;
            margin-top: -4px;
          }
        }
        .tac {
          text-align: center;
        }
        .simple_title {
          font-size: 20px;
          font-weight: 600;
          color: #ffffff;
          line-height: 32.5px;
          cursor: pointer;
          text-align: left;
          white-space: nowrap;
          padding-top: 5px;
          padding-bottom: 5px;
          margin-top: -5px;
          // margin-bottom: 29px;
          // margin-left: -10px;
          white-space: nowrap;
          margin-top: 9px;

          .title_icon {
            vertical-align: middle;
            display: inline-block;
            width: 14px;
            height: 20px;
            margin-right: 9px;
            background-image: url('@assets/png/meui/home_mic_img.png');
            background-size: 14px 20px;
            background-repeat: no-repeat;
            margin-top: -4px;
          }
        }

        .standard_title.have_cover {
          // margin-left:-2px;
          padding-left: 0.2rem;
        }

        .standard_queries {
          margin-left: -12px;
          // padding-left:12px;
        }
        .simple_standard_queries {
          margin-top: 5px;
          margin-left: 0;
          width: 340px;
        }
        .simple_area {
          display: inline-block;
          width: 98px;
          height: 25px;
        }

        .standard_queries.have_cover {
          margin-left: -0.5rem;
        }

        .swiper-container {
          margin: 0 auto;
          position: relative;
          overflow: hidden;
          padding: 0;
          z-index: 1;
          padding-bottom: 20px;
          margin-bottom: 15px;

          .swiper-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            z-index: 1;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-transition-property: -webkit-transform;
            transition-property: -webkit-transform;
            -o-transition-property: transform;
            transition-property: transform;
            transition-property: transform, -webkit-transform;
            -webkit-box-sizing: content-box;
            box-sizing: content-box;
          }

          .swiper-slide {
            -webkit-flex-shrink: 0;
            -ms-flex-negative: 0;
            flex-shrink: 0;
            width: 100% !important;
            height: 100%;
            position: relative;
            cursor: pointer;
            display: flex;

            .standard_area {
              flex: 1;
              max-width: 50%;

              p {
                width: auto;
                display: inline-block;
                padding-left: 6px;
                padding-right: 6px;
                width: 92%;

                span {
                  display: inline-block;
                  font-size: 15.62px;
                  color: #ffffff;
                  line-height: 18.75px;
                  margin-top: 12px;
                  margin-bottom: 12px;
                  opacity: 0.9;
                  white-space: nowrap;
                }
              }
            }
          }

          /* Pagination Styles */
          .swiper-pagination {
            position: absolute;
            text-align: center;
            z-index: 10;
            left: 50%;
            transform: translate(-50%);
            width: 100px;
          }

          /* Bullets */
          .swiper-pagination-bullet {
            width: 8px;
            height: 8px;
            display: inline-block;
            border-radius: 100%;
            background: rgba(255, 255, 255, 0.3);
            margin-right: 16px;
            cursor: pointer;
          }

          .swiper-pagination-bullet-active {
            background: #fff;
          }
        }
        .simple-swiper-container {
          margin: 0 auto;
          position: relative;
          overflow: hidden;
          padding: 0;
          z-index: 1;
          padding-bottom: 20px;
          margin-bottom: 15px;

          .swiper-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            z-index: 1;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-transition-property: -webkit-transform;
            transition-property: -webkit-transform;
            -o-transition-property: transform;
            transition-property: transform;
            transition-property: transform, -webkit-transform;
            -webkit-box-sizing: content-box;
            box-sizing: content-box;
          }

          .swiper-slide {
            // -webkit-flex-shrink: 0;
            // -ms-flex-negative: 0;
            // flex-shrink: 0;
            // width: 100% !important;
            height: 100%;
            position: relative;
            cursor: pointer;
            display: flex;

            .standard_area {
              flex: 1;
              max-width: 50%;

              p {
                width: auto;
                display: inline-block;
                // padding-left: 8px;
                // padding-right: 8px;
                // // width: 92%;
                // background: linear-gradient(0deg,#4482ff, #7f9fff 96%);
                // border-radius: 15px;

                span {
                  display: inline-block;
                  font-size: 14px;
                  color: #ffffff;
                  // line-height: 18.75px;
                  // margin-top: 6px;
                  // margin-bottom: 6px;
                  padding-top: 6px;
                  padding-bottom: 6px;
                  opacity: 0.9;
                  white-space: nowrap;
                  padding-left: 8px;
                  padding-right: 8px;
                  // width: 92%;
                  background: linear-gradient(0deg, #4482ff, #7f9fff 96%);
                  border-radius: 15px;
                }
              }
              .tip_cover {
                opacity: 0.7;
                background: none;
              }
            }
            .sa0 {
              p {
                span {
                  background: linear-gradient(180deg, #53c8de 1%, #1fbdcf);
                }
              }
            }
            .sa1 {
              p {
                span {
                  background: linear-gradient(0deg, #4482ff, #7f9fff 96%);
                }
              }
            }
            .sa2 {
              p {
                span {
                  background: linear-gradient(180deg, #a18af3, #7755ff);
                }
              }
            }
          }

          /* Pagination Styles */
          .swiper-pagination {
            position: absolute;
            text-align: center;
            z-index: 10;
            left: 50%;
            transform: translate(-50%);
            width: 100px;
          }

          /* Bullets */
          .swiper-pagination-bullet {
            width: 8px;
            height: 8px;
            display: inline-block;
            border-radius: 100%;
            background: rgba(255, 255, 255, 0.3);
            margin-right: 16px;
            cursor: pointer;
          }

          .swiper-pagination-bullet-active {
            background: #fff;
          }
        }

        .standard_skills {
          margin-right: -40px;
          margin-left: -12px;
          white-space: nowrap;
          padding: 3px;
          padding-bottom: 0px;

          p.firstContent {
            display: inline-block;
            padding: 6px 6px;
            margin-right: -6px;
            // padding-left: 0;
            // margin-left: .3rem;

            span {
              display: inline-block;
              width: 123px;
              height: 150px;
              border-radius: 3.12px;
              padding: 12.5px;
              font-size: 18.12px;
              color: #ffffff;
              line-height: 16.85px;
              background-size: cover;
              background-repeat: no-repeat;
              background-position: right bottom;
              background-color: rgba(85, 195, 251, 0.5);
              text-overflow: ellipsis;
              overflow: hidden;
            }

            .FCline2 {
              height: auto;
              background: none;
              left: 11px;
              display: inline;
              position: absolute;
              top: 39px;
            }
          }

          .standard_area2 {
            display: inline-block;
            vertical-align: top;
            margin-right: -6px;

            p {
              display: block;
              padding: 6px 6px;
              height: 85px;
              margin-top: -6px;

              span {
                display: inline-block;
                width: 131px;
                height: 72px;
                border-radius: 3.12px;
                padding: 28px 30px 28px 13px;
                font-size: 14.38px;
                color: #ffffff;
                line-height: 16.85px;
                background-size: cover;
                background-repeat: no-repeat;
                background-position: right bottom;
                background-color: rgba(85, 195, 251, 0.5);
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }

            p:nth-child(odd) {
              margin-top: 0px;
            }
          }
        }
        .dpn {
          display: none;
        }
        // .ConciseImg
        .ConciseImg {
          width: 306px;
          height: 264px;
          background-size: cover;
          margin-top: -16px;
          border-radius: 12px;
        }

        .standard_skills.add_cover {
          margin-left: -0.9rem;
        }
      }

      .gov_state {
        min-height: 600px;
        padding: 19px 17px 21px;
        padding-bottom: 0px;

        .gov_top {
          width: 340px;
          padding: 6px;
          margin: 0 auto;
          margin-bottom: 9px;
        }

        .gov_title {
          padding-bottom: 0px;
          text-align: left;
          cursor: pointer;
          white-space: nowrap;
          font-size: 15.62px;
          color: #ffffff;
          letter-spacing: 0.39px;

          .title_icon {
            vertical-align: middle;
            display: inline-block;
            width: 22px;
            height: 22px;
            margin-right: 8px;
            background-image: url('@assets/png/meui/gov_icon_big.png');
            background-size: 22px;
            background-repeat: no-repeat;
            margin-top: -4px;
          }
        }

        .gov_button {
          display: flex;
          cursor: pointer;

          div {
            flex: 1;
            padding: 12px;

            p {
              padding: 24px 24px 30px;
              border-radius: 3px;
              background-repeat: no-repeat;
              background-size: 100% 100%;
              background-position: center;

              span {
                display: inline-block;
              }

              span:nth-child(1) {
                width: 43px;
                height: 43px;
                background: #f00;
                margin-right: 12.5px;
              }

              span:nth-child(2) {
                font-size: 25px;
                line-height: 43px;
                color: #ffffff;
                width: 100px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }
          }
        }

        .gov_content {
          margin-top: -12px;

          div {
            width: 162.5px;
            display: inline-block;
            padding-left: 12px;
            padding-top: 12px;

            p {
              padding: 22px;
              border-radius: 3px;
              background-repeat: no-repeat;
              background-size: 100% 100%;
              background-position: center;

              span {
                display: inline-block;
              }

              span:nth-child(1) {
                width: 25px;
                height: 25px;
                background: #f00;
                margin-right: 6.5px;
              }

              span:nth-child(2) {
                font-size: 19px;
                line-height: 25px;
                color: #ffffff;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }
          }
        }

        .gov_queries_title {
          font-size: 25px;
          color: #ffffff;
          letter-spacing: 0.62px;
          text-align: center;
          margin-top: 19px;
          margin-bottom: 19px;
        }

        .queries_content {
          .gov_area {
            flex: 1;
            max-width: 50%;
            display: inline-block;

            p {
              width: auto;
              display: inline-block;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding-left: 12px;
              width: 89%;

              span {
                display: inline-block;
                font-size: 16.25px;
                color: #ffffff;
                line-height: 18.75px;
                margin-top: 12px;
                margin-bottom: 12px;
                opacity: 0.9;
              }
            }
          }
        }
      }

      .list_state::-webkit-scrollbar {
        display: none; //Safari and Chrome
      }

      .list_state,
      .card_state,
      .standard_state {
        height: 525px;
      }

      .list_state {
        overflow: hidden;
        overflow-y: scroll;
        padding: 16px 21px;
        padding-bottom: 5px;

        .list_top {
          width: 340px;
          padding: 5px 10px;
          height: 72px;
          margin: 0 auto;
          margin-bottom: 9px;
        }

        .list_title {
          font-size: 25px;
          color: #ffffff;
          line-height: 62.5px;
          cursor: pointer;
          text-align: center;
          white-space: nowrap;

          .title_icon {
            vertical-align: middle;
            display: inline-block;
            width: 17.5px;
            height: 24px;
            margin-right: 3px;
            background-image: url('@assets/png/meui/home_mic_img.png');
            background-size: 17.5px 24px;
            background-repeat: no-repeat;
            margin-top: -4px;
          }
        }

        .arabic_list_title {
          direction: rtl;
        }

        .list_Icon {
          border-radius: 8px;
          height: 62px;
          width: 100%;
        }

        .list_button {
          display: flex;
          cursor: pointer;
          margin-bottom: 8px;

          div {
            flex: 1;
            padding: 6px;
            max-width: 51%;

            p {
              padding: 21px 15px;
              border-radius: 3px;
              background-repeat: no-repeat;
              background-size: 100% 100%;
              background-position: center;

              span {
                display: block;
              }

              span:nth-child(1) {
                font-size: 15.62px;
                color: #ffffff;
                height: 18.75px;
                line-height: 18.75px;
                margin-bottom: 3px;

                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }

              span:nth-child(2) {
                opacity: 0.6;
                font-size: 14.38px;
                color: #ffffff;
                height: 18.75px;
                line-height: 18.75px;

                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }

              .chsspan {
                width: 100px;
              }

              .engspan {
                width: 100%;
              }
            }
          }

          div:nth-child(2) {
            margin-left: -6px;
          }
        }

        .list_content {
          p {
            padding: 15px 10px;
            // margin-bottom:5px;
            // margin-top:5px;
            cursor: pointer;

            span {
              display: block;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }

            .icon {
              display: inline-block;
              width: 35px;
              height: 35px;
              margin-right: 16px;
              float: left;
              border-radius: 100%;
            }

            span.title {
              font-size: 14.38px;
              color: #ffffff;
              height: 18.75px;
              line-height: 18.75px;
              margin-bottom: 2px;
            }

            span.info {
              opacity: 0.6;
              font-size: 14.38px;
              color: #ffffff;
              height: 18.75px;
              line-height: 18.75px;
            }
          }

          p::before {
            content: '';
            float: right;
            margin-right: 5px;
            position: relative;
            display: inline-block;
            opacity: 0.7;
            top: 12px;
            border-right: 1px solid #ffffff;
            border-top: 1px solid #ffffff;
            width: 10px;
            height: 10px;
            transform: rotate(45deg);
          }
        }

        .arabic_list_content {
          direction: rtl;

          p {
            .icon {
              float: right;
              margin-right: 0;
              margin-left: 10px;
            }
          }

          p::before {
            float: left;
            transform: rotate(-135deg);
          }
        }
      }

      .card_state::-webkit-scrollbar {
        display: none; //Safari and Chrome
      }

      .card_state {
        padding: 20px 12.5px;
        padding-bottom: 0px;
        overflow: hidden;
        overflow-y: scroll;

        .card_top {
          width: 321px;
          padding: 5px 10px;
          height: 72px;
          margin: 0 auto;
          margin-bottom: 9px;
        }

        .card_title {
          font-size: 25px;
          color: #ffffff;
          line-height: 32.5px;
          padding-top: 15px;
          padding-bottom: 15px;
          cursor: pointer;
          text-align: center;
          margin-bottom: 15px;
          white-space: nowrap;

          .title_icon {
            vertical-align: middle;
            display: inline-block;
            width: 17.5px;
            height: 24px;
            margin-right: 3px;
            background-image: url('@assets/png/meui/home_mic_img.png');
            background-size: 17.5px 24px;
            background-repeat: no-repeat;
            margin-top: -4px;
          }
        }

        .card_Icon {
          border-radius: 8px;
          height: 62px;
          width: 100%;
        }

        .card_button {
          padding: 6px;

          div {
            padding: 6px;
            width: 50%;
            display: inline-block;
            margin-top: -5px;

            p {
              width: 100%;
              height: 103px;
              padding: 15px;
              display: inline-block;
              border-radius: 3px;
              background-repeat: no-repeat;
              background-size: 100%;
              background-position: center;

              span.card_name {
                display: block;
                font-size: 15.6px;
                color: #ffffff;
                height: 18.72px;
                line-height: 18.72px;
                margin-bottom: 3px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              span.card_info {
                display: block;
                opacity: 0.6;
                font-size: 14.35px;
                color: #ffffff;
                height: 18.72px;
                line-height: 18.72px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          div:nth-child(odd) {
          }

          div:nth-child(even) {
            //margin-left:-6px;
          }
        }
      }

      .AI_state {
        min-height: 600px;
        padding: 218px 21px 68px 21px;
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: center;

        .AI_title {
          font-size: 25px;
          color: #ffffff;
          letter-spacing: 0.6px;
          text-align: center;
          margin-bottom: 30px;
        }

        .AI_info {
          line-height: 40px;
          margin-bottom: 3px;
        }

        .AI_big {
          font-size: 18px;
          color: #ffffff;
          letter-spacing: 1.33px;
          text-align: center;
        }

        .AI_small {
          text-align: center;
          padding-left: 40px;
          font-size: 15px;
          color: rgba(255, 255, 255, 0.5);
          letter-spacing: 0.51px;
        }

        .AI_info:before {
          content: '';
          display: inline-block;
          width: 7px;
          height: 5px;
          margin-right: 4px;
        }

        .AI_info:after {
          content: '';
          display: inline-block;
          width: 7px;
          height: 5px;
          border-radius: 100%;
          margin-left: 4px;
        }

        .AI_big:before,
        .AI_big:after {
          background-image: radial-gradient(closest-side at 50% 84%, #0a5df8 50%, rgba(11, 96, 255, 0) 85%);
        }

        .AI_small:before,
        .AI_small:after {
          background-image: radial-gradient(closest-side at 50% 84%, #0e8ba3 50%, rgba(14, 139, 163, 0) 85%);
        }
      }
    }
  }
}
