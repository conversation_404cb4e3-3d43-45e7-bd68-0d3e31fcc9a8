import React from 'react'
import { connect } from 'react-redux'
import { extend, getLocaleId } from 'koot'
import { Icon } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
import ChooseModal from './chooseModal'
import List from './template/list'
import Card from './template/card'
import AI from './template/AI'
import Standard from './template/Standard'
import Simple from './template/Simple'
import { getModuleConfigList, getModuleConfig, recoverDefaultStatus, searchAnswer } from '@api/_home'
import recommendQues from '@api/_home/recommendQues'
import { selectRobot, getRobotOpkList } from '@api/robots'
import { checkReleaseRaw } from '@utils/tools'
import { setLocalStorageItem } from '@utils/tools'
import { message } from 'antd'
@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    // console.log('页面：自定义首页', state._home)
    devUrl = 'http://test-jiedai.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
    curLang: state.user.curLang,
    user: state.user,
    brand: state.brand || 'ktv',
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  state = {
    iconJson: recommendQues.funcConfig,
    currentButton: 0, //3个按钮
    currentIndex: -2, //模版内容
    currentSort: false, //排序j
    changeContent: 'tips', //编辑区显示内容
    showInputTips: false,
    chooseQueTypes: '', //编辑区question类型
    modalStyle: '', //列表式List 卡片式Card 科技范Technical 标准式Standard,
    curScenes: '', //当前场景
    isDefault: false,
    isShowSetting: true,
    uploadUrl: '', //上传icon
    logo: '', //上传logo
    backImg: '', //自定义背景图
    progressNum: 0, //同步进度
    allNum: 0, //总同步数
    chooseList: [], //同步机器人列表
    fiveTimer: true, //进入页面5秒提示
    queriesIndex: -1, //当前选中queries索引
    showWarning: false, //不显示警示
    queryStyle: '', //底部query
    backImgstyle: '', //自定义背景
    queries_idx: 0,
    showLoading: false,
    currentRobotId: null,
    currentRobotSn: null,
    roleStr: '0',
    robotType: 'globall',
    robotName: undefined,
    lang: 'chs',
    overVersion51: false, //是版本高于5.1,
    overVersion57: false, //是版本高于5.7,
    templateInfoBackground: '',
    faceBackground: '',
    templateNeedUpdate: false, // 模板需要更新
    lowOpkVersion: false,
  }
  initRobotNum = 0
  componentDidMount() {
    //只执行一次。
    this.getRobotName()
    this.simple_config_json = {}
    this.common_config_json = {}

    // window.onbeforeunload = function () {
    //     return __('customHome.modalMsg.leaveMsg')
    // }
    // window.onunload = function () {
    //     return __('customHome.modalMsg.leaveMsg')
    // }

    const timer = setInterval(() => {
      // if (this.props.currentRobotId) {
      if (this.state.robotName) {
        clearInterval(timer)
        this.getConfigList()
      }
    }, 500)

    //5秒关闭提示蒙层
    const fiveTimer = setTimeout(() => {
      clearTimeout(fiveTimer)
      this.setState({
        fiveTimer: false,
        haveCover: this.props.homeModule.templateInfo.haveCover ? true : false,
      })
    }, 5000)
    // setTimeout(()=>{
    //     // this.getRobotOpkList();
    // },3*1e3)
    // 待默认列表取完之后再取opk 的信息
    let opkcheck = setInterval(() => {
      console.log('opkcheck', this.props.robots)
      if (Object.keys(this.props.robots).length > 0) {
        clearInterval(opkcheck)
        this.getRobotOpkList()
      }
    }, 900)

    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
  }
  getRobotOpkList() {
    return
    let formdata = new FormData()
    formdata.append('is_pad_baseopk', 1)
    getRobotOpkList(formdata).then((res) => {
      console.log(res, 'opk')
      res.robot_list.map((val) => {
        let obj = {}
        obj.id = val.robot.robot_uuid
        obj.data = { robot_baseopk_version: val.robot.robot_baseopk_version }

        // console.log(val,val.robot.robot_baseopk_version,'opl',this.props.robots)
        this.props.dispatch({
          type: 'UPDATE_ROBOT_DATA',
          id: obj.id,
          data: obj.data,
        })
      })
    })
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    // 	if (this.props.homeModule&&this.props.homeModule.templateInfo&&this.props.homeModule.templateInfo.bubbleContents && (this.props.homeModule.templateInfo.bubbleContents!=nextProps.homeModule.templateInfo.bubbleContents)&&this.props.homeModule.templateInfo.bubbleContents.faceStyle != "") {
    // 		this.setState({
    // 			Bubble1:this.props.homeModule.templateInfo.bubbleContents.robot[0],
    // 			Bubble2:this.props.homeModule.templateInfo.bubbleContents.robot[1]
    // 		})
    // 	}
    // if(nextProps.homeModule&&nextProps.homeModule.templateInfo&&nextProps.homeModule.templateInfo&&nextProps.homeModule.templateInfo.background&&this.props.homeModule&&this.props.homeModule.templateInfo&&(this.props.homeModule.templateInfo.background!=nextProps.homeModule.templateInfo.background)){
    // 	this.setState({
    // 		templateInfoBackground:nextProps.homeModule.templateInfo.background
    // 	})
    // }
  }
  componentWillUnmount() {
    this.props.dispatch({
      type: 'GET_HOME_MODULE',
      data: {
        homeModule: null,
      },
    })
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    if (
      this.props.currentRobotId != undefined &&
      prevProps.currentRobotId != undefined &&
      prevProps.currentRobotId != this.props.currentRobotId
    ) {
      console.log('componentDidUpdate', prevProps.currentRobotId, this.props.currentRobotId)
      this.setState({
        isShowSetting: true,
        isDefault: false,
        uploadUrl: '', //上传icon
        logo: '', //上传logo
        backImg: '', //自定义背景图
      })
      this.getConfigList()
    }

    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          console.log('-----------------', this.props.robots[el].version, versionGap)
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }

    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      prevProps.homeModule.templateInfo.bubbleContents &&
      prevProps.homeModule.templateInfo.bubbleContents != this.props.homeModule.templateInfo.bubbleContents
    ) {
      this.setState({
        Bubble1: '',
      })
    }
    if (
      this.props.homeModule &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo &&
      this.props.homeModule.templateInfo.background &&
      prevProps.homeModule &&
      prevProps.homeModule.templateInfo &&
      this.state.templateInfoBackground != this.props.homeModule.templateInfo.background
    ) {
      this.setState({
        templateInfoBackground: this.props.homeModule.templateInfo.background,
      })
    }
  }
  getConfigList(success) {
    const lang = this.state.lang
    let module_code_list = ['module_skill_home']
    let formData = new FormData()
    formData.append('module_code_list', JSON.stringify(module_code_list))
    formData.append('robot_uuid', this.props.currentRobotId)
    // formData.append('robot_uuid', localStorage.getItem("globallRobotId"));
    formData.append('config_status', 'publish')
    if (lang === 'english') {
      formData.set('lang', 'en_US')
    }
    if (lang === 'chs') {
      formData.set('lang', 'zh_CN')
    }

    getModuleConfigList(formData)
      .then((res) => {
        console.log('获取config_list', res)
        let config_id = '',
          version = ''
        if (res.module_config_list.length != 0) {
          config_id = res.module_config_list[0].config_id
          version = res.module_config_list[0].version
          this.gethomeModule(config_id, version, success)
        } else {
          config_id = Date.parse(new Date()) / 1000
          version = ''
          if (this.isKTV) {
            this.gethomeModule('ktv_simple_id', version, success)
          } else {
            this.gethomeModule('default_id', version, success)
          }
        }
        this.module_config_list = res.module_config_list
        sessionStorage.setItem('config_id', config_id)
        sessionStorage.setItem('version', version)
      })
      .catch((res) => {})
  }

  gethomeModule(config_id, version, success, lang) {
    let formData = new FormData()
    formData.append('module_code', 'module_skill_home')
    formData.append('config_id', config_id)
    formData.append('version', version)
    if (lang === 'english' || this.state.lang == 'english') {
      formData.set('lang', 'en_US')
    }
    if (lang === 'chs' || this.state.lang == 'chs') {
      formData.set('lang', 'zh_CN')
    }

    console.log('首页模板数据 0')
    getModuleConfig(formData)
      .then((res) => {
        let config_json = res.module_config.config_json
        if (typeof config_json === 'string') {
          try {
            config_json = JSON.parse(config_json)
          } catch (e) {}
        }
        console.log(config_json)
        if (config_json.templateInfo.templateCode == 'Simple') {
          this.simple_config_json = JSON.parse(JSON.stringify(config_json))
        } else {
          this.common_config_json = JSON.parse(JSON.stringify(config_json))
        }
        if (config_json.templateInfo.conciseContent == undefined) {
          config_json.templateInfo.conciseContent = {
            title: '猎户风景区欢迎你',
            image: 'module_public/module_skill_home/conciseimage.png',
            suggestion: '我要买门票',
          }
        }
        console.log('get_home_module 1')
        this.props.dispatch({
          type: 'GET_HOME_MODULE',
          data: {
            homeModule: config_json,
          },
        })
        if (
          this.isKTV &&
          this.props.homeModule.templateInfo.templateCode != 'Simple' &&
          !this.state.lowOpkVersion &&
          !this.state.isDefault &&
          !(this.state.isShowSetting && this.state.isDefault)
        ) {
          this.setState({
            templateNeedUpdate: true,
          })
          modal({
            title: '提示',
            fullscreen: false,
            content: (
              <div>
                <p>首页模板已更新，是否需要切换</p>
                <p>否则将不能在此平台配置，请跳转到原后台</p>
              </div>
            ),
            cancel: false,
            confirm: '确认切换',
            onConfirm: () => {
              this.setState(
                {
                  templateNeedUpdate: false,
                },
                () => {
                  this.choosemodalStyle('Simple', 'KTV', 'chs')
                },
              )
            },
          })
          let lowOpkVersion = this.state.lowOpkVersion
          let isDefault = this.state.isDefault
          // window.tipSetData({
          //     templateNeedUpdate: true,
          //     lowOpkVersion,
          //     isDefault
          // })
        } else {
          let lowOpkVersion = this.state.lowOpkVersion
          let isDefault = this.state.isDefault
          // window.tipSetData({
          //     templateNeedUpdate: false,
          //     lowOpkVersion,
          //     isDefault
          // })
        }
        let scenes = config_json.templateInfo.scenes ? config_json.templateInfo.scenes : 'default'
        let curScenes = ''
        let path = ''
        // 修复 选择类型时 切换 简洁式其他的类型，会造成简洁式 背景被重置的问题
        let backUrl = this.props.homeModule.templateInfo.background
        !this.state.SimplebackImg &&
          this.props.homeModule.templateInfo.templateCode == 'Simple' &&
          this.setState({
            //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
            SimplebackImg: backUrl,
            backImg: backUrl,
          })

        let _faceBackground = this.props.homeModule.templateInfo.faceBackground
        this.state.faceBackground == '' &&
          this.setState({
            //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
            faceBackground: _faceBackground,
          })
        if (scenes == 'default') {
          path = 'default'
        } else {
          path = path + '_default'
        }
        let lang_ext = ''
        if (this.state.lang == 'english') {
          lang_ext = '_en'
        }
        if (res.module_config.config_json_path == 'module_default/module_skill_home/' + path + lang_ext + '.json') {
          this.setState({
            isDefault: true,
          })
        } else {
          this.setState({
            isDefault: false,
          })
        } //是不是默认JSON
        if (this.isKTV) {
          if (this.module_config_list.length == 0) {
            this.setState({
              isDefault: true,
            })
          } else {
            this.setState({
              isDefault: false,
            })
          }
        }

        if (scenes) {
          switch (scenes) {
            // case __('customHome.sceneType.default').toLowerCase():
            case 'default':
              // curScenes = "默认"
              curScenes = __('customHome.sceneType.default')
              console.log(curScenes)
              break
            // case __('customHome.sceneType.market'):
            case 'market':
              // curScenes = "商场"
              curScenes = __('customHome.sceneType.market')
              break
            // case __('customHome.sceneType.hotel'):
            case 'hotel':
              // curScenes = "酒店"
              curScenes = __('customHome.sceneType.hotel')
              break
            // case __('customHome.sceneType.bank'):
            case 'bank':
              // curScenes = "银行"
              curScenes = __('customHome.sceneType.bank')
              break
            // case __('customHome.sceneType.museum'):
            case 'museum':
              // curScenes = "博物馆"
              curScenes = __('customHome.sceneType.museum')
              break
            // case __('customHome.sceneType.library'):
            case 'library':
              // curScenes = "图书馆"
              curScenes = __('customHome.sceneType.library')
              break
            case 'KTV':
              // curScenes = "图书馆"
              curScenes = 'KTV'
              break
            default:
              curScenes = __('customHome.sceneType.default')
          }
        }
        this.setState({
          curScenes: curScenes,
          modalStyle: config_json.templateInfo.templateCode,
        })
        success && success()
      })
      .catch((res) => {
        console.log(res)
      })
  }
  // changeRobot(id) {
  // 	this.props.dispatch(selectRobot(id));
  // 	this.props.dispatch(getRobotStatistics(id));
  // }
  recoverSet() {
    //删除上传的背景图
    this.setState({
      backImg: '',
      queryStyle: '',
      isShowSetting: true,
      faceBackground: '',
      SimplebackImg: '',
    })
    console.log('恢复出厂设置')
    if (sessionStorage.getItem('version') == '') {
      this.getConfigList(() => {
        message.success(__('customHome.modalMsg.successRestMsg'))
        // modal({
        //     title: __('customHome.modalMsg.successfullyReset'),
        //     content: __('customHome.modalMsg.successRestMsg'),
        //     cancel: false,
        //     confirm: __('customHome.btn.gotIt'),
        // });
        this.changeTab_backImg()
      })
    } else {
      let robotID = [this.props.currentRobotId]
      let formData = new FormData()
      formData.append('module_code', 'module_skill_home')
      if (this.state.lang == 'english') {
        formData.append('lang', 'en_US')
      } else {
        formData.append('lang', 'zh_CN')
      }
      // formData.append('module_code', 'module_skill_home');
      formData.append('config_id', sessionStorage.getItem('config_id'))
      formData.append('version', sessionStorage.getItem('version'))
      formData.append('robot_uuid_list', JSON.stringify(robotID))
      formData.append('status', 'default')
      recoverDefaultStatus(formData)
        .then((res) => {
          this.getConfigList()
          this.changeTab_backImg()
          message.success(__('customHome.modalMsg.successRestMsg'))
        })
        .catch((res) => {
          modal({
            title: __('customHome.modalMsg.failedToReset'),
            content: __('customHome.modalMsg.failedRestMsg'),
            cancel: __('customHome.btn.cancel2'),
            confirm: __('customHome.btn.retry'),
            btnCenter: true,
            onConfirm: () => {
              this.recoverSet()
            },
          })
        })
    }
  }
  getRobotName() {
    const robots = this.props.robots,
      roleStr = this.state.roleStr != undefined && this.state.roleStr,
      robotType = this.state.robotType != undefined && this.state.robotType
    let robotName = '',
      current
    if (Object.keys(robots).length == 0 && this.initRobotNum < 25) {
      this.initRobotNum += 1
      return setTimeout(() => {
        this.getRobotName()
      }, 500)
    }

    if (Object.keys(robots).length == 0) {
      robotName = __('customHome.modalMsg.noRobots')
    } else {
      if (robotType == 'local') {
        current = localStorage.getItem('localRobotId')
      } else {
        // current = localStorage.getItem('globallRobotId')
        if (roleStr == '0' || roleStr == '1') {
          current = localStorage.getItem('receptionRobotId')
        }
        if (roleStr == '2') {
          current = localStorage.getItem('docentRobotId')
        }
      }

      const firstRobotId = Object.keys(robots)[0]
      if (current == 'all') {
        const robotType = this.state.robotType
        const localRobotId =
          robotType == 'local' ? localStorage.getItem('localRobotId') : localStorage.getItem('globallRobotId')

        if (localRobotId != undefined || localRobotId != null) {
          robotName = robots[localRobotId] && robots[localRobotId].robot_name
          current = localRobotId
          //this.props.dispatch(selectRobot(localRobotId));
        } else {
          robotName = robots[firstRobotId] && robots[firstRobotId].robot_name
          //this.props.dispatch(selectRobot(firstRobotId));
          current = firstRobotId
        }
      } else {
        robotName = robots[current] && robots[current].robot_name
        console.log('当前机器人robotName@@@@', robotName)
        if (robotName == undefined || robotName == null) {
          robotName = robots[firstRobotId] && robots[firstRobotId].robot_name
          console.log('当前机器人robotName!!!!', robotName)
          setLocalStorageItem('globallRobotId', firstRobotId)
          setLocalStorageItem('localRobotId', firstRobotId)
          //this.props.dispatch(selectRobot(firstRobotId));
        } else {
          //this.props.dispatch(selectRobot(current));
          // current = current
        }
      }
    }
    let version = this.props.robots && this.props.robots[current] && this.props.robots[current].version
    let robot_baseopk_version =
      (this.props.robots && this.props.robots[current] && this.props.robots[current].robot_baseopk_version) || ''
    let baseVersion = '02.0001.200821'
    let lowOpkVersion = checkReleaseRaw(robot_baseopk_version, baseVersion, true) ? false : true
    let overVersion51 = checkReleaseRaw(version, 'V5.1', true)
    let overVersion57 = checkReleaseRaw(version, 'V5.7', true)
    console.log('机器人版本51', overVersion51)
    this.setState(
      {
        robotName: robotName,
        overVersion51: overVersion51,
        overVersion57: overVersion57,
        lowOpkVersion,
      },
      () => {
        this.props.dispatch(selectRobot(current))
      },
    )
    // window.tipSetData && window.tipSetData({ lowOpkVersion })
    let isDefault = this.state.isDefault
    // window.tipSetData({
    //     templateNeedUpdate: false,
    //     lowOpkVersion,
    //     isDefault
    // })
    if (this.isKTV && lowOpkVersion) {
      setTimeout(() => {
        modal({
          title: '提示',
          fullscreen: false,
          content: (
            <div>
              <p>当前机器人系统版本太低，不支持配置，请跳转至原后台</p>
              <p>若想体验全新首页模板，请联系您的售前</p>
            </div>
          ),
          cancel: false,
          confirm: __('customHome.btn.confirm'),
          onConfirm: () => {},
        })
      }, 900)
    }
  }
  render() {
    const robotName = this.state.robotName != undefined && this.state.robotName

    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      } else if (templateCode == 'Simple') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      }
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }

      // var styles = { "backgroundImage": "url(" + this.props.devUrl + "media/" + (this.state.templateInfoBackground||this.props.homeModule.templateInfo.background ) + ")" };

      if (
        this.props.homeModule.templateInfo.faceBackground &&
        this.props.homeModule.templateInfo.faceBackground != ''
      ) {
        var facebackground = {
          backgroundImage:
            'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.faceBackground + ')',
        }
      }
      var faceStyle = 'Bubble'
      var Bubble1, Bubble2, Bubble1Big, Bubble2Big
      if (
        this.props.homeModule.templateInfo.bubbleContents &&
        this.props.homeModule.templateInfo.bubbleContents.faceStyle != ''
      ) {
        faceStyle = this.props.homeModule.templateInfo.bubbleContents.faceStyle
        Bubble1 = this.props.homeModule.templateInfo.bubbleContents.robot[0]
        Bubble2 = this.props.homeModule.templateInfo.bubbleContents.robot[1]
      }
      Bubble1 = this.state.Bubble1 || Bubble1
      let Queries = [],
        isQueries = false,
        queryStyle = ''
      var bottom_queries_con = ''
      if (this.state.queryStyle == '') {
        queryStyle = 'mainPage'
      } else {
        queryStyle = this.state.queryStyle
      }
      this.props.homeModule.queries.forEach((item) => {
        if (item.name == queryStyle) {
          isQueries = true
          Queries = item.query
        }
      })
      if (isQueries) {
        bottom_queries_con = Queries[this.state.queries_idx]
      } else {
        bottom_queries_con = __('customHome.warm.callMe')
      }
    }
    return (
      <div className={this.props.className}>
        {this.state.showLoading && (
          <div className='loading'>
            <div className='loading_modal'>
              <img src={require('@assets/svg-icon/loading.gif')} />
              <span>{__('customHome.modalMsg.waitMsg')}</span>
            </div>
          </div>
        )}

        <div className='content'>
          <div className='preview'>
            <div className='previewCon' style={styles}>
              {this.state.queryStyle != '' && (
                <img className='query_bg' src={require('@assets/png/meui/home_' + this.state.queryStyle + '_bg.jpg')} />
              )}
              {this.state.backImgstyle != '' && (
                <div className='faceBackground' style={facebackground}>
                  <img
                    className={
                      (this.state.overVersion51 && faceStyle == 'Bubble') || faceStyle === 'Card' ? 'moveup' : ''
                    }
                    src={require('@assets/png/meui/home4_eyes.png')}
                  />
                  {/* 只有一条的时候 */}
                  {this.state.overVersion51 && faceStyle == 'Bubble' && Bubble1 && !Bubble2 && (
                    <img
                      className={Bubble1.length > 6 ? 'bubble1m_two' : 'bubble1m_one'}
                      alt=''
                      src={
                        Bubble1.length > 6
                          ? require('@assets/png/meui/bubble1big.png')
                          : require('@assets/png/meui/bubble1.png')
                      }
                    />
                  )}
                  {this.state.overVersion51 && faceStyle == 'Bubble' && Bubble1 && !Bubble2 && (
                    <div className={Bubble1.length > 6 ? 'bubbleText1m_two' : 'bubbleText1m_one'}> {`${Bubble1}`}</div>
                  )}

                  {Bubble1Big && <div className='bubbleText1'> {`“${Bubble1Big}”`}</div>}

                  {this.state.overVersion51 && faceStyle == 'Bubble' && Bubble1 && Bubble2 && (
                    <img
                      className={Bubble1.length > 6 ? 'bubble1_two' : 'bubble1_one'}
                      alt=''
                      src={
                        Bubble1.length > 6
                          ? require('@assets/png/meui/bubble1big.png')
                          : require('@assets/png/meui/bubble1.png')
                      }
                    />
                  )}
                  {this.state.overVersion51 && faceStyle == 'Bubble' && Bubble1 && Bubble2 && Bubble1.length <= 6 && (
                    <div className='bubbleText1_one'> {`${Bubble1}`}</div>
                  )}
                  {this.state.overVersion51 && faceStyle == 'Bubble' && Bubble1 && Bubble2 && Bubble1.length > 6 && (
                    <div className='bubbleText1_two 1'> {`${Bubble1}`}</div>
                  )}

                  {Bubble1Big && <div className='bubbleText1'> {`“${Bubble1Big}”`}</div>}

                  {this.state.Bubble1big && (
                    <img className='bubble1big' alt='' src={require('@assets/png/meui/bubble1big.png')} />
                  )}

                  {this.state.overVersion51 && faceStyle == 'Bubble' && Bubble2 && (
                    <img
                      className={Bubble2.length > 6 ? 'bubble2_two' : 'bubble2_one'}
                      alt=''
                      src={
                        Bubble2.length > 6
                          ? require('@assets/png/meui/bubble2big.png')
                          : require('@assets/png/meui/bubble2.png')
                      }
                    />
                  )}
                  {this.state.overVersion51 && faceStyle == 'Bubble' && Bubble2 && Bubble2.length <= 6 && (
                    <div className='bubbleText2_one'> {`${Bubble2}`}</div>
                  )}
                  {this.state.overVersion51 && faceStyle == 'Bubble' && Bubble2 && Bubble2.length > 6 && (
                    <div className='bubbleText2_two'> {`${Bubble2}`}</div>
                  )}

                  {this.state.Bubble2big && (
                    <img className='bubble2big' alt='' src={require('@assets/png/meui/bubble2big.png')} />
                  )}

                  {/*张维青卡片英文模式  */}
                  {this.state.overVersion57 && faceStyle == 'Card' && this.state.lang === 'english' && (
                    <div className='cardContent'>
                      <div className='title'>
                        {/* <Icon icon='icon_title' className='icon' /> */}
                        <Icon icon='icon_title' className='icon' />
                        {this.props.homeModule.templateInfo.bubbleContents.title}
                      </div>
                      {/* <img className={this.props.homeModule.templateInfo.bubbleContents.title.length > 6 ? 'title_two' : 'title_one'} alt="" src={this.props.homeModule.templateInfo.bubbleContents.title.length > 6 ? require('@assets/png/meui/card_title.png') : require('@assets/png/meui/card_title.png')} /> */}
                      <img className='title_two' alt='' src={require('@assets/png/meui/card_title.png')} />
                      <img className='txt_one' alt='' src={require('@assets/png/meui/card_bg.png')} />
                      {/* {Bubble1 && <img className={Bubble1.length > 6 ? 'txt_two' : 'txt_one'} alt="" src={Bubble1.length > 6 ? require('@assets/png/meui/card_bg.png') : require('@assets/png/meui/card_bg.png')} />} */}

                      <div className='txt'>{Bubble1 && `${Bubble1}`}</div>
                    </div>
                  )}

                  <div className='compare1'></div>
                  <div className='compare2'></div>
                  <div className='one_m'></div>
                </div>
              )}
              {this.state.queryStyle == '' && this.state.backImgstyle == '' && (
                <div className='previewCon_height'>
                  {this.state.isDefault && this.state.isShowSetting && (
                    <div className='startSetting_tips'>
                      <div className='startSetting_modal'>
                        <Icon icon='icon_Warning' className='prompt_icon' />
                        <p className='title'>{__('customHome.toast.msg1')}</p>
                        <p className='button'>
                          <span
                            onClick={() => {
                              this.setState({
                                isShowSetting: false,
                              })
                              if (!this.isKTV) {
                                this.changeModal()
                              } else {
                                this.setState(
                                  {
                                    templateNeedUpdate: false,
                                  },
                                  () => {
                                    this.choosemodalStyle('Simple', 'KTV', 'chs')
                                  },
                                )

                                let isDefault = false
                                // window.tipSetData({
                                //     templateNeedUpdate: false,
                                //     // lowOpkVersion,
                                //     isDefault
                                // })
                              }
                            }}
                          >
                            {__('customHome.btn.startSetup')}
                          </span>
                        </p>
                      </div>
                    </div>
                  )}
                  {this.isKTV && this.state.lowOpkVersion && (
                    <div className='startSetting_tips'>
                      <div className='startSetting_modal lowopkversion'>
                        <Icon icon='icon_Warning' className='prompt_icon' />
                        <p className='title'>{'当前机器人版本太低，不支持配置，请跳转至原后台'}</p>
                        <p className='title'>{'若想体验全新首页模板，请联系您的售前'}</p>
                        {/* <p className="button"><span onClick={() => {
                                                    
                                                    // this.changeModal()
                                                }}>{'我知道了'}</span></p> */}
                      </div>
                    </div>
                  )}
                  {this.isKTV &&
                    this.state.templateNeedUpdate &&
                    !this.state.lowOpkVersion &&
                    !this.state.isDefault && (
                      <div className='startSetting_tips'>
                        <div className='startSetting_modal templateNeedUpdate'>
                          <Icon icon='icon_Warning' className='prompt_icon' />
                          <p className='title'>{'首页模板已更新，是否需要切换'}</p>
                          <p className='title'>{'否则不能在此平台配置，请跳转到原后台'}</p>
                          <p className='button'>
                            <span
                              onClick={() => {
                                // this.changeModal()
                                this.setState(
                                  {
                                    templateNeedUpdate: false,
                                  },
                                  () => {
                                    this.choosemodalStyle('Simple', 'KTV', 'chs')
                                  },
                                )
                                let isDefault = false
                                // window.tipSetData({
                                //     templateNeedUpdate: false,
                                //     // lowOpkVersion,
                                //     isDefault
                                // })
                              }}
                            >
                              {'确认切换'}
                            </span>
                          </p>
                        </div>
                      </div>
                    )}
                  {this.state.modalStyle == 'List' || this.state.modalStyle == 'list' ? (
                    <List
                      fiveTimer={this.state.fiveTimer}
                      backImg={this.state.backImg}
                      devUrl={this.props.devUrl}
                      lang={this.state.lang}
                      currentSort={this.state.currentSort}
                      currentIndex={this.state.currentIndex}
                      changeCurrentIndex={this.changeCurrentIndex.bind(this)}
                      changeContent={this.changeContent.bind(this)}
                      checkJson={this.checkJson.bind(this)}
                    />
                  ) : (
                    ''
                  )}

                  {this.state.modalStyle == 'Card' && (
                    <Card
                      fiveTimer={this.state.fiveTimer}
                      backImg={this.state.backImg}
                      devUrl={this.props.devUrl}
                      currentSort={this.state.currentSort}
                      currentIndex={this.state.currentIndex}
                      changeCurrentIndex={this.changeCurrentIndex.bind(this)}
                      changeContent={this.changeContent.bind(this)}
                      checkJson={this.checkJson.bind(this)}
                    />
                  )}

                  {this.state.modalStyle == 'Technical' && (
                    <AI
                      fiveTimer={this.state.fiveTimer}
                      backImg={this.state.backImg}
                      devUrl={this.props.devUrl}
                      currentSort={this.state.currentSort}
                      currentIndex={this.state.currentIndex}
                      changeCurrentIndex={this.changeCurrentIndex.bind(this)}
                      changeContent={this.changeContent.bind(this)}
                      checkJson={this.checkJson.bind(this)}
                    />
                  )}

                  {this.state.modalStyle == 'Standard' && this.state.queryStyle == '' && (
                    <Standard
                      fiveTimer={this.state.fiveTimer}
                      haveCover={this.state.haveCover}
                      backImg={this.state.backImg}
                      devUrl={this.props.devUrl}
                      lang={this.state.lang}
                      currentSort={this.state.currentSort}
                      currentIndex={this.state.currentIndex}
                      changeCurrentIndex={this.changeCurrentIndex.bind(this)}
                      changeContent={this.changeContent.bind(this)}
                      checkJson={this.checkJson.bind(this)}
                      className='standard-con'
                    />
                  )}
                  {this.state.modalStyle == 'Simple' && this.state.queryStyle == '' && (
                    <Simple
                      fiveTimer={this.state.fiveTimer}
                      haveCover={this.state.haveCover}
                      backImg={this.state.backImg}
                      devUrl={this.props.devUrl}
                      lang={this.state.lang}
                      currentSort={this.state.currentSort}
                      currentIndex={this.state.currentIndex}
                      changeCurrentIndex={this.changeCurrentIndex.bind(this)}
                      changeContent={this.changeContent.bind(this)}
                      checkJson={this.checkJson.bind(this)}
                      className='standard-con'
                    />
                  )}
                </div>
              )}
              {this.props.homeModule && this.state.backImgstyle == '' ? (
                // <div className={this.state.currentButton == -1 ? "bottom_queries" : "bottom_queries"}>
                <div className={this.state.currentButton == -1 ? 'bottom_queries tip_cover' : 'bottom_queries'}>
                  <div
                    className='bottom_queries_con'
                    onClick={() => {
                      if (this.checkJson('bottomQueries')) {
                        this.setState({
                          currentButton: -1,
                          currentIndex: -2,
                          currentSort: false,
                        })
                        this.changeContent('bottomQueries')
                      }
                    }}
                  >
                    <img src={require('@assets/png/meui/recommend_robot_img.png')} />
                    {/* <img src={require('@assets/png/meui/robot_head.png')} /> */}

                    {this.state.lang == 'chs' && (
                      <span>
                        {bottom_queries_con.length > 15 ? bottom_queries_con.substr(0, 15) + '...' : bottom_queries_con}
                      </span>
                    )}
                    {this.state.lang == 'english' && (
                      <span>
                        {bottom_queries_con.length > 28 ? bottom_queries_con.substr(0, 28) + '...' : bottom_queries_con}
                      </span>
                    )}
                  </div>
                </div>
              ) : (
                ''
              )}
              {this.props.homeModule && this.state.modalStyle == 'Card' && this.state.changeContent != 'backImg' ? (
                <img src={require('@assets/png/meui/home1_query_bg_img.png')} className='shadow' />
              ) : (
                ''
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }
  // 场景模板修改
  changeModal() {
    if (this.checkJson()) {
      this.setState({
        currentButton: 1,
        currentIndex: -2,
        currentSort: false,
      })
      modal({
        title: false,
        confirm: false,
        cancel: false,
        fullscreen: false,
        content: (
          <ChooseModal
            curScenes={this.state.curScenes}
            lang={this.state.lang}
            modalStyle={this.state.modalStyle}
            choosemodalStyle={this.choosemodalStyle.bind(this)}
          />
        ),
      })
    }
  }
  chooseRobots(arr) {
    console.log(arr)
    this.setState({
      chooseList: arr,
    })
  }
  changeCurrentIndex(num) {
    this.setState({
      currentIndex: num,
      currentButton: 0,
      currentSort: false,
    })
  }
  changeInputTips(flag) {
    this.setState({
      showInputTips: flag,
    })
  }
  changeContent(txt, sty, queriesIndex) {
    console.log(txt, sty, queriesIndex, '~~~~~~')
    if (queriesIndex != -1) {
      this.setState({
        changeContent: txt,
        chooseQueTypes: sty,
        queriesIndex: queriesIndex,
      })
    } else {
      if (sty) {
        this.setState({
          changeContent: txt,
          chooseQueTypes: sty,
        })
      } else {
        this.setState({
          changeContent: txt,
        })
      }
    }
    this.setState({
      showInputTips: false,
    })
  }
  // 模板选择确定
  choosemodalStyle(txt, curScenes, lang) {
    console.log('模板选择 choosemodalStyle 1')
    // console.log('qy-curScenes',curScenes);
    // console.log('qy-lang',lang);
    let oldLang = this.state.lang
    this.setState(
      {
        lang,
        backImg: '',
      },
      () => {
        if (lang != oldLang) {
          this.getConfigList(() => {
            this.chooseRobots_success(txt, curScenes)
          })
          return
        }
        let config_id = 'default_id'
        // 切换都要场景就要重新获取
        if (curScenes != this.state.curScenes) {
          this.simple_config_json = {}
          this.common_config_json = {}
        }
        if (
          curScenes != this.state.curScenes ||
          (txt == 'Simple' && Object.keys(this.simple_config_json).length == 0) ||
          (txt != 'Simple' && Object.keys(this.common_config_json).length == 0)
        ) {
          //切换场景
          switch (curScenes) {
            // case "默认":
            case __('customHome.sceneType.default'):
              config_id = 'default_id'
              break
            // case "商场":
            case __('customHome.sceneType.market'):
              config_id = 'market_default_id'
              break
            // case "酒店":
            case __('customHome.sceneType.hotel'):
              config_id = 'hotel_default_id'
              break
            // case "银行":
            case __('customHome.sceneType.bank'):
              config_id = 'bank_default_id'
              break
            case __('customHome.sceneType.museum'):
              // case "博物馆":
              config_id = 'museum_default_id'
              // config_id = "museum_museum_id"
              break
            case __('customHome.sceneType.library'):
              // case "图书馆":
              config_id = 'library_default_id'
              break
            case 'KTV':
              // case "":
              config_id = 'ktv_default_id'
              break
          }
          if (curScenes != this.state.curScenes) {
            this.setState({
              SimplebackImg: '',
              backImg: '',
            })
          }

          if (txt == 'Simple') {
            config_id = config_id.replace('default', 'simple')
            if (config_id == 'simple_id') {
              if (curScenes != 'KTV') {
                config_id = 'default_simple_id'
              } else {
                config_id = 'ktv_simple_id'
              }
            }
          }
          this.gethomeModule(
            config_id,
            '',
            () => {
              this.chooseRobots_success(txt, curScenes)
            },
            lang,
          )
        } else if (
          txt == 'Simple' &&
          curScenes == this.state.curScenes &&
          Object.keys(this.simple_config_json).length > 0
        ) {
          let config_json = this.simple_config_json
          console.log('get_home_module 2')
          this.props.dispatch({
            type: 'GET_HOME_MODULE',
            data: {
              homeModule: config_json,
            },
          })
          this.chooseRobots_success(txt, curScenes)
        } else if (
          txt != 'Simple' &&
          curScenes == this.state.curScenes &&
          Object.keys(this.common_config_json).length > 0
        ) {
          let config_json = this.common_config_json
          console.log('get_home_module 3')
          this.props.dispatch({
            type: 'GET_HOME_MODULE',
            data: {
              homeModule: config_json,
            },
          })
          this.chooseRobots_success(txt, curScenes)
        } else {
          this.chooseRobots_success(txt, curScenes)
        }
      },
    )
  }
  // 模板选择回调函数
  chooseRobots_success(txt, curScenes) {
    let backUrl = 'module_public/module_skill_home/home1.png'
    this.setState({
      isShowSetting: false,
      modalStyle: txt,
      curScenes: curScenes,
      changeContent: 'tips',
      chooseQueTypes: '1',
      currentButton: 0,
      currentSort: false,
      currentIndex: -2,
    })

    this.props.homeModule.templateInfo.templateCode = txt
    // debugger
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'templateCode',
        content: this.props.homeModule.templateInfo.templateCode,
      },
    })

    if (curScenes == 'KTV') {
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'scenes',
          content: 'KTV',
        },
      })
    }
    if (txt == 'List') {
      backUrl = 'module_public/module_skill_home/home1.png'
      for (var j = 0; j <= this.props.homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (this.props.homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j <= 1) {
              //this.props.homeModule.skills[j].icon = this.state.iconJson[i].list_icon2
              if (this.state.lang != 'chs') {
                // return;
              } else {
                if (this.props.homeModule.skills[j].label == 'chat2') {
                  this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].list_icon2
                } else {
                  this.props.homeModule.skills[j].icon = this.state.iconJson[i].list_icon2
                }
              }
            } else {
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].list_icon1
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].list_icon1
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 1,
                content: this.props.homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Card') {
      backUrl = 'module_public/module_skill_home/home2.png'
      //切换图标
      for (var j = 0; j <= this.props.homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (this.props.homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (this.props.homeModule.skills[j].label == 'chat2') {
              this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].card_icon
            } else {
              this.props.homeModule.skills[j].icon = this.state.iconJson[i].card_icon
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 2,
                content: this.props.homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Technical') {
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'titleBarType',
          content: 'text',
        },
      })
      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'titleBarContent',
          content: __('customHome.warm.callMe'),
        },
      })
      backUrl = 'module_public/module_skill_home/home3.png'
      //切换图标
      // for (var i = 0; i <= this.state.iconJson.length - 2; i++) {
      // 	this.props.homeModule.skills[i].icon = "";
      // 	this.props.dispatch({
      // 		type: 'CHANGE_MODULE_SKILL',
      // 		data: {
      // 			type: 'icon',
      // 			index: i,
      // 			content: this.props.homeModule.skills[i]
      // 		}
      // 	})
      // }
    } else if (txt == 'Standard') {
      if (this.props.homeModule.templateInfo.titleBarType != 'text') {
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarType',
            content: 'text',
          },
        })
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarContent',
            content: __('customHome.warm.callMe'),
          },
        })
      }

      backUrl = 'module_public/module_skill_home/home4.png'
      //切换图标
      for (var j = 0; j <= this.props.homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (this.props.homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j == 0) {
              //this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon;
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].standard2_icon
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon
              }
            } else {
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].standard1_icon
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard1_icon
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 3,
                content: this.props.homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    } else if (txt == 'Simple') {
      backUrl = 'module_public/module_skill_home/simpleBg.png'
      if (this.props.homeModule.templateInfo.titleBarType != 'text') {
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarType',
            content: 'text',
          },
        })
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'titleBarContent',
            content: __('customHome.warm.callMe'),
          },
        })
      }

      if (this.props.homeModule.templateInfo.conciseContent == undefined)
        [
          this.props.dispatch({
            type: 'CHANGE_INFO',
            data: {
              type: 'conciseContent',
              content: {
                title: '猎户风景区欢迎你',
                image: 'module_public/module_skill_home/conciseimage.png',
                suggestion: '我要买门票',
              },
            },
          }),
        ]

      // backUrl = "module_public/module_skill_home/home4.png";
      //切换图标
      for (var j = 0; j <= this.props.homeModule.skills.length - 1; j++) {
        for (var i = 0; i <= this.state.iconJson.length - 1; i++) {
          if (this.props.homeModule.skills[j].function == this.state.iconJson[i].key) {
            if (j == 0) {
              //this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon;
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].standard2_icon
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard2_icon
              }
            } else {
              if (this.props.homeModule.skills[j].label == 'chat2') {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i + 1].standard1_icon
              } else {
                this.props.homeModule.skills[j].icon = this.state.iconJson[i].standard1_icon
              }
            }
            this.props.dispatch({
              type: 'CHANGE_MODULE_SKILL',
              data: {
                type: 'icon',
                index: j,
                key: 4,
                content: this.props.homeModule.skills[j],
              },
            })
            break
          }
        }
      }
    }

    // 有配置，就是用配置里的背景 // 简约式除外
    if (this.props.homeModule.templateInfo.background && this.props.homeModule.templateInfo.background.length > 44) {
      backUrl = this.props.homeModule.templateInfo.background
      this.state.backImg == '' &&
        this.setState({
          //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
          backImg: backUrl,
        })
    }
    if (this.props.homeModule.templateInfo.templateCode == 'Simple' && this.state.SimplebackImg) {
      backUrl = this.state.SimplebackImg
    }

    // 有配置，就是用配置里的faceBackground背景 // 简约式除外
    if (this.state.faceBackground && this.state.faceBackground.length > 0) {
      let _faceBackground = this.state.faceBackground || this.props.homeModule.templateInfo.faceBackground
      this.state.faceBackground == '' &&
        this.setState({
          //修复简洁式背景保存后，切到其他场景再切回来，背景被重置
          faceBackground: _faceBackground,
        })

      if (_faceBackground) {
        console.log('改背景 1')
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            key: 1,
            type: 'faceBackground',
            content: _faceBackground,
          },
        })
      }
    }

    //切换背景图
    if (this.state.backImg != '') {
      backUrl = this.state.backImg
    }
    if (this.props.homeModule.templateInfo.templateCode == 'Simple' && this.state.SimplebackImg) {
      backUrl = this.state.SimplebackImg
    }
    console.log('改背景 4')
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'background',
        content: backUrl,
      },
    })

    closeModal()
    if (
      this.isKTV &&
      this.props.homeModule.templateInfo.templateCode != 'Simple' &&
      !this.state.lowOpkVersion &&
      !this.state.isDefault
    ) {
      this.setState({
        templateNeedUpdate: true,
      })
      modal({
        title: '提示',
        fullscreen: false,
        content: (
          <div>
            <p>首页模板已更新，是否需要切换</p>
            <p>否则将不能在此平台配置，请跳转到原后台</p>
          </div>
        ),
        cancel: false,
        confirm: '确认切换',
        onConfirm: () => {
          this.setState(
            {
              templateNeedUpdate: false,
            },
            () => {
              this.choosemodalStyle('Simple', 'KTV', 'chs')
            },
          )
        },
      })
      let lowOpkVersion = this.state.lowOpkVersion
      let isDefault = this.state.isDefault
      // window.tipSetData({
      //     templateNeedUpdate: true,
      //     lowOpkVersion,
      //     isDefault
      // })
    } else {
      let lowOpkVersion = this.state.lowOpkVersion
      let isDefault = this.state.isDefault
      // window.tipSetData({
      //     templateNeedUpdate: false,
      //     lowOpkVersion,
      //     isDefault
      // })
    }
  }
  deleteBackImg() {
    this.setState({
      backImg: '',
    })
    if (this.props.homeModule.templateInfo.templateCode == 'Simple') {
      this.setState({
        SimplebackImg: '',
      })
    }
  }
  deleteFaceImg() {
    this.setState({
      faceBackground: '',
    })
  }

  checkJson(kind) {
    if (kind != 'bottomQueries') {
      this.setState({
        queryStyle: '',
        backImgstyle: '',
        queries_idx: 0,
      })
    }
    if (JSON.stringify(this.props.robots) == '{}') {
      message.error(__('customHome.modalMsg.noRobotsOfCompany'))
      return
    }
    let json = this.props.homeModule
    //console.log(json);
    let title = json.templateInfo.titleBarContent
    let flag = true
    if (title.trim() == '') {
      message.error(__('customHome.modalMsg.enterTitleName'))
      flag = false
      return
    }
    var standardQueries = []
    json.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
    })
    standardQueries.map((item, index) => {
      // if (item == "") {
      if (item.trim() == '') {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))
        flag = false
        return
      }
    })
    if (this.state.overVersion51 && json.templateInfo.bubbleContents && json.templateInfo.bubbleContents.faceStyle) {
      if (json.templateInfo.bubbleContents.robot && json.templateInfo.bubbleContents.faceStyle == 'Bubble') {
        if (json.templateInfo.bubbleContents.robot.length == 0) {
          message.error(__('customHome.modalMsg.bubbleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.bubbleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
      }
    }
    // 配置英文卡片模式标题，内容不能为空
    if (this.state.overVersion57 && json.templateInfo.bubbleContents && json.templateInfo.bubbleContents.faceStyle) {
      if (json.templateInfo.bubbleContents.robot && json.templateInfo.bubbleContents.faceStyle == 'Card') {
        if (json.templateInfo.bubbleContents.robot.length == 0) {
          message.error(__('customHome.modalMsg.bubbleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.bubbleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
        if (json.templateInfo.bubbleContents.title.trim().length == 0) {
          message.error(__('customHome.modalMsg.cardTitleMsg'))
          // modal({
          //     content: __('customHome.modalMsg.cardTitleMsg'),
          //     cancel: false,
          //     confirm: __('customHome.btn.confirm')
          // });
          flag = false
          return
        }
      }
    }

    json.skills.map((item, index) => {
      // if (item.title.trim() == "") {
      // 	modal({
      // 		content: __('customHome.modalMsg.enterFunctionName'),
      // 		cancel: false,
      // 		confirm: __('customHome.btn.confirm')
      // 	});
      // 	flag = false
      // 	return;
      // }
      if (item.tip.trim() == '' || item.tip == undefined) {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))
        // modal({
        //     content: __('customHome.modalMsg.enterRecommendedQuestions'),
        //     cancel: false,
        //     confirm: __('customHome.btn.confirm')
        // });
        flag = false
        return
      }
    })

    if (json.templateInfo.templateCode == 'Simple') {
      if (json.templateInfo.conciseContent.title.trim() == '') {
        message.error(__('customHome.modalMsg.enterTitleName'))

        flag = false
        return
      } else if (json.templateInfo.conciseContent.suggestion.trim() == '') {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))

        flag = false
        return
      }
    }
    return flag
  }

  bottomQueries(queries_style, idx) {
    if (queries_style == 'mainPage') {
      this.setState({
        queryStyle: '',
      })
    } else {
      this.setState({
        queryStyle: queries_style,
      })
    }
    this.setState({
      queries_idx: idx,
    })
  }
  changeTab_backImg(j) {
    if (j == 1) {
      this.setState({
        backImgstyle: 'face',
      })
    } else {
      this.setState({
        backImgstyle: '',
      })
    }
  }
  addCover(flag) {
    this.setState({
      // fiveTimer: flag,
      haveCover: flag,
    })
  }
  checkAllQuery(cb) {
    this.setState({
      showLoading: true,
    })
    closeModal()
    let existed = false
    let questions = [],
      questionList = []
    let json = this.props.homeModule
    var standardQueries = []
    json.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
        standardQueries.map((item, index) => {
          if (item != '') {
            questions.push(item)
          }
        })
      }
    })
    if (json.templateInfo.templateCode != 'Simple') {
      json.skills.map((item, index) => {
        if (item.tip != '' && item.tip != undefined) {
          questions.push(item.tip)
        }
      })
    }

    let data = {
      question_list: JSON.stringify(new Set(questions)),
    }
    searchAnswer(data)
      .then((res) => {
        console.log(res)
        res.items.map((item) => {
          if (!item.existed) {
            existed = true
            questionList.push(item.question)
          }
        })
        this.setState({
          showLoading: false,
        })
        console.log(questionList)
        if (existed) {
          // modal({
          //     title: __('customHome.modalMsg.noAnswersInHomePage'),
          //     content: <QueryItem questionList={questionList}></QueryItem>,
          //     cancel: false,
          //     confirm: false,
          // })
        } else {
          cb && cb()
        }
      })
      .catch({})
  }
  changeRobotData(id, sn) {
    if (this.state.currentRobotId != id) {
      this.simple_config_json = {} //重置缓存的配置
      this.common_config_json = {}
    }
    this.setState({
      currentRobotId: id,
      currentRobotSn: sn,
      templateNeedUpdate: false,
      lowOpkVersion: false,
    })
  }
  changeRobot(id) {
    this.props.dispatch(selectRobot(id))
    console.log('机器人id@@@@', id)
    const robotType = this.state.robotType,
      roleStr = this.state.roleStr != undefined && this.state.roleStr
    if (robotType == 'local') {
      setLocalStorageItem('localRobotId', id)
    } else {
      setLocalStorageItem('localRobotId', id)
      setLocalStorageItem('globallRobotId', id)
      if (roleStr == '0' || roleStr == '1') {
        setLocalStorageItem('receptionRobotId', id)
      }
      if (roleStr == '2') {
        setLocalStorageItem('docentRobotId', id)
      }
    }
    this.getRobotName(id)
  }
}
