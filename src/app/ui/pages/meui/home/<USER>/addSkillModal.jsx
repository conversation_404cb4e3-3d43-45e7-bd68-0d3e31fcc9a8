import React from 'react'
import modal, { close as closeModal } from '@utils/modal'
// import { connect } from 'react-redux'
import { ButtonSelectFile } from 'biz-components'
import { uploadResource } from '@api/_home'
// import { CHANGE_MODULE_SKILL } from '@redux/action-types'
import recommendQues from '@api/_home/recommendQues'
import { extend } from 'koot'
import { message } from 'antd'
@extend({
  styles: require('./addSkillModal.less'),
})

// @connect(state => {
//     if (__DEV__) console.log('页面：自定义首页', state._home)
//     return {

//     }
// })
class AddSkill extends React.Component {
  constructor(props) {
    super(props)
    let _defaultPic = ''
    switch (this.props.modalStyle) {
      case 'List':
        _defaultPic = 'module_public/module_skill_home/List1_interview.png'
        break
      case 'Card':
        _defaultPic = 'module_public/module_skill_home/Card_interview.png'
        break
      case 'Standard':
        _defaultPic = 'module_public/module_skill_home/Standard1_interview.png'
        break
      default:
        _defaultPic = 'module_public/module_skill_home/List1_interview.png'
    }
    this.state = {
      funcConf: recommendQues.funcConfig,
      recmmendQuesList: recommendQues.recommendQues,
      allQuesList: recommendQues.allQuesList,
      whichChat: '',
      skill: {
        title: '',
        icon: _defaultPic,
        // tip: __('customHome.qa.appointment'),
        tip: '我来找人',
        function: 'interview',
        display: true,
      },
      queries: recommendQues.allQuesList[0],
      showQueryList: false,
      showQueryTips: true,
      showPicTips: false,
      picTips: '',
    }
  }
  render() {
    var image_url = this.props.devUrl + 'media/'
    if (this.props.modalStyle == 'List') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：110px*110px</span>
      var imageStyle = 'defaultImg  circleImg'
      var newIconStyle = 'newIcon_circle'
    } else if (this.props.modalStyle == 'Card') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：550px*380px</span>
      var imageStyle = 'defaultImg  rectangleImg1'
      var newIconStyle = 'newIcon_rectangle1'
    } else if (this.props.modalStyle == 'Standard') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：420px*230px</span>
      var imageStyle = 'defaultImg  rectangleImg2'
      var newIconStyle = 'newIcon_rectangle2'
    }
    var isComplete = false //判断即将新增的skill中数据是否完整
    for (let i in this.state.skill) {
      if (!this.state.skill[i]) {
        isComplete = false
        break
      } else {
        isComplete = true
      }
    }
    return (
      <div className={this.props.className}>
        <div className='setting'>
          <div className='changeQue'>
            {!this.props.addStyle || this.props.addStyle != 'queries' ? (
              <div className='list'>
                <span className='title'>{__('customHome.label.function')}</span>
                <select
                  className='chooseSetting'
                  value={this.state.skill.function}
                  onChange={(e) => {
                    console.log(e.target.selectedIndex)
                    let defaultPic = '',
                      labelObj = {}
                    if (e.target.selectedIndex == 3) {
                      labelObj.label = 'chat1'
                      this.setState({
                        whichChat: 1,
                      })
                    } else if (e.target.selectedIndex == 4) {
                      labelObj.label = 'chat2'
                      this.setState({
                        whichChat: 2,
                      })
                    } else {
                      this.setState({
                        whichChat: '',
                      })
                    }
                    if (this.props.modalStyle == 'List') {
                      defaultPic = this.state.funcConf[e.target.selectedIndex].list_icon1
                    } else if (this.props.modalStyle == 'Card') {
                      defaultPic = this.state.funcConf[e.target.selectedIndex].card_icon
                    } else if (this.props.modalStyle == 'Standard') {
                      defaultPic = this.state.funcConf[e.target.selectedIndex].standard1_icon
                    }
                    let skill = Object.assign(
                      {},
                      this.state.skill,
                      {
                        function: e.target.value,
                        tip: this.state.recmmendQuesList[e.target.value][0],
                        icon: defaultPic,
                      },
                      labelObj,
                    )
                    this.setState({
                      funcIndex: e.target.selectedIndex,
                      skill: skill,
                    })
                  }}
                >
                  {this.state.funcConf.map((item, index) => (
                    <option key={index} value={item.key}>
                      {item.name}
                    </option>
                  ))}
                </select>
              </div>
            ) : (
              ''
            )}
            {!this.props.addStyle || this.props.addStyle != 'queries' ? (
              <div className='list'>
                <span className='title'>{__('customHome.label.icon')}</span>
                <div
                  className={
                    this.state.skill.icon !=
                    'module_public/module_skill_home/' +
                      this.props.modalStyle +
                      (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
                      '_' +
                      this.state.skill.function +
                      this.state.whichChat +
                      '.png'
                      ? 'upload_area list_area'
                      : 'list_area'
                  }
                >
                  <ButtonSelectFile
                    className={this.props.modalStyle == 'List' ? 'upload-btn  circle' : 'upload-btn  square'}
                    onSelect={(evt) => {
                      if (this.props.modalStyle == 'List') {
                        this.selectImg(evt, 'list_icon1')
                      } else if (this.props.modalStyle == 'Card') {
                        this.selectImg(evt, 'card_icon')
                      } else if (this.props.modalStyle == 'Standard') {
                        this.selectImg(evt, 'standard1_icon')
                      }
                    }}
                  />
                  {this.state.skill.icon !=
                  'module_public/module_skill_home/' +
                    this.props.modalStyle +
                    (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
                    '_' +
                    this.state.skill.function +
                    this.state.whichChat +
                    '.png' ? (
                    <img className={newIconStyle} src={image_url + this.state.skill.icon} alt='' />
                  ) : (
                    ''
                  )}
                  <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
                </div>
                <div
                  className='list_area'
                  onClick={() => {
                    let newSkill = Object.assign({}, this.state.skill, {
                      icon:
                        'module_public/module_skill_home/' +
                        this.props.modalStyle +
                        (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
                        '_' +
                        this.state.skill.function +
                        this.state.whichChat +
                        '.png',
                    })
                    this.setState({
                      skill: newSkill,
                    })
                  }}
                >
                  <img
                    src={
                      image_url +
                      'module_public/module_skill_home/' +
                      this.props.modalStyle +
                      (this.props.modalStyle == 'List' || this.props.modalStyle == 'Standard' ? '1' : '') +
                      '_' +
                      this.state.skill.function +
                      this.state.whichChat +
                      '.png'
                    }
                    className={imageStyle}
                  />
                  <span className='defaultInfo'>{__('customHome.pic.default')}</span>
                </div>
                <div className='list_area'>
                  <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
                  {imageSizeTip}
                  <span className='uploadTips'>
                    2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
                  </span>
                  <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
                </div>
                {this.state.showPicTips ? <span className='pic_tips'>{this.state.picTips}</span> : ''}
              </div>
            ) : (
              ''
            )}
            {!this.props.addStyle || this.props.addStyle != 'queries' ? (
              <div className='list'>
                <span className='title'>{__('customHome.label.name')}</span>
                <input
                  type='text'
                  placeholder=''
                  maxLength='8'
                  onChange={(e) => {
                    let skill = Object.assign({}, this.state.skill, { title: e.target.value })
                    this.setState({
                      skill: skill,
                    })
                  }}
                />
                <span className='word-len-tips'>{`${this.state.skill.title.length} / 8`}</span>
              </div>
            ) : (
              ''
            )}
            {!this.props.addStyle || this.props.addStyle != 'queries' ? (
              <div className='list'>
                {this.state.showQueryTips ? (
                  <span className='input_tips show_input_tips'>{__('customHome.warm.msg4')}</span>
                ) : (
                  ''
                )}
                <span className='title'>{__('customHome.label.question')}</span>
                {/* <select className="chooseQuestion" value={this.state.skill.tip} onChange={(e) => {
                                    let skill = Object.assign({}, this.state.skill, { tip: e.target.value })
                                    this.setState({
                                        skill: skill
                                    })
                                }
                                }>
                                    {
                                        this.state.recmmendQuesList[this.state.skill.function].map((item, index) => (
                                            <option key={index}>{item}</option>
                                        ))
                                    }
                                    {
                                        this.state.recmmendQuesList[this.state.skill.function].map((item, index) => (
                                            <option key={index}>{`小豹，${item}`}</option>
                                        ))
                                    }
                                </select> */}
                <div
                  className='query-set'
                  onMouseEnter={() => {
                    this.setState({
                      showQueryList: true,
                    })
                  }}
                  onMouseLeave={() => {
                    setTimeout(() => {
                      this.setState({
                        showQueryList: false,
                      })
                    }, 200)
                  }}
                >
                  <input
                    className='queryInput'
                    type='text'
                    maxLength='11'
                    value={this.state.skill.tip}
                    onInput={(e) => {
                      this.setState({
                        showQueryTips: true,
                      })
                    }}
                    onChange={(e) => {
                      let skill = Object.assign({}, this.state.skill, { tip: e.target.value })
                      this.setState({
                        skill: skill,
                      })
                    }}
                    name=''
                    id=''
                  />
                  <span className='word-len-tips'>{`${this.state.skill.tip.length} / 11`}</span>
                </div>
              </div>
            ) : (
              ''
            )}
            {!this.props.addStyle || this.props.addStyle != 'queries' ? (
              <button
                disabled={!isComplete}
                className={!isComplete ? 'addSkillBtn disBtn' : 'addSkillBtn'}
                onClick={() => {
                  this.props.newAddSkill(this.state.skill)
                }}
              >
                {__('customHome.btn.confirm')}
              </button>
            ) : (
              ''
            )}
            {this.props.addStyle && this.props.addStyle == 'queries' ? (
              <div className='list'>
                {this.state.showQueryTips ? (
                  <span className='input_tips show_input_tips'>{__('customHome.warm.msg4')}</span>
                ) : (
                  ''
                )}
                <span className='title'>{__('customHome.label.question')}</span>
                {/* <select className="chooseQuestion" size="10"
                             onChange={(e) => {
                                this.setState({
                                    queries: e.target.value
                                })  
                            }}>
                                {
                                    this.state.allQuesList.map((item, index) => (
                                        <option key={index}>{item}</option>
                                    ))
                                }
                            </select> */}
                <div
                  className='query-set'
                  onMouseEnter={() => {
                    this.setState({
                      showQueryList: true,
                    })
                  }}
                  // onMouseLeave={() => {
                  //     setTimeout(() => {
                  //         this.setState({
                  //             showQueryList: false
                  //         })
                  //     }, 200)
                  // }}
                >
                  <input
                    className='queryInput'
                    type='text'
                    maxLength='11'
                    value={this.state.queries}
                    onInput={() => {
                      this.setState({
                        showQueryTips: true,
                      })
                    }}
                    onChange={(e) => {
                      this.setState({
                        queries: e.target.value,
                      })
                    }}
                    name=''
                    id=''
                  />
                  <span className='word-len-tips'>{`${this.state.queries.length} / 11`}</span>
                </div>
              </div>
            ) : (
              ''
            )}
            {this.props.addStyle && this.props.addStyle == 'queries' ? (
              <button
                className={this.state.queries == '' ? 'addSkillBtn disBtn' : 'addSkillBtn'}
                onClick={() => {
                  if (this.state.queries == '') {
                    return
                  }
                  this.props.newAddQueries(this.state.queries)
                }}
              >
                {__('customHome.btn.confirm')}
              </button>
            ) : (
              ''
            )}
          </div>
        </div>
      </div>
    )
  }
  selectImg(e, type) {
    console.log(e)
    console.log(type)
    const file = e.target.files[0]
    let reader = new FileReader()
    var uploadImgWidth = 0,
      uploadImgHeight = 0
    if (!file) {
      return
    }
    if (file.type != 'image/png' && file.type != 'image/jpeg') {
      // modal({
      //     content: '请选择png jpg文件上传',
      //     cancel: false,
      //     confirm:  __('customHome.btn.gotIt')
      // })
      this.setState({
        picTips: __('customHome.pic.wrongPictureFormat'),
        showPicTips: true,
      })
      return
    } else {
      this.setState({
        picTips: '',
        showPicTips: false,
      })
    }
    switch (type) {
      case 'card_icon':
        uploadImgWidth = 550
        uploadImgHeight = 380
        break
      case 'list_icon1':
        uploadImgWidth = 110
        uploadImgHeight = 110
        break
      case 'standard1_icon':
        uploadImgWidth = 420
        uploadImgHeight = 230
        break
    }
    if (file.size / 1024 / 1000 > 5) {
      // modal({
      //     content: '请选择5M以内的图片',
      //     cancel: false,
      //     confirm:  __('customHome.btn.gotIt')
      // })
      this.setState({
        picTips: __('customHome.pic.picSizeLimit'),
        showPicTips: true,
      })
      return
    } else {
      this.setState({
        picTips: '',
        showPicTips: false,
      })
    }
    var _this = this
    reader.onload = function (e) {
      let image = new Image()
      image.onload = function () {
        if (image.width != uploadImgWidth || image.height != uploadImgHeight) {
          console.log(image.width)
          console.log(image.height)
          // return modal({
          //     title: __('customHome.pic.WrongPictureDimensions2'),
          //     content: '请上传图片尺寸大小:' + uploadImgWidth + '*' + uploadImgHeight + '像素',
          //     cancel: false,
          //     confirm:  __('customHome.btn.gotIt')
          // })
          _this.setState({
            picTips: __('customHome.pic.WrongPictureDimensions'),
            showPicTips: true,
          })
        } else {
          _this.setState({
            picTips: '',
            showPicTips: false,
          })
          let formData = new FormData()
          formData.append('resource_file', file)
          formData.append('module_code', 'module_skill_home')
          formData.append('config_id', sessionStorage.getItem('config_id'))
          formData.append('resource_type', 'image')
          formData.append('resource_file', file.name)
          formData.append('version', '')
          uploadResource(formData)
            .then((res) => {
              console.log('上传图片')
              let upload_url = res.resource_url
              console.log(upload_url)
              let skill = Object.assign({}, _this.state.skill, { icon: upload_url })
              _this.setState({
                skill: skill,
              })
              // modal({
              //     content: '上传成功'
              // });
            })
            .catch((res) => {
              console.log(res)
              message.error(__('customHome.pic.uploadFailed'))
              {
                /* modal({
                                content: __('customHome.pic.uploadFailed'),
                                cancel: false,
                                confirm: __('customHome.btn.gotIt')
                            }) */
              }
            })
        }
      }
      image.src = e.target.result
    }
    reader.readAsDataURL(e.target.files[0])
  }
}
export default AddSkill
export { AddSkill }
