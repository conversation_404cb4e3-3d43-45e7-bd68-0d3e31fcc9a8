import React from 'react'
import { connect } from 'react-redux'
import { ButtonSelectFile } from 'biz-components'
import { CHANGE_MODULE_SKILL, CHANGE_MODULE_QUERIES } from '@redux/action-types'
import recommendQues from '@api/_home/_gov/recommendQues'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页', state._home)
  return {
    skills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
    editIndex: state._home.editIndex || 0,
  }
})
class Question extends React.Component {
  state = {
    funcConf: recommendQues.funcConfig,
    selectedIndex: this.props.editIndex,
    defaultPIC: '',
    showQueryList: false,
  }
  render() {
    var image_url = this.props.devUrl + 'media/'
    let defaultSkill = {}
    let defaultPIC = '',
      defaultPIC2 = ''
    for (let i = 0; i < this.state.funcConf.length; i++) {
      if (this.state.funcConf[i].key == this.props.skills[this.props.editIndex].function) {
        defaultSkill = this.state.funcConf[i]
        break
      }
    }

    var standardQueries = []
    this.props.queries.forEach((item) => {
      if (item.name == 'governmentMain') {
        standardQueries = item.query
      }
    })
    if (this.props.types == 'gov_l') {
      defaultPIC = defaultSkill.icon_l
    } else {
      defaultPIC = defaultSkill.icon_s
    }

    if (this.props.types == 'gov_l') {
      var imageStyle = 'defaultImg  bigImg'
      var newIconStyle = 'newIcon_big'
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：1084px*300px</span>
    } else {
      var imageStyle = 'defaultImg  smallImg'
      var newIconStyle = 'newIcon_small'
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：520px*220px</span>
    }
    return (
      <div className='changeQue'>
        {this.props.types != 'government_query' ? (
          <div className='list'>
            <span className='title'>{__('customHome.label.function')}</span>
            <select
              className='chooseSetting'
              value={this.props.skills[this.props.editIndex].function}
              onChange={(e) => {
                console.log(e.target)
                let fun = e.target.value
                this.setState({
                  selectedIndex: e.target.selectedIndex,
                })
                ////更换function
                this.props.skills[this.props.editIndex].function = fun
                this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'function',
                    index: this.props.editIndex,
                    content: this.props.skills[this.props.editIndex],
                  },
                })
                if (this.props.types == 'gov_l') {
                  defaultPIC2 = this.state.funcConf[e.target.selectedIndex].icon_l
                } else {
                  defaultPIC2 = this.state.funcConf[e.target.selectedIndex].icon_s
                }
                this.props.skills[this.props.editIndex].icon = defaultPIC2
                this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'icon',
                    key: 19,
                    index: this.props.editIndex,
                    content: this.props.skills[this.props.editIndex],
                  },
                })
              }}
            >
              {this.state.funcConf.map((item, index) => (
                <option key={index} value={item.key}>
                  {item.name}
                </option>
              ))}
            </select>
          </div>
        ) : (
          ''
        )}
        {this.props.types != 'government_query' ? (
          <div className='list'>
            <span className='title'>{__('customHome.label.icon')}</span>
            <p
              className={
                this.props.skills[this.props.editIndex].icon != defaultPIC ? 'list_area upload_area' : 'list_area'
              }
            >
              <ButtonSelectFile
                className='upload-btn  square'
                onSelect={(evt) => {
                  if (this.props.types == 'gov_l') {
                    this.props.selectImg(evt, 'gov_bigIcon', this.props.editIndex)
                  } else {
                    this.props.selectImg(evt, 'gov_smallIcon', this.props.editIndex)
                  }
                }}
              />
              <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
              {this.props.skills[this.props.editIndex].icon != defaultPIC ? (
                <img src={image_url + this.props.skills[this.props.editIndex].icon} className={newIconStyle} />
              ) : (
                ''
              )}
            </p>
            <p
              className='list_area default_area'
              onClick={() => {
                console.log(defaultPIC)
                this.props.skills[this.props.editIndex].icon = defaultPIC
                this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'icon',
                    key: 20,
                    index: this.props.editIndex,
                    content: this.props.skills[this.props.editIndex],
                  },
                })
              }}
            >
              <img src={image_url + defaultPIC} className={imageStyle} />
              <span className='defaultInfo'>{__('customHome.pic.default')}</span>
            </p>
            <p className='list_area'>
              <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
              {imageSizeTip}
              <span className='uploadTips'>
                2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
              </span>
              <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
            </p>
          </div>
        ) : (
          ''
        )}
        {this.props.types != 'government_query' ? (
          <div className='list listFunTitle'>
            <span className='title'>{__('customHome.label.name')}</span>
            <input
              type='text'
              placeholder=''
              maxLength='6'
              value={this.props.skills[this.props.editIndex].title}
              onChange={(e) => {
                if (this.props.skills[this.props.editIndex].title == e.target.value.replace(/\s+/g, '')) {
                  return
                }
                this.props.skills[this.props.editIndex].title = e.target.value
                this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'title',
                    index: this.props.editIndex,
                    content: this.props.skills[this.props.editIndex],
                  },
                })
              }}
            />
            <span className='word-len-tips'>{`${this.props.skills[this.props.editIndex].title.length} / 6`}</span>
          </div>
        ) : (
          ''
        )}
        {this.props.types == 'government_query' ? (
          <div className='list' style={{ marginTop: '-30px' }}>
            <span className={this.props.showInputTips ? 'input_tips show_input_tips' : 'input_tips hide_input_tips'}>
              {__('customHome.warm.msg4')}
            </span>
            <span className='title'>{__('customHome.label.question')}</span>
            <div
              className='query-set'
              onMouseEnter={() => {
                this.setState({
                  showQueryList: true,
                })
              }}
              onMouseLeave={() => {
                setTimeout(() => {
                  this.setState({
                    showQueryList: false,
                  })
                }, 200)
              }}
            >
              <input
                className='queryInput'
                type='text'
                maxLength='11'
                value={standardQueries[this.props.queriesIndex]}
                onInput={(e) => {
                  this.props.changeInputTips(true)
                }}
                onChange={(e) => {
                  if (standardQueries[this.props.queriesIndex] == e.target.value.replace(/\s+/g, '')) {
                    return
                  }
                  this.props.dispatch({
                    type: 'CHANGE_MODULE_QUERIES',
                    data: {
                      index: this.props.queriesIndex,
                      content: e.target.value,
                      modalStyle: 'government',
                    },
                  })
                }}
              />
              <span className='word-len-tips'>{`${standardQueries[this.props.queriesIndex].length} / 11`}</span>
            </div>
          </div>
        ) : (
          ''
        )}
      </div>
    )
  }
}
export default Question
export { Question }
