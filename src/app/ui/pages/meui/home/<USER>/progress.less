@import '~base.less';

.component {
  p {
    margin: 0;
    padding: 0;
  }

  .progress_modal {
    .progress_title {
      font-size: 20px;
      color: #555d61;
      letter-spacing: 0;
      text-align: center;
      line-height: 26px;
      margin-bottom: 26px;
    }
    .percentBg {
      height: 5px;
      background: #e1e7ea;
      width: 350px;
      border-radius: 0.15625rem;
    }
    .percentNow {
      height: 10px;
      margin-top: -7.5px;
      width: 20px;
      background: #55c3fb;
      border-radius: 0.3125rem;
      animation: percent 40s forwards;
      -webkit-animation: percent 40s forwards;
      animation-timing-function: ease;
      -webkit-animation-timing-function: ease;
    }
    @keyframes percent {
      from {
        width: 0;
      }
      to {
        width: 100%;
      }
    }

    @-webkit-keyframes percent {
      from {
        width: 0;
      }
      to {
        width: 100%;
      }
    }
  }
}
