/* eslint-disable indent */
import React from 'react'
import { connect } from 'react-redux'
import { ButtonSelectFile } from 'biz-components'
import classNames from 'classnames'
import { Upload } from 'antd'
import ImgCrop from 'antd-img-crop'
import 'antd/es/modal/style'
import 'antd/es/slider/style'

import QueryList from './queryList'
import { CHANGE_MODULE_SKILL, CHANGE_MODULE_QUERIES } from '@redux/action-types'
import recommendQues from '@api/_home/recommendQues'
import { getWebQuery, searchAnswer } from '@api/_home'
import { Icon } from 'biz-components'
import AddItem from './addItem'
import modal, { close as closeModal } from '@utils/modal'
import { inputMaxCnQ, inputMaxEnQ, inputMaxCnA, inputMaxEnA } from '@utils/tools'

@connect((state) => {
  return {
    skills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
    editIndex: state._home.editIndex || 0,
    curLang: state.user.curLang,
  }
})
class Question extends React.Component {
  state = {
    // funcConf: this.props.lang=='english'?recommendQues.funcConfigEng:recommendQues.funcConfig,
    funcConf:
      this.props.lang !== 'zh_CN'
        ? recommendQues.funcConfigEng
        : this.props.lang === 'zh_CN'
        ? recommendQues.funcConfig
        : recommendQues.funcConfigArabic,
    // recmmendQuesList: this.props.lang=='english'?recommendQues.recommendQuesEng:recommendQues.recommendQues,
    recmmendQuesList:
      this.props.lang !== 'zh_CN'
        ? recommendQues.recommendQuesEng
        : this.props.lang === 'zh_CN'
        ? recommendQues.recommendQues
        : recommendQues.recommendQuesArabic,
    allQuesList: recommendQues.allQuesList,
    selectedIndex: this.props.editIndex,
    defaultPIC: '',
    showQueryList: false,
    isShowFuncWordTip: false,
    isShowQueryWordTip: false,
    lang: this.props.lang || 'chs',
    maxFunLength: this.props.lang !== 'zh_CN' ? inputMaxEnQ : inputMaxCnQ,
    maxRecLength: this.props.lang !== 'zh_CN' ? inputMaxEnA : inputMaxCnA,
  }
  componentDidMount() {
    const formData = new FormData()
    formData.append('lang', this.props.curLang.lang_code)
    getWebQuery(formData).then((res) => {
      let webQuery = [],
        appQuery = []
      res.items.map((item, index) => {
        if (item.type == 0) {
          webQuery.push(item.norm_word)
          item.similar_words.map((el, id) => {
            webQuery.push(el)
          })
        } else {
          appQuery.push(item.norm_word)
          item.similar_words.map((el, id) => {
            appQuery.push(el)
          })
        }
      })
      const web_set = new Set(webQuery)
      webQuery = [...web_set]
      const app_set = new Set(appQuery)
      appQuery = [...app_set]
      this.state.recmmendQuesList['web'] = webQuery
      this.state.recmmendQuesList['open_app'] = appQuery
      this.setState(
        {
          recmmendQuesList: this.state.recmmendQuesList,
        },
        () => {
          console.log(this.state.recmmendQuesList, 'this.state.recmmendQuesList')
        },
      )
    })
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    console.log(111, nextProps.lang)
    let maxFunLength = 8,
      maxRecLength = 11,
      showQueryList = true
    if (this.props.lang && nextProps.lang) {
      maxFunLength = nextProps.lang !== 'zh_CN' ? inputMaxEnQ : inputMaxCnQ
      maxRecLength = nextProps.lang !== 'zh_CN' ? inputMaxEnA : inputMaxCnA
      showQueryList = nextProps.lang !== 'zh_CN' ? false : true
      this.setState({
        lang: nextProps.lang,
        maxFunLength,
        maxRecLength,
        showQueryList,
        funcConf: recommendQues.funcConfig,
      })
    }
  }
  checkQuery(txt) {
    if (txt == '' || txt == undefined) return
    let questions = []
    questions.push(txt)
    let data = {
      question_list: JSON.stringify(questions),
    }
    searchAnswer(data)
      .then((res) => {
        if (!res.items[0].existed) {
          modal({
            title: __('customHome.modalMsg.msg5'),
            confirm: false,
            cancel: false,
            fullscreen: false,
            content: <AddItem questions={questions} />,
          })
        }
      })
      .catch({})
  }
  render() {
    var image_url = this.props.devUrl + 'media/'
    let defaultSkill = {}
    let defaultPIC = '',
      defaultPIC2 = ''
    for (let i = 0; i < this.state.funcConf.length; i++) {
      if (this.state.funcConf[i].key == this.props.skills[this.props.editIndex].function) {
        if (this.props.skills[this.props.editIndex].label == 'chat2') {
          //聊天
          defaultSkill = this.state.funcConf[i + 1]
        } else {
          defaultSkill = this.state.funcConf[i]
        }
        break
      }
    }

    var standardQueries = []
    this.props.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
    })
    let sizeRule = {}
    if (this.props.types == 'list_icon1') {
      var imageStyle = 'defaultImg  squareImg'
      var newIconStyle = 'newIcon_square'
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：490px*260px</span>
      defaultPIC = defaultSkill.list_icon2
      sizeRule = { width: 490, height: 260 }
    } else if (this.props.types == 'list_icon2') {
      var imageStyle = 'defaultImg  circleImg'
      var newIconStyle = 'newIcon_circle'
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：110px*110px</span>
      defaultPIC = defaultSkill.list_icon1
      sizeRule = { width: 110, height: 110 }
    } else if (this.props.types == 'card_icon') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：550px*380px</span>
      var imageStyle = 'defaultImg  rectangleImg1'
      var newIconStyle = 'newIcon_rectangle1'
      defaultPIC = defaultSkill.card_icon
      sizeRule = { width: 550, height: 380 }
    } else if (this.props.types == 'standard1_icon') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：420px*230px</span>
      var imageStyle = 'defaultImg  rectangleImg2'
      var newIconStyle = 'newIcon_rectangle2'
      defaultPIC = defaultSkill.standard1_icon
      sizeRule = { width: 420, height: 230 }
    } else if (this.props.types == 'standard2_icon') {
      var imageSizeTip = <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：392px*480px</span>
      var imageStyle = 'defaultImg  rectangleImg3'
      var newIconStyle = 'newIcon_rectangle3'
      defaultPIC = defaultSkill.standard2_icon
      sizeRule = { width: 392, height: 480 }
    }

    return (
      <div className={classNames({ changeQue: true })}>
        {this.props.types != 'standard1' ? (
          <div className='list'>
            <span className='prompt'>
              <Icon icon='Alert_Default_Info' className='prompt_tips' />
              {__('customHome.warm.msg2')}
            </span>
            <span className='title abc'>{__('customHome.label.function')}</span>
            {
              <select
                className='chooseSetting'
                value={
                  this.props.skills[this.props.editIndex].function == 'chat'
                    ? this.props.skills[this.props.editIndex].label
                    : this.props.skills[this.props.editIndex].function
                }
                onChange={(e) => {
                  let fun = e.target.value == 'chat1' || e.target.value == 'chat2' ? 'chat' : e.target.value
                  let tit =
                    e.target.value == 'chat1' || e.target.value == 'chat2'
                      ? this.props.editIndex == 0
                        ? 'check in'
                        : 'check out'
                      : this.props.editIndex == 0
                      ? 'Navigation'
                      : 'check out'
                  let tip =
                    e.target.value == 'chat1' || e.target.value == 'chat2'
                      ? this.props.editIndex == 0
                        ? 'How to check in'
                        : 'How to check out'
                      : this.props.editIndex == 0
                      ? 'Take me to toilet'
                      : 'Take me to XXX'
                  this.setState({
                    selectedIndex: e.target.selectedIndex,
                  })
                  //更换function
                  this.props.skills[this.props.editIndex].function = fun
                  this.props.dispatch({
                    type: 'CHANGE_MODULE_SKILL',
                    data: {
                      type: 'function',
                      index: this.props.editIndex,
                      content: this.props.skills[this.props.editIndex],
                    },
                  })

                  // 更换title
                  this.props.skills[this.props.editIndex].title =
                    this.props.lang == 'chs' || this.props.lang == 'english'
                      ? this.props.skills[this.props.editIndex].title
                      : this.props.editIndex != 0 && this.props.editIndex != 1
                      ? this.props.skills[this.props.editIndex].title
                      : tit
                  this.props.dispatch({
                    type: 'CHANGE_MODULE_SKILL',
                    data: {
                      type: 'title',
                      index: this.props.editIndex,
                      content: this.props.skills[this.props.editIndex],
                    },
                  })

                  //更换tip
                  // this.props.skills[this.props.editIndex].tip = this.state.recmmendQuesList[fun][0];
                  this.props.skills[this.props.editIndex].tip =
                    this.props.lang == 'chs' || this.props.lang == 'english'
                      ? this.state.recmmendQuesList[fun][0]
                      : this.props.editIndex != 0 && this.props.editIndex != 1
                      ? this.props.skills[this.props.editIndex].tip
                      : tip
                  this.props.dispatch({
                    type: 'CHANGE_MODULE_SKILL',
                    data: {
                      type: 'tip',
                      index: this.props.editIndex,
                      content: this.props.skills[this.props.editIndex],
                    },
                  })

                  if (e.target.value == 'chat1' || e.target.value == 'chat2') {
                    this.props.skills[this.props.editIndex]['label'] = e.target.value
                    this.props.dispatch({
                      type: 'CHANGE_MODULE_SKILL',
                      data: {
                        type: 'tip',
                        index: this.props.editIndex,
                        content: this.props.skills[this.props.editIndex],
                      },
                    })
                  } else if (this.props.skills[this.props.editIndex]['label']) {
                    delete this.props.skills[this.props.editIndex]['label']
                    this.props.dispatch({
                      type: 'CHANGE_MODULE_SKILL',
                      data: {
                        type: 'tip',
                        index: this.props.editIndex,
                        content: this.props.skills[this.props.editIndex],
                      },
                    })
                  }
                  //更改json图片
                  //由于执行顺序，不能使上以上defaultPIC
                  // for (let i = 0; i < this.state.funcConf.length; i++) {
                  // 	if (this.state.funcConf[i].key == this.props.skills[this.props.editIndex].function) {
                  // 		if (this.props.skills[this.props.editIndex].label == 'chat2') {	//聊天
                  // 			defaultSkill = this.state.funcConf[i + 1]
                  // 		} else {
                  // 			defaultSkill = this.state.funcConf[i]
                  // 		}
                  // 		break;
                  // 	}
                  // }
                  if (this.props.types == 'list_icon1') {
                    defaultPIC2 = this.state.funcConf[e.target.selectedIndex].list_icon2
                  } else if (this.props.types == 'list_icon2') {
                    defaultPIC2 = this.state.funcConf[e.target.selectedIndex].list_icon1
                  } else if (this.props.types == 'card_icon') {
                    defaultPIC2 = this.state.funcConf[e.target.selectedIndex].card_icon
                  } else if (this.props.types == 'standard1_icon') {
                    defaultPIC2 = this.state.funcConf[e.target.selectedIndex].standard1_icon
                  } else if (this.props.types == 'standard2_icon') {
                    defaultPIC2 = this.state.funcConf[e.target.selectedIndex].standard2_icon
                  }
                  this.props.skills[this.props.editIndex].icon = defaultPIC2
                  this.props.dispatch({
                    type: 'CHANGE_MODULE_SKILL',
                    data: {
                      type: 'icon',
                      key: 14,
                      index: this.props.editIndex,
                      content: this.props.skills[this.props.editIndex],
                    },
                  })
                }}
              >
                {this.state.funcConf.map((item, index) => {
                  if (item.name === 'Chat' || item.name === '聊天') {
                    return ''
                  }
                  return (
                    <option key={index} value={item.key == 'chat' ? item.label : item.key}>
                      {item.name}
                    </option>
                  )
                })}
              </select>
            }
          </div>
        ) : (
          ''
        )}
        {this.props.types != 'Technical' && this.props.types != 'standard1' ? (
          <div className='list'>
            <span className='title'>{__('customHome.label.icon')}</span>
            <p
              className={
                this.props.skills[this.props.editIndex].icon != defaultPIC ? 'list_area upload_area' : 'list_area'
              }
            >
              {/* <ButtonSelectFile
								className={this.props.types == 'list_icon2' ? 'upload-btn  circle' : 'upload-btn  square'}
								onSelect={(evt) => {
									if (this.props.types == 'list_icon1') {
										this.props.selectImg(evt, "list_icon1", this.props.editIndex)
									} else if (this.props.types == "list_icon2") {
										this.props.selectImg(evt, "list_icon2", this.props.editIndex)
									} else if (this.props.types == "card_icon") {
										this.props.selectImg(evt, "card_icon", this.props.editIndex)
									} else if (this.props.types == "standard1_icon") {
										this.props.selectImg(evt, "standard1_icon", this.props.editIndex)
									} else if (this.props.types == "standard2_icon") {
										this.props.selectImg(evt, "standard2_icon", this.props.editIndex)
									}
								}}
							/> */}
              <ImgCrop
                aspect={sizeRule.width / sizeRule.height}
                grid
                modalTitle={__('customHome.btn.img_cut')} //
                modalWidth='650px'
                modalOk={__('customHome.btn.confirm')}
                modalCancel={__('customHome.btn.cancel2')}
                fillColor={'transparent'}
              >
                <Upload
                  //   className="upload-btn square"
                  //   className={this.props.modalStyle == "List" ? 'upload-btn  circle' : 'upload-btn  square'}
                  customRequest={() => {}}
                  className={this.props.types == 'list_icon2' ? 'upload-btn  circle' : 'upload-btn  square'}
                  listType='picture-card'
                  beforeUpload={(e) => {
                    console.log(e)
                    let _this = this
                    let img = e
                    let reader = new FileReader()
                    reader.readAsDataURL(img)
                    reader.onload = function (e) {
                      let image = new Image()
                      image.onload = function () {
                        let mycanvas = document.querySelector('#myQueCanvas')
                        let ctx = mycanvas.getContext('2d')
                        ctx.drawImage(image, 0, 0, sizeRule.width, sizeRule.height)
                        let dataurl = mycanvas.toDataURL('image/png')
                        function dataURLtoBlob(dataurl) {
                          let arr = dataurl.split(','),
                            mime = arr[0].match(/:(.*?);/)[1],
                            bstr = atob(arr[1]),
                            n = bstr.length,
                            u8arr = new Uint8Array(n)
                          while (n--) {
                            u8arr[n] = bstr.charCodeAt(n)
                          }
                          return new Blob([u8arr], { type: mime })
                        }
                        // _this.props.selectImg(dataURLtoBlob(dataurl), "backImg", "")
                        // if (_this.state.tabIndex == 0) {
                        //     _this.props.selectImg(dataURLtoBlob(dataurl), "backImg", "")
                        // } else {
                        //     _this.props.selectImg(dataURLtoBlob(dataurl), "facebackImg", "")
                        // }

                        if (_this.props.types == 'list_icon1') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'list_icon1', _this.props.editIndex)
                        } else if (_this.props.types == 'list_icon2') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'list_icon2', _this.props.editIndex)
                        } else if (_this.props.types == 'card_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'card_icon', _this.props.editIndex)
                        } else if (_this.props.types == 'standard1_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'standard1_icon', _this.props.editIndex)
                        } else if (_this.props.types == 'standard2_icon') {
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'standard2_icon', _this.props.editIndex)
                        }
                      }
                      image.src = e.target.result
                    }
                    return true
                  }}
                  onChange={(e) => {
                    // console.log(e)
                  }}
                ></Upload>
              </ImgCrop>
              <canvas style={{ display: 'none' }} id='myQueCanvas' width={sizeRule.width} height={sizeRule.height} />
              <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
              {this.props.skills[this.props.editIndex].icon != defaultPIC ? (
                <img src={image_url + this.props.skills[this.props.editIndex].icon} className={newIconStyle} />
              ) : (
                ''
              )}
            </p>
            <p
              className='list_area default_area'
              onClick={() => {
                this.props.skills[this.props.editIndex].icon = defaultPIC
                this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'icon',
                    key: 15,
                    index: this.props.editIndex,
                    content: this.props.skills[this.props.editIndex],
                  },
                })
              }}
            >
              <img src={image_url + defaultPIC} className={imageStyle} />
              <span className='defaultInfo'>{__('customHome.pic.default')}</span>
            </p>
            <p className='list_area'>
              <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
              {imageSizeTip}
              <span className='uploadTips'>
                2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
              </span>
              <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
            </p>
          </div>
        ) : (
          ''
        )}
        {this.props.types != 'Technical' && this.props.types != 'standard1' ? (
          <div className='list listFunTitle'>
            <span className='title'>{__('customHome.label.name')}</span>
            <input
              type='text'
              placeholder={__('customHome.warm.msg3')}
              maxLength={this.state.maxFunLength}
              value={this.props.skills[this.props.editIndex].title}
              onFocus={() => {
                this.setState({ isShowFuncWordTip: true })
              }}
              onBlur={() => {
                this.setState({ isShowFuncWordTip: false })
              }}
              onChange={(e) => {
                // this.props.skills[this.props.editIndex].title = e.target.value.length == 0 ? this.props.skills[this.props.editIndex].title : e.target.value;
                if (
                  this.state.lang == 'chs' &&
                  this.props.skills[this.props.editIndex].title == e.target.value.replace(/\s+/g, '')
                ) {
                  return
                }
                this.props.skills[this.props.editIndex].title = e.target.value
                this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'title',
                    index: this.props.editIndex,
                    content: this.props.skills[this.props.editIndex],
                  },
                })
              }}
            />
            {this.state.isShowFuncWordTip ? (
              <span className={classNames({ 'word-len-tips': true })}>{`${
                this.props.skills[this.props.editIndex].title.length
              } / ${this.state.maxFunLength}`}</span>
            ) : (
              ''
            )}
          </div>
        ) : (
          ''
        )}
        {this.props.types != 'standard1' ? (
          <div className='list'>
            {/* <span className={this.props.showInputTips ? "input_tips show_input_tips" : "input_tips hide_input_tips"}>注意：请确保【手动输入】的推荐问法机器人能正常回答或使用，否则会错误引导访客！</span> */}
            <span className='title'>{__('customHome.label.question')}</span>
            <div
              className='query-set'
              onMouseEnter={() => {
                this.state.lang == 'chs' &&
                  this.setState({
                    showQueryList: true,
                  })
              }}
              onMouseLeave={() => {
                setTimeout(() => {
                  this.setState({
                    showQueryList: false,
                  })
                }, 200)
              }}
            >
              <input
                className='queryInput'
                type='text'
                maxLength={this.state.maxRecLength}
                placeholder={__('customHome.warm.msg3')}
                value={this.props.skills[this.props.editIndex].tip || ''}
                onInput={(e) => {
                  this.props.changeInputTips(true)
                }}
                onFocus={() => {
                  this.setState({ isShowQueryWordTip: true })
                }}
                onBlur={() => {
                  this.setState({ isShowQueryWordTip: false })
                  this.checkQuery(this.props.skills[this.props.editIndex].tip)
                }}
                onChange={(e) => {
                  // if (this.props.skills[this.props.editIndex].tip == e.target.value.replace(/\s+/g, "")) {
                  // 	return;
                  // }
                  this.props.skills[this.props.editIndex].tip = e.target.value
                  this.props.dispatch({
                    type: 'CHANGE_MODULE_SKILL',
                    data: {
                      type: 'tip',
                      index: this.props.editIndex,
                      content: this.props.skills[this.props.editIndex],
                    },
                  })
                }}
                name=''
                id=''
              />
              {this.state.isShowQueryWordTip ? (
                <span className='word-len-tips'>
                  {this.props.skills[this.props.editIndex].tip
                    ? `${this.props.skills[this.props.editIndex].tip.length} / ${this.state.maxRecLength}`
                    : ''}
                </span>
              ) : (
                ''
              )}
              {this.state.showQueryList ? (
                <QueryList
                  type='0'
                  className='querySelCon'
                  querys={this.state.recmmendQuesList[this.props.skills[this.props.editIndex].function]}
                  selectQuery={(query) => {
                    this.props.skills[this.props.editIndex].tip = query
                    this.props.dispatch({
                      type: 'CHANGE_MODULE_SKILL',
                      data: {
                        type: 'tip',
                        index: this.props.editIndex,
                        content: this.props.skills[this.props.editIndex],
                      },
                    })
                    this.setState({
                      showQueryList: false,
                    })
                    this.props.changeInputTips(false)
                  }}
                ></QueryList>
              ) : (
                ''
              )}
            </div>
          </div>
        ) : (
          ''
        )}
        {this.props.types == 'standard1' ? (
          // <div className="list" style={{marginTop:"-30px"}}>
          <div className='list'>
            <span className='prompt'>
              <Icon icon='Alert_Default_Info' className='prompt_tips' />
              {__('customHome.warm.msg2')}
            </span>
            {/* <span className={this.props.showInputTips ? "input_tips show_input_tips" : "input_tips hide_input_tips"}>注意：请确保【手动输入】的推荐问法机器人能正常回答或使用，否则会错误引导访客！</span> */}
            <span className='title'>{__('customHome.label.question')}</span>
            <div
              className='query-set'
              onMouseEnter={() => {
                this.setState({
                  showQueryList: true,
                })
              }}
              onMouseLeave={() => {
                setTimeout(() => {
                  this.setState({
                    showQueryList: false,
                  })
                }, 200)
              }}
            >
              <input
                className='queryInput'
                type='text'
                maxLength={this.state.maxRecLength}
                value={standardQueries[this.props.queriesIndex]}
                placeholder={__('customHome.warm.msg3')}
                onInput={(e) => {
                  this.props.changeInputTips(true)
                }}
                onFocus={() => {
                  this.setState({ isShowQueryOnlyWordTip: true })
                }}
                onBlur={() => {
                  this.setState({ isShowQueryOnlyWordTip: false })
                  this.checkQuery(
                    standardQueries[this.props.queriesIndex] &&
                      standardQueries[this.props.queriesIndex] != '' &&
                      standardQueries[this.props.queriesIndex],
                  )
                }}
                onChange={(e) => {
                  // if (standardQueries[this.props.queriesIndex] == e.target.value.replace(/\s+/g, "")) {
                  // 	return;
                  // }
                  this.props.dispatch({
                    type: 'CHANGE_MODULE_QUERIES',
                    data: {
                      index: this.props.queriesIndex,
                      content: e.target.value,
                    },
                  })
                }}
                name=''
                id=''
              />
              {this.state.isShowQueryOnlyWordTip ? (
                <span className='word-len-tips'>{`${standardQueries[this.props.queriesIndex].length} / ${
                  this.state.maxRecLength
                }`}</span>
              ) : (
                ''
              )}
              {this.state.showQueryList && this.state.lang == 'chs' ? (
                <QueryList
                  type='1'
                  className='querySelCon'
                  querys={this.state.allQuesList}
                  selectQuery={(query) => {
                    this.props.dispatch({
                      type: 'CHANGE_MODULE_QUERIES',
                      data: {
                        index: this.props.queriesIndex,
                        content: query,
                      },
                    })
                    this.setState({
                      showQueryList: false,
                    })
                    this.props.changeInputTips(false)
                  }}
                ></QueryList>
              ) : (
                ''
              )}
            </div>
          </div>
        ) : (
          ''
        )}
      </div>
    )
  }
}
export default Question
export { Question }
