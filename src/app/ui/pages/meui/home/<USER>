@import '~base.less';

.component {
  width: 820px;
  padding: 0 10px;

  .publishContain {
    text-align: left;

    .title {
      font-size: 20px;
      color: #555d61;
      text-align: center;
      margin-bottom: 22px;
    }

    .searchBox {
      width: 100%;
      height: 60px;

      .search {
        width: 100%;
        margin: 0 auto 24px auto;

        input {
          background: #ffffff;
          border-radius: 18px;
          border: none;
          outline: none;
          border: 1px solid #e1e3e9;
          width: 100%;
          height: 36px;
          line-height: 36px;
          padding-left: 29px;
          display: inline-block;
          vertical-align: middle;

          &:focus {
            border-color: #3776ef;
          }
        }

        .searchIcon {
          cursor: pointer;
          color: #3776ef;
          margin-left: -40px;
          vertical-align: middle;
        }
      }
    }

    .sort {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      font-size: 14px;
      li {
        flex: 268px 1;
        &:nth-child(1) {
          margin-right: 40px;
        }
        span {
          float: right;
          color: #3776ef;
          &.disabled {
            color: #bdbdbd;
            cursor: not-allowed !important;
          }
        }
      }
    }

    .containBox {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .routeInfo {
        flex: 1;
        width: 268px;
        margin-right: 34px;

        dt {
          width: 268px;
          height: 174px;
          background-size: cover;
          background: url('@assets/png/issue_default_picture.svg') no-repeat;
          position: relative;

          img {
            width: 268px;
            height: 174px;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
          }

          .infoBox {
            width: 100%;
            height: 54px;
            background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8));
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 2;
            .info {
              padding-top: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              li {
                color: #fff;

                .icon {
                  margin-right: 10px;
                  vertical-align: middle;
                }
              }
              li:nth-child(1):after {
                width: 1px;
                content: '|';
                color: #fff;
                height: 12px;
                margin-left: 10px;
                padding-right: 10px;
              }
            }
          }
        }

        dd {
          width: 268px;
          height: 188px;
          border-bottom-left-radius: 4.6px;
          border-bottom-right-radius: 4.6px;
          color: #fff;
          padding: 14px 18px;
          margin-top: -4px;
          background-image: linear-gradient(to top, #2ba7d3, #26c2e2);

          .name {
            width: 100%;
            font-size: 27px;
            font-weight: 500;
            color: #fff !important;
            letter-spacing: 0.3px;
            height: 38px;
            line-height: 38px;
            margin: 0 0 10px 0;
            word-break: break-all;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .bar {
            font-size: 18px;
            line-height: 24px;
            margin-bottom: 4px;

            .icon {
              width: 14px;
              height: 14px;
              margin-right: 10px;
              vertical-align: baseline;
            }
            &.info {
              max-height: 72px;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }
      }

      .list {
        flex: 1;

        height: 360px;
        border: 1px solid #eff1f2;
        border-radius: 8px;
        overflow: auto;

        li {
          height: 40px;
          line-height: 40px;
          font-size: 14px;
          color: #99a3a8;
          padding: 0 16px;
          border-bottom: 1px solid #eff1f2;

          span:nth-child(1) {
            width: calc(100%-60px);
            float: left;
            text-align: left;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            white-space: nowrap;
            &.online {
              color: #3776ef;
            }
          }

          .status {
            float: right;
          }

          .icon {
            float: right;
            margin-top: 8px;

            &:hover {
              cursor: pointer;
            }
          }

          .del {
            color: #3776ef;
          }
        }

        .noData {
          border: none;
          text-align: center;
          margin-top: 150px;
        }
      }

      .robotBox {
        margin-right: 0;
      }

      .arrow {
        margin: 160px 10px;
      }
    }
    .marked {
      // margin-top: 19/px;
      background: #fffbe6;
      border: 1px solid #ffe58f;
      border-radius: 4px;
      white-space: nowrap;
      padding: 10px;
      text-align: left;
      display: inline-block;
      margin-bottom: 0;
      margin-top: 8px;
      width: 100%;

      &:lang(en) {
        margin-left: 0;
        margin-top: 10px;
      }

      .marked_icon {
        color: #faad14;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -2px;
        margin-left: 8px;
      }

      span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        letter-spacing: 0;
        white-space: nowrap;
        white-space: pre-wrap;
        width: 94%;
      }
      .blue {
        color: #3776ef;
      }
    }
  }
}
