@import '~base.less';

.component {
  .modal_choose {
    display: flex;

    p {
      flex: 1;
      text-align: center;
      margin-top: 0;

      // max-width:162px;
      img {
        display: block;
        margin: 0 auto;
        margin-bottom: 6px;
        width: 162px;
        height: 260px;
      }

      label,
      span {
        font-size: 12px;
        color: #555d61;
        letter-spacing: 0;
        line-height: 16px;
        vertical-align: middle;
      }
    }

    .blue {
      label,
      span {
        color: #3776ef;
      }
    }

    p:nth-child(even) {
      margin-left: 12.5px;
      margin-right: 12.5px;
    }
  }

  p.marked {
    margin-top: 19px;
    background: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 4px;
    white-space: nowrap;
    padding: 10px;
    text-align: left;
    .marked_icon {
      color: #faad14;
      margin-right: 8px;
      vertical-align: middle;
      margin-top: -2px;
      margin-left: 8px;
    }
    span {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      letter-spacing: 0;
      white-space: nowrap;
    }
  }
  .modal_button {
    // border-top: 1px solid #E1E7EA;
    // text-align: center;
    // padding-top:19px;
    // font-size: 15px;
    // color: #3776ef;
    // margin-top:20px;
    // cursor: pointer;

    cursor: pointer;
    margin: 20px auto 0;
  }
}
