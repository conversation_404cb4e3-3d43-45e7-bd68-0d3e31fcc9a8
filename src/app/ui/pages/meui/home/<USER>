import React from 'react'
import { connect } from 'react-redux'
import { extend, getLocaleId } from 'koot'
import { Dropdown, MenuItem } from 'biz-components'
import { Icon } from 'biz-components'
import { Radio, Button } from 'biz-components'
import { Popover } from 'antd'

@connect((state) => {
  return {
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
  }
})
@extend({
  styles: require('./chooseModal.less'),
})
class ChooseModal extends React.Component {
  langDetail = ''

  state = {
    style: this.props.modalStyle,
    curScenes: this.props.curScenes == 'KTV' ? 'KTV' : this.props.curScenes.toLowerCase(),
    allScenes: [
      __('customHome.sceneType.default'),
      __('customHome.sceneType.market'),
      __('customHome.sceneType.museum'),
      __('customHome.sceneType.bank'),
      __('customHome.sceneType.hotel'),
      __('customHome.sceneType.library'),
    ],
    languages: [__('customHome.language.chinese'), __('customHome.language.english')],
    lang: this.props.lang,
  }
  componentDidMount() {
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
    if (this.isKTV) {
    }
    let allScenes = this.state.allScenes
    allScenes.push('KTV')
    this.setState({
      allScenes,
    })
  }
  render() {
    let opk_version = true /// 默认禁用 简洁式
    let currentRobotId = this.props.currentRobotId
    let robots = this.props.robots
    let baseVersion = '02.0001.200821'
    // baseVersion = "0";

    if (robots[currentRobotId] && robots[currentRobotId].robot_baseopk_version) {
      let opkversion = robots[currentRobotId].robot_baseopk_version
      let overVersion = checkReleaseRaw(opkversion, baseVersion, true)
      if (overVersion) {
        opk_version = false
      } else {
        opk_version = true
      }
    }
    return (
      <div className={this.props.className}>
        <div className='modal_content'>
          <p className='modal_title'>{__('customHome.label.template')}</p>
          <p className='list1ext list1 2'>
            <span className='small_title'>{__('customHome.label.language')}</span>
            <Dropdown className='list_dropdown' label={this.state.langDetail}>
              {this.state.languages.map((item, idx) => {
                return (
                  <MenuItem
                    className={item == this.state.languages ? 'cur' : ''}
                    children={item}
                    key={idx}
                    onClick={() => {
                      let langDetail = ''
                      let mlang = 'chs'
                      this.setState({
                        lang: mlang,
                        langDetail,
                      })
                    }}
                  />
                )
              })}
            </Dropdown>
          </p>
          {this.state.lang == 'chs' && (
            <p className='list1'>
              <span className='small_title'>{__('customHome.warm.scenario')}</span>
              <Dropdown className='list_dropdown' label={this.state.curScenes}>
                {this.state.allScenes.map((item, idx) => {
                  return (
                    <MenuItem
                      className={item.toLowerCase() == this.state.curScenes ? 'cur' : ''}
                      children={item}
                      key={idx}
                      onClick={() => {
                        this.setState(
                          {
                            curScenes: item,
                          },
                          () => {
                            console.log(item.toLowerCase())
                          },
                        )
                      }}
                    />
                  )
                })}
              </Dropdown>
            </p>
          )}

          {this.state.lang == 'chs' && (
            <p className='list2'>
              <span className='small_title'>{__('customHome.SelectStyle.title')}</span>
            </p>
          )}
          {this.state.lang == 'chs' && (
            <div className='modal_choose'>
              {this.robotType != 'bao_xiao_di' && (
                <p
                  className={this.state.style == 'Standard' ? 'blue' : ''}
                  onClick={() => {
                    this.changeStyle('Standard')
                  }}
                >
                  <img src={require('@assets/png/meui/bg_4_s.png')} />
                  <Radio
                    name='sort'
                    label={__('customHome.SelectStyle.standard')}
                    checked={this.state.style == 'Standard'}
                  />
                  {this.props.modalStyle == 'Standard' && <span>({__('customHome.current')})</span>}
                </p>
              )}
              {this.robotType != 'bao_xiao_di' && (
                <p
                  className={this.state.style == 'List' ? 'blue' : ''}
                  onClick={() => {
                    this.changeStyle('List')
                  }}
                >
                  <img src={require('@assets/png/meui/bg_1_s.png')} />
                  <Radio name='sort' label={__('customHome.SelectStyle.list')} checked={this.state.style == 'List'} />
                  {this.props.modalStyle == 'List' && <span>({__('customHome.current')})</span>}
                </p>
              )}
              {this.robotType != 'bao_xiao_di' && (
                <p
                  className={this.state.style == 'Card' ? 'blue' : ''}
                  onClick={() => {
                    this.changeStyle('Card')
                  }}
                >
                  <img src={require('@assets/png/meui/bg_2_s.png')} />
                  <Radio name='sort' label={__('customHome.SelectStyle.card')} checked={this.state.style == 'Card'} />
                  {this.props.modalStyle == 'Card' && <span>({__('customHome.current')})</span>}
                </p>
              )}
              {opk_version ? (
                <Popover content={'需升级机器版本'} title='Title'>
                  <p
                    disabled={opk_version}
                    className={this.state.style == 'Simple' ? 'blue' : ''}
                    onClick={() => {
                      this.changeStyle('Simple')
                    }}
                  >
                    <img src={require('@assets/png/meui/simple.png')} />
                    {!opk_version && (
                      <Radio
                        name='sort'
                        attr={opk_version}
                        disabled={opk_version}
                        label={__('customHome.SelectStyle.simple') + '(当前版本不兼容,请升级)'}
                        checked={this.state.style == 'Simple'}
                      />
                    )}
                    {opk_version && <span className='lowver'>当前版本不兼容,请升级</span>}
                    {this.props.modalStyle == 'Simple' && <span>({__('customHome.current')})</span>}
                  </p>
                </Popover>
              ) : (
                <p
                  disabled={opk_version}
                  className={this.state.style == 'Simple' ? 'blue' : ''}
                  onClick={() => {
                    this.changeStyle('Simple')
                  }}
                >
                  <img src={require('@assets/png/meui/simple.png')} />
                  {
                    <Radio
                      name='sort'
                      attr={opk_version}
                      disabled={opk_version}
                      label={__('customHome.SelectStyle.simple')}
                      checked={this.state.style == 'Simple'}
                    />
                  }
                  {this.props.modalStyle == 'Simple' && <span>({__('customHome.current')})</span>}
                </p>
              )}

              {/* <p className={this.state.style == "Technical" ? "blue" : ""}
							onClick={() => {
								this.changeStyle("Technical")
							}}>
							<img src={require('@assets/png/meui/bg_3_s.png')} />
							<Radio name="sort" label="科技范" checked={his.state.style == "Technical"}/>
							{this.props.modalStyle == "Technical" && <span>(当前)</span>}
						</p> */}
              {/* <p className={this.state.style == "Technical" ? "blue" : ""}
							onClick={() => {
								this.changeStyle("Technical")
							}}>
							<img src={require('@assets/png/meui/bg_5_s.png')} />
							<Radio name="sort" label="简约式" checked={this.state.style == "Technical"}/>
							{this.props.modalStyle == "Technical" && <span>(当前)</span>}
						</p> */}
            </div>
          )}
          {this.state.lang !== 'zh_CN' && (
            <div className='modal_choose'>
              <p
                className={this.state.style == 'List' ? 'blue' : ''}
                onClick={() => {
                  this.changeStyle('List')
                }}
              >
                <img src={require('@assets/png/meui/bg_1_s_eng.png')} />
                <Radio name='sort' label={__('customHome.SelectStyle.list')} checked={this.state.style == 'List'} />
                {this.props.modalStyle == 'List' && <span>({__('customHome.current')})</span>}
              </p>
            </div>
          )}

          <p className='marked'>
            <Icon icon='Alert_Default_Warning' className='marked_icon' />
            {this.state.lang !== 'zh_CN' && <span>{__('customHome.warm.msg7')}</span>}
            {this.state.lang === 'zh_CN' && <span>{__('customHome.warm.msg1')}</span>}
          </p>
          <Button
            className='modal_button'
            onClick={() => {
              this.props.choosemodalStyle(this.state.style, this.state.curScenes, this.state.lang)
            }}
          >
            {__('customHome.btn.save2')}
          </Button>
        </div>
      </div>
    )
  }
  changeStyle(txt) {
    this.setState({
      style: txt,
    })
  }
}
export default ChooseModal
export { ChooseModal }
