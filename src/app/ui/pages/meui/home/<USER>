import React from 'react'
import modal, { close as closeModal } from '@utils/modal'
import { Icon } from 'biz-components'
import AddSkill from './addSkillModal'
import { SortableContainer, SortableElement, SortableHandle, arrayMove } from 'react-sortable-hoc'
import { connect } from 'react-redux'
import { Radio } from 'biz-components'
import { Popover, message } from 'antd'
import recommendQues from '@api/_home/recommendQues'
import {
  CHANGE_SKILL_SORT,
  REMOVE_HOMEMODULE_SKILL,
  ADD_HOMEMODULE_SKILL,
  ADD_NEW_SKILL,
  CHANGE_QUERY_SORT,
  REMOVE_HOMEMODULE_QUERY,
  EDIT_QUERIES,
  PUSH_QUERIES,
  NEW_PUSH_QUERIES,
  MODIFY_QUERIES_SORT,
} from '@redux/action-types'
import { checkReleaseRaw } from '@utils/tools'

const nav_fun = ['mainPage', 'chat', 'query_locations', 'weather', 'calendar', 'dance', 'web', 'video']
// let nav_content = ["首页", "问答聊天", "问路引领", "天气", "日历", "跳舞", "轻应用", "视频(唱歌等)"];
let nav_content = [
  __('customHome.functionList.homepage'),
  __('customHome.functionList.chat2'),
  __('customHome.functionList.guide2'),
  __('customHome.functionList.weather'),
  __('customHome.functionList.calendar'),
  __('customHome.functionList.dance'),
  __('customHome.functionList.miniApp2'),
  __('customHome.functionList.vidwoSing'),
]
@connect((state) => {
  return {
    skills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
    robotId: state.robots.current,
    robots: state.robots.robots,
  }
})
class BottomQueries extends React.Component {
  state = {
    localQueries: recommendQues.queries,
    tabIndex: 0,
    tempaletType: 0,
    queries_style: 'mainPage',
    nav_fun: [],
    nav_content: [],
    versionFlag: false,
    lang: this.props.lang || 'chs',
  }
  componentDidMount() {
    this.pushSort()
    this.setTemplateType()

    if (this.state.lang === 'zh_CN') {
      nav_content = [
        __('customHome.functionList.homepage'),
        __('customHome.functionList.chat2'),
        __('customHome.functionList.guide2'),
        __('customHome.functionList.weather'),
        __('customHome.functionList.calendar'),
        __('customHome.functionList.dance'),
        __('customHome.functionList.miniApp2'),
        __('customHome.functionList.vidwoSing'),
      ]
    } else {
      nav_content = [__('customHome.functionList.homepage')]
    }
    // this.changeTab(0);
    Object.keys(this.props.robots).map((el) => {
      if (this.props.robotId == el) {
        let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.11', true)
        console.log(this.props.robots[el].version, versionGap)
        if (versionGap) {
          //4.11
          this.setState({
            versionFlag: true,
            nav_fun: nav_fun,
            nav_content: nav_content,
          })
        } else {
          //非4.11
          this.setState({
            versionFlag: false,
            nav_content: nav_content.slice(0, 5),
            nav_fun: nav_fun.slice(0, 5),
          })
        }
      }
    })
  }
  componentDidUpdate(prevProps) {
    if (prevProps.robotId != this.props.robotId) {
      console.log('---------更换机器人')
      // this.setTemplateType();
      setTimeout(() => {
        this.changeTab(this.state.tabIndex)
      }, 800)

      let changeRobotFlag = false
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.11', true)
          console.log(this.props.robots[el].version, versionGap)
          if (versionGap) {
            this.setState(
              {
                versionFlag: true,
                nav_fun: nav_fun,
                nav_content: nav_content,
              },
              () => {
                this.state.nav_fun.map((item) => {
                  if (this.state.queries_style == item) {
                    changeRobotFlag = true
                  }
                })
                console.log('changeRobotFlag------------' + changeRobotFlag)
                if (!changeRobotFlag) {
                  this.setState({
                    queries_style: 'mainPage',
                    tabIndex: 0,
                  })
                }
              },
            )
          } else {
            this.setState(
              {
                versionFlag: false,
                nav_content: nav_content.slice(0, 5),
                nav_fun: nav_fun.slice(0, 5),
              },
              () => {
                this.state.nav_fun.map((item) => {
                  if (this.state.queries_style == item) {
                    changeRobotFlag = true
                  }
                })
                console.log('changeRobotFlag------------' + changeRobotFlag)
                if (!changeRobotFlag) {
                  this.setState({
                    queries_style: 'mainPage',
                    tabIndex: 0,
                  })
                }
              },
            )
          }
        }
      })
    }
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    console.log(nextProps)
    console.log(11111)
    if (this.props.lang && nextProps.lang) {
      this.setState({
        lang: nextProps.lang,
      })
    }
  }
  render() {
    let flag = false,
      standardQueries = []
    this.props.queries.forEach((item) => {
      if (item.name == this.state.queries_style) {
        standardQueries = item.query
        flag = true
        return
      }
    })
    if (!flag) {
      this.state.localQueries.forEach((item) => {
        if (item.name == this.state.queries_style) {
          standardQueries = item.query.slice()
          console.log(standardQueries)
          if (this.state.versionFlag) {
            this.props.dispatch({
              type: 'NEW_PUSH_QUERIES',
              data: {
                content: standardQueries,
                bottomQueries: this.state.queries_style,
                sort: 0,
              },
            })
          } else {
            this.props.dispatch({
              type: 'PUSH_QUERIES',
              data: {
                content: standardQueries,
                bottomQueries: this.state.queries_style,
              },
            })
          }
        }
        return
      })
    }
    let DragHandle_query = SortableHandle(() => <Icon icon='move2' className='sort_button' />)
    let SortableItem_query = SortableElement(({ idx, value, onDelQuery }) => {
      return (
        <p className='sort_info'>
          <Popover content={value}>
            <span className='sort_content'>{value}</span>
          </Popover>
          <Icon
            icon='map-_edit'
            className='icon_edit'
            onClick={() => {
              modal({
                title: __('customHome.modalMsg.editRecommendedQuestions'),
                confirm: false,
                cancel: false,
                content: (
                  <AddSkill
                    devUrl={this.props.devUrl}
                    modalStyle={this.props.modalStyle}
                    editQueries={this.editQueries.bind(this)}
                    queriesIndex={idx}
                    lang={this.state.lang}
                    queries_style={this.state.queries_style}
                    addStyle='bottom_queries'
                  />
                ),
              })
            }}
          />
          <Icon icon='rd_trash' className='icon' onClick={() => onDelQuery(idx)} />
          <DragHandle_query />
        </p>
      )
    })

    let SortableList_query = SortableContainer(({ items, onDelQuery }) => {
      return (
        <ul className='sort_area1'>
          {items.map((item, index) => {
            return (
              <SortableItem_query
                key={`item-${index}`}
                index={index}
                idx={index}
                value={item}
                onDelQuery={onDelQuery}
              />
            )
          })}
        </ul>
      )
    })

    return (
      <div className='bottomsort-container'>
        <ul className='nav'>
          {this.state.nav_content.map((item, index) => {
            if (!this.state.versionFlag || (this.state.versionFlag && item != __('customHome.functionList.chat2'))) {
              //机器人版本大于V4.11不展示问答聊天
              return (
                <li
                  key={index}
                  className={this.state.tabIndex == index ? 'active' : ''}
                  onClick={() => {
                    this.changeTab(index)
                  }}
                >
                  {item}
                </li>
              )
            }
          })}
        </ul>
        {standardQueries.length != 0 && !this.state.versionFlag ? (
          <span className='sort_tips'>{__('customHome.warm.palyWarm')}</span>
        ) : (
          ''
        )}
        {this.state.versionFlag && (
          <div className='sort'>
            <Radio
              name='is-sort'
              label={__('customHome.btn.sequence')}
              checked={this.state.tempaletType == 0}
              onClick={() => {
                // this.editSort(0, nav_content)
                this.editSort(0, standardQueries)
              }}
            />
            <Radio
              name='is-sort'
              label={__('customHome.btn.random')}
              checked={this.state.tempaletType == 1}
              onClick={() => {
                // this.editSort(1, nav_content)
                this.editSort(1, standardQueries)
              }}
            />
          </div>
        )}
        {standardQueries.length != 0 ? (
          <div className='bottomquery-sort-container'>
            <div className='sort'>
              <p
                className='sort_add'
                onClick={() => {
                  if (standardQueries.length < 16) {
                    modal({
                      title: __('customHome.btn.addNewQuestion'),
                      confirm: false,
                      cancel: false,
                      content: (
                        <AddSkill
                          devUrl={this.props.devUrl}
                          modalStyle={this.props.modalStyle}
                          queries_style={this.state.queries_style}
                          lang={this.state.lang}
                          newAddQueries={this.newAddQueries.bind(this)}
                          addStyle='bottom_queries'
                        />
                      ),
                    })
                  } else {
                    message.error(__('customHome.modalMsg.recommendedNum', { num: 16 }))
                    {
                      /* modal({
										content: __('customHoe.modalMsg.recommendedNum'),
										cancel: false,
									}) */
                    }
                  }
                }}
              >
                {__('customHome.btn.addNewQuestion')}
              </p>
              <SortableList_query
                items={standardQueries}
                axis='xy'
                onSortEnd={this.onSortQueryEnd}
                useDragHandle={true}
                onDelQuery={(idx) => this.onDelQuery(idx)}
              />
            </div>
          </div>
        ) : (
          ''
        )}
        {standardQueries && standardQueries.length == 0 ? (
          <div className='no_queries'>
            <p>{__('customHome.modaMsg.restMsg')}</p>
          </div>
        ) : (
          ''
        )}
      </div>
    )
  }
  pushSort() {
    this.props.queries.forEach((item) => {
      if (item.sort == undefined) {
        this.props.dispatch({
          type: 'MODIFY_QUERIES_SORT',
          data: {
            content: item.query,
            bottomQueries: item.name,
            sort: 0,
          },
        })
      }
    })
  }
  editSort(idx, content) {
    console.log(content, '排序content')
    this.setState({
      tempaletType: idx,
    })
    this.props.dispatch({
      type: 'MODIFY_QUERIES_SORT',
      data: {
        content: content,
        bottomQueries: this.state.queries_style,
        sort: idx,
      },
    })
  }
  newAddQueries(text) {
    this.props.dispatch({
      type: 'ADD_NEW_QUERIES',
      data: {
        index: 0,
        content: text,
        bottomQueries: this.state.queries_style,
      },
    })
    closeModal()
    this.props.bottomQueries(this.state.queries_style, 0)
  }
  setTemplateType() {
    let sort = 0
    this.props.queries.forEach((item) => {
      if (item.name == this.state.queries_style) {
        console.log(item.sort)
        if (item.sort == undefined) {
          sort = 0
        } else {
          sort = item.sort
        }
      }
    })
    this.setState({
      tempaletType: sort,
    })
  }
  changeTab(index) {
    let sort = 0
    this.props.queries.forEach((item) => {
      if (item.name == this.state.nav_fun[index]) {
        console.log(item.sort)
        if (item.sort == undefined) {
          sort = 0
        } else {
          sort = item.sort
        }
      }
    })
    this.setState({
      tabIndex: index,
      queries_style: this.state.nav_fun[index],
      tempaletType: sort,
    })
    this.props.bottomQueries(this.state.nav_fun[index], 0)
  }
  remove = (idx, display) => {
    let showSkillNum = 0
    this.props.skills.forEach((item) => {
      if (item.display) {
        showSkillNum = showSkillNum + 1
      }
    })
    console.log('显示skills数量' + showSkillNum)
    if (showSkillNum <= 3) {
      message.error(__('customHome.modalMsg.keepNum'))
      // modal({
      // 	title: __('customHome.modalMsg.failedToHide'),
      // 	content: __('customHome.modalMsg.keepNum'),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除index：', idx, '；当前display：', display)
      let index = idx
      switch (this.props.modalStyle) {
        case 'List':
          index = idx + 2
          break
        case 'Card':
          index = idx
          break
        case 'Standard':
          index = idx + 1
          break
        default:
          index = idx
      }
      this.props.dispatch({
        type: REMOVE_HOMEMODULE_SKILL,
        data: {
          index: index,
          display: !display,
        },
      })
    }
  }
  add = (idx, display) => {
    console.log('添加index：', idx, '；当前display：', display)
    let index = idx
    switch (this.props.modalStyle) {
      case 'List':
        index = idx + 2
        break
      case 'Card':
        index = idx
        break
      case 'Standard':
        index = idx + 1
        break
      default:
        index = idx
    }
    this.props.dispatch({
      type: ADD_HOMEMODULE_SKILL,
      data: {
        index: index,
        display: !display,
        modalStyle: this.props.modalStyle,
      },
    })
  }
  editQueries(text, indexs) {
    this.props.dispatch({
      type: EDIT_QUERIES,
      data: {
        index: indexs,
        content: text,
        bottomQueries: this.state.queries_style,
      },
    })
    this.props.bottomQueries(this.state.queries_style, indexs)
    closeModal()
  }
  onDelQuery = (idx) => {
    let standardQueries
    this.props.queries.forEach((item) => {
      if (item.name == this.state.queries_style) {
        standardQueries = item.query
      }
    })
    if (standardQueries.length <= 2) {
      message.error(__('customHome.modalMsg.keepRecommendedNum', { num: 2 }))
      // modal({
      // 	title: __('customHome.modalMsg.failedToDelete'),
      // 	content: __('customHome.modalMsg.keepRecommendedNum',{num:2}),
      // 	cancel: false,
      // 	confirm:  __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除queryies index：', idx)
      this.props.dispatch({
        type: REMOVE_HOMEMODULE_QUERY,
        data: {
          index: idx,
          bottomQueries: this.state.queries_style,
        },
      })
      console.log(this.props.queries)
      this.props.bottomQueries(this.state.queries_style, 0)
    }
  }
  onSortQueryEnd = ({ oldIndex, newIndex }) => {
    this.props.dispatch({
      type: CHANGE_QUERY_SORT,
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
        bottomQueries: this.state.queries_style,
      },
    })
    this.props.bottomQueries(this.state.queries_style, 0)
  }
}

export default BottomQueries
export { BottomQueries }
