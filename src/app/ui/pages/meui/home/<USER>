@import '~base.less';
.component {
  width: 494px;
  max-height: 370px;
  margin-bottom: -25px;
  .prompt {
    display: block;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    padding: 10px;
    border: solid 1px #ffe58f;
    background-color: #fffbe6;
    white-space: nowrap;
    margin-bottom: 24px;
    .prompt_icon {
      color: #faad14;
      margin-right: 8px;
      vertical-align: middle;
      margin-top: -2px;
    }
  }
  .tips {
    display: block;
    color: #ff713c;
    font-size: 0.7rem;
    margin-top: -20px;
    height: 24px;
    padding-top: 10px;
  }
  .querylist-item {
    max-height: 180px;
    overflow-y: scroll;
    ::-webkit-scrollbar {
      width: 8px;
      height: 30px;
    }

    /* 滚动槽 */
    ::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.06);
      -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
      border-radius: 10px;
    }
    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(0, 0, 0, 0.1);
      -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
    }
    ::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(0, 0, 0, 0.1);
    }
    p.item {
      display: block;
      border-bottom: 1px solid #eff1f2;
      margin-bottom: 15px;
      padding-bottom: 7px;
      text-align: left;
      span {
        display: inline-block;
        color: #555d61;
        font-size: 14px;
        width: 350px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      em {
        font-style: normal;
        color: #3776ef;
        font-size: 14px;
        float: right;
        cursor: pointer;
        margin-right: 15px;
      }
    }
  }
  .modal_button {
    display: flex;
    border-top: solid 1px #e1e7ea;
    height: 84px;
    text-align: center;
    line-height: 84px;
    padding-top: 20px;
    span {
      font-size: 15px;
      background: #3776ef;
      color: #fff;
      display: inline-block;
      flex: 1;
      width: 400px;
      height: 44px;
      border-radius: 22px;
      line-height: 44px;
      text-align: center;
      cursor: pointer;
    }
  }
}
