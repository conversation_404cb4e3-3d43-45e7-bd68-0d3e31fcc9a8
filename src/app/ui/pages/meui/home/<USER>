import React from 'react'
import modal, { close as closeModal } from '@utils/modal'
import { Icon } from 'biz-components'
import AddSkill from './addSkillModal'
import { Popover, message } from 'antd'
import { SortableContainer, SortableElement, SortableHandle, arrayMove } from 'react-sortable-hoc'
import { connect } from 'react-redux'
import {
  CHANGE_SKILL_SORT,
  REMOVE_HOMEMODULE_SKILL,
  ADD_HOMEMODULE_SKILL,
  ADD_NEW_SKILL,
  UPDATE_SKILL,
  CHANGE_QUERY_SORT,
  REMOVE_HOMEMODULE_QUERY,
} from '@redux/action-types'

const DragHandle = SortableHandle(() => <Icon icon='move2' className='sort_button' />)
const SortableItem = SortableElement(({ modalStyle, idx, value, onRemove, onDelFun }) => {
  return (
    <p className={modalStyle == 'Card' ? 'sort_info2 sort_info' : 'sort_info'}>
      <Popover content={modalStyle != 'Technical' ? value.title : value.tip}>
        <span className='sort_content'>{modalStyle != 'Technical' ? value.title : value.tip}</span>
      </Popover>
      {modalStyle == 'Standard' && (
        <Icon
          icon='rd_trash'
          className='icon toright'
          onClick={() => {
            console.log('to right click')
            // onDelQuery(idx)
            onDelFun(idx)
          }}
        />
      )}
      {modalStyle == 'Simple' && (
        <Icon
          icon='rd_trash'
          className='icon toright'
          onClick={() => {
            console.log('to right click')
            // onDelQuery(idx)
            onDelFun(idx)
          }}
        />
      )}
      <span className='sort_switch' onClick={() => onRemove(idx, value.display)}>
        <em className='switch_on'></em>
      </span>
      <DragHandle />
    </p>
  )
})

const SortableList = SortableContainer(({ modalStyle, items, onRemove, onDelFun }) => {
  return (
    <ul className={modalStyle != 'Card' ? 'sort_area1' : 'sort_area2'}>
      {items.map((item, index) => {
        if (item.display) {
          return (
            <SortableItem
              key={`item-${index}`}
              modalStyle={modalStyle}
              index={index}
              idx={index}
              value={item}
              onDelFun={onDelFun}
              onRemove={onRemove}
            />
          )
        }
      })}
    </ul>
  )
})

const DragHandle_query = SortableHandle(() => <Icon icon='move2' className='sort_button' />)
const SortableItem_query = SortableElement(({ modalStyle, idx, value, onDelQuery }) => {
  return (
    <p className='sort_info'>
      <Popover content={value}>
        <span className='sort_content'>{value}</span>
      </Popover>
      <Icon icon='rd_trash' className='icon' onClick={() => onDelQuery(idx)} />
      <DragHandle_query />
    </p>
  )
})

const SortableList_query = SortableContainer(({ modalStyle, items, onDelQuery }) => {
  // debugger
  return (
    <ul className={modalStyle != 'Card' ? 'sort_area1' : 'sort_area2'}>
      {items &&
        items.map((item, index) => {
          return (
            <SortableItem_query
              key={`item-${index}`}
              modalStyle={modalStyle}
              index={index}
              idx={index}
              value={item}
              onDelQuery={onDelQuery}
            />
          )
        })}
    </ul>
  )
})

@connect((state) => {
  return {
    skills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
  }
})
class Sort extends React.Component {
  state = {}
  componentDidMount() {}
  newAddSkill(skill) {
    console.log(skill)
    if (this.props.modalStyle == 'List') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 2,
          skill: skill,
        },
      })
    } else if (this.props.modalStyle == 'Standard') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 1,
          skill: skill,
        },
      })
    } else if (this.props.modalStyle == 'Simple') {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 1,
          skill: skill,
        },
      })
    } else {
      this.props.dispatch({
        type: 'ADD_NEW_SKILL',
        data: {
          index: 0,
          skill: skill,
        },
      })
    }
    closeModal()
  }
  newAddQueries(text) {
    this.props.dispatch({
      type: 'ADD_NEW_QUERIES',
      data: {
        index: 0,
        content: text,
      },
    })
    closeModal()
  }
  render() {
    let skills, standardQueries
    console.log(this.props)
    if (this.props.modalStyle == 'List') {
      skills = this.props.skills.slice(2)
    } else if (this.props.modalStyle == 'Card' || this.props.modalStyle == 'Technical') {
      skills = this.props.skills
    } else if (this.props.modalStyle == 'Standard') {
      this.props.queries.forEach((item) => {
        if (item.name == 'standardMain') {
          standardQueries = item.query
        }
      })
      skills = this.props.skills.slice(1)
    } else if (this.props.modalStyle == 'Simple') {
      this.props.queries.forEach((item) => {
        if (item.name == 'standardMain') {
          standardQueries = item.query
        }
      })
      skills = this.props.skills.slice(1)
    }

    return (
      <div className='sort-container'>
        {this.props.modalStyle == 'Standard' ? (
          <div className='query-sort-container'>
            <div className='sort'>
              <p className='sort-title'>{__('customHome.label.question')}</p>
              <p
                className='sort_add'
                onClick={() => {
                  if (standardQueries.length < 16) {
                    modal({
                      title: __('customHome.btn.addNewQuestion'),
                      confirm: false,
                      cancel: false,
                      content: (
                        <AddSkill
                          devUrl={this.props.devUrl}
                          modalStyle={this.props.modalStyle}
                          newAddSkill={this.newAddSkill.bind(this)}
                          newAddQueries={this.newAddQueries.bind(this)}
                          addStyle='queries'
                        />
                      ),
                    })
                  } else {
                    message.error(__('customHome.modalMsg.recommendedNum', { num: 16 }))
                    {
                      /* modal({
										content: __('customHome.modalMsg.recommendedNum',{num:16}),
										cancel: false,
									}) */
                    }
                  }
                }}
              >
                {__('customHome.btn.addNewQuestion')}
              </p>
              <SortableList_query
                modalStyle={this.props.modalStyle}
                items={standardQueries}
                axis='xy'
                onSortEnd={this.onSortQueryEnd}
                useDragHandle={true}
                onDelQuery={(idx) => this.onDelQuery(idx)}
              />
            </div>
            <div className='ver-line'></div>
          </div>
        ) : (
          ''
        )}
        {this.props.modalStyle == 'Simple' ? (
          <div className='query-sort-container'>
            <div className='sort'>
              <p className='sort-title'>{__('customHome.label.question')}</p>
              <p
                className='sort_add'
                onClick={() => {
                  if (standardQueries.length < 16) {
                    modal({
                      title: __('customHome.btn.addNewQuestion'),
                      confirm: false,
                      cancel: false,
                      content: (
                        <AddSkill
                          devUrl={this.props.devUrl}
                          modalStyle={this.props.modalStyle}
                          newAddSkill={this.newAddSkill.bind(this)}
                          newAddQueries={this.newAddQueries.bind(this)}
                          addStyle='queries'
                        />
                      ),
                    })
                  } else {
                    message.error(__('customHome.modalMsg.recommendedNum', { num: 16 }))
                    {
                      /* modal({
										content: __('customHome.modalMsg.recommendedNum',{num:16}),
										cancel: false,
									}) */
                    }
                  }
                }}
              >
                {__('customHome.btn.addNewQuestion')}
              </p>
              <SortableList_query
                modalStyle={this.props.modalStyle}
                items={standardQueries}
                axis='xy'
                onSortEnd={this.onSortQueryEnd}
                useDragHandle={true}
                onDelQuery={(idx) => this.onDelQuery(idx)}
              />
            </div>
            {this.props.modalStyle != 'Simple' && <div className='ver-line'></div>}
          </div>
        ) : (
          ''
        )}
        {this.props.modalStyle != 'Simple' && (
          <div className={this.props.modalStyle != 'Card' ? 'sort ' : 'sort sort2'}>
            {this.props.modalStyle == 'List' ? (
              <p className='sort_txt'>
                <span className='txt interview'>{this.props.skills[0].title}</span>
                <span className='txt visited'>{this.props.skills[1].title}</span>
              </p>
            ) : (
              ''
            )}
            {this.props.modalStyle == 'Standard' ? (
              <p className='sort-title'>{__('customHome.label.functionCard')}</p>
            ) : (
              ''
            )}
            {this.props.modalStyle == 'Simple' ? (
              <p className='sort-title'>{__('customHome.label.functionCard')}</p>
            ) : (
              ''
            )}
            {this.props.modalStyle == 'Standard' ? (
              <p className='sort_txt'>
                <span className='txt interview'>{this.props.skills[0].title}</span>
              </p>
            ) : (
              ''
            )}
            {this.props.modalStyle == 'Simple' ? (
              <p className='sort_txt'>
                <span className='txt interview'>{this.props.skills[0].title}</span>
              </p>
            ) : (
              ''
            )}
            <p
              className='sort_add'
              onClick={() => {
                modal({
                  title: __('customHome.btn.addNewFunction'),
                  confirm: false,
                  cancel: false,
                  content: (
                    <AddSkill
                      devUrl={this.props.devUrl}
                      modalStyle={this.props.modalStyle}
                      newAddSkill={this.newAddSkill.bind(this)}
                      lang={this.props.lang}
                    />
                  ),
                })
              }}
            >
              {__('customHome.btn.addNewFunction')}
            </p>
            <SortableList
              modalStyle={this.props.modalStyle}
              items={skills}
              axis='xy'
              onSortEnd={this.onSortEnd}
              useDragHandle={true}
              onDelFun={(idx) => this.onDelFun(idx)}
              onRemove={(idx, display) => this.remove(idx, display)}
            />
            <div className={this.props.modalStyle != 'Card' ? 'sort_area1' : 'sort_area2'}>
              {this.props.modalStyle != 'Simple' &&
                skills.map((item, i) => {
                  if (!item.display) {
                    return (
                      <p className='sort_info gray' key={`item-${i}`}>
                        <Popover content={this.props.modalStyle != 'Technical' ? item.title : item.tip}>
                          <span className='sort_content'>
                            {this.props.modalStyle != 'Technical' ? item.title : item.tip}
                          </span>
                        </Popover>
                        <span
                          className='sort_switch'
                          onClick={() => {
                            this.add(i, item.display)
                          }}
                        >
                          <em className='switch_off'></em>
                        </span>
                        <Icon icon='icon_home_move' className='sort_button' />
                      </p>
                    )
                  }
                })}
            </div>
          </div>
        )}
      </div>
    )
  }
  remove = (idx, display) => {
    let showSkillNum = 0
    this.props.skills.forEach((item) => {
      if (item.display) {
        showSkillNum = showSkillNum + 1
      }
    })
    console.log('显示skills数量' + showSkillNum)
    if (showSkillNum <= 3) {
      message.error(__('customHome.modalMsg.keepNum'))
      // modal({
      // 	title: __('customHome.modalMsg.failedToHide'),
      // 	content: __('customHome.modalMsg.keepNum'),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除index：', idx, '；当前display：', display)
      let index = idx
      switch (this.props.modalStyle) {
        case 'List':
          index = idx + 2
          break
        case 'Card':
          index = idx
          break
        case 'Standard':
          index = idx + 1
          break
        case 'Simple':
          index = idx + 1
          break
        default:
          index = idx
      }
      this.props.dispatch({
        type: REMOVE_HOMEMODULE_SKILL,
        data: {
          index: index,
          display: !display,
        },
      })
    }
  }
  add = (idx, display) => {
    console.log('添加index：', idx, '；当前display：', display)
    let index = idx
    switch (this.props.modalStyle) {
      case 'List':
        index = idx + 2
        break
      case 'Card':
        index = idx
        break
      case 'Standard':
        index = idx + 1
        break
      case 'Simple':
        index = idx + 1
        break
      default:
        index = idx
    }
    this.props.dispatch({
      type: ADD_HOMEMODULE_SKILL,
      data: {
        index: index,
        display: !display,
        modalStyle: this.props.modalStyle,
      },
    })
  }
  onSortEnd = ({ oldIndex, newIndex }) => {
    if (this.props.modalStyle == 'List') {
      oldIndex = oldIndex + 2
      newIndex = newIndex + 2
    } else if (this.props.modalStyle == 'Standard') {
      oldIndex = oldIndex + 1
      newIndex = newIndex + 1
    } else if (this.props.modalStyle == 'Simple') {
      oldIndex = oldIndex + 1
      newIndex = newIndex + 1
    }
    this.props.dispatch({
      type: CHANGE_SKILL_SORT,
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
      },
    })
  }
  onDelQuery = (idx) => {
    let standardQueries
    this.props.queries.forEach((item) => {
      if (item.name == 'standardMain') {
        standardQueries = item.query
      }
    })
    console.log('获取query数量' + standardQueries.length)
    if (standardQueries.length <= 4) {
      message.error(__('customHome.modalMsg.keepRecommendedNum', { num: 4 }))
      // modal({
      // 	title: __('customHome.modalMsg.failedToDelete'),
      // 	content: __('customHome.modalMsg.keepRecommendedNum',{num:4}),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除index：', idx)
      this.props.dispatch({
        type: REMOVE_HOMEMODULE_QUERY,
        data: {
          index: idx,
        },
      })
    }
  }
  onDelFun = (id) => {
    let idx = id + 1
    let standardFuns = []
    this.props.skills.forEach((item) => {
      standardFuns.push(item)
    })
    console.log('获取query数量' + standardFuns.length)
    if (standardFuns.length <= 3) {
      message.error(__('customHome.modalMsg.keepFunction', { num: 3 }))
      // modal({
      // 	title:  __('customHome.modalMsg.failedToDelete'),
      // 	content: __('customHome.modalMsg.keepFunction',{num:3}),
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      standardFuns.splice(idx, 1)
      this.props.dispatch({
        type: UPDATE_SKILL,
        data: {
          skills: standardFuns,
        },
      })
    }
  }
  onSortQueryEnd = ({ oldIndex, newIndex }) => {
    this.props.dispatch({
      type: CHANGE_QUERY_SORT,
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
      },
    })
  }
}

export default Sort
export { Sort }
