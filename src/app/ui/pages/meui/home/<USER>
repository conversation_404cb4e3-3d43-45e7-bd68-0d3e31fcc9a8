import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import classNames from 'classnames'
import { Checkbox } from 'biz-components'
import { checkReleaseRaw } from '@utils/tools'
window.checkReleaseRaw = checkReleaseRaw
@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页机器人列表', state.robots.robots)
  return {
    robotId: state.robots.current,
    robots: state.robots.robots,
    templateCode: state._home.homeModule.templateInfo.templateCode,
  }
})
@extend({
  styles: require('./robot.less'),
})
class Robot extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      robotLists: {},
      chooseAll: false,
    }
  }
  componentDidMount() {
    let flag = false
    if (Object.keys(this.props.robots).length > 0 && Object.keys(this.state.robotLists).length == 0) {
      Object.keys(this.props.robots).map((el, idx) => {
        if (this.props.robots[el].mode == '0' || this.props.robots[el].mode == '1' || el === 'VIRTUAL_ROBOT_UUID') {
          this.state.robotLists[el] = {
            status: false,
            robot_baseopk_version: this.props.robots[el].robot_baseopk_version,
          }

          if (this.props.robotId == el) {
            flag = true
            this.state.robotLists[el].status = true
          }
        }
      })
      this.setState({
        robotLists: this.state.robotLists,
      })
    }
    if (this.props.robotId != 'all' && flag) {
      this.props.chooseRobots([this.props.robotId])
    } else {
      document.querySelector('.btn-confirm').classList.add('disabled')
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (Object.keys(this.props.robots).length > 0 && Object.keys(this.state.robotLists).length == 0) {
      Object.keys(this.props.robots).map((el, idx) => {
        // if (this.props.robots[el].mode == "0" || this.props.robots[el].mode == "1") {
        this.state.robotLists[el] = { status: false }
        if (this.props.robotId == el) {
          this.state.robotLists[el].status = true
        }
        // }
      })
      if (JSON.stringify(prevState.robotLists) != JSON.stringify(this.state.robotLists)) {
        this.setState({
          robotLists: this.state.robotLists,
        })
      }
    }
  }
  UNSAFE_componentWillUpdate() {}
  chooseAll() {
    let baseVersion = '02.0001.200821'
    Object.keys(this.state.robotLists).map((id, idx) => {
      if (this.props.templateCode == 'Simple') {
        let opkversion = this.state.robotLists[id].robot_baseopk_version
        let overVersion = checkReleaseRaw(opkversion, baseVersion, true)
        if (overVersion) {
          this.state.robotLists[id].status = !this.state.chooseAll
        }
      } else {
        this.state.robotLists[id].status = !this.state.chooseAll
      }
    })
    let robotLists = this.state.robotLists
    if (this.props.templateCode == 'Simple') {
      // baseVersion = "0";

      let filterrobot = {}
      Object.keys(this.state.robotLists).filter((el) => {
        let opkversion = this.state.robotLists[el].robot_baseopk_version
        let overVersion = checkReleaseRaw(opkversion, baseVersion, true)
        if (overVersion) {
          filterrobot[el] = this.state.robotLists[el]
        }
      })
      // robotLists = filterrobot;
    } else {
      robotLists = this.state.robotLists
    }
    this.setState(
      {
        chooseAll: !this.state.chooseAll,
        robotLists,
      },
      () => {
        this.props.chooseRobots(this.chooseRobots())
      },
    )
  }
  changeRobotStatus(id) {
    this.state.robotLists[id].status = !this.state.robotLists[id].status
    const falseLen = Object.keys(this.state.robotLists).findIndex((id, idx) => {
      return this.state.robotLists[id].robot_baseopk_version == false
    })
    const data = {
      robotLists: this.state.robotLists,
    }
    if (falseLen >= 0) {
      data.chooseAll = false
    } else {
      data.chooseAll = true
    }
    this.setState(data, () => {
      this.props.chooseRobots(this.chooseRobots())
    })
  }
  chooseRobots() {
    const lists = Object.keys(this.state.robotLists).filter((el) => {
      return this.state.robotLists[el].status == true
    })
    if (lists.length == 0) {
      document.querySelector('.btn-confirm').classList.add('disabled')
    } else {
      document.querySelector('.btn-confirm').classList.remove('disabled')
    }
    return lists
  }
  checkversion(opkversion) {
    if (this.props.templateCode == 'Simple') {
      let baseVersion = '02.0001.200821'
      // baseVersion = "0";
      // debugger;
      let overVersion = checkReleaseRaw(opkversion, baseVersion, true)
      if (overVersion) {
        return false
      } else {
        return true
      }
    } else {
      return false
    }
  }
  render() {
    if (Object.keys(this.props.robots).length == 0 && Object.keys(this.state.robotLists).length == 0) return ''
    const currentKey = this.state.robotLists[this.props.robotId]
      ? this.props.robotId + this.state.robotLists[this.props.robotId].status
      : this.props.robotId
    return (
      <div className={this.props.className}>
        {/* <div className="robot">
					<p className="current">
					<Checkbox
						label={this.state.currentRobotName+"(当前)"}
						defaultChecked={this.state.robotList[this.props.current_robotId].checkStatus}
						onClick={(e) => {this.changeRobotsCheckStatus(e, this.props.current_robotId) }}
					/>
					</p>
					<ul className="all_robot">
						<li>
						<Checkbox
							label="所有机器人"
							className="all-check"
							defaultChecked={this.allCheckStatus()}
							onClick={(e) => { this.allCheckClick() }}
							key={this.allCheckStatus()}
						/>
						</li>
						{Object.keys(this.state.robotList).map((keys, i) => {
							if(this.props.current_robotId!=this.state.robotList[keys].robot_id){
								return (
									<li key={this.state.robotList[keys].robot_id} >
										<Checkbox
											key={this.state.robotList[keys].checkStatus}
											label={this.state.robotList[keys].robot_name}
											defaultChecked={this.state.robotList[keys].checkStatus}
											onClick={(e) => { this.changeRobotsCheckStatus(e, this.state.robotList[keys].robot_id) }}
										/>
									</li>)
							}
							

						})}
					</ul>
				</div> */}
        {Object.keys(this.props.robots).length > 0 && Object.keys(this.state.robotLists).length && (
          <ul className='all_robot'>
            <li key={'all' + this.state.chooseAll} className={classNames({ current: this.state.chooseAll })}>
              <Checkbox
                defaultChecked={this.state.chooseAll}
                onClick={this.chooseAll.bind(this)}
                label={__('customHome.btn.selectAll')}
              />
            </li>
            {this.props.robotId != 'all' && this.state.robotLists[this.props.robotId] && (
              <li
                key={currentKey}
                className={classNames({
                  current:
                    this.state.robotLists[this.props.robotId] && this.state.robotLists[this.props.robotId].status,
                })}
              >
                <Checkbox
                  defaultChecked={
                    this.state.robotLists[this.props.robotId] && this.state.robotLists[this.props.robotId].status
                  }
                  onClick={this.changeRobotStatus.bind(this, this.props.robotId)}
                  label={this.props.robots[this.props.robotId].robot_name + '（' + __('customHome.current') + '）'}
                />
              </li>
            )}
            {Object.keys(this.state.robotLists).map((el, idx) => {
              if (el != this.props.robotId) {
                const key = this.state.robotLists[el] ? el + this.state.robotLists[el].status : el
                return (
                  <li
                    key={key}
                    disabled={this.checkversion(this.state.robotLists[el].robot_baseopk_version)}
                    className={classNames({ current: this.state.robotLists[el].status })}
                  >
                    <Checkbox
                      // disabled={!this.state.robotLists[el].robot_baseopk_version}
                      defaultChecked={this.state.robotLists[el] && this.state.robotLists[el].status}
                      onClick={this.changeRobotStatus.bind(this, el)}
                      label={
                        <span>
                          {this.props.robots[el].robot_name}{' '}
                          {this.checkversion(this.state.robotLists[el].robot_baseopk_version) && (
                            <span className='lowver'>(版本不兼容)</span>
                          )}{' '}
                        </span>
                      }
                    />
                    {}
                  </li>
                )
              }
            })}
          </ul>
        )}
      </div>
    )
  }
  allCheckStatus() {
    return Object.keys(this.state.robotList)
      .map((keys) => {
        return this.state.robotList[keys].checkStatus ? 1 : 0
      })
      .includes(0)
      ? false
      : true
  }
  allCheckClick() {
    const checkStatus = this.allCheckStatus()
    let robotList = Object.assign({}, this.state.robots)
    let robotLists = {}
    this.state.chooseRobotList = []
    this.setState({
      chooseRobotList: this.state.chooseRobotList,
    })
    Object.keys(robotList).map((keys) => {
      robotList[keys].checkStatus = !checkStatus
      robotLists[keys] = robotList[keys]
      this.state.chooseRobotList.push(robotList[keys].robot_id)
    })
    if (checkStatus) {
      //取消选中
      this.state.chooseRobotList = []
      this.state.chooseRobotList.push(this.props.current_robotId)
      this.setState({
        robotList: Object.assign({}, robotLists),
        chooseRobotList: this.state.chooseRobotList,
      })
    } else {
      //选中
      this.setState({
        robotList: Object.assign({}, robotLists),
        chooseRobotList: this.state.chooseRobotList,
      })
    }
    //console.log(this.state.chooseRobotList)
    this.props.chooseList(this.state.chooseRobotList)
  }
  changeRobotsCheckStatus(e, id) {
    this.state.robots[id].checkStatus = e.target.checked
    if (e.target.checked) {
      this.state.chooseRobotList.push(id)
    } else {
      for (var i = 0; i <= this.state.chooseRobotList.length - 1; i++) {
        if (this.state.chooseRobotList[i] == id) {
          this.state.chooseRobotList.splice(i, 1)
        }
      }
    }
    this.setState({
      robotList: Object.assign({}, this.state.robots),
      chooseRobotList: this.state.chooseRobotList,
    })
    this.props.chooseList(this.state.chooseRobotList)
  }
}
export default Robot
export { Robot }
