import React from 'react'
import { connect } from 'react-redux'
import classNames from 'classnames'
import { ButtonSelectFile } from 'biz-components'
import { CHANGE_INFO } from '@redux/action-types'
import { Radio } from 'biz-components'
import { Icon } from 'biz-components'
import { Upload } from 'antd'
import ImgCrop from 'antd-img-crop'
import 'antd/es/modal/style'
import 'antd/es/slider/style'
import { inputMaxCnA, inputMaxEnA } from '@utils/tools'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页标题', state._home)
  return {
    templateInfo: state._home.homeModule.templateInfo,
    langDataActive: state.user.langDataActive,
  }
})
class Title extends React.Component {
  constructor(props) {
    super(props)

    this.uploadingData = 0
    this.state = {
      titleText: this.props.templateInfo.titleBarType == 'text' ? this.props.templateInfo.titleBarContent : '',
      titleImage: this.props.templateInfo.titleBarType == 'Image' ? this.props.templateInfo.titleBarContent : '',
      TitleStyle: this.props.templateInfo.titleBarType,
      isShowTitleWordTip: false,
      isShowSuggWordTip: false,
      maxFunLength: this.props.lang !== 'zh_CN' ? inputMaxEnA : inputMaxCnA,
      tabIndex: 1,
    }
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
  }

  handleChange(title) {
    this.setState({
      TitleStyle: title,
    })
    if (title == 'text') {
      this.props.templateInfo.titleBarContent = this.state.titleText
    } else {
      if (this.props.logo != '') {
        this.props.templateInfo.titleBarContent = this.props.logo
      } else {
        this.props.templateInfo.titleBarContent = this.state.titleImage
      }
    }
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'titleBarType',
        content: title,
      },
    })
    this.props.dispatch({
      type: 'CHANGE_INFO',
      data: {
        type: 'titleBarContent',
        content: this.props.templateInfo.titleBarContent,
      },
    })
  }
  UNSAFE_componentWillReceiveProps() {
    const {
      langDataActive: { lang_code },
    } = this.props

    let maxFunLength = lang_code !== 'zh_CN' ? inputMaxEnA : inputMaxCnA
    this.setState({
      lang: lang_code,
      maxFunLength,
    })
  }
  componentDidMount() {
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
    console.log(this.props.lang, 'componentDidMount66')
  }
  render() {
    var image_url = this.props.devUrl + 'media/'
    let backgroundImg = ''
    if (this.props.templateInfo.templateCode == 'List') {
      backgroundImg = 'module_public/module_skill_home/home1.png'
    } else if (this.props.templateInfo.templateCode == 'Card') {
      backgroundImg = 'module_public/module_skill_home/home2.png'
    } else if (this.props.templateInfo.templateCode == 'Standard') {
      backgroundImg = 'module_public/module_skill_home/home4.png'
    }
    let simpleImg = 'module_public/module_skill_home/conciseimage.png'
    if (this.isKTV) {
      simpleImg = 'module_public/module_skill_home/concisektv.png'
    }

    if (this.props.templateInfo.scenes == 'KTV' && this.props.templateInfo.templateCode == 'Simple') {
      simpleImg = 'module_public/module_skill_home/concisektv.png'
    }

    return (
      <div className='changeTitle a1'>
        {this.props.templateInfo.templateCode != 'Standard' && this.props.templateInfo.templateCode != 'Simple' && (
          <div className='list'>
            <span className='title'>{__('customHome.modalMsg.titleType')}</span>
            <div className='chooseStyle 1'>
              <Radio
                name='sort'
                label={__('customHome.btn.text')}
                checked={this.state.TitleStyle == 'text'}
                onClick={() => {
                  this.handleChange('text')
                }}
              />
              <Radio
                name='sort'
                label={__('customHome.btn.text')}
                checked={this.state.TitleStyle == 'Image'}
                onClick={() => {
                  this.handleChange('Image')
                }}
              />
            </div>
          </div>
        )}
        {this.state.TitleStyle == 'text' && this.props.templateInfo.templateCode != 'Simple' && (
          <div className='list'>
            <span className='title'>{__('customHome.modalMsg.mainTitleName')}</span>
            <p className={classNames({ newTitle: true })}>
              <input
                d='ff 0'
                type='text'
                maxLength={this.state.maxFunLength}
                value={this.props.templateInfo.titleBarType == 'text' ? this.props.templateInfo.titleBarContent : ''}
                onFocus={() => {
                  this.setState({ isShowTitleWordTip: true })
                }}
                onBlur={() => {
                  this.setState({ isShowTitleWordTip: false })
                }}
                onChange={(e) => {
                  this.setState({
                    titleText: e.target.value,
                  })
                  this.props.templateInfo.titleBarContent = e.target.value
                  this.props.dispatch({
                    type: 'CHANGE_INFO',
                    data: {
                      type: 'titleBarContent',
                      content: this.props.templateInfo.titleBarContent,
                    },
                  })
                }}
              />
              {this.state.isShowTitleWordTip ? (
                <span>
                  <em>{this.props.templateInfo.titleBarContent.length}</em> / <em>{this.state.maxFunLength}</em>
                </span>
              ) : (
                ''
              )}
            </p>
          </div>
        )}
        {this.props.templateInfo.templateCode == 'Simple' && (
          <div className='list'>
            <span className='prompt'>
              <Icon icon='Alert_Default_Info' className='prompt_tips' />
              {__('customHome.warm.msg2')}
            </span>
            <span className='simple_title'>{__('customHome.modalMsg.mainTitleName')}</span>
            <p className={classNames({ newTitle: true })}>
              <input
                type='text'
                maxLength={11}
                value={this.props.templateInfo.conciseContent.title}
                onFocus={() => {
                  this.setState({ isShowTitleWordTip: true })
                }}
                onBlur={() => {
                  this.setState({ isShowTitleWordTip: false })
                }}
                onChange={(e) => {
                  this.setState({
                    titleText: e.target.value,
                  })
                  // this.props.templateInfo.titleBarContent = e.target.value;
                  let conciseContent = this.props.templateInfo.conciseContent
                  conciseContent.title = e.target.value
                  this.props.dispatch({
                    type: 'CHANGE_INFO',
                    data: {
                      type: 'conciseContent',
                      content: conciseContent,
                    },
                  })
                }}
              />
              {this.state.isShowTitleWordTip ? (
                <span>
                  <em>{this.props.templateInfo.conciseContent.title.length}</em> / <em>{11}</em>
                </span>
              ) : (
                ''
              )}
            </p>

            <div className='list'>
              <span className='simple_title'>{__('customHome.label.funPic')}</span>
              <p
                className={
                  this.props.templateInfo.conciseContent.image != simpleImg
                    ? 'list_area upload_area simple_list_area 1 '
                    : 'list_area simple_list_area 2'
                }
              >
                {/* <ButtonSelectFile className="upload-btn square"
                            onSelect={(evt) => {
                                
                                    this.props.selectImg(evt, "conciseImg", "")
                               
                            }}
                        /> */}
                {/* <ButtonSelectFile
									className="upload-btn square"
									onSelect={(evt) => {
										this.props.selectImg(evt, "conciseImg", "")
									}}
								/> */}
                <ImgCrop
                  aspect={980 / 846}
                  grid
                  modalTitle={__('customHome.btn.img_cut')} //
                  modalWidth='650px'
                  modalOk={__('customHome.btn.confirm')}
                  modalCancel={__('customHome.btn.cancel2')}
                  fillColor={'transparent'}
                >
                  <Upload
                    customRequest={() => {}}
                    className='upload-btn square'
                    listType='picture-card'
                    beforeUpload={(e) => {
                      console.log(e)
                      let _this = this
                      let img = e
                      let reader = new FileReader()
                      reader.readAsDataURL(img)
                      reader.onload = function (e) {
                        let image = new Image()
                        image.onload = function () {
                          let mycanvas = document.querySelector('#myCanvas')
                          let ctx = mycanvas.getContext('2d')
                          ctx.drawImage(image, 0, 0, 980, 846)
                          let dataurl = mycanvas.toDataURL('image/png')
                          function dataURLtoBlob(dataurl) {
                            let arr = dataurl.split(','),
                              mime = arr[0].match(/:(.*?);/)[1],
                              bstr = atob(arr[1]),
                              n = bstr.length,
                              u8arr = new Uint8Array(n)
                            while (n--) {
                              u8arr[n] = bstr.charCodeAt(n)
                            }
                            return new Blob([u8arr], { type: mime })
                          }
                          _this.props.selectImg(dataURLtoBlob(dataurl), 'conciseImg', '')
                        }
                        image.src = e.target.result
                      }
                      return true
                    }}
                    onChange={(e) => {
                      // console.log(e)
                    }}
                  ></Upload>
                </ImgCrop>

                {this.props.templateInfo.conciseContent.image != simpleImg ? (
                  <img src={image_url + this.props.templateInfo.conciseContent.image} className='simpleIcon_default' />
                ) : (
                  ''
                )}

                <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
              </p>
              <p
                className='list_area'
                onClick={() => {
                  if (this.props.templateInfo.templateCode == 'List') {
                    backgroundImg = 'module_public/module_skill_home/home1.png'
                  } else if (this.props.templateInfo.templateCode == 'Card') {
                    backgroundImg = 'module_public/module_skill_home/home2.png'
                  } else if (this.props.templateInfo.templateCode == 'Standard') {
                    backgroundImg = 'module_public/module_skill_home/home4.png'
                  }
                  let conciseContent = this.props.templateInfo.conciseContent
                  conciseContent.image = 'module_public/module_skill_home/conciseimage.png'
                  if (this.isKTV) {
                    conciseContent.image = 'module_public/module_skill_home/concisektv.png'
                  }
                  if (this.props.templateInfo.scenes == 'KTV' && this.props.templateInfo.templateCode == 'Simple') {
                    conciseContent.image = 'module_public/module_skill_home/concisektv.png'
                  }
                  this.props.dispatch({
                    type: 'CHANGE_INFO',
                    data: {
                      type: 'conciseContent',
                      content: conciseContent,
                    },
                  })
                }}
              >
                <p className='simpleBackImg' style={{ backgroundImage: `url(${image_url + simpleImg})` }}></p>

                <span className='defaultInfo'>{__('customHome.pic.default')}</span>
              </p>
              <p className='list_area fon_list_area'>
                <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
                <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：980px*846px</span>
                <span className='uploadTips'>
                  2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
                </span>
                <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
              </p>
            </div>
            <div className='list'>
              <span className='simple_title'>{__('customHome.label.question')}</span>
              <p className={classNames({ newTitle: true })}>
                <input
                  type='text'
                  maxLength={11}
                  disabled1={
                    this.props.templateInfo.conciseContent.image == 'module_public/module_skill_home/conciseimage.png'
                  }
                  value={this.props.templateInfo.conciseContent.suggestion}
                  onFocus={() => {
                    this.setState({ isShowSuggWordTip: true })
                  }}
                  onBlur={() => {
                    this.setState({ isShowSuggWordTip: false })
                  }}
                  onChange={(e) => {
                    this.setState({
                      suggestionText: e.target.value,
                    })
                    // this.props.templateInfo.titleBarContent = e.target.value;
                    let conciseContent = this.props.templateInfo.conciseContent
                    conciseContent.suggestion = e.target.value
                    this.props.dispatch({
                      type: 'CHANGE_INFO',
                      data: {
                        type: 'conciseContent',
                        content: conciseContent,
                      },
                    })
                  }}
                />
                {this.state.isShowSuggWordTip ? (
                  <span>
                    <em>{this.props.templateInfo.conciseContent.suggestion.length}</em> / <em>{11}</em>
                  </span>
                ) : (
                  ''
                )}
              </p>
            </div>
          </div>
        )}
        {this.state.TitleStyle == 'Image' && (
          <div className='list'>
            <span className='title'>{__('customHome.pic.uploadPhoto')}</span>
            <p className={this.props.logo != '' ? 'list_area upload_area' : 'list_area'}>
              {/* <ButtonSelectFile
								className="upload-btn square"
								onSelect={(evt) => {
									this.props.selectImg(evt, "logo", "")
								}}
							/> */}
              <ImgCrop
                aspect={1000 / 200}
                grid
                modalTitle={__('customHome.btn.img_cut')} //
                modalWidth='650px'
                modalOk={__('customHome.btn.confirm')}
                modalCancel={__('customHome.btn.cancel2')}
                fillColor={'transparent'}
              >
                <Upload
                  customRequest={() => {}}
                  className='upload-btn square'
                  listType='picture-card'
                  beforeUpload={(e) => {
                    let _this = this
                    let img = e
                    let reader = new FileReader()
                    reader.readAsDataURL(img)
                    reader.onload = function (e) {
                      let image = new Image()
                      image.onload = function () {
                        let mycanvas = document.querySelector('#myCanvasLogo')
                        let ctx = mycanvas.getContext('2d')
                        ctx.drawImage(image, 0, 0, 1000, 200)
                        let dataurl = mycanvas.toDataURL('image/png')
                        function dataURLtoBlob(dataurl) {
                          let arr = dataurl.split(','),
                            mime = arr[0].match(/:(.*?);/)[1],
                            bstr = atob(arr[1]),
                            n = bstr.length,
                            u8arr = new Uint8Array(n)
                          while (n--) {
                            u8arr[n] = bstr.charCodeAt(n)
                          }
                          return new Blob([u8arr], { type: mime })
                        }
                        _this.props.selectImg(dataURLtoBlob(dataurl), 'logo', '')
                      }
                      image.src = e.target.result
                    }
                    return true
                  }}
                  onChange={(e) => {
                    // console.log(e)
                  }}
                ></Upload>
              </ImgCrop>
              <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
              {this.props.logo != '' ? <img src={image_url + this.props.logo} className='newLogo' /> : ''}
            </p>
            <p className='list_area'>
              <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
              <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：1000px*200px</span>
              <span className='uploadTips'>
                2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
              </span>
              <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
            </p>
          </div>
        )}
        <canvas style={{ display: 'none' }} id='myCanvas' width='980' height='846' />
        <canvas style={{ display: 'none' }} id='myCanvasLogo' width='1000' height='200' />
      </div>
    )
  }
}
export default Title
export { Title }
