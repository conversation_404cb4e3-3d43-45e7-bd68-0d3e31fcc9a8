/* eslint-disable indent */
import React from 'react'
import { connect } from 'react-redux'
import classNames from 'classnames'
import { Radio } from 'biz-components'
import { Button } from 'biz-components'
import { Input } from 'biz-components'
import { Icon } from 'biz-components'
import { ButtonSelectFile } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
import ChooseFaceTemplate from './chooseFaceTemplate'
import { checkReleaseRaw } from '@utils/tools'
import { Upload } from 'antd'
import ImgCrop from 'antd-img-crop'
import 'antd/es/modal/style'
import 'antd/es/slider/style'
const initDatachs = {
  askMeQuestions: '有问题都可以问我',
  helpWarm: '需要帮忙吗',
}
const initDataenglish = {
  askMeQuestions: 'You can ask me questions',
  helpWarm: 'Can I help you?',
}
const initDateEnglishWarmarrayTxt = [
  'What can you do?',
  'Where is the restroom?',
  'What’s your name?',
  'Where is the meeting room?',
  'What are you doing here?',
  'How old are you?',
]
const initDateEnglishWarmarray = ['What can you do?', 'Where is the restroom?']
const initDataEnglishTitle = 'You can ask me:'
const initDataChsTitle = '你可以问我'
const initDataarabic = {
  askMeQuestions: 'هل تحتاج مساعدة ؟   ' + '1',
  helpWarm: 'تسألني أسئلة',
}

@connect((state) => {
  return {
    // eslint-disable-next-line indent
    templateInfo: state._home.homeModule.templateInfo,
    robotId: state.robots.current,
    robots: state.robots.robots,
  }
})
class BackImg extends React.Component {
  //  askMe =  ['initData'+this.props.lang].askMeQuestions ;
  //helpMe =  ['initData'+this.props.lang].helpWarm;
  askMe =
    this.props.lang === 'chs'
      ? initDatachs.askMeQuestions
      : this.props.lang === 'english'
      ? initDataenglish.askMeQuestions
      : initDataarabic.askMeQuestions
  helpMe =
    this.props.lang === 'chs'
      ? initDatachs.helpWarm
      : this.props.lang === 'english'
      ? initDataenglish.helpWarm
      : initDataarabic.helpWarm
  cardTitle = this.props.lang === 'chs' ? initDataChsTitle : initDataEnglishTitle

  state = {
    tabIndex: 0,
    nav_content: [__('customHome.btn.homepageBackground'), __('customHome.label.expressions')],
    haveCover: this.props.templateInfo.haveCover == undefined ? true : this.props.templateInfo.haveCover,
    version13: false,
    overVersion51: false,
    overVersion57: false,
    sugg1: '',
    style: 'Standard',
    // faceStyle: this.props.templateInfo.bubbleContents ? this.props.templateInfo.bubbleContents.faceStyle :  'Bubble',
    faceStyle:
      this.props.lang === 'english'
        ? this.props.templateInfo.bubbleContents && this.props.templateInfo.bubbleContents.faceStyle === 'Standard'
          ? this.props.templateInfo.bubbleContents.faceStyle
          : 'Card'
        : this.props.templateInfo.bubbleContents
        ? this.props.templateInfo.bubbleContents.faceStyle
        : 'Bubble',
    // rawbubbleContents: this.props.templateInfo.bubbleContents ? this.props.templateInfo.bubbleContents.raw : [this.askMe, this.helpMe, '', '', '', ''],
    rawbubbleContents: this.props.templateInfo.bubbleContents
      ? this.props.templateInfo.bubbleContents.raw
      : this.props.lang !== 'english'
      ? [this.askMe, this.helpMe, '', '', '', '']
      : ['What can you do?', 'Where is the restroom?', '', '', '', ''],

    cardTitle: this.props.templateInfo.bubbleContents ? this.props.templateInfo.bubbleContents.title : this.cardTitle,
    placeholder1: __('customHome.qa.askMeQuestions'),
    placeholder2: __('customHome.qa.helpWarm'),
    // maxLength: this.props.lang !== 'chs' ? 24 : 12,
    maxLength: this.props.lang !== 'chs' ? (this.props.lang === 'english' ? 45 : 24) : 12,
    cardTitleMaxLength: 20,
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.lang && nextProps.lang && nextProps.lang != this.props.lang) {
      this.setState({
        lang: nextProps.lang,
      })
    }
    //数据有更新
    if (
      this.props.templateInfo &&
      this.props.templateInfo.bubbleContents &&
      nextProps.templateInfo.bubbleContents &&
      nextProps.templateInfo.bubbleContents != this.props.templateInfo.bubbleContents
    ) {
      console.log('数据更新')
      console.log(nextProps.templateInfo.bubbleContents, 'nextProps.templateInfo.bubbleContents')
      this.setState({
        faceStyle: nextProps.templateInfo.bubbleContents.faceStyle,
        raw: nextProps.templateInfo.bubbleContents.raw,
        robot: nextProps.templateInfo.bubbleContents.robot,
        title: nextProps.templateInfo.bubbleContents.title,
      })
    }

    // 数据重置
    if (
      this.props.templateInfo &&
      this.props.templateInfo.bubbleContents &&
      nextProps.templateInfo.bubbleContents == undefined &&
      nextProps.templateInfo.bubbleContents != this.props.templateInfo.bubbleContents
    ) {
      console.log('数据重置')
      console.log(initDateEnglishWarmarray, 'initDateEnglishWarmarray')
      this.setState(
        {
          // faceStyle: 'Bubble',
          faceStyle: this.props.lang === 'english' ? 'Card' : 'Bubble',
          raw:
            this.props.lang !== 'english'
              ? [this.askMe, this.helpMe, '', '', '', '']
              : ['What can you do?', 'Where is the restroom?', '', '', '', ''],
          robot:
            this.props.lang !== 'english'
              ? [this.askMe, this.helpMe, '', '', '', '']
              : ['What can you do?', 'Where is the restroom?', '', '', '', ''],
          rawbubbleContents:
            this.props.lang !== 'english'
              ? [this.askMe, this.helpMe, '', '', '', '']
              : ['What can you do?', 'Where is the restroom?', '', '', '', ''],
          title: this.cardTitle,
        },
        () => {
          let rawbubbleContents =
            this.props.lang !== 'english'
              ? [this.askMe, this.helpMe, '', '', '', '']
              : ['What can you do?', 'Where is the restroom?', '', '', '', '']

          this.props.updateBubble1('')
          let bubbleContents = {
            // faceStyle: 'Bubble',
            faceStyle: this.props.lang === 'english' ? 'Card' : 'Bubble',
            robot: rawbubbleContents.filter((e) => e),
            raw: rawbubbleContents,
            title: this.cardTitle,
          }

          this.props.dispatch({
            type: 'CHANGE_INFO',
            data: {
              type: 'bubbleContents',
              content: bubbleContents,
            },
          })
        },
      )
    }
  }
  componentDidMount() {
    // console.log('this.props.lang', this.props.lang)
    this.props.addCover(this.state.haveCover)

    var localrobotId = localStorage.getItem('globallRobotId') || this.props.robotId

    Object.keys(this.props.robots).map((el) => {
      if (localrobotId == el) {
        let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
        let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
        let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
        this.setState({
          overVersion51,
          overVersion57,
        })
        console.log(this.props.robots[el].version, versionGap)
        console.log('auto change info')
        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'haveCover',
            content: this.state.haveCover,
          },
        })
        if (versionGap) {
          //4.13
          this.setState({
            version13: true,
          })
        } else {
          //非4.13
          this.setState({
            version13: false,
          })
        }
      }
    })

    if (!this.props.templateInfo.bubbleContents) {
      console.log('哈哈哈哈')
      console.log(this.props.templateInfo)
      console.log(initDateEnglishWarmarray, 'initDateEnglishWarmarray')
      let rawbubbleContents = this.state.rawbubbleContents

      let bubbleContents = {
        faceStyle: this.state.faceStyle,
        robot: rawbubbleContents.filter((e) => e),
        raw: rawbubbleContents,
        title: this.state.cardTitle,
      }

      this.props.dispatch({
        type: 'CHANGE_INFO',
        data: {
          type: 'bubbleContents',
          content: bubbleContents,
        },
      })
    }
    this.isKTV = location.href.indexOf('ktv.ainirobot.com') > -1 ? true : false
  }
  componentDidUpdate(prevProps) {
    if (prevProps.robotId != this.props.robotId) {
      Object.keys(this.props.robots).map((el) => {
        if (this.props.robotId == el) {
          let versionGap = checkReleaseRaw(this.props.robots[el].version, 'V4.17', true)
          let overVersion51 = checkReleaseRaw(this.props.robots[el].version, 'V5.1', true)
          let overVersion57 = checkReleaseRaw(this.props.robots[el].version, 'V5.7', true)
          this.setState({
            overVersion51,
            overVersion57,
          })
          console.log('-----------------', this.props.robots[el].version, versionGap)
          if (versionGap) {
            //4.13
            this.setState({
              version13: true,
            })
          } else {
            //非4.12
            this.setState({
              version13: false,
            })
          }
        }
      })
    }
  }
  chooseFaceStyle(faceStyle) {
    console.log(faceStyle, 'face')
    this.setState(
      {
        faceStyle,
      },
      () => {
        closeModal()

        let rawbubbleContents = this.state.rawbubbleContents

        let bubbleContents = {
          faceStyle,
          robot: rawbubbleContents.filter((e) => e),
          raw: rawbubbleContents,
          title: this.state.cardTitle,
        }

        this.props.dispatch({
          type: 'CHANGE_INFO',
          data: {
            type: 'bubbleContents',
            content: bubbleContents,
          },
        })
      },
    )
  }
  render() {
    console.log(
      'this.props.lang',
      this.props.lang,
      this.state.cardTitle,
      this.state.rawbubbleContents,
      this.askMe,
      this.props.templateInfo,
      this.props.templateInfo.bubbleContents,
    )
    var image_url = this.props.devUrl + 'media/'
    let backgroundImg = ''
    if (this.props.templateInfo.templateCode == 'List') {
      backgroundImg = 'module_public/module_skill_home/home1.png'
    } else if (this.props.templateInfo.templateCode == 'Card') {
      backgroundImg = 'module_public/module_skill_home/home2.png'
    } else if (this.props.templateInfo.templateCode == 'Standard') {
      backgroundImg = 'module_public/module_skill_home/home4.png'
    } else if (this.props.templateInfo.templateCode == 'Simple') {
      if (!this.isKTV) {
        backgroundImg = 'module_public/module_skill_home/simpleBg.png'
      } else {
        backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
      }
      if (this.props.templateInfo.scenes == 'KTV' && this.props.templateInfo.templateCode == 'Simple') {
        backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
      }
    }
    return (
      <div className='backImg'>
        <ul className='nav'>
          {this.state.nav_content.map((item, index) => {
            return (
              <li
                key={index}
                className={this.state.tabIndex == index ? 'active' : ''}
                onClick={() => {
                  this.setState({
                    tabIndex: index,
                  })
                  this.props.changeTab_backImg(index)
                }}
              >
                {item}
              </li>
            )
          })}
        </ul>
        {this.state.version13 &&
          this.state.tabIndex == 0 &&
          this.props.templateInfo.templateCode == 'Standard' &&
          // &&!this.props.homeModule && !this.props.backImgstyle
          (!this.props.isShowSetting || !this.props.isDefault) && (
            <div className='marked'>
              <Radio
                name='sort'
                label={__('customHome.pic.picWarm')}
                checked={this.state.haveCover == true}
                onClick={() => {
                  this.setState(
                    {
                      haveCover: !this.state.haveCover,
                    },
                    () => {
                      this.props.addCover(this.state.haveCover)
                      this.props.dispatch({
                        type: 'CHANGE_INFO',
                        data: {
                          type: 'haveCover',
                          content: this.state.haveCover,
                        },
                      })
                    },
                  )
                }}
              />
            </div>
          )}
        {this.state.tabIndex == 0 && !this.state.version13 ? (
          <p className='backImg_tips'>{__('customHome.pic.backgroundWarm1')}</p>
        ) : (
          ''
        )}
        {this.state.tabIndex == 1 && !this.state.version1 ? (
          <p className='backImg_tips'>{__('customHome.pic.backgroundWarm2')}</p>
        ) : (
          ''
        )}
        {this.state.tabIndex == 1 && <div className='bgtitle'>{__('customHome.pic.backgroundPicture')}</div>}
        <div className='list'>
          {/* <span className="title">背景图片</span> */}
          <p
            className={
              (this.props.templateInfo.background != backgroundImg && this.state.tabIndex == 0) ||
              (this.props.templateInfo.faceBackground &&
                this.props.templateInfo.faceBackground != '' &&
                this.state.tabIndex == 1)
                ? 'list_area upload_area'
                : 'list_area'
            }
          >
            {/* <ButtonSelectFile className="upload-btn square"
                            onSelect={(evt) => {
                                if (this.state.tabIndex == 0) {
                                    this.props.selectImg(evt, "backImg", "")
                                } else {
                                    this.props.selectImg(evt, "facebackImg", "")
                                }
                            }}
                        /> */}
            <ImgCrop
              aspect={1200 / 1920}
              grid
              modalTitle={__('customHome.btn.img_cut')} //
              modalWidth='650px'
              modalOk={__('customHome.btn.confirm')}
              modalCancel={__('customHome.btn.cancel2')}
              fillColor={'transparent'}
            >
              <Upload
                customRequest={() => {}}
                className='upload-btn square'
                listType='picture-card'
                beforeUpload={(e) => {
                  console.log(e)
                  let _this = this
                  let img = e
                  let reader = new FileReader()
                  reader.readAsDataURL(img)
                  reader.onload = function (e) {
                    let image = new Image()
                    image.onload = function () {
                      let mycanvas = document.querySelector('#mybackCanvas')
                      let ctx = mycanvas.getContext('2d')
                      ctx.drawImage(image, 0, 0, 1200, 1920)
                      let dataurl = mycanvas.toDataURL('image/png')
                      function dataURLtoBlob(dataurl) {
                        let arr = dataurl.split(','),
                          mime = arr[0].match(/:(.*?);/)[1],
                          bstr = atob(arr[1]),
                          n = bstr.length,
                          u8arr = new Uint8Array(n)
                        while (n--) {
                          u8arr[n] = bstr.charCodeAt(n)
                        }
                        return new Blob([u8arr], { type: mime })
                      }
                      // _this.props.selectImg(dataURLtoBlob(dataurl), "backImg", "")
                      if (_this.state.tabIndex == 0) {
                        _this.props.selectImg(dataURLtoBlob(dataurl), 'backImg', '')
                      } else {
                        _this.props.selectImg(dataURLtoBlob(dataurl), 'facebackImg', '')
                      }
                    }
                    image.src = e.target.result
                  }
                  return true
                }}
                onChange={(e) => {
                  // console.log(e)
                }}
              ></Upload>
            </ImgCrop>
            {this.state.tabIndex == 0 && this.props.templateInfo.background != backgroundImg ? (
              <img src={image_url + this.props.templateInfo.background} className='newIcon_default' />
            ) : (
              ''
            )}
            {this.state.tabIndex == 1 &&
            this.props.templateInfo.faceBackground &&
            this.props.templateInfo.faceBackground != '' ? (
              <img src={image_url + this.props.templateInfo.faceBackground} className='newIcon_default' />
            ) : (
              ''
            )}
            <span className='uploadInfo'>{__('customHome.pic.uploadNew')}</span>
          </p>
          <p
            className='list_area'
            onClick={() => {
              if (this.props.templateInfo.templateCode == 'List') {
                backgroundImg = 'module_public/module_skill_home/home1.png'
              } else if (this.props.templateInfo.templateCode == 'Card') {
                backgroundImg = 'module_public/module_skill_home/home2.png'
              } else if (this.props.templateInfo.templateCode == 'Standard') {
                backgroundImg = 'module_public/module_skill_home/home4.png'
              } else if (this.props.templateInfo.templateCode == 'Simple') {
                // backgroundImg = "module_public/module_skill_home/simpleBg.png";

                if (!this.isKTV) {
                  backgroundImg = 'module_public/module_skill_home/simpleBg.png'
                } else {
                  backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
                }

                if (this.props.templateInfo.scenes == 'KTV' && this.props.templateInfo.templateCode == 'Simple') {
                  backgroundImg = 'module_public/module_skill_home/simplektvBg.png'
                }
              }
              if (this.state.tabIndex == 0) {
                this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'background',
                    content: backgroundImg,
                  },
                })
                this.props.deleteBackImg()
              } else {
                this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    key: 2,
                    type: 'faceBackground',
                    content: '',
                  },
                })
                this.props.deleteBackImg()
                this.props.faceBackground()
              }
            }}
          >
            {this.state.tabIndex == 0 ? (
              <img src={image_url + backgroundImg} className='defaultBackImg' />
            ) : (
              <p className='defaultBackImg'></p>
            )}
            <span className='defaultInfo'>{__('customHome.pic.default')}</span>
          </p>
          <p className='list_area'>
            <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
            <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：1200px*1920px</span>
            <span className='uploadTips'>
              2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
            </span>
            <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
          </p>
        </div>
        {this.state.tabIndex == 0 && (
          <div>
            <div className='error'>
              <img src={require('@assets/png/meui/error.png')} className='imageTips' />
              <p>{__('customHome.pic.wrong')}</p>
            </div>
            <div className='correct'>
              <img src={require('@assets/png/meui/correct.png')} className='imageTips' />
              <p>{__('customHome.pic.correct')}</p>
            </div>
          </div>
        )}
        <canvas style={{ display: 'none' }} id='mybackCanvas' width='1200' height='1920' />
        {((this.state.overVersion51 && this.state.tabIndex == 1 && this.props.lang === 'zh_CN') ||
          (this.state.overVersion57 && this.state.tabIndex == 1 && this.props.lang !== 'zh_CN')) && (
          <div className={classNames({ face_temp: true })}>
            <div className='title'>{__('customHome.pic.selectExpressionTemplate')}</div>
            <Button
              icon='map-_edit'
              appearance='hollow'
              className='bubble_mode'
              onClick={() => {
                console.log(this.state)
                modal({
                  content: (
                    <ChooseFaceTemplate
                      FaceStyle={this.state.faceStyle}
                      chooseFaceStyle={this.chooseFaceStyle.bind(this)}
                      lang={this.props.lang}
                    />
                  ),
                  confirm: false,
                  cancel: false,
                })
              }}
            >
              {this.state.faceStyle == 'Bubble'
                ? __('customHome.pic.bubble')
                : this.state.faceStyle == 'Card'
                ? __('customHome.pic.card')
                : __('customHome.pic.simple')}
            </Button>
            {this.props.lang === 'zh_CN' && (
              <p className='marked'>
                <Icon icon='Alert_Default_Warning' className='marked_icon' />
                <span>{__('customHome.pic.bubbleWarm')}</span>
              </p>
            )}
            {this.props.lang !== 'zh_CN' && (
              <p className='marked'>
                <Icon icon='Alert_Default_Warning' className='marked_icon 1' />
                <span>{__('customHome.pic.cardWarm')}</span>
              </p>
            )}
            {this.state.overVersion57 && this.state.faceStyle == 'Card' && (
              <div className='cardTitle line_name'>
                <div className='title'>{__('customHome.qa.cardTitle')}</div>
                <Input
                  type='text'
                  placeholder={this.state.cardTitle}
                  maxLength={this.state.cardTitleMaxLength}
                  value={this.state.cardTitle}
                  onChange={(e) => {
                    let txt = e.target.value
                    const rawbubbleContents = this.state.rawbubbleContents
                    let bubbleContents = {
                      faceStyle: this.state.faceStyle,
                      robot: rawbubbleContents,
                      raw: rawbubbleContents,
                      title: txt.trim(),
                    }
                    this.setState(
                      {
                        rawbubbleContents,
                        cardTitle: txt,
                      },
                      () => {
                        this.props.dispatch({
                          type: 'CHANGE_INFO',
                          data: {
                            type: 'bubbleContents',
                            content: bubbleContents,
                          },
                        })
                      },
                    )
                  }}
                  onFocus={() => {
                    this.setState({
                      focusIdx: 'cardTitle',
                    })
                  }}
                  onBlur={() => {
                    this.setState({
                      focusIdx: null,
                    })
                  }}
                />
                {this.state.focusIdx === 'cardTitle' && (
                  <span className='count'>{`${this.state.cardTitle.length}/${this.state.cardTitleMaxLength}`}</span>
                )}
              </div>
            )}
            {((this.state.overVersion51 && this.state.faceStyle == 'Bubble') ||
              (this.state.overVersion57 && this.state.faceStyle == 'Card')) &&
              this.state.rawbubbleContents.map((item, index) => {
                return (
                  <div className='line_name 1'>
                    <div className='title'>
                      {__('customHome.qa.bubbleContent')}
                      {index + 1}
                    </div>
                    <Input
                      type='text'
                      // placeholder={index % 2 == 0 ? this.state.placeholder1 : this.state.placeholder2}
                      placeholder={
                        this.state.faceStyle == 'Card'
                          ? item === ''
                            ? initDateEnglishWarmarrayTxt[index]
                            : item
                          : index % 2 == 0
                          ? this.state.placeholder1
                          : this.state.placeholder2
                      }
                      maxLength={this.state.maxLength}
                      value={item}
                      onChange={(e) => {
                        let text = e.target.value
                        let rawbubbleContents = this.state.rawbubbleContents
                        rawbubbleContents[index] = text
                        if (this.props.lang === 'english') {
                          this.props.updateBubble1(text)
                        } else {
                          if (index != 1) {
                            this.props.updateBubble1(text)
                          }
                        }

                        let bubbleContents = {
                          // "faceStyle": "Bubble",
                          faceStyle: this.state.faceStyle,
                          robot: rawbubbleContents.filter((e) => e.trim()),
                          raw: rawbubbleContents,
                          title: this.state.cardTitle,
                        }
                        this.setState(
                          {
                            rawbubbleContents,
                          },
                          () => {
                            this.props.dispatch({
                              type: 'CHANGE_INFO',
                              data: {
                                type: 'bubbleContents',
                                content: bubbleContents,
                              },
                            })
                          },
                        )
                      }}
                      onFocus={() => {
                        this.setState({
                          focusIdx: index,
                        })
                      }}
                      onBlur={() => {
                        this.setState({
                          focusIdx: null,
                        })
                      }}
                    />
                    {/* {0==index&& <span className="count">{`${item.length}/12`}</span>} */}
                    {this.state.focusIdx == index ? (
                      <span className='count'>{`${item.length}/${this.state.maxLength}`}</span>
                    ) : (
                      <div
                        className='bpreview'
                        onClick={() => {
                          let rawbubbleContents = this.state.rawbubbleContents
                          let text = rawbubbleContents[index]
                          if (this.props.lang !== 'english') {
                            if (index != 1) {
                              this.props.updateBubble1(text)
                            }
                          } else {
                            this.props.updateBubble1(text)
                          }
                        }}
                      >
                        {' '}
                        <Icon icon='guide_editor_preview_b' />
                        <span>{__('customHome.btn.preview')}</span>
                      </div>
                    )}
                    {0 != index && (
                      <Icon
                        icon='monitor_x '
                        className='delete'
                        onClick={() => {
                          let rawbubbleContents = this.state.rawbubbleContents
                          rawbubbleContents[index] = ''
                          // this.props.updateBubble1(text)
                          let bubbleContents = {
                            // "faceStyle": "Bubble",
                            faceStyle: this.state.faceStyle,
                            robot: rawbubbleContents.filter((e) => e),
                            raw: rawbubbleContents,
                            title: this.state.cardTitle,
                          }

                          this.setState(
                            {
                              rawbubbleContents,
                            },
                            () => {
                              this.props.dispatch({
                                type: 'CHANGE_INFO',
                                data: {
                                  type: 'bubbleContents',
                                  content: bubbleContents,
                                },
                              })
                            },
                          )
                        }}
                      />
                    )}
                  </div>
                )
              })}

            {/* <input type="text" placeholder={this.state.sugg1}/> */}
          </div>
        )}
      </div>
    )
  }
}

export default BackImg
export { BackImg }
