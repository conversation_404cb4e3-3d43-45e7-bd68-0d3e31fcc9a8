import React from 'react'
import { extend } from 'koot'

@extend({
  styles: require('./chooseModal.less'),
})
class Progress extends React.Component {
  state = {
    style: this.props.modalStyle,
  }
  render() {
    return (
      <div className={this.props.className}>
        <div className='progress_modal'>
          <p className='progress_title'>{this.props.progress_title || __('customHome.modalMsg.syncingNewHomepage')}</p>
          <div className='percent'>
            <div className='percentBg'></div>
            <div className='percentNow'></div>
          </div>
        </div>
      </div>
    )
  }
}
export default Progress
export { Progress }
