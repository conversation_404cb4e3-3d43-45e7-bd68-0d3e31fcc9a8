import React from 'react'
import Robot from './robot'
import { connect } from 'react-redux'
import { Icon } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
import { message } from 'antd'
import PublishRobot from './publishRobot'
// import {PublishRobot } from 'biz-components'
// import {PublishRobot} from '/Users/<USER>/ainirobot/robot_code/biz_components'
@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页机器人列表', state.robots.robots)
  return {
    robotId: state.robots.current,
    robots: state.robots.robots,
    templateCode: state._home.homeModule ? state._home.homeModule.templateInfo.templateCode : '',
  }
})
class Tips extends React.Component {
  constructor(props) {
    super(props)

    this.uploadingData = 0
    this.state = {
      checkedArray: [],
      unCheckArray: [],
      success: [],
      fail: [],
      lowOpkVersion: false,
      isShowSetting: false,
      isDefault: false,
    }
    this.isKTV = false
  }

  getRobotId(data, unCheckArray) {
    // publish Robot  组件更新机器人的方法
    console.log(unCheckArray, 'wei选机器人列表')
    console.log(data, 'yi选机器人列表')
    this.setState(
      {
        robotArray: data,
        unCheckArray: unCheckArray,
      },
      () => {
        let rid = []
        rid = data.map((item) => {
          return item.robot_id
        })
        this.props.chooseRobots(rid)
      },
    )
  }
  getRobot(cb) {
    // 设置选中的机器人 和待选机器人
    const idInRobot = this.state.checkedArray, //
      _allRobot = JSON.parse(Object.assign(JSON.stringify(this.props.robots)))
    let checkedArray = [],
      unCheckArray = [],
      allRobot = []
    Object.keys(_allRobot).map((robot, val) => {
      let versionGap = checkReleaseRaw(_allRobot[robot].version, 'V5.10', true)
      versionGap = true
      if (versionGap) {
        allRobot.push(_allRobot[robot])
      }
    })

    if (idInRobot.length == 0) {
      let currRobot = this.props.robots[this.props.robotId]
      if (currRobot) {
        // currRobot.addstatus = true;
        idInRobot.push(currRobot)
      }
    }

    Object.keys(allRobot).map((robot, index) => {
      allRobot[robot].addstatus = false
      // allRobot[robot].state==1&&

      unCheckArray.push(allRobot[robot])
      if (idInRobot.length > 0) {
        idInRobot.map((id, key) => {
          if (allRobot[robot].robot_id === id.robot_id) {
            checkedArray.push(allRobot[index])
            unCheckArray[index].addstatus = true
          }
        })
      }
    })

    if (checkedArray.length == 0) {
      let currRobot = this.props.robots[this.props.robotId]
      if (currRobot) {
        // currRobot.addstatus = true;
        checkedArray.push(currRobot)
      }
    }

    this.setState(
      {
        robotArray: checkedArray,
        checkedArray,
        unCheckArray: unCheckArray,
      },
      () => {
        let rid = [],
          data = checkedArray
        rid = data.map((item) => {
          return item.robot_id
        })
        this.props.chooseRobots(rid)
        cb && cb()
      },
    )
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.isShowSetting != undefined && nextProps.isShowSetting != this.state.isShowSetting) {
      this.setState({
        isShowSetting: nextProps.isShowSetting,
      })
    }

    if (nextProps.isDefault != undefined && nextProps.isDefault != this.state.isDefault) {
      this.setState({
        isDefault: nextProps.isDefault,
      })
    }
  }
  componentDidMount() {
    // this.getRobot();
    this.isKTV = location.href.indexOf('ktv.ainirobot') > -1
    window.tipSetData = (data) => {
      this.setState(data)
    }
  }
  render() {
    const { checkedArray, unCheckArray } = this.state
    return (
      <div className='setting_tips'>
        <div className='tips_a'>
          <a
            href='https://bbs.ainirobot.com/forum.php?mod=viewthread&tid=176&from=jiedai'
            className='tips_left'
            target='_blank'
          ></a>
          <p className='tips_right'>
            <a
              href='https://bbs.ainirobot.com/forum.php?mod=viewthread&tid=152&from=jiedai'
              className='tips_top'
              target='_blank'
            ></a>
            <a
              href='https://bbs.ainirobot.com/forum.php?mod=viewthread&tid=153&from=jiedai'
              className='tips_bottom'
              target='_blank'
            ></a>
          </p>
        </div>
        <p className='tips_arrow'>
          <span></span>
          <span></span>
        </p>
        <div className='tips'>
          {!this.isKTV && (
            <div className='tips_content'>
              <p className='tips_title'>{__('customHome.step.title')}</p>
              <p>
                1. {__('customHome.step.one')}
                <em
                  onClick={() => {
                    this.props.changeModal()
                  }}
                >
                  {__('customHome.step.one_choose')}
                </em>
              </p>
              <p>2. {__('customHome.step.two')}</p>
              <p>3. {__('customHome.step.three')}</p>
              <p>4. {__('customHome.step.four')}</p>
            </div>
          )}
          {this.isKTV && (
            <div className='tips_content'>
              <p className='tips_title'>{__('customHome.step.title')}</p>
              <p>1. {__('customHome.step.two')}</p>
              <p>2. {__('customHome.step.three')}</p>
              <p>3. {__('customHome.step.four')}</p>
            </div>
          )}
          {/* <p className="tips_warning">警示：配置完成后不同步机器人会导致保存失效，需重新配置！</p> */}
          <p
            className='tips_button'
            disabled={
              (this.isKTV && this.state.lowOpkVersion) ||
              (this.isKTV && this.state.templateNeedUpdate) ||
              (this.isKTV && this.state.isShowSetting && this.state.isDefault)
            }
            onClick={() => {
              if (
                (this.isKTV && this.state.lowOpkVersion) ||
                (this.isKTV && this.state.templateNeedUpdate) ||
                (this.isKTV && this.state.isShowSetting && this.state.isDefault)
              ) {
                return
              }
              if (!this.props.checkJson()) {
                return
              }
              this.getRobot(() => {
                this.props.checkAllQuery(() => {
                  if (this.props.allNum != 0) {
                    closeModal()
                    message.success(__('customHome.modalMsg.syncing'))
                    {
                      /* modal({
                                        content: __('customHome.modalMsg.syncing'),
                                        cancel: false,
                                        confirm: __('customHome.btn.gotIt'),
                                    }); */
                    }
                    return
                  }

                  modal({
                    title: __('customHome.modalMsg.chooseRobotToSync'),
                    content1: <Robot chooseRobots={this.props.chooseRobots.bind(this)} />,

                    content: (
                      <PublishRobot
                        // chooseRobots={this.props.chooseRobots.bind(this)}
                        chooseRobots={(arr) => {
                          this.props.chooseRobots(arr)
                        }}
                        checkedArray={this.state.checkedArray}
                        unCheckArray={this.state.unCheckArray}
                        ableFun={(a, b) => {
                          // console.log(a,'disabled');
                          let opkversion = a.robot_baseopk_version

                          if (this.props.templateCode == 'Simple') {
                            // 简洁式需要额外判断opk版本
                            let baseVersion = '02.0001.200821'
                            // baseVersion = "0";

                            let overVersion = checkReleaseRaw(opkversion, baseVersion, true)
                            if (overVersion) {
                              return true
                            } else {
                              return false
                            }
                          } else {
                            return true
                          }
                          return true
                        }}
                        disableTip=' (版本不兼容)'
                        getRobotId={this.getRobotId.bind(this)}
                      />
                    ),
                    cancel: __('customHome.btn.cancel2'),
                    confirm: __('customHome.btn.confirm'),
                    onConfirm: () => {
                      if (!this.uploadingData) {
                        this.uploadingData = 1
                        this.props.uploadData()
                        setTimeout(() => {
                          this.uploadingData = 0
                        }, 3 * 1e3)
                      } else {
                        console.log('uploading home data')
                        return
                      }
                    },
                  })
                })
              })
            }}
          >
            {/* <Icon icon='fix_log_synchro' className='icon' style={{ verticalAlign: "middle", marginRight: "10px" }}/> */}
            {__('customHome.btn.publish')}
          </p>
        </div>
      </div>
    )
  }
}
export default Tips
export { Tips }
