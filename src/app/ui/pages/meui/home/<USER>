@import '~base.less';
.component {
  width: 720px;
  margin-bottom: -25px;
  .list-item {
    margin-top: 10px;
    background: #ffffff;
    border-radius: 8px;
    position: relative;
    transition: all ease 0.3s;
    cursor: pointer;
    text-align: left;
    .prompt {
      width: 430px;
      display: block;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      padding: 10px;
      border: solid 1px #ffe58f;
      background-color: #fffbe6;
      white-space: nowrap;
      .prompt_icon {
        color: #faad14;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -2px;
      }
    }
    .tips {
      color: #ccd2d6;
      font-size: 14px;
      margin-top: 17px;
      display: inline-block;
      text-align: left;
    }
    .red {
      color: #ff713c;
      margin-left: 40px;
    }
    &.border {
      box-shadow: rgba(85, 195, 251, 0.5) 0 0 5px 4px;
    }

    &:hover {
      .check-box {
        display: block;
        color: rgba(85, 195, 251, 0.5);
      }

      .delete {
        display: block !important;
      }
    }
    .qa-content {
      padding-top: 17px;
      padding-bottom: 28px;
      padding-right: 7px;
      padding-left: 4px;
      .init-box {
        width: 100%;
        display: flex;
      }
      .left-q {
        flex: 2;
        display: flex;

        .q-info {
          width: calc(100% - 80px);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          line-height: 30px;
        }
      }
      .icon {
        user-select: none;
        display: inline-block;
        font-size: 18px;
        color: #ffffff;
        text-align: center;
        line-height: 30px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-right: 10px;
        margin-top: 7px;
        background: #3776ef;
      }
      .right-a {
        flex: 2;
        display: flex;

        .a-info {
          width: calc(100% - 80px);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          line-height: 30px;

          &.is-robots {
            width: calc(100% - 230px);
            margin-right: 10px;
          }
        }

        .robots {
          width: 180px;
          display: flex;
          justify-content: flex-start;

          .robot-icon {
            width: 50px;
            padding-right: 5px;
            margin-right: 10px;
            margin-top: 6px;
            height: 20px;
            background: url('@assets/svg-icon/icon_executed.svg') no-repeat center center;
            background-size: 100% 100%;
            color: #fff;
            position: relative;
          }

          .robot-names {
            width: 120px;
            line-height: 30px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &:hover {
              color: #3776ef;
            }
          }
        }
      }
      .input-qa {
        display: block;

        input {
          padding-right: 35px;
          padding-left: 10px;
        }

        input:focus {
          border-color: transparent;
          background: rgba(85, 195, 251, 0.2);
          color: @color-theme;
          border-radius: 8px;
          outline: none;
        }

        & > svg {
          position: absolute;
          right: 10px;
          height: 48px; // top:0;
          top: 10px;
          left: unset !important;
          z-index: 1;
          cursor: pointer;

          &:hover {
            transform: translateY(-3px);
          }
        }
      }
      .a-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        position: relative;
        right: 30px;
        background: #fff;
        background-image: url('@assets/svg-icon/qa_pic.svg');
        background-size: contain;
        transition: all ease 0.2s;
        .tip {
          width: 86px;
          font-size: 14px;
          position: absolute;
          left: 46px;
          top: 50%;
          transform: translateY(-50%);
        }
        &.list-a-img {
          position: absolute;
          right: 40px;
          top: 10px;
        }

        &:hover {
          transform: translateY(-3px);
          z-index: 99;
        }

        .img-content {
          border: 2px solid #3776ef;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          overflow: hidden;
          position: absolute;

          .video_icon {
            display: block;
            width: 100%;
            height: 100%;
            background: rgba(85, 93, 97, 0.2) url('@assets/svg-icon/more-white.svg') no-repeat center center;
            background-size: 19px 19px;
            transform: rotate(-90deg);
            position: absolute;
            left: 0px;
            top: 0px;
          }
        }

        img.small {
          position: relative;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          vertical-align: top;
        }

        .hover-img {
          max-width: 160px;
          max-height: 160px;
          border: 1px solid @color-theme;
          position: absolute;
          bottom: 40px;
          z-index: 98;
          left: 50%;
          transform: translateX(-50%);
          border-radius: 5px;
          background: #fff;
          display: none;
          align-items: center;
          justify-content: center;
          overflow: hidden;

          img {
            max-width: 158px;
            max-height: 158px;
            border-radius: 5px;
            vertical-align: top;
          }

          .video_icon {
            display: block;
            width: 100%;
            height: 100%;
            background: url('@assets/svg-icon/more-white.svg') no-repeat center center;
            background-size: 40px 40px;
            transform: rotate(-90deg);
            position: absolute;
            left: 0px;
            top: 0px;
          }

          .video_bg {
            display: block;
            width: 100%;
            height: 100%;
            background: rgba(85, 93, 97, 0.2);
            position: absolute;
            left: 0px;
            top: 0px;
          }

          video {
            max-width: 158px;
            max-height: 158px;
            border-radius: 5px;
            vertical-align: top;
          }

          &:after {
            display: block;
            content: '';
            width: 0;
            height: 0;
            border: 7px solid transparent;
            border-top-color: @color-theme;
            position: absolute;
            bottom: -14px;
            left: 50%;
            transform: translateX(-50%);
          }
        }

        &:hover {
          .hover-img {
            display: flex;
          }
        }
      }

      .delete {
        flex: 0.1;
        width: 25px;
        height: 28px;
        cursor: pointer;
        position: absolute;
        right: 10px;
        top: 17px;
        color: @color-theme;
        display: none;

        & > svg {
          width: 22px;
          height: 26px;
          color: #ccd2d6;
          transition: all ease 0.3s;
          color: @color-theme;

          &:hover {
            transform: translateY(-3px);
            color: @color-theme;
          }
        }
      }
      .standard,
      .other {
        display: flex;

        &.standard {
          position: relative;
          z-index: 9;

          .a-img {
            height: 70px;

            .tip {
              margin-top: 16px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .del {
              cursor: pointer;
              position: absolute;
              right: -120px;
              top: 40px;
              height: 30px;
              width: 12px;
              color: #ccd2d6;
              transition: all ease 0.3s;

              &:hover {
                color: @color-theme;
              }
            }
          }
        }

        .left-q,
        .right-a {
          display: block;
        }

        .title {
          font-size: 14px;
          color: @color-theme;
          line-height: 20px;
          padding: 13px 0 0px 40px;
          width: 100%;
          text-align: left;
        }

        .scrollQuestion,
        .scrollAnswers {
          max-height: 14.8rem;

          ::-webkit-scrollbar {
            width: 8px;
          }

          /* 滚动槽 */
          ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.06);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
            border-radius: 10px;
          }

          /* 滚动条滑块 */
          ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
          }

          ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(0, 0, 0, 0.1);
          }

          .textarea {
            margin-bottom: 16px;
          }
        }

        label {
          display: block;
          margin-top: 7px;
          margin-left: 40px;
          width: calc(100% - 100px);
          height: 30px;

          input {
            height: 30px;
            font-size: 14px;

            &:focus {
              border-color: transparent;
              background: rgba(85, 195, 251, 0.2);
              color: @color-theme;
              border-radius: 8px;
              outline: none;
            }
          }
        }

        .textarea {
          background: #f3f5f6;
          border: 0.05rem solid transparent;
          border-radius: 0.4rem;
          line-height: 1.25rem;
          padding: 0.2rem 0.7rem;
          display: block;
          margin-top: 7px;
          margin-left: 40px;
          width: calc(100% - 100px);
          // min-height: 30px;
          // max-height:68px;
          color: #555e61;
          font-size: 14px;
          resize: none;

          ::-webkit-scrollbar {
            width: 6px;
          }

          /* 滚动槽 */
          ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.06);
            border-radius: 6px;
          }

          /* 滚动条滑块 */
          ::-webkit-scrollbar-thumb {
            border-radius: 6px;
            background: @color-theme;
          }

          ::-webkit-scrollbar-thumb:window-inactive {
            background: @color-theme;
          }

          &:focus {
            border-color: transparent;
            background: rgba(85, 195, 251, 0.2);
            color: @color-theme;
            border-radius: 6px;
            outline: none;
            box-shadow: 0 0 5px rgba(85, 195, 251, 0.8);
          }
        }

        .list-items {
          position: relative;

          .del {
            cursor: pointer;
            position: absolute;
            right: 35px;
            top: 0px;
            height: 30px;
            width: 12px;
            color: #ccd2d6;
            transition: all ease 0.3s;

            &:hover {
              color: @color-theme;
            }
          }
        }
      }
    }
    .modal_button {
      display: flex;
      border-top: solid 1px #e1e7ea;
      height: 54px;
      text-align: center;
      line-height: 54px;
      span {
        font-size: 15px;
        color: #3776ef;
        display: inline-block;
        flex: 1;
      }
    }
  }
}
