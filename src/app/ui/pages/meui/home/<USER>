@import '~base.less';

.component {
  .ant-upload-select-picture-card {
    display: block !important;
    opacity: 0;
    width: 2.85rem;
    height: 2.85rem;
  }
  .ant-upload-list-picture-card-container {
    display: none;
  }
  .setting {
    position: relative;
    flex: 1;
    text-align: initial;
    input::-webkit-input-placeholder,
    textarea::-webkit-input-placeholder {
      color: #ccd2d6 !important;
      font-size: 12px;
    }
    // min-height:300px;
    .setting_tips {
      .tips_arrow {
        position: absolute;
        left: 20px;
        top: 46%;
        width: 30%;
        vertical-align: middle;
        span:nth-child(1) {
          display: inline-block;
          border-left: 2px solid #3776ef;
          border-bottom: 2px solid #3776ef;
          width: 40px;
          height: 40px;
          transform: rotate(45deg);
        }
        span:nth-child(2) {
          display: inline-block;
          width: 100%;
          height: 2px;
          background: #3776ef;
          position: relative;
          top: -40px;
        }
      }

      .tips {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        p.tips_title {
          font-size: 14px;
          color: #3776ef;
          line-height: 21px;
        }
        .tips_content {
          margin-top: 28px;
          margin-bottom: 10px;
          position: relative;
          text-align: left;
          left: 50%;
          transform: translateX(-25%);
          p {
            font-size: 14px;
            color: #555d61;
          }
        }

        p.tips_warning {
          font-size: 14px;
          color: #ff713c;
          letter-spacing: 0;
          text-align: center;
        }
        p.tips_button {
          width: 200px;
          padding: 13px 0px;
          font-size: 18px;
          color: #ffffff;
          letter-spacing: 0;
          background: #3776ef;
          border-radius: 100px;
          text-align: center;
          margin: 0 auto;
          margin-top: 20px;
        }
      }
    }

    .changeQue,
    .changeTitle,
    .backImg {
      .addSkillBtn {
        width: 100%;
        background: #3776ef;
        border: none;
        border-radius: 1.225rem;
        color: #fff;
        font-size: 0.9rem;
        margin-top: 25px;
        margin-bottom: 25px;
        padding: 0.5rem 5.95rem;
        text-align: center;
        outline: none;
        cursor: pointer;
      }
      .disBtn {
        background-color: #bcc4ca;
      }
      .input_tips {
        font-size: 12px;
        line-height: 20px;
        color: #ff713c;
        margin-bottom: 15px;
        margin-top: 5px;
        display: block;
      }
      .hide_input_tips {
        visibility: hidden;
      }
      .show_input_tips {
        visibility: visible;
      }
      div.list {
        // display:-webkit-box;
        // display: -moz-box;
        // display: -ms-flexbox;
        // display: -webkit-flex;
        // display: flex;
        margin-bottom: 30px;
        .pic_tips {
          display: block;
          font-size: 12px;
          color: #ff713c;
          position: absolute;
          left: 75px;
        }

        .chooseStyle {
          font-size: 12px;
          color: #555d61;
          letter-spacing: 0;
          line-height: 16px;
          .chooseCircle {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(85, 195, 251, 0.5);
            border-radius: 100%;
            margin-right: 8px;
          }
          .blue {
            background: #3776ef;
          }
          label {
            margin-right: 40px;
            line-height: 22px;
          }
          input:checked {
            background: #3776ef;
            color: #3776ef;
          }
        }
        .newTitle {
          clear: both;
          display: inline-block;
          border-bottom: 1px solid #e4e8ea;
          width: 50%;
          input[type='text'] {
            border: none;
            width: 80%;
          }
          span {
            float: right;
            color: #3776ef;
            font-size: 16px;
            color: #3776ef;
            text-align: right;
            em {
              font-style: normal;
            }
          }
        }

        .query-set {
          display: inline-block;
          width: 80%;
          .queryInput {
            width: 100%;
            padding-left: 14px;
            padding-right: 50px;
          }
          ul {
            z-index: 10;
            position: absolute;
            width: 80%;
            background: #fff;
            list-style: none;
            max-height: 120px;
            padding: 0;
            padding-bottom: 0;
            margin-top: -1px;
            box-shadow: 0 2px 6px 2px rgba(161, 170, 178, 0.15);
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            overflow-y: scroll;
            li {
              cursor: pointer;
              // padding-bottom: 14px;
              padding-left: 14px;
              line-height: 36px;
              &:hover {
                background-color: #f3f5f6;
              }
            }
          }
          .scroll-setting::-webkit-scrollbar {
            width: 5px;
            height: 10px;
          }
          .scroll-setting::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            background-color: #b5b1b1;
          }
          .scroll-setting::-webkit-scrollbar-thumb {
            border-radius: 10px;
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            background-color: black;
          }
        }

        select.chooseSetting {
          width: 100px;
          background: rgba(161, 170, 178, 0.15);
          border-radius: 4px;
          border: none;
          outline: none;
          font-size: 12px;
          color: #555d61;
          letter-spacing: 0;
          line-height: 16px;
          padding: 5px 9px;
        }
        select.chooseSetting:hover {
          option {
            background: #fff;
            border: none;
            line-height: 50px;
            height: 50px;
            text-align: center;
          }
        }
        select.chooseQuestion {
          // -webkit-box-flex: 1;
          // -moz-box-flex: 1;
          // -webkit-flex: 1;
          // -ms-flex: 1;
          // flex: 1;
          width: 80%;
          border: none;
          border-bottom: 1px solid #e4e8ea;
          outline: none;
          font-size: 16px;
          color: #555d61;
          letter-spacing: 0;
          line-height: 16px;
          padding-bottom: 11px;
        }
        option {
          text-align: center;
        }
        .title {
          font-size: 16px;
          color: #3776ef;
          letter-spacing: 0;
          margin-right: 14px;
          float: left;
          white-space: nowrap;
        }
        .word-len-tips {
          display: inline-block;
          width: 70px;
          color: #3776ef;
          font-size: 0.8rem;
          text-align: right;
          margin-left: -70px;
        }

        .list_area {
          display: inline-block;
          margin-right: 40px;
          span {
            display: block;
          }
          .circle {
            border-radius: 100%;
          }
          .square {
            border-radius: 8px;
          }
          .upload-btn {
            width: 57px;
            height: 57px;
            background: rgba(0, 146, 245, 0.2) !important;
            position: relative;
            margin: 0 auto;
            z-index: 10;
            &:before {
              display: block;
              content: '';
              width: 21px;
              height: 2px;
              background: @color-theme;
              position: absolute;
              left: 17px;
              top: 27px;
            }
            &:after {
              display: block;
              content: '';
              width: 2px;
              height: 21px;
              background: @color-theme;
              position: absolute;
              left: 27px;
              top: 17px;
            }
          }
          .defaultBackImg {
            width: 36px;
            height: 57px;
            border: 3px solid #3776ef;
            border-radius: 8px;
            margin: 0 auto;
            text-align: center;
          }
          .defaultImg {
            width: 57px;
            border: 3px solid #3776ef;
            border-radius: 100%;
          }

          .circleImg {
            height: 57px;
            border-radius: 100%;
          }
          .squareImg {
            height: 30px;
            border-radius: 8px;
            margin-bottom: 10px;
          }
          .rectangleImg1 {
            width: 57px;
            height: 40px;
            border-radius: 8px;
            margin-bottom: 8px;
          }
          .rectangleImg2 {
            width: 57px;
            height: 31px;
            border-radius: 8px;
            margin-bottom: 10px;
          }
          .rectangleImg {
            width: 57px;
            height: 40px;
            border-radius: 8px;
            margin-bottom: 8px;
          }
          span.uploadInfo {
            font-size: 12px;
            color: #99a3a8;
            letter-spacing: 0;
            text-align: center;
            margin-top: 2px;
          }
          span.defaultInfo {
            font-size: 12px;
            color: #3776ef;
            letter-spacing: 0;
            text-align: center;
          }
          span.uploadTips {
            font-size: 12px;
            color: #bcc4ca;
          }
        }
        .upload_area {
          position: relative;
          .upload-btn {
            opacity: 0;
          }
          &:hover {
            .upload-btn {
              opacity: 1;
            }
            .newIcon {
              opacity: 0;
            }
          }
          .newIcon_circle {
            width: 57px;
            height: 57px;
            border-radius: 100px;
            top: 0px;
            float: left;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
          }
          .newIcon_rectangle1 {
            width: 57px;
            height: 40px;
            border-radius: 8px;
            float: left;
            position: absolute;
            left: 50%;
            top: 0;
            margin-top: 8px;
            transform: translateX(-50%);
          }
          .newIcon_rectangle2 {
            width: 57px;
            height: 31px;
            border-radius: 8px;
            float: left;
            position: absolute;
            left: 50%;
            top: 0;
            margin-top: 13px;
            transform: translateX(-50%);
          }
          // .newIcon{
          //     width:57px;
          //     height:57px;
          //     border-radius: 100px;
          //     top: 0px;
          //     float: left;
          //     position: absolute;
          //     left: 50%;
          //     transform: translateX(-50%);
          // }
        }
        input[type='text'] {
          // -webkit-box-flex: 1;
          // -moz-box-flex: 1;
          // -webkit-flex: 1;
          // -ms-flex: 1;
          // flex: 1;
          width: 80%;
          border: none;
          border-bottom: 1px solid #e4e8ea;
          font-size: 16px;
          color: #555d61;
          line-height: 21px;
          outline: none;
          padding-bottom: 11px;
        }
        input::placeholder {
          font-size: 16px;
          color: #555d61;
        }
        .arabic_input {
          direction: rtl;
        }
      }
    }
    .arabic_changeQue {
      input[type='text'] {
        direction: rtl;
      }

      .arabic_list,
      .arabic_box,
      .box {
        display: flex;
      }
      .word-len-tips {
        margin-left: 0 !important;
      }
    }
  }
}
