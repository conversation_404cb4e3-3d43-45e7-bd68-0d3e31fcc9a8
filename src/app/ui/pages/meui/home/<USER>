import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import { Icon } from 'biz-components'
import { Input } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
import { inputMaxLengthInQ, inputMaxLengthInA } from '@utils/tools'
import classNames from 'classnames'
import { qaAdd, uploadQa, addDevices, deviceEdit } from '@api/_newReplies'

@connect((state) => {
  if (__DEV__) {
  }

  return {
    cropId: state.user.detail.corp_uuid,
    currentGroup: state._newReplies.current,
    curRobot: state._newReplies.curRobot,
    robots: state.robots.robots,
  }
})
@extend({
  styles: require('./addItem.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      q_icon: 'rd_add_none',
      a_icon: 'rd_add_none',
      qTxt: '',
      qTxtFlag: false,
      aTxt: '',
      aTxtFlag: false,
      questions: this.props.questions,
      answers: [],
      errMsg: null,
      uploadData: {},
      unCheckArray: [],
    }
  }

  componentDidUpdate(prev) {
    if (this.props.type != 'edit') {
      if (prev.curRobot != this.props.curRobot) {
        // console.log(prev.curRobot , this.props.curRobot)
        this.cancel()
      }
    }
  }
  //保存数据
  saveData() {
    console.log('------saveData')
    if (this.state.questions.length == 0) {
      return this.showMessage('您还没有添加问题')
    }
    if (this.state.answers.length == 0) {
      return this.showMessage('您还没有添加答案')
    }
    const q = this.state.questions.map((el) => {
      return el.trim()
    })
    const a = this.state.answers.map((el) => {
      return el.trim()
    })

    const qArr = q.filter((el, idx) => {
      return el != ''
    })
    const aArr = a.filter((el, idx) => {
      return el != ''
    })

    if (qArr.length == 0) {
      return this.showMessage('问题不能为空')
    }

    const qErr = qArr.findIndex((el) => {
      return el.length < 2
    })
    const qErr2 = qArr.findIndex((el) => {
      return el.length > 25
    })
    if (qErr >= 0) {
      return this.showMessage('问题不能少于2个字符')
    }
    if (qErr2 >= 0) {
      return this.showMessage('问题不能大于25个字符')
    }
    if (!this.state.imgUrl && aArr.length == 0) {
      return this.showMessage('答案不能为空')
    }
    let res = {}
    res.media_type = 0
    res.media_info = '[]'
    res.query = JSON.stringify(qArr)
    res.answer = JSON.stringify(aArr)
    res.enterprise_id = this.props.cropId
    res.group_id = this.props.currentGroup
      ? this.props.currentGroup.attr.group_id == 'DEFAULT'
        ? ''
        : this.props.currentGroup.attr.group_id
      : ''
    res.intent = 'orion_baike'
    const formData = new FormData()
    formData.append('data', JSON.stringify([res]))
    qaAdd(formData)
      .then((res) => {
        if (res.success.length == 0) {
          this.showMessage(res.error[0].msg)
        } else {
          this.showMessage('创建成功')
          closeModal()
        }
      })
      .catch((err) => {
        return this.showMessage('创建失败')
      })
      .finally(() => {})
  }
  //键入问答文字
  onInput(type, value) {
    value = value.trim()
    if (type == 'q') {
      this.setState({
        qTxt: value,
      })
    }
    if (type == 'a') {
      this.setState({
        aTxt: value,
      })
    }
  }

  changeData(res) {
    this.setState(
      {
        uploadData: Object.assign({}, res),
      },
      () => {
        if (this.state.uploadData.type == 'img') {
          if (this.state.uploadData.imgUrl == null) {
            document.querySelector('.btn-confirm').classList.add('disabled')
          } else {
            document.querySelector('.btn-confirm').classList.remove('disabled')
          }
        } else {
          if (this.state.uploadData.imgUrl == null || this.state.uploadData.videoUrl == null) {
            document.querySelector('.btn-confirm').classList.add('disabled')
          } else {
            document.querySelector('.btn-confirm').classList.remove('disabled')
          }
        }
      },
    )
  }
  showMessage(txt) {
    this.setState(
      {
        errMsg: txt,
      },
      () => {
        let timer = setTimeout(() => {
          clearTimeout(timer)
          this.setState({
            errMsg: null,
          })
        }, 3000)
      },
    )
  }
  //增加问 & 答
  addQA(type) {
    if (type == 'q') {
      // if (this.state.questions.length >= 10) {
      //     return this.props.errMsg('问题最多添加10条')
      // }
      // console.log(this.state.qTxt)
      if (this.state.qTxt.length == 0) {
        return this.showMessage('您还没有填写问题')
      }
      if (!this.state.qTxt || this.state.qTxt.length < 2) {
        return this.showMessage('问题不能少于两个字符')
      }
      if (this.state.qTxt.length > 25) {
        return this.showMessage('问题最多25个字符')
      }
      this.state.questions.push(this.state.qTxt)
      this.setState({
        questions: this.state.questions,
        qTxt: '',
        qTxtFlag: !this.state.qTxtFlag,
      })
    }
    if (type == 'a') {
      // if (this.state.answers.length >= 10) {
      //     return this.props.errMsg('答案最多添加10条')
      // }
      if (this.state.aTxt.length == 0) {
        return
      }
      // if (!this.state.aTxt || this.state.aTxt.length < 2) {
      //     return this.props.errMsg('答案不能少于2个字符')
      // }
      if (this.state.aTxt.length > 500) {
        return this.showMessage('答案最多500个字符')
      }
      this.state.answers.push(this.state.aTxt)
      this.setState({
        answers: this.state.answers,
        aTxt: '',
        aTxtFlag: !this.state.aTxtFlag,
      })
    }
  }
  //删除问&答
  deleteQA(type, index) {
    if (type == 'q' && index >= 0 && this.state.questions.length - 1 >= index) {
      this.state.questions.splice(index, 1)
      this.setState({
        questions: this.state.questions,
      })
    }
    if (type == 'a' && index >= 0 && this.state.answers.length - 1 >= index) {
      this.state.answers.splice(index, 1)
      this.setState({
        answers: this.state.answers,
      })
    }
  }
  //删除图片 视频 资源
  deleteFile(e) {
    e.stopPropagation()
    e.preventDefault()
    this.setState({
      imgUrl: null,
      videoUrl: null,
      imgName: '',
      videoName: '',
    })
  }
  cancel() {
    closeModal()
  }
  mapQuestion() {
    let res = []
    if (this.state.questions.length > 0) {
      res.push(
        <div key={0 + this.state.questions[0]}>
          <div className='title'>标准问法</div>
          <div className='list-items'>
            <textarea
              className='textarea'
              rows={
                (this.state.answers[0] && this.state.answers[0].length > 30) ||
                (this.state.questions[0] && this.state.questions[0].length > 20)
                  ? '2'
                  : '1'
              }
              maxLength='25'
              defaultValue={this.state.questions[0]}
              onInput={(e) => {
                this.state.questions[0] = e.target.value
              }}
            ></textarea>
            <Icon
              className='del'
              icon='map_point_x'
              onClick={(e) => {
                this.deleteQA('q', 0)
              }}
            />
          </div>
        </div>,
      )
      let otherQuestion = []
      this.state.questions.map((el, idx) => {
        if (idx == 1) {
          res.push(
            <div key='otherQuestion' className='title'>
              其他问法
            </div>,
          )
        }
        if (idx >= 1) {
          otherQuestion.unshift(
            <div className='list-items' key={idx + el}>
              <textarea
                className='textarea'
                rows={el.length > 20 ? '2' : '1'}
                maxLength='25'
                defaultValue={el}
                onInput={(e) => {
                  this.state.questions[idx] = e.target.value
                }}
              ></textarea>
              <Icon
                className='del'
                icon='map_point_x'
                onClick={(e) => {
                  this.deleteQA('q', idx)
                }}
              />
            </div>,
          )
        }
      })

      if (otherQuestion.length >= 1) {
        res.push(
          <div key='scrollQuestion' className='scrollQuestion' style={{ overflowY: 'auto' }}>
            {otherQuestion}
          </div>,
        )
      }
      return res
    } else {
      return ''
    }
  }
  mapAnswers() {
    let res = []
    if (this.state.answers.length > 0) {
      res.push(
        <div key={0 + this.state.answers[0]}>
          <div className='title'>标准答案</div>
          <div className='list-items'>
            {/* <Input type="text" appearance="block" maxLength="500" defaultValue={this.state.answers[0]}
                            onInput={(e) => {
                                this.state.answers[0] = e.target.value;
                            }} /> */}
            <textarea
              ref={(e) => {
                this.firstText = e
              }}
              rows={
                (this.state.answers[0] && this.state.answers[0].length > 30) ||
                (this.state.questions[0] && this.state.questions[0].length > 20)
                  ? '2'
                  : '1'
              }
              cols='30'
              className='textarea'
              maxLength='500'
              defaultValue={this.state.answers[0]}
              onInput={(e) => {
                this.state.answers[0] = e.target.value
                {
                  /* this.setState({
                                    answers: this.state.answers
                                }) */
                }
              }}
            ></textarea>
            <Icon
              className='del'
              icon='map_point_x'
              onClick={(e) => {
                this.deleteQA('a', 0)
              }}
            />
          </div>
        </div>,
      )
      let otherAnswers = []
      this.state.answers.map((el, idx) => {
        if (idx == 1) {
          res.push(
            <div key='otherAnswers' className='title'>
              其他答案
            </div>,
          )
        }
        if (idx >= 1) {
          otherAnswers.unshift(
            <div className='list-items' key={idx + el}>
              <textarea
                rows={el.length > 30 ? '2' : '1'}
                cols='30'
                className='textarea'
                maxLength='500'
                defaultValue={el}
                onInput={(e) => {
                  this.state.answers[idx] = e.target.value
                  {
                    /* this.setState({
                                        answers: this.state.answers
                                    }) */
                  }
                }}
              ></textarea>
              {/* <Input type="text" appearance="block" maxLength="500" defaultValue={el}
                                onInput={(e) => {
                                    this.state.answers[idx] = e.target.value;
                                }} /> */}
              <Icon
                className='del'
                icon='map_point_x'
                onClick={(e) => {
                  this.deleteQA('a', idx)
                }}
              />
            </div>,
          )
        }
      })

      if (otherAnswers.length >= 1) {
        res.push(
          <div key='scrollAnswers' className='scrollAnswers' style={{ overflowY: 'auto' }}>
            {otherAnswers}
          </div>,
        )
      }
      return res
    } else {
      return ''
    }
  }
  render() {
    return (
      <div className={this.props.className}>
        <div className='list-item list-item'>
          <span className='prompt'>
            <Icon icon='Alert_Default_Info' className='prompt_icon' />
            该问题无答案，不可作为首页推荐，如需推荐请填答案
          </span>
          <span className='tips'>*完善问答请到“企业问答库”里</span>
          <span className='tips red'>{this.state.errMsg}</span>
          <div className='qa-content'>
            <div className='init-box'>
              <div className='left-q'>
                <span className='icon'>问</span>
                <div className='q-info'>
                  <Input
                    key={this.state.qTxtFlag}
                    className='input-qa'
                    type='text'
                    placeholder='快捷添加问题，最多25个汉字'
                    maxLength='25'
                    appearance='block'
                    icon={this.state.q_icon}
                    onInput={(e) => {
                      this.onInput('q', e.target.value)
                    }}
                    onKeyUp={(evt) => {
                      if (evt.keyCode === 13) {
                        this.addQA('q')
                      }
                    }}
                    onChangeType={(e) => {
                      e.stopPropagation()
                      e.preventDefault()
                    }}
                    onFocus={() => {
                      this.qIconTimer && clearInterval(this.qIconTimer)
                      this.setState({
                        q_icon: 'map_add_click',
                      })
                    }}
                    onBlur={() => {
                      this.addQA('q')
                      this.qIconTimer = setTimeout(() => {
                        this.setState({
                          q_icon: 'rd_add_none',
                        })
                      }, 100)
                    }}
                  />
                </div>
              </div>
              <div className='right-a'>
                <span className='icon'>答</span>
                <div className='a-info'>
                  <Input
                    key={this.state.aTxtFlag}
                    className='input-qa'
                    type='text'
                    placeholder='快捷添加答案，最多500个汉字'
                    maxLength='500'
                    appearance='block'
                    icon={this.state.a_icon}
                    onInput={(e) => {
                      this.onInput('a', e.target.value)
                    }}
                    onKeyUp={(evt) => {
                      if (evt.keyCode === 13) {
                        this.addQA('a')
                      }
                    }}
                    onChangeType={(e) => {
                      e.stopPropagation()
                      e.preventDefault()
                    }}
                    onFocus={() => {
                      this.aIconTimer && clearInterval(this.aIconTimer)
                      this.setState({
                        a_icon: 'map_add_click',
                      })
                    }}
                    onBlur={() => {
                      this.addQA('a')
                      this.aIconTimer = setTimeout(() => {
                        this.setState({
                          a_icon: 'rd_add_none',
                        })
                      }, 100)
                    }}
                  />
                </div>
              </div>
              {/* <div className={classNames({ 'a-img': true, 'disabled': this.state.imgUrl || this.state.videoUrl })}
                                onClick={this.uploadModal.bind(this)}
                            >
                                <span className="tip">多媒体答案</span>
                            </div> */}
            </div>
            <div className='standard'>
              <div className='left-q'>{this.mapQuestion()}</div>
              <div className='right-a'>{this.mapAnswers()}</div>
            </div>
          </div>
          <p className='modal_button'>
            <span
              onClick={() => {
                this.cancel()
              }}
            >
              返回修改问题
            </span>
            <span
              onClick={() => {
                this.saveData()
              }}
            >
              保存答案
            </span>
          </p>
        </div>
      </div>
    )
  }
}
