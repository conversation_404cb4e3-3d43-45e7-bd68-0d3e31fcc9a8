import React from 'react'
import { extend } from 'koot'
import { Dropdown, MenuItem } from 'biz-components'
import { Icon } from 'biz-components'
import { Radio, Button } from 'biz-components'

@extend({
  styles: require('./chooseFaceTemplate.less'),
})
class ChooseFaceTemplate extends React.Component {
  state = {
    style: this.props.FaceStyle || 'Standard',
    FaceStyle: this.props.FaceStyle || 'Standard',
  }
  changeStyle(FaceStyle) {
    this.setState({
      FaceStyle,
    })
  }
  render() {
    return (
      <div className={this.props.className}>
        <p>{__('customHome.btn.selectExpressionTemplate')}</p>

        <div className='modal_choose'>
          {this.props.lang === 'zh_CN' && (
            <p
              className={this.state.style == 'List' ? 'blue' : ''}
              onClick={() => {
                this.changeStyle('Bubble')
              }}
            >
              <img src={require('@assets/png/meui/qipao2.png')} />
              <Radio name='sort' label={__('customHome.pic.bubble')} checked={this.state.FaceStyle == 'Bubble'} />
              {/* {this.props.FaceStyle == "Bubble" && <span>(当前)</span>} */}
            </p>
          )}
          {this.props.lang !== 'zh_CN' && (
            <p
              className={this.state.style == 'List' ? 'blue' : ''}
              onClick={() => {
                this.changeStyle('Card')
              }}
            >
              <img src={require('@assets/png/meui/card.png')} />
              <Radio name='sort' label={__('customHome.pic.card')} checked={this.state.FaceStyle == 'Card'} />
            </p>
          )}

          <p
            className={this.state.style == 'Standard' ? 'blue' : ''}
            onClick={() => {
              this.changeStyle('Standard')
            }}
          >
            <img src={require('@assets/png/meui/biaozhun.png')} />
            <Radio name='sort' label={__('customHome.pic.simple')} checked={this.state.FaceStyle == 'Standard'} />
            {/* {this.props.FaceStyle == "Standard" && <span>(当前)</span>} */}
          </p>
        </div>
        <p className='marked'>
          <Icon icon='Alert_Default_Warning' className='marked_icon' />
          {/* {this.state.lang=='english'&&<span>注意：英文版属于定制功能，在机器人上安装后才可配置首页</span>} */}
          <span>{__('customHome.warm.msg6')}</span>
        </p>
        {/* <p className="modal_button"
						onClick={() => {
							console.log(this.props)
							this.props.chooseFaceStyle(this.state.FaceStyle)
						}}
					>{__('customHome.btn.save2')}</p> */}
        <Button
          className='modal_button'
          onClick={() => {
            console.log(this.props)
            this.props.chooseFaceStyle(this.state.FaceStyle)
          }}
        >
          {__('customHome.btn.save2')}
        </Button>
      </div>
    )
  }
}
export default ChooseFaceTemplate
export { ChooseFaceTemplate }
