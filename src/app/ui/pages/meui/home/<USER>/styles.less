@import '~base.less';

.component {
  background: #f6f9f9;
  position: relative;

  .title-con {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    top: 10px;
  }

  input::-webkit-input-placeholder {
    color: #99a3a8;
    font-size: 12px;
  }

  input {
    -webkit-appearance: none;
  }

  button {
    border: none;
    outline: none;
    -webkit-highlight: none;
  }

  p {
    margin: 0;
    padding: 0;
  }

  ul,
  li {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  .tip_cover {
    background-color: rgba(85, 195, 251, 0.5);
    border-radius: 8px;
  }

  .tip_empty {
    background: none;
    border-radius: 0px;
  }

  .click_background {
    background: #3776ef !important;
    color: #fff !important;
  }

  .robot-list {
    // float: right;
    margin-top: -6px;
  }

  .home-title-bar {
    // float: right;
    // margin-bottom: 16px;
    margin-left: 15px;

    .progress {
      font-size: 16px;
      color: #3776ef;
      letter-spacing: 0;
      padding: 8px 17px;
      display: inline-block;
      margin-right: 40px;
      float: right;
    }

    .warning {
      display: inline-block;
      font-size: 14px;
      line-height: 14px;
      color: #ffffff;
      padding: 10px 14px;
      background: #ff713c;
      border-radius: 15px;
      margin-right: 40px;
      float: right;
      margin-top: 5px;
    }

    .success_setting {
      border-radius: 25px;
      font-size: 18px;
      letter-spacing: 0;
      line-height: 24px;
      padding: 6px 17px;
      cursor: pointer;
      float: right;
      box-sizing: border-box;
      color: #ffffff;
      background: #3776ef;

      &:hover {
        margin-top: -3px;
      }
    }

    .recover_setting {
      background: #ffffff;
      border: 1px solid #3776ef;
      color: #3776ef;
      margin: 0 40px;
    }
  }

  .content {
    display: flex;
    height: 675px;
    margin-top: 6px;

    .preview,
    .setting {
      background: #ffffff;
      box-shadow: 0 20px 20px 0 rgba(161, 170, 178, 0.15);
      border-radius: 8px;
    }

    .preview {
      width: 395px;
      margin-right: 20px;
      padding: 10px;

      .buttons {
        width: 395px;
        margin: -10px;
        padding: 10px;
        display: flex;
        position: fixed;
        background: #fff;
        z-index: 20;

        button {
          cursor: pointer;
          flex: 1;
          width: 118px;
          font-size: 16px;
          color: #3776ef;
          text-align: center;
          padding: 8px 0px;
          border: 1px solid #3776ef;
          border-radius: 24.5px;
          background: #fff;
          display: inline-block;
          white-space: nowrap;
        }

        button:hover {
          background: #3776ef;
          color: #fff;
        }

        button.sort {
          margin-left: 10px;
          margin-right: 10px;
        }
      }

      .previewCon {
        margin-top: 50px;
        width: 375px;
        height: 600px;
        overflow-y: scroll;
        -ms-overflow-style: none; //IE 10+
        overflow: -moz-scrollbars-none; //Firefox
        background-size: 100%;
        background-repeat: no-repeat;
        padding-bottom: 20px;

        .shadow {
          width: 375px;
          height: 80px;
          position: absolute;
          bottom: 10px;
        }
      }

      .previewCon::-webkit-scrollbar {
        display: none; //Safari and Chrome
      }

      .gov_state {
        padding: 19px 17px 21px;
        padding-bottom: 0px;

        .gov_top {
          width: 340px;
          padding: 6px;
          margin: 0 auto;
          margin-bottom: 3px;
        }

        .gov_title {
          padding-bottom: 0px;
          text-align: left;
          cursor: pointer;
          white-space: nowrap;
          font-size: 15.62px;
          color: #ffffff;
          letter-spacing: 0.39px;

          .title_icon {
            vertical-align: middle;
            display: inline-block;
            width: 22px;
            height: 22px;
            margin-right: 8px;
            // border-radius: 100%;
            background-size: 22px;
            background-repeat: no-repeat;
            margin-top: -4px;
          }
        }

        .gov_button {
          display: flex;
          cursor: pointer;

          div {
            flex: 1;
            padding: 12px;

            p {
              padding: 24px 24px 30px;
              border-radius: 3px;
              background-repeat: no-repeat;
              background-size: 100% 100%;
              background-position: center;
              line-height: 0px;

              span {
                display: inline-block;
                font-size: 25px;
                line-height: 43px;
                color: #ffffff;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                padding-left: 53px;
              }
            }
          }
        }

        .gov_content {
          margin-top: -12px;
          padding-top: 12px;

          div {
            width: 150.5px;
            display: inline-block;
            padding-left: 12px;
            padding-top: 12px;
            padding-right: 12px;
            padding-bottom: 12px;
            box-sizing: content-box;
            margin-top: -12px;

            p {
              padding: 22px;
              border-radius: 3px;
              background-repeat: no-repeat;
              background-size: 100% 100%;
              background-position: center;
              line-height: 0px;

              span {
                display: inline-block;
                font-size: 19px;
                line-height: 25px;
                height: 25px;
                color: #ffffff;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                padding-left: 32px;
              }
            }
          }

          div:nth-child(odd) {
            margin-right: -12px;
          }
        }

        .gov_queries_title {
          font-size: 25px;
          color: #ffffff;
          letter-spacing: 0.62px;
          text-align: center;
          margin-top: 19px;
          margin-bottom: 19px;
        }

        // .queries_content{
        //     .gov_area{
        //         flex:1;
        //         max-width:50%;
        //         display:inline-block;
        //         p{
        //             width:auto;
        //             display:inline-block;
        //             white-space: nowrap;
        //             overflow:hidden;
        //             text-overflow:ellipsis;
        //             padding-left:12px;
        //             width:90%;
        //             span{
        //                 display:inline-block;
        //                 font-size: 16.25px;
        //                 color: #FFFFFF;
        //                 line-height: 18.75px;
        //                 margin-top:12px;
        //                 margin-bottom:12px;
        //                 opacity: 0.9;

        //             }
        //         }
        //     }
        // }
        .queries_content {
          p {
            width: auto;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-left: 12px;
            width: 50%;

            span {
              display: inline-block;
              font-size: 16.25px;
              color: #ffffff;
              line-height: 18.75px;
              margin-top: 12px;
              margin-bottom: 12px;
              opacity: 0.9;
            }
          }
        }

        .broadcast {
          padding: 12.5px 25px;
          background: #fff;
          text-align: center;
          font-size: 16px;
          line-height: 22px;
          color: #000000;
          letter-spacing: 0.39px;
          border-radius: 28.12px;
          margin-top: 20px;

          .icon {
            width: 22px;
            height: 22px;
            color: #3776ef;
            margin-right: 12px;
          }
        }
      }
    }

    .setting::-webkit-scrollbar {
      display: none;
    }

    .setting {
      position: relative;
      flex: 1;
      padding: 40px 25px;
      overflow-y: scroll;

      .sort-container {
        display: flex;
      }

      .setting_tips {
        .tips_arrow {
          position: absolute;
          left: 20px;
          top: 46%;
          width: 30%;
          vertical-align: middle;

          span:nth-child(1) {
            display: inline-block;
            border-left: 2px solid #3776ef;
            border-bottom: 2px solid #3776ef;
            width: 40px;
            height: 40px;
            transform: rotate(45deg);
          }

          span:nth-child(2) {
            display: inline-block;
            width: 100%;
            height: 3px;
            background: #3776ef;
            position: relative;
            top: -40px;
          }
        }

        .tips {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);

          p.tips_title {
            font-size: 25px;
            color: #3776ef;
            line-height: 21px;
            white-space: nowrap;
            text-align: center;
          }

          .tips_content {
            margin-top: 28px;
            margin-bottom: 10px;
            position: relative;
            text-align: left;
            left: 50%;
            transform: translateX(-25%);

            p {
              font-size: 14px;
              color: #555d61;
            }
          }

          p.tips_warning {
            font-size: 14px;
            color: #ff713c;
            letter-spacing: 0;
            text-align: center;
            white-space: nowrap;
          }

          p.tips_button {
            width: 200px;
            padding: 13px 0px;
            font-size: 18px;
            color: #ffffff;
            letter-spacing: 0;
            background: #3776ef;
            border-radius: 100px;
            text-align: center;
            margin: 0 auto;
            margin-top: 20px;
            cursor: pointer;
          }
        }
      }

      .changeQue,
      .changeTitle,
      .backImg {
        div.listFunTitle {
          margin-bottom: 0px !important;

          input {
            padding-right: 50px;
          }
        }

        div.list {
          margin-bottom: 30px;

          .input_tips {
            font-size: 12px;
            line-height: 20px;
            color: #ff713c;
            margin-bottom: 5px;
            margin-top: 5px;
            display: block;
          }

          .hide_input_tips {
            visibility: hidden;
          }

          .show_input_tips {
            visibility: visible;
          }

          .chooseStyle {
            font-size: 12px;
            color: #555d61;
            letter-spacing: 0;
            line-height: 16px;

            .chooseCircle {
              display: inline-block;
              width: 12px;
              height: 12px;
              border: 2px solid rgba(85, 195, 251, 0.5);
              border-radius: 100%;
              margin-right: 8px;
            }

            .blue {
              background: #3776ef;
            }

            label {
              margin-right: 40px;
              line-height: 22px;
            }

            input:checked {
              background: #3776ef;
              color: #3776ef;
            }
          }

          .newTitle {
            clear: both;
            display: inline-block;
            border-bottom: 1px solid #e4e8ea;
            width: 50%;

            input[type='text'] {
              border: none;
              width: 80%;
            }

            span {
              float: right;
              color: #3776ef;
              font-size: 16px;
              color: #3776ef;
              text-align: right;

              em {
                font-style: normal;
              }
            }
          }

          .dropdown {
            background: rgba(161, 170, 178, 0.15);
            border-radius: 4px;

            button {
              line-height: 26px;
              height: 26px;
              font-size: 12px;
              color: #555d61;
              letter-spacing: 0;
              line-height: 16px;
            }

            .txt {
              display: inline-block;
              padding-right: 67px;
            }

            .menu-container {
              .menu {
                width: 100px;
                text-align: center;

                .menu-item {
                  font-size: 14px;
                  color: #555d61;
                }

                .menu-item:hover {
                  color: #3776ef;
                  background: #f3f5f6;
                }
              }
            }
          }

          .query-set {
            display: inline-block;
            width: 50%;

            .queryInput {
              width: 100%;
              padding-left: 14px;
              padding-right: 50px;
            }

            ul {
              position: absolute;
              width: 50%;
              background: #fff;
              max-height: 300px;
              // padding: 14px;
              padding-bottom: 0;
              margin-top: -1px;
              box-shadow: 0 2px 6px 2px rgba(161, 170, 178, 0.15);
              border-bottom-left-radius: 8px;
              border-bottom-right-radius: 8px;
              overflow-y: scroll;

              li {
                cursor: pointer;
                padding-left: 14px;
                line-height: 36px;

                &:hover {
                  background-color: #f3f5f6;
                }
              }
            }

            .scroll-setting::-webkit-scrollbar {
              width: 5px;
              height: 10px;
            }

            .scroll-setting::-webkit-scrollbar-track {
              -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
              box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
              border-radius: 10px;
              background-color: #b5b1b1;
            }

            .scroll-setting::-webkit-scrollbar-thumb {
              border-radius: 10px;
              -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
              box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
              background-color: black;
            }
          }

          select.chooseSetting {
            background: rgba(161, 170, 178, 0.15);
            border-radius: 4px;
            border: none;
            outline: none;
            font-size: 12px;
            color: #555d61;
            letter-spacing: 0;
            line-height: 16px;
            padding: 5px 9px;
          }

          select.chooseSetting:hover {
            option {
              background: #fff;
              border: none;
              line-height: 50px;
              height: 50px;
              text-align: center;
            }
          }

          select.chooseQuestion {
            width: 50%;
            border: none;
            border-bottom: 1px solid #e4e8ea;
            outline: none;
            font-size: 16px;
            color: #555d61;
            letter-spacing: 0;
            line-height: 16px;
            padding-bottom: 11px;
          }

          option {
            text-align: center;
          }

          .title {
            font-size: 16px;
            color: #3776ef;
            letter-spacing: 0;
            margin-right: 14px;
            float: left;
          }

          .word-len-tips {
            display: inline-block;
            width: 70px;
            color: #3776ef;
            font-size: 0.8rem;
            text-align: right;
            margin-left: -70px;
          }

          .list_area {
            display: inline-block;
            margin-right: 50px;

            span {
              display: block;
            }

            .circle {
              border-radius: 100%;
            }

            .square {
              border-radius: 8px;
            }

            .upload-btn {
              width: 57px;
              height: 57px;
              background: rgba(0, 146, 245, 0.2) !important;
              position: relative;
              margin: 0 auto;
              z-index: 10;

              &:before {
                display: block;
                content: '';
                width: 21px;
                height: 2px;
                background: @color-theme;
                position: absolute;
                left: 17px;
                top: 27px;
              }

              &:after {
                display: block;
                content: '';
                width: 2px;
                height: 21px;
                background: @color-theme;
                position: absolute;
                left: 27px;
                top: 17px;
              }
            }

            .defaultBackImg {
              width: 36px;
              height: 57px;
              border: 3px solid #3776ef;
              border-radius: 8px;
              margin: 0 auto;
              text-align: center;
            }

            .defaultImg {
              width: 57px;
              border: 3px solid #3776ef;
              border-radius: 8px;
            }

            .bigImg {
              height: 16px;
              margin-bottom: 20px;
            }

            .smallImg {
              height: 24px;
              margin-bottom: 16px;
            }

            .rectangleImg1 {
              width: 57px;
              height: 40px;
              border-radius: 8px;
              margin-bottom: 8px;
            }

            .rectangleImg2 {
              width: 57px;
              height: 31px;
              border-radius: 8px;
              margin-bottom: 10px;
            }

            .rectangleImg3 {
              width: 46px;
              height: 57px;
              border-radius: 8px;
              margin-bottom: -2px;
            }

            span.uploadInfo {
              font-size: 12px;
              color: #99a3a8;
              letter-spacing: 0;
              text-align: center;
              margin-top: 2px;
            }

            span.defaultInfo {
              font-size: 12px;
              color: #3776ef;
              letter-spacing: 0;
              text-align: center;
            }

            span.uploadTips {
              font-size: 12px;
              color: #bcc4ca;
            }
          }

          .default_area {
            cursor: pointer;
            text-align: center;
          }

          .upload_area {
            position: relative;

            .upload-btn {
              opacity: 0;
            }

            &:hover {
              .upload-btn {
                opacity: 1;
              }

              .newLogo,
              .newIcon_small,
              .newIcon_big,
              .newIcon_default {
                opacity: 0;
              }
            }

            .newLogo {
              width: 20px;
              height: 20px;
              float: left;
              position: absolute;
              left: 50%;
              top: 0;
              margin-top: 15px;
              transform: translateX(-50%);
            }

            .newIcon_small {
              width: 57px;
              height: 24px;
              border-radius: 8px;
              float: left;
              position: absolute;
              left: 50%;
              top: 0;
              margin-top: 16px;
              transform: translateX(-50%);
            }

            .newIcon_big {
              width: 57px;
              height: 16px;
              border-radius: 8px;
              float: left;
              position: absolute;
              left: 50%;
              top: 0;
              margin-top: 20.5px;
              transform: translateX(-50%);
            }

            .newIcon_default {
              width: 30px;
              height: 57px;
              border-radius: 8px;
              top: 0px;
              float: left;
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
            }
          }

          input[type='text'] {
            border: none;
            border-bottom: 1px solid #e4e8ea;
            font-size: 16px;
            color: #555d61;
            line-height: 21px;
            width: 50%;
            outline: none;
            padding-bottom: 11px;
          }

          input::placeholder {
            font-size: 16px;
            color: #555d61;
          }
        }
      }

      .query-sort-container {
        display: flex;
      }

      .ver-line {
        width: 1px;
        background-color: #eff1f2;
      }

      .sort {
        width: 340px;
        min-width: 340px;
        padding-left: 30px;
        padding-right: 30px;

        .sort-title {
          font-size: 24px;
          color: #3776ef;
          letter-spacing: 0;
          height: 33px;
          line-height: 33px;
          margin-bottom: 15px;
        }

        .sort_txt {
          display: flex;
          margin-bottom: 10px;

          span.txt {
            flex: 1;
            display: inline-block;
            height: 44px;
            background: rgba(85, 195, 251, 0.2);
            border-radius: 4px;
            text-align: center;
            line-height: 44px;
            font-size: 16px;
            color: #3776ef;
            letter-spacing: 0;
            text-align: center;

            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          span.visited {
            margin-left: 10px;
          }
        }

        .sort_add {
          height: 44px;
          line-height: 44px;
          background: #3776ef;
          border-radius: 4px;
          margin-top: 10px;
          margin-bottom: 10px;
          font-size: 16px;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
        }

        .sort_area1,
        .sort_area2 {
          position: relative;
          z-index: 9;

          .sort_info {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            width: 100%;
            height: 44px;
            line-height: 44px;
            background: rgba(85, 195, 251, 0.2);
            border-radius: 4px;
            margin-bottom: 10px;
            padding-left: 30px;
            padding-right: 30px;
            position: relative;
            transition: initial;

            .sort_content {
              font-size: 16px;
              color: #3776ef;
              letter-spacing: 0;
            }

            .sort_switch {
              width: 33px;
              height: 20px;
              border-style: solid;
              border-color: #fff;
              border-width: 2px;
              border-radius: 15.33px;
              position: absolute;
              right: 63px;
              top: 50%;
              transform: translateY(-50%);

              em {
                width: 16px;
                height: 16px;
                background: #ffffff;
                position: absolute;
                right: 2px;
                top: 50%;
                transform: translateY(-50%);
                border-radius: 50%;
              }

              .switch_on {
                right: 2px;
              }

              .switch_off {
                left: 2px;
              }
            }

            .sort_button {
              display: inline-block;
              float: right;
              margin-top: 17px;
              width: 14px;
              height: 10px;
              // display:inline-block;
              // float:right;
              // margin-top:17px;
              // width: 14px;
              // height: 10px;
              // border-top: 2px solid #3776ef;
              // border-bottom: 2px solid #3776ef;
              // background-color: #3776ef;
              // padding: 2px 0;
              // background-clip:content-box
            }
          }
        }

        .sort_area1 {
          .sort_info {
            color: #3776ef;

            .icon {
              width: 16px;
              height: 16px;
              position: absolute;
              right: 63px;
              top: 50%;
              transform: translateY(-50%);
            }
          }
        }

        .sort_area2 {
          width: 380px;

          .sort_info {
            width: 150px;
            height: 44px;
            line-height: 44px;
            padding-left: 15px;
            padding-right: 15px;
            display: inline-block;
            margin-right: 10px;
            position: relative;

            .sort_content {
              font-size: 14px;
            }

            .sort_switch {
              right: 43px;
            }
          }
        }

        .sort_area1,
        .sort_area2 {
          .gray {
            position: relative;
            background: #eff1f2;

            .sort_content {
              color: #bcc4ca;
            }

            .sort_button {
              // border-top: 2px solid #BCC4CA;
              // border-bottom: 2px solid #BCC4CA;
              // background-color: #BCC4CA;
            }
          }
        }
      }

      .sort2 {
        width: 580px;

        .sort_add {
          width: 510px;
        }

        .sort_area2 {
          width: auto;

          .sort_info {
            width: 250px;
          }
        }
      }
    }
  }
}
