import React from 'react'
import { connect } from 'react-redux'
import { ButtonSelectFile } from 'biz-components'
import { Upload } from 'antd'
import ImgCrop from 'antd-img-crop'
import 'antd/es/modal/style'
import 'antd/es/slider/style'

@connect((state) => {
  return {
    templateInfo: state._home.homeModule.templateInfo,
  }
})
class BackImg extends React.Component {
  render() {
    var image_url = this.props.devUrl + 'media/'
    let backgroundImg = ''
    backgroundImg = 'module_public/module_gov_home/home.png'
    return (
      <div className='backImg'>
        <div className='list'>
          <span className='title'>{__('customHome.pic.backgroundPicture')}</span>
          <p className={this.props.templateInfo.background != backgroundImg ? 'list_area upload_area' : 'list_area'}>
            <ButtonSelectFile
              className='upload-btn square'
              onSelect={(evt) => {
                this.props.selectImg(evt, 'backImg', '')
              }}
            />
            <ImgCrop
              aspect={1200 / 1920}
              grid
              modalTitle={__('customHome.btn.img_cut')} //
              modalWidth='650px'
              modalOk={__('customHome.btn.confirm')}
              modalCancel={__('customHome.btn.cancel2')}
              fillColor={'transparent'}
            >
              {/* <ButtonSelectFile
									className="upload-btn square"
									onSelect={(evt) => {
										this.props.selectImg(evt, "conciseImg", "")
									}}
								/> */}
              <Upload
                customRequest={() => {}}
                className='upload-btn square'
                listType='picture-card'
                beforeUpload={(e) => {
                  console.log(e)
                  let _this = this
                  let img = e
                  let reader = new FileReader()
                  reader.readAsDataURL(img)
                  reader.onload = function (e) {
                    let image = new Image()
                    image.onload = function () {
                      let mycanvas = document.querySelector('#myCanvas')
                      let ctx = mycanvas.getContext('2d')
                      ctx.drawImage(image, 0, 0, 1200, 1920)
                      let dataurl = mycanvas.toDataURL('image/png')
                      function dataURLtoBlob(dataurl) {
                        let arr = dataurl.split(','),
                          mime = arr[0].match(/:(.*?);/)[1],
                          bstr = atob(arr[1]),
                          n = bstr.length,
                          u8arr = new Uint8Array(n)
                        while (n--) {
                          u8arr[n] = bstr.charCodeAt(n)
                        }
                        return new Blob([u8arr], { type: mime })
                      }
                      _this.props.selectImg(dataURLtoBlob(dataurl), 'backImg', '')
                    }
                    image.src = e.target.result
                  }
                  return true
                }}
                onChange={(e) => {
                  // console.log(e)
                }}
              ></Upload>
            </ImgCrop>

            {this.props.templateInfo.background != backgroundImg ? (
              <img src={image_url + this.props.templateInfo.background} className='newIcon_default' />
            ) : (
              ''
            )}
            <span className='uploadInfo'>{__('customHome.pic.uploadNew')}</span>
          </p>
          <p
            className='list_area'
            onClick={() => {
              backgroundImg = 'module_public/module_gov_home/home.png'
              this.props.dispatch({
                type: 'CHANGE_INFO',
                data: {
                  type: 'background',
                  content: backgroundImg,
                },
              })
              this.props.deleteBackImg()
            }}
          >
            <img src={image_url + backgroundImg} className='defaultBackImg' />
            <span className='defaultInfo'>{__('customHome.pic.default')}</span>
          </p>
          <p className='list_area'>
            <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
            <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：1200px*1920px</span>
            <span className='uploadTips'>
              2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
            </span>
            <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
          </p>
        </div>
      </div>
    )
  }
}
export default BackImg
export { BackImg }
