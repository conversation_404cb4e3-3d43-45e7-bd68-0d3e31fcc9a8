import React from 'react'
import { connect } from 'react-redux'
import { Icon } from 'biz-components'
import { GET_EDIT_LIST } from '@redux/action-types'
import { throws } from 'assert'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页政府', state._home)
  return {
    showSkills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
    templateInfo: state._home.homeModule.templateInfo,
  }
})
class Government extends React.Component {
  render() {
    var image_url = this.props.devUrl + 'media/'
    var standardQueries = []
    this.props.queries.forEach((item) => {
      if (item.name == 'governmentMain') {
        standardQueries = item.query
      }
    })
    standardQueries = standardQueries.slice(0, 8)
    return (
      <div className={this.props.className}>
        <div className='gov_state'>
          <div className='gov_top'>
            <p
              className='gov_title'
              onClick={() => {
                if (this.props.checkJson()) {
                  this.props.changeCurrentIndex(-1)
                  this.props.changeContent('title')
                }
              }}
            >
              <em
                className='title_icon'
                style={{ backgroundImage: 'url(' + image_url + this.props.templateInfo.titleBarIcon + ')' }}
              ></em>
              {this.props.templateInfo.titleBarText}
            </p>
          </div>
          <div className='gov_button'>
            {this.props.showSkills[0].display && (
              <div className={this.props.currentIndex == 'skills0' ? 'tip_cover' : ''}>
                <p
                  style={{ backgroundImage: 'url(' + image_url + this.props.showSkills[0].icon + ')' }}
                  onClick={() => {
                    if (this.props.checkJson()) {
                      this.props.changeCurrentIndex('skills0')
                      this.props.changeContent('question', 'gov_l')
                      this.props.dispatch({
                        type: GET_EDIT_LIST,
                        data: {
                          index: 0,
                        },
                      })
                    }
                  }}
                >
                  <span title={this.props.showSkills[0].tip}>{this.props.showSkills[0].title}</span>
                </p>
              </div>
            )}
          </div>
          <div className={this.props.currentSort ? 'gov_content tip_cover' : 'gov_content'}>
            {this.props.showSkills.slice(1).map((item, i) => {
              if (item.display) {
                return (
                  <div
                    key={'skills' + (i + 1)}
                    className={this.props.currentIndex == 'skills' + (i + 1) ? 'tip_cover' : ''}
                  >
                    <p
                      style={{ backgroundImage: 'url(' + image_url + this.props.showSkills[i + 1].icon + ')' }}
                      onClick={() => {
                        this.clickSkills('skills' + (i + 1), i + 1)
                      }}
                    >
                      <span>
                        {this.props.showSkills[i + 1].title.length > 4
                          ? this.props.showSkills[i + 1].title.substr(0, 4) + '...'
                          : this.props.showSkills[i + 1].title}
                      </span>
                    </p>
                  </div>
                )
              }
            })}
          </div>
          <p className='gov_queries_title'>喊我“小豹”，问我问题</p>
          <div className={this.props.currentSort ? 'queries_content tip_cover' : 'queries_content'}>
            {/* {queries_result_all} */}
            {standardQueries.map((item, i) => {
              return (
                <p
                  className={this.props.currentIndex == i ? 'tip_cover' : ''}
                  onClick={() => {
                    if (this.props.checkJson()) {
                      this.clickQueries(i, standardQueries[i])
                    }
                  }}
                >
                  <span>
                    {'“' +
                      (standardQueries[i].length > 8 ? standardQueries[i].substr(0, 8) + '...' : standardQueries[i]) +
                      '”'}
                  </span>
                </p>
              )
            })}
          </div>
          {/* <div className="broadcast">
                        <Icon icon="secend_skill_voice_click" className="icon" />
                        <span>“纪检委的主要职责是什么”</span>
                    </div> */}
        </div>
      </div>
    )
  }
  clickQueries(i, txt) {
    if (this.props.checkJson()) {
      this.props.changeCurrentIndex(i)
      this.props.changeContent('question', 'government_query', i)
    }
  }
  clickSkills(currentI, i) {
    if (this.props.checkJson()) {
      this.props.changeCurrentIndex(currentI)
      if (i == 0) {
        this.props.changeContent('question', 'gov_l')
      } else {
        this.props.changeContent('question', 'gov_s')
      }
      this.props.dispatch({
        type: GET_EDIT_LIST,
        data: {
          index: i,
        },
      })
    }
  }
}
export default Government
export { Government }
