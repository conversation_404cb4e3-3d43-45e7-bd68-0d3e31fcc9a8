import React from 'react'
import Robot from './robot'
import { Icon } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
import { message } from 'antd'

class Tips extends React.Component {
  render() {
    return (
      <div className='setting_tips'>
        <p className='tips_arrow'>
          <span></span>
          <span></span>
        </p>
        <div className='tips'>
          <p className='tips_title'>自定义首页，从这里开始</p>
          <div className='tips_content'>
            <p>配置步骤</p>
            <p>1.鼠标选择左侧对应区域</p>
            <p>2.在右侧进行编辑</p>
            <p>3.同步给机器人生效</p>
          </div>
          {/* <p className="tips_warning">警示：配置完成后不同步机器人会导致保存失效，需重新配置！</p> */}
          <p
            className='tips_button'
            onClick={() => {
              if (!this.props.checkJson()) {
                return
              }
              if (this.props.allNum != 0) {
                closeModal()
                message.success(__('customHome.modalMsg.syncing'))
                {
                  /* modal({
                                    content: __('customHome.modalMsg.syncing'),
                                    cancel: false,
                                    confirm: __('customHome.btn.gotIt'),
                                }); */
                }
                return
              }
              modal({
                title: __('customHome.modalMsg.chooseRobotToSync'),
                content: <Robot chooseRobots={this.props.chooseRobots.bind(this)} />,
                cancel: __('customHome.btn.cancel2'),
                confirm: __('customHome.btn.sync'),
                onConfirm: () => {
                  this.props.uploadData()
                },
              })
            }}
          >
            <Icon icon='fix_log_synchro' className='icon' style={{ verticalAlign: 'middle', marginRight: '10px' }} />
            {__('customHome.btn.syncToRobot')}
          </p>
        </div>
      </div>
    )
  }
}
export default Tips
export { Tips }
