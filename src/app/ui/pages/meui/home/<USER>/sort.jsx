import React from 'react'
import modal, { close as closeModal } from '@utils/modal'
import { Icon } from 'biz-components'
import { Popover, message } from 'antd'
import AddSkill from './addSkillModal'
import { SortableContainer, SortableElement, SortableHandle, arrayMove } from 'react-sortable-hoc'
import { connect } from 'react-redux'
import {
  CHANGE_SKILL_SORT,
  REMOVE_HOMEMODULE_SKILL,
  ADD_HOMEMODULE_SKILL,
  ADD_NEW_SKILL,
  CHANGE_QUERY_SORT,
  REMOVE_HOMEMODULE_QUERY,
} from '@redux/action-types'

const DragHandle = SortableHandle(() => <Icon icon='move' className='sort_button' />)
const SortableItem = SortableElement(({ modalStyle, idx, value, onRemove }) => {
  return (
    <p className={modalStyle == 'Card' ? 'sort_info2 sort_info' : 'sort_info'}>
      <Popover content={modalStyle != 'Technical' ? value.title : value.tip}>
        <span className='sort_content'>{modalStyle != 'Technical' ? value.title : value.tip}</span>
      </Popover>
      {/* <span className="sort_content">{modalStyle != "Technical" ? value.title : value.tip}</span> */}
      <DragHandle />
    </p>
  )
})

const SortableList = SortableContainer(({ modalStyle, items, onRemove }) => {
  return (
    <ul className={modalStyle != 'Card' ? 'sort_area1' : 'sort_area2'}>
      {items.map((item, index) => {
        if (item.display) {
          return (
            <SortableItem
              key={`item-${index}`}
              modalStyle={modalStyle}
              index={index}
              idx={index}
              value={item}
              onRemove={onRemove}
            />
          )
        }
      })}
    </ul>
  )
})

const DragHandle_query = SortableHandle(() => <Icon icon='move' className='sort_button' />)
const SortableItem_query = SortableElement(({ modalStyle, idx, value, onDelQuery }) => {
  return (
    <p className='sort_info'>
      <Popover content={value}>
        <span className='sort_content'>{value}</span>
      </Popover>
      <Icon icon='rd_trash' className='icon' onClick={() => onDelQuery(idx)} />
      <DragHandle_query />
    </p>
  )
})

const SortableList_query = SortableContainer(({ modalStyle, items, onDelQuery }) => {
  return (
    <ul className={modalStyle != 'Card' ? 'sort_area1' : 'sort_area2'}>
      {items.map((item, index) => {
        return (
          <SortableItem_query
            key={`item-${index}`}
            modalStyle={modalStyle}
            index={index}
            idx={index}
            value={item}
            onDelQuery={onDelQuery}
          />
        )
      })}
    </ul>
  )
})

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页排序', state._home)
  return {
    skills: state._home.homeModule.skills,
    queries: state._home.homeModule.queries,
  }
})
class Sort extends React.Component {
  state = {}
  newAddSkill(skill) {
    console.log(skill)
    this.props.dispatch({
      type: 'ADD_NEW_SKILL',
      data: {
        index: 1,
        skill: skill,
      },
    })
    closeModal()
  }
  newAddQueries(text) {
    this.props.dispatch({
      type: 'ADD_NEW_QUERIES',
      data: {
        index: 0,
        content: text,
        modalStyle: 'government',
      },
    })
    closeModal()
  }
  render() {
    let skills, standardQueries
    this.props.queries.forEach((item) => {
      if (item.name == 'governmentMain') {
        standardQueries = item.query
      }
    })
    skills = this.props.skills.slice(1)
    return (
      <div className='sort-container'>
        <div className={this.props.modalStyle != 'Card' ? 'sort' : 'sort sort2'}>
          <p className='sort-title'>{__('customHome.label.functionCard')}</p>
          <p className='sort_txt'>
            <span className='txt interview'>{this.props.skills[0].title}</span>
          </p>
          <SortableList
            modalStyle={this.props.modalStyle}
            items={skills}
            axis='xy'
            onSortEnd={this.onSortEnd}
            useDragHandle={true}
            onRemove={(idx, display) => this.remove(idx, display)}
          />
          <div className={this.props.modalStyle != 'Card' ? 'sort_area1' : 'sort_area2'}>
            {skills.map((item, i) => {
              if (!item.display) {
                return (
                  <p className='sort_info gray' key={`item-${i}`}>
                    <Popover content={this.props.modalStyle != 'Technical' ? item.title : item.tip}>
                      <span className='sort_content'>
                        {this.props.modalStyle != 'Technical' ? item.title : item.tip}
                      </span>
                    </Popover>
                    <span
                      className='sort_switch'
                      onClick={() => {
                        this.add(i, item.display)
                      }}
                    >
                      <em className='switch_off'></em>
                    </span>
                    <Icon icon='move' className='sort_button' />
                  </p>
                )
              }
            })}
          </div>
        </div>
        <div className='ver-line'></div>

        <div className='query-sort-container'>
          <div className='sort'>
            <p className='sort-title'>推荐问法</p>
            <p
              className='sort_add'
              onClick={() => {
                if (standardQueries.length < 10) {
                  modal({
                    title: __('customHome.btn.addNewQuestion'),
                    confirm: false,
                    cancel: false,
                    content: (
                      <AddSkill
                        devUrl={this.props.devUrl}
                        modalStyle={this.props.modalStyle}
                        newAddSkill={this.newAddSkill.bind(this)}
                        newAddQueries={this.newAddQueries.bind(this)}
                        addStyle='queries'
                      />
                    ),
                  })
                } else {
                  message.error(__('customHome.modalMsg.recommendedNum', { num: 10 }))
                  {
                    /* modal({
									content: __('customHome.modalMsg.recommendedNum',{num:10}),
									cancel: false,
								}) */
                  }
                }
              }}
            >
              {__('customHome.btn.addNewQuestion')}
            </p>
            <SortableList_query
              modalStyle={this.props.modalStyle}
              items={standardQueries}
              axis='xy'
              onSortEnd={this.onSortQueryEnd}
              useDragHandle={true}
              onDelQuery={(idx) => this.onDelQuery(idx)}
            />
          </div>
        </div>
      </div>
    )
  }
  remove = (idx, display) => {
    let showSkillNum = 0
    this.props.skills.forEach((item) => {
      if (item.display) {
        showSkillNum = showSkillNum + 1
      }
    })
    console.log('显示skills数量' + showSkillNum)
    if (showSkillNum <= 3) {
      message.error('请至少保留3个功能')
      // modal({
      // 	title: '隐藏失败',
      // 	content: '请至少保留3个功能',
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除index：', idx, '；当前display：', display)
      let index = (index = idx + 1)
      this.props.dispatch({
        type: REMOVE_HOMEMODULE_SKILL,
        data: {
          index: index,
          display: !display,
        },
      })
    }
  }
  add = (idx, display) => {
    console.log('添加index：', idx, '；当前display：', display)
    let index = idx + 1
    this.props.dispatch({
      type: ADD_HOMEMODULE_SKILL,
      data: {
        index: index,
        display: !display,
        modalStyle: this.props.modalStyle,
      },
    })
  }
  onSortEnd = ({ oldIndex, newIndex }) => {
    oldIndex = oldIndex + 1
    newIndex = newIndex + 1
    this.props.dispatch({
      type: CHANGE_SKILL_SORT,
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
      },
    })
  }
  onDelQuery = (idx) => {
    let standardQueries
    this.props.queries.forEach((item) => {
      if (item.name == 'governmentMain') {
        standardQueries = item.query
      }
    })
    console.log('获取query数量' + standardQueries.length)
    if (standardQueries.length <= 4) {
      message.error('请至少保留4条推荐问法')
      // modal({
      // 	title: '删除失败',
      // 	content: '请至少保留4条推荐问法',
      // 	cancel: false,
      // 	confirm: __('customHome.btn.gotIt'),
      // });
      return
    } else {
      console.log('移除index：', idx)
      this.props.dispatch({
        type: REMOVE_HOMEMODULE_QUERY,
        data: {
          index: idx,
          modalStyle: this.props.modalStyle,
        },
      })
    }
  }
  onSortQueryEnd = ({ oldIndex, newIndex }) => {
    this.props.dispatch({
      type: CHANGE_QUERY_SORT,
      data: {
        dragBefore: oldIndex,
        dragAfter: newIndex,
        modalStyle: this.props.modalStyle,
      },
    })
  }
}

export default Sort
export { Sort }
