import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
// import { Dropdown, MenuItem } from 'biz-components'
@extend({
  styles: require('./styles.less'),
})
@connect((state) => ({}))
export default class QueryList extends React.Component {
  constructor(props) {
    super(props)
  }

  state = {
    querys: this.props.querys,
  }

  componentDidMount() {}
  render() {
    return (
      <div>
        <ul className='scroll-setting'>
          {this.state.querys.map((item, index) => {
            return (
              <li
                key={index}
                onClick={() => {
                  this.props.selectQuery(item)
                }}
              >
                {item}
              </li>
            )
          })}
          {this.props.type == '0'
            ? this.state.querys.map((item, index) => {
                return (
                  <li
                    key={index}
                    onClick={() => {
                      this.props.selectQuery(`小豹，${item}`)
                    }}
                  >{`小豹，${item}`}</li>
                )
              })
            : ''}
        </ul>
      </div>
    )
  }
}
