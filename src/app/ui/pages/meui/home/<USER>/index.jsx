import React from 'react'
import { connect } from 'react-redux'
import { extend } from 'koot'
import { Icon } from 'biz-components'
import modal, { close as closeModal } from '@utils/modal'
import request, { getUrl as getApiUrl } from '@utils/request'
import { TitleBlock } from 'biz-components'
import Government from './government'
import Progress from './progress'
import Tips from './tips'
import Question from './question'
import Title from './title'
import Sort from './sort'
import BackImg from './backImg'
import Robot from './robot'
import { GET_HOME_MODULE, CHANGE_INFO, CHANGE_MODULE_SKILL, ORIGIN_HOME_MODULE } from '@redux/action-types'
import {
  getModuleConfigList,
  getModuleConfig,
  uploadResource,
  publishModule,
  getModuleStatus,
  directPublishModule,
  recoverDefaultStatus,
} from '@api/_home/'
import recommendQues from '@api/_home/_gov/recommendQues'

import { getRobotStatistics, selectRobot } from '@api/robots'
import { RobotList } from 'biz-components'
import { message } from 'antd'

@connect((state) => {
  let devUrl = '/'
  if (__DEV__) {
    console.log('页面：自定义首页', state._home)
    devUrl = 'http://tjd.ainirobot.com/'
  }
  return {
    homeModule: state._home.homeModule,
    originHomeModule: state._home.originHomeModule,
    isModuleChange: state._home.isModuleChange,
    currentRobotId: state.robots.current,
    robots: state.robots.robots,
    fetchStatus: state.robots.fetchStatus,
    devUrl: devUrl,
  }
})
@extend({
  styles: require('./styles.less'),
  pageinfo: () => ({
    title: __('customHome.title'),
  }),
})
export default class extends React.Component {
  state = {
    iconJson: recommendQues.funcConfig,
    currentButton: 0, //3个按钮
    currentIndex: -2, //模版内容
    currentSort: false, //排序
    changeContent: 'tips', //编辑区显示内容
    showInputTips: false,
    chooseQueTypes: '', //编辑区question类型
    modalStyle: '', //列表式List 卡片式Card 科技范Technical 标准式Standard
    uploadUrl: '', //上传icon
    logo: '', //上传logo
    backImg: '', //自定义背景图
    progressNum: 0, //同步进度
    allNum: 0, //总同步数
    chooseList: [], //同步机器人列表
    fiveTimer: true, //进入页面5秒提示
    queriesIndex: -1, //当前选中queries索引
    showWarning: false, //不显示警示
  }
  componentDidMount() {
    //只执行一次。
    window.onbeforeunload = function () {
      return __('customHome.modalMsg.leaveMsg')
    }
    window.onunload = function () {
      return __('customHome.modalMsg.leaveMsg')
    }

    const timer = setInterval(() => {
      if (this.props.currentRobotId) {
        clearInterval(timer)
        this.getHomeModule()
      }
    }, 500)

    //5秒关闭提示蒙层
    const fiveTimer = setTimeout(() => {
      clearTimeout(fiveTimer)
      this.setState({
        fiveTimer: false,
      })
    }, 5000)
  }
  componentWillUnmount() {
    this.interval && clearInterval(this.interval)
    window.onbeforeunload = function () {}
    window.onunload = function () {}
  } //组件销毁 这两个弹窗也要销毁
  componentDidUpdate(prevProps, prevState) {
    // console.log("componentDidUpdate");
    // console.log(prevProps.currentRobotId);
    // console.log(this.props.currentRobotId);
    if (prevProps.currentRobotId != this.props.currentRobotId) {
      this.getHomeModule()
    }
  }
  getHomeModule() {
    let module_code_list = ['module_gov_home']
    let formData = new FormData()
    formData.append('module_code_list', JSON.stringify(module_code_list))
    formData.append('robot_uuid', this.props.currentRobotId)
    formData.append('config_status', 'publish')
    getModuleConfigList(formData)
      .then((res) => {
        console.log('获取robot_id')
        console.log(res.module_config_list)
        if (res.module_config_list.length != 0) {
          var config_id = res.module_config_list[0].config_id
          var version = res.module_config_list[0].version
        } else {
          var config_id = Date.parse(new Date()) / 1000
          var version = ''
        }
        sessionStorage.setItem('config_id', config_id)
        sessionStorage.setItem('version', version)
        let formData = new FormData()
        formData.append('module_code', 'module_gov_home')
        formData.append('config_id', sessionStorage.getItem('config_id'))
        formData.append('version', sessionStorage.getItem('version'))

        console.log('首页模板数据 3')
        getModuleConfig(formData)
          .then((res) => {
            let config_json = res.module_config.config_json
            if (typeof config_json === 'string') {
              try {
                config_json = JSON.parse(config_json)
              } catch (e) {}
            }
            console.log('get_home_module 8')
            this.props.dispatch({
              type: 'GET_HOME_MODULE',
              data: {
                homeModule: config_json,
              },
            })
            this.setState({
              modalStyle: config_json.templateInfo.templateCode,
            })
          })
          .catch((res) => {
            console.log(res)
          })
      })
      .catch((res) => {})
  }

  changeRobot(id) {
    this.props.dispatch(selectRobot(id))
    this.props.dispatch(getRobotStatistics(id))
  }
  recoverSet() {
    //删除上传的背景图
    this.setState({
      backImg: '',
    })
    console.log('恢复默认设置')
    if (sessionStorage.getItem('version') == '') {
      this.getHomeModule(() => {
        message.success('首页已成功恢复成默认标准式配置')
        // modal({
        //     title: __('customHome.modalMsg.successfullyReset'),
        //     content: '首页已成功恢复成默认标准式配置',
        //     cancel: false,
        //     confirm: __('customHome.btn.gotIt'),
        // });
      })
    } else {
      let robotID = [this.props.currentRobotId]
      let formData = new FormData()
      formData.append('module_code', 'module_gov_home')
      formData.append('config_id', sessionStorage.getItem('config_id'))
      formData.append('version', sessionStorage.getItem('version'))
      formData.append('robot_uuid_list', JSON.stringify(robotID))
      formData.append('status', 'default')
      recoverDefaultStatus(formData)
        .then((res) => {
          this.getHomeModule()
          message.success(__('customHome.modalMsg.homepageSuccessfullyReset'))
          // modal({
          //     title: __('customHome.modalMsg.successfullyReset'),
          //     content: __('customHome.modalMsg.homepageSuccessfullyReset'),
          //     cancel: false,
          //     confirm: __('customHome.btn.gotIt'),
          // });
        })
        .catch((res) => {
          modal({
            title: __('customHome.modalMsg.failedToReset'),
            content: __('customHome.modalMsg.homepageResetFailed'),
            cancel: __('customHome.btn.cancel2'),
            confirm: __('customHome.btn.retry'),
            btnCenter: true,
            onConfirm: () => {
              this.recoverSet()
            },
          })
        })
    }
  }
  render() {
    if (this.props.homeModule) {
      let templateCode = this.props.homeModule.templateInfo.templateCode,
        backgroundStr = ''
      if (templateCode == 'List') {
        backgroundStr = 'module_public/module_skill_home/home1.png'
      } else if (templateCode == 'Card') {
        backgroundStr = 'module_public/module_skill_home/home2.png'
      } else if (templateCode == 'Standard') {
        backgroundStr = 'module_public/module_skill_home/home4.png'
      }
      // if(this.props.homeModule.templateInfo.background==backgroundStr){
      // 	var styles={}
      // }else{
      var styles = {
        backgroundImage: 'url(' + this.props.devUrl + 'media/' + this.props.homeModule.templateInfo.background + ')',
      }
      // }
    }

    return (
      <div className={this.props.className}>
        <TitleBlock title={__('customHome.title')} component='h1'>
          <div className='title-con'>
            <div className='home-title-bar'>
              <button
                className='success_setting recover_setting'
                onClick={() => {
                  if (JSON.stringify(this.props.robots) == '{}') {
                    message.error(__('customHome.modalMsg.noRobotsOfCompany'))
                    {
                      /* modal({
                                            content: __('customHome.modalMsg.noRobotsOfCompany'),
                                            cancel: false,
                                            confirm: __('customHome.btn.confirm')
                                        }); */
                    }
                    return
                  }
                  modal({
                    title: __('customHome.btn.resetToDefault'),
                    content: __('customHome.modalMsg.msg3'),
                    cancel: __('customHome.btn.cancel2'),
                    confirm: __('customHome.btn.confirm'),
                    btnCenter: true,
                    onConfirm: () => {
                      this.recoverSet()
                    },
                  })
                }}
              >
                {__('customHome.btn.resetToDefault')}
              </button>
              <button
                className='success_setting'
                onClick={() => {
                  if (this.checkJson()) {
                    modal({
                      title: __('customHome.modalMsg.title1'),
                      content: __('customHome.modalMsg.msg1'),
                      cancel: __('customHome.btn.cancel2'),
                      confirm: __('customHome.modalMsg.title1'),
                      btnCenter: true,
                      onConfirm: () => {
                        this.setState({
                          changeContent: 'tips',
                          showWarning: true,
                        })
                      },
                    })
                  }
                }}
              >
                {__('customHome.btn.save')}
              </button>
              {this.state.allNum != 0 && (
                <p className='progress'>
                  <Icon
                    icon='fix_log_synchro'
                    className='icon'
                    style={{ verticalAlign: 'middle', marginRight: '10px' }}
                  />
                  机器人同步中({this.state.progressNum}/{this.state.allNum})
                </p>
              )}
              {this.state.showWarning && <p className='warning'>{__('customHome.modalMsg.syncWarning')}</p>}
            </div>
            <RobotList
              fetchStatus={this.props.fetchStatus}
              robots={this.props.robots}
              className='robot-list'
              currentId={this.props.currentId}
              onChangeRobot={this.changeRobot.bind(this)}
            />
          </div>
        </TitleBlock>

        <div className='content'>
          <div className='preview'>
            <div className='buttons'>
              <button
                className={this.state.currentButton == 2 ? 'click_background sort' : 'sort'}
                onClick={() => {
                  if (this.checkJson()) {
                    this.setState({
                      currentButton: 2,
                      currentIndex: -2,
                      currentSort: true,
                    })
                    this.changeContent('sort')
                  }
                }}
              >
                {__('customHome.btn.sequenceManagement')}
              </button>
              {this.state.modalStyle != 'Technical' ? (
                <button
                  className={this.state.currentButton == 3 ? 'click_background diy' : 'diy'}
                  onClick={() => {
                    if (this.checkJson()) {
                      this.setState({
                        currentButton: 3,
                        currentIndex: -2,
                        currentSort: false,
                      })
                      this.changeContent('backImg')
                    }
                  }}
                >
                  {__('customHome.btn.customizeBackground')}
                </button>
              ) : (
                ''
              )}
            </div>
            <div className='previewCon' style={styles}>
              {this.state.modalStyle == 'government' ? (
                <Government
                  fiveTimer={this.state.fiveTimer}
                  backImg={this.state.backImg}
                  devUrl={this.props.devUrl}
                  currentSort={this.state.currentSort}
                  currentIndex={this.state.currentIndex}
                  changeCurrentIndex={this.changeCurrentIndex.bind(this)}
                  changeContent={this.changeContent.bind(this)}
                  checkJson={this.checkJson.bind(this)}
                />
              ) : (
                ''
              )}
            </div>
          </div>
          <div className='setting'>
            {this.state.changeContent == 'tips' ? (
              <Tips
                allNum={this.state.allNum}
                uploadData={this.uploadData.bind(this)}
                chooseRobots={this.chooseRobots.bind(this)}
                checkJson={this.checkJson.bind(this)}
              />
            ) : (
              ''
            )}
            {this.state.changeContent == 'question' ? (
              <Question
                changeInputTips={this.changeInputTips.bind(this)}
                showInputTips={this.state.showInputTips}
                uploadUrl={this.state.uploadUrl}
                types={this.state.chooseQueTypes}
                devUrl={this.props.devUrl}
                queriesIndex={this.state.queriesIndex}
                selectImg={this.selectImg.bind(this)}
              />
            ) : (
              ''
            )}
            {this.state.changeContent == 'title' ? (
              <Title logo={this.state.logo} devUrl={this.props.devUrl} selectImg={this.selectImg.bind(this)} />
            ) : (
              ''
            )}
            {this.state.changeContent == 'sort' ? (
              <Sort modalStyle={this.state.modalStyle} devUrl={this.props.devUrl} />
            ) : (
              ''
            )}
            {this.state.changeContent == 'backImg' ? (
              <BackImg
                selectImg={this.selectImg.bind(this)}
                devUrl={this.props.devUrl}
                deleteBackImg={this.deleteBackImg.bind(this)}
              />
            ) : (
              ''
            )}
          </div>
        </div>
      </div>
    )
  }
  chooseRobots(arr) {
    console.log(arr)
    this.setState({
      chooseList: arr,
    })
  }
  changeCurrentIndex(num) {
    this.setState({
      currentIndex: num,
      currentButton: 0,
      currentSort: false,
    })
  }
  changeInputTips(flag) {
    this.setState({
      showInputTips: flag,
    })
  }
  changeContent(txt, sty, queriesIndex) {
    console.log(sty)
    if (queriesIndex != -1) {
      this.setState({
        changeContent: txt,
        chooseQueTypes: sty,
        queriesIndex: queriesIndex,
      })
    } else {
      if (sty) {
        this.setState({
          changeContent: txt,
          chooseQueTypes: sty,
        })
      } else {
        this.setState({
          changeContent: txt,
        })
      }
    }
    this.setState({
      showInputTips: false,
    })
  }
  deleteBackImg() {
    this.setState({
      backImg: '',
    })
  }
  direct_uploadData() {
    console.log('直接发布模版')
    let formData = new FormData()
    let config_info_list_json = {
      config_id: sessionStorage.getItem('config_id'),
      version: sessionStorage.getItem('version'),
    }
    let config_info_list_arr = []
    config_info_list_arr.push(config_info_list_json)
    formData.append('module_code', 'module_gov_home')
    formData.append('config_info_list', JSON.stringify(config_info_list_arr))
    formData.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
    directPublishModule(formData)
      .then((res) => {
        console.log('保存并发布模块7')
        this.uploadDataPorgress()
      })
      .catch((res) => {
        console.log(res)
      })
  }
  uploadData() {
    console.log('uploadData-mini2')
    if (this.state.chooseList.length == 0) {
      // modal({
      //     content: __('customHome.modalMsg.selectTheRobotToSync'),
      //     cancel: false,
      //     confirm: __('customHome.btn.gotIt'),
      // });
      message.error(__('customHome.modalMsg.selectTheRobotToSync'))
      return
    }
    // console.log(this.props.originHomeModule);
    // console.log(this.props.homeModule)
    // if(JSON.stringify(this.props.homeModule) == JSON.stringify(this.props.originHomeModule) && sessionStorage.getItem("version")!=""){
    // 	console.log('same');
    // 	this.direct_uploadData();
    // 	return;
    // }
    console.log(this.props.homeModule)
    let formData = new FormData()
    var config_json = this.props.homeModule
    var config_json_str = JSON.stringify(config_json)
    var resource_path_list = []
    if (this.props.homeModule.templateInfo.titleBarIcon != '') {
      resource_path_list.push(this.props.homeModule.templateInfo.titleBarIcon) //顶部logo图
    }
    resource_path_list.push(this.props.homeModule.templateInfo.background) //背景图
    if (this.state.modalStyle != 'Technical') {
      this.props.homeModule.skills.map((item, index) => {
        resource_path_list.push(item.icon) //skills图
      })
    }
    console.log('图片数据 -2')
    console.log(resource_path_list)
    var resource_path_list_str = JSON.stringify(resource_path_list)
    var public_resource_path_list = []
    var public_resource_path_list_str = JSON.stringify(public_resource_path_list)
    formData.append('module_code', 'module_gov_home')
    formData.append('config_id', sessionStorage.getItem('config_id'))
    formData.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
    formData.append('config_json', config_json_str)
    formData.append('name', 'list')
    formData.append('version', sessionStorage.getItem('version'))
    formData.append('resource_path_list', resource_path_list_str) //资源列表(选填)
    formData.append('public_resource_path_list', public_resource_path_list_str) //公共资源列表(选填)
    publishModule(formData)
      .then((res) => {
        console.log('保存并发布模块8')
        console.log(res)
        sessionStorage.setItem('version', res.version)
        this.uploadDataPorgress()
      })
      .catch((res) => {
        console.log(res)
      })
  }
  uploadDataPorgress() {
    console.log('获取同步进度')
    modal({
      title: false,
      confirm: false,
      cancel: false,
      fullscreen: false,
      content: <Progress str={__('customHome.modalMsg.syncingNewHomepage')} />,
    })
    document.getElementsByClassName('btn-close')[0].style.display = 'none'
    let _time = 0
    this.interval = setInterval(() => {
      _time++
      if (_time >= 20) {
        closeModal()
        this.setState(
          {
            allNum: 0,
          },
          () => {
            message.error(__('customHome.modalMsg.syncingTimeout'))
            // modal({
            //     content: __('customHome.modalMsg.syncingTimeout'),
            //     cancel: false,
            //     confirm: __('customHome.btn.gotIt'),
            // });
          },
        )
        clearInterval(this.interval)
        this.interval = null
        return
      }
      let formData2 = new FormData()
      formData2.append('module_code', 'module_gov_home')
      formData2.append('config_id', sessionStorage.getItem('config_id'))
      formData2.append('robot_uuid_list', JSON.stringify(this.state.chooseList))
      formData2.append('version', sessionStorage.getItem('version'))
      getModuleStatus(formData2)
        .then((res) => {
          console.log(res)
          this.setState({
            showWarning: false,
            allNum: res.pull_total_num,
            progressNum: res.pull_suc_num,
          })
          if (res.pull_total_num == res.pull_suc_num && res.pull_suc_num != 0) {
            closeModal()
            clearInterval(this.interval)
            message.success(__('customHome.modalMsg.syncingSuccessful'))
            // modal({
            //     content: __('customHome.modalMsg.syncingSuccessful'),
            //     cancel: false,
            //     confirm: __('customHome.btn.gotIt'),
            // });
            this.setState({
              allNum: 0,
            })
          }
        })
        .catch((res) => {
          console.log('获取同步进度失败')
          clearInterval(this.interval)
        })
    }, 2000)
    // setTimeout(function() {//拿不到this
    // 	console.log("1分钟")
    // 	clearInterval(this.interval);
    // 	modal({
    // 		content: '同步成功：'+this.state.progressNum+"台",
    // 		cancel: false,
    // 		confirm:  __('customHome.btn.gotIt'),
    // 	});
    // },1*60*1000);//1分钟执行一次
  }
  selectImg(e, type, idx) {
    console.log(e)
    console.log(type)
    console.log(idx)
    const file = e.target.files[0]
    let reader = new FileReader()
    var uploadImgWidth = 0,
      uploadImgHeight = 0
    if (!file) {
      return
    }
    if (file.type != 'image/png' && file.type != 'image/jpeg') {
      message.success(__('customHome.pic.uploadFilesFormat', { format: 'png/jpg/jpeg' }))
      // modal({
      //     content: __('customHome.pic.uploadFilesFormat', { format: 'png/jpg/jpeg' }),
      //     cancel: false,
      //     confirm: __('customHome.btn.gotIt')
      // })
      return
    }
    switch (type) {
      case 'backImg':
        uploadImgWidth = 1200
        uploadImgHeight = 1920
        break
      case 'logo':
        uploadImgWidth = 100
        uploadImgHeight = 100
        break
      case 'gov_smallIcon':
        uploadImgWidth = 520
        uploadImgHeight = 220
        break
      case 'gov_bigIcon':
        uploadImgWidth = 1084
        uploadImgHeight = 300
        break
    }
    if (file.size / 1024 / 1000 > 5) {
      message.success(__('customHome.pic.picSizeLimit2'))
      // modal({
      //     content: __('customHome.pic.picSizeLimit2'),
      //     cancel: false,
      //     confirm: __('customHome.btn.gotIt')
      // })
      return
    }
    var _this = this
    reader.onload = function (e) {
      let image = new Image()
      image.onload = function () {
        if (image.width != uploadImgWidth || image.height != uploadImgHeight) {
          console.log(image.width)
          console.log(image.height)
          return message.error(
            __('customHome.pic.uploadpicDimensions') +
              ':' +
              uploadImgWidth +
              '*' +
              uploadImgHeight +
              __('customHome.btn.pixels'),
          )
          // return modal({
          //     title: __('customHome.pic.WrongPictureDimensions'),
          //     content: __('customHome.pic.uploadpicDimensions') + ':' + uploadImgWidth + '*' + uploadImgHeight + __('customHome.btn.pixels'),
          //     cancel: false,
          //     confirm: __('customHome.btn.gotIt')
          // })
        } else {
          let formData = new FormData()
          formData.append('resource_file', file)
          formData.append('module_code', 'module_gov_home')
          formData.append('config_id', sessionStorage.getItem('config_id'))
          formData.append('resource_type', 'image')
          formData.append('resource_file', file.name)
          formData.append('version', '')
          uploadResource(formData)
            .then((res) => {
              console.log('上传图片')
              let upload_url = res.resource_url
              console.log(upload_url)
              if (type != 'backImg') {
                _this.setState({
                  uploadUrl: upload_url,
                })
              } else {
                _this.setState({
                  backImg: upload_url,
                })
              }

              if (type == 'logo') {
                _this.setState({
                  logo: upload_url,
                })
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'titleBarIcon',
                    content: upload_url,
                  },
                }) //更改json标题内容
              } else if (type == 'backImg') {
                _this.props.dispatch({
                  type: 'CHANGE_INFO',
                  data: {
                    type: 'background',
                    content: upload_url,
                  },
                }) //更改json标题内容
              } else {
                _this.props.homeModule.skills[idx].icon = upload_url
                _this.props.dispatch({
                  type: 'CHANGE_MODULE_SKILL',
                  data: {
                    type: 'icon',
                    index: idx,
                    key: 18,
                    content: _this.props.homeModule.skills[idx],
                  },
                }) //更改skills中icon路径
              }
              message.success(__('customHome.pic.uploadSuccess'))
              // modal({
              //     content: __('customHome.pic.uploadSuccess'),
              //     cancel: false,
              // });
            })
            .catch((res) => {
              console.log(res)
              message.error(__('customHome.pic.uploadFailed'))
              // modal({
              //     content: __('customHome.pic.uploadFailed'),
              //     cancel: false,
              //     confirm: __('customHome.btn.gotIt')
              // })
            })
        }
      }
      image.src = e.target.result
    }
    reader.readAsDataURL(e.target.files[0])
  }
  checkJson() {
    if (JSON.stringify(this.props.robots) == '{}') {
      message.error(__('customHome.modalMsg.noRobotsOfCompany'))
      // modal({
      //     content: __('customHome.modalMsg.noRobotsOfCompany'),
      //     cancel: false,
      //     confirm: __('customHome.btn.confirm')
      // });
      return
    }
    let json = this.props.homeModule
    //console.log(json);
    let title = json.templateInfo.titleBarText
    let flag = true
    if (title == '') {
      message.error(__('customHome.modalMsg.enterTitleName'))
      // modal({
      //     content: __('customHome.modalMsg.enterTitleName'),
      //     cancel: false,
      //     confirm: __('customHome.btn.confirm')
      // });
      flag = false
      return
    }
    var standardQueries = []
    json.queries.forEach((item) => {
      if (item.name == 'governmentMain') {
        standardQueries = item.query
      }
    })
    standardQueries.map((item, index) => {
      if (item == '') {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))
        // modal({
        //     content: __('customHomoe.modalMsg.enterRecommendedQuestions'),
        //     cancel: false,
        //     confirm: __('customHome.btn.confirm')
        // });
        flag = false
        return
      }
    })
    json.skills.map((item, index) => {
      if (item.title == '') {
        message.error(__('customHome.modalMsg.enterFunctionName'))
        // modal({
        //     content: __('customHome.modalMsg.enterFunctionName'),
        //     cancel: false,
        //     confirm: __('customHome.btn.confirm')
        // });
        flag = false
        return
      }
      if (item.tip == '') {
        message.error(__('customHome.modalMsg.enterRecommendedQuestions'))
        // modal({
        //     content: __('customHomoe.modalMsg.enterRecommendedQuestions'),
        //     cancel: false,
        //     confirm: __('customHome.btn.confirm')
        // });
        flag = false
        return
      }
    })
    return flag
  }
}
