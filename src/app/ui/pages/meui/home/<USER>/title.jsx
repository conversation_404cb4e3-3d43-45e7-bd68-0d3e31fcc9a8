import React from 'react'
import { connect } from 'react-redux'
import { ButtonSelectFile } from 'biz-components'
import { CHANGE_INFO } from '@redux/action-types'

@connect((state) => {
  if (__DEV__) console.log('页面：自定义首页标题', state._home)
  return {
    templateInfo: state._home.homeModule.templateInfo,
  }
})
class Title extends React.Component {
  state = {
    TitleStyle: 'text',
  }
  handleChange(title) {
    this.setState({
      TitleStyle: title,
    })
  }
  render() {
    var image_url = this.props.devUrl + 'media/'
    return (
      <div className='changeTitle a2'>
        {this.props.templateInfo.templateCode != 'Standard' ? (
          <div className='list'>
            <span className='title'>标题类型</span>
            <div className='chooseStyle 2'>
              <span
                className={this.state.TitleStyle == 'text' ? 'chooseCircle blue' : 'chooseCircle'}
                onClick={() => {
                  this.handleChange('text')
                }}
              ></span>
              <label>{__('customHome.btn.text')}</label>
              <span
                className={this.state.TitleStyle == 'Image' ? 'chooseCircle blue' : 'chooseCircle'}
                onClick={() => {
                  this.handleChange('Image')
                }}
              ></span>
              <label>自定义logo</label>
            </div>
          </div>
        ) : (
          ''
        )}
        {this.state.TitleStyle == 'text' ? (
          <div className='list'>
            <span className='title'>大标题名</span>
            <p className='newTitle'>
              <input
                type='text'
                maxLength='11'
                value={this.props.templateInfo.titleBarText}
                onChange={(e) => {
                  if (this.props.templateInfo.titleBarText == e.target.value.replace(/\s+/g, '')) {
                    return
                  }
                  this.props.templateInfo.titleBarText = e.target.value
                  this.props.dispatch({
                    type: 'CHANGE_INFO',
                    data: {
                      type: 'titleBarText',
                      content: this.props.templateInfo.titleBarText,
                    },
                  })
                }}
              />
              <span>
                <em>{this.props.templateInfo.titleBarText.length}</em> / <em>11</em>
              </span>
            </p>
          </div>
        ) : (
          ''
        )}
        {this.state.TitleStyle == 'Image' ? (
          <div className='list'>
            <span className='title'>上传图片</span>
            <p className={this.props.logo != '' ? 'list_area upload_area' : 'list_area'}>
              <ButtonSelectFile
                className='upload-btn square'
                onSelect={(evt) => {
                  this.props.selectImg(evt, 'logo', '')
                }}
              />
              <span className='uploadInfo'>{__('customHome.pic.upload')}</span>
              {this.props.logo != '' ? <img src={image_url + this.props.logo} className='newLogo' /> : ''}
            </p>
            <p className='list_area'>
              <span className='uploadTips'>{__('customHome.pic.pictureRequirements1')}：</span>
              <span className='uploadTips'>1.{__('customHome.pic.dimensions')}：100px*100px</span>
              <span className='uploadTips'>
                2.{__('customHome.pic.size')}：{__('customHome.pic.sizeNum')}
              </span>
              <span className='uploadTips'>3.{__('customHome.pic.format')}：JPG/PNG</span>
            </p>
          </div>
        ) : (
          ''
        )}
      </div>
    )
  }
}
export default Title
export { Title }
