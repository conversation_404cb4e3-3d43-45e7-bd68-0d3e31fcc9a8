import request from '@utils/request'
import {} from '@redux/action-types'

//获取企业问答
export const getLearnList = (data) => {
  return request('/api/control/issues_list', {
    method: 'POST',
    data: data,
  })
}

//设置状态
export const setIssuesStatus = (data) => {
  return request('/api/control/set_issue_status', {
    method: 'POST',
    data: data,
  })
}

//删除
export const issuesDel = (data) => {
  return request('/api/control/issues_delete', {
    method: 'POST',
    data: data,
  })
}

// /api/control/protect_answer/list
// 获取企业兜底回复
export const protectAnswerList = (data) => {
  return request('/api/control/protect_answer/list', {
    method: 'GET',
    data: data,
  })
}
// 添加兜底回复记录
export const AddProtectAnswer = (data) => {
  return request('/api/control/protect_answer/add', {
    method: 'POST',
    data: data,
  })
}
// 更新兜底回复记录
export const updataProtectAnswer = (data) => {
  return request('/api/control/protect_answer/update', {
    method: 'POST',
    data: data,
  })
}
// 删除兜底回复记录
export const deleteProtectAnswer = (data) => {
  return request('/api/control/protect_answer/delete', {
    method: 'POST',
    data: data,
  })
}

// 保存关键词答案
export const saveKeyAnswer = (data) => {
  return request('/api/control/keywords', {
    method: 'POST',
    data: data,
  })
}

// 保存问答答案
export const saveQueryAnswer = (data) => {
  return request('/api/control/qa/add', {
    method: 'POST',
    data: data,
  })
}

// 获取关键词答案
export const getKeyAnswer = (data) => {
  return request('/api/control/keywords', {
    method: 'GET',
    data: data,
  })
}

// 获取问答库答案
export const getQueryAnswer = (data) => {
  return request('/api/control/qa/search', {
    method: 'GET',
    data: data,
  })
}
