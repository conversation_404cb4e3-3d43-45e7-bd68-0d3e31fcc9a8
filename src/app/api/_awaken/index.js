import request from '@utils/request'
import {} from '@redux/action-types'

export const uploadImg = (data) => {
  return request('/api/control/skill_icon', {
    method: 'POST',
    data,
  })
}
export const getskillsSettingList = (data) => {
  return request('/api/control/skills_setting', {
    method: 'GET',
    data,
  })
}
export const uploadskillsSetting = (data) => {
  return request('/api/control/skills_setting', {
    method: 'POST',
    data,
  })
}
export const getAbSkillAbility = () => {
  return request('/api/control/skill_ability', {
    method: 'GET',
  })
}
export const uploadAbSkillAbility = (data) => {
  return request('/api/control/skill_ability', {
    method: 'POST',
    data,
  })
}
export const delSkillAbility = (data) => {
  return request('/api/control/del_skill_ability', {
    method: 'POST',
    data,
  })
}
export const filterSkillAbility = (data) => {
  return request('/api/control/skill_ability', {
    method: 'GET',
    data,
  })
}
