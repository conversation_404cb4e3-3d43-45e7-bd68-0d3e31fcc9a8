import { GET_STAFF_OBJ, GET_WELCOME_OBJ } from '@redux/action-types'

const initialState = {}

export default function (state = initialState, { type, ...obj }) {
  switch (type) {
    /*
        case SAMPLE: {
            return Object.assign({}, state, data)
        }
        */
    case GET_STAFF_OBJ: {
      return Object.assign({}, state, obj.data)
    }
    case GET_WELCOME_OBJ: {
      return Object.assign({}, state, obj.data)
    }
  }

  return state
}
