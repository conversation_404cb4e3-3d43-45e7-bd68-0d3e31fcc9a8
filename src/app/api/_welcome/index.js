// import {
//     // INIT_LIST
// } from '@redux/action-types'

import {} from '@redux/action-types'
// import getStore from '@utils/store'
import request from '@utils/request'
/**
 * Redux action: 示例
 * @param {String} arg1
 */
// export const sample = (arg1) => dispatch => dispatch({
//     type: INIT_ROBOTS,
//     data: arg1
// })

//提交自定义的欢迎语
export const postWelcome = (data) => {
  return request('/api/control/corpus', {
    method: 'post',
    data,
  })
}

//getList
// 获取访客数据列表
export const getList = () => {
  return request('/api/control/corpus', {
    method: 'get',
  })
}
