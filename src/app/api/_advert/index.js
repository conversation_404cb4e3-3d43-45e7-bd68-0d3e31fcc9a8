import request from '@utils/request'

export const uploadResource = (data) => {
  return request('/api/control/upload_config_resource', {
    method: 'post',
    data: data,
  })
}

//获得详情
export const getModuleDetail = (data) => {
  console.log('详情- 1')
  return request('/api/control/module_config_detail', {
    method: 'post',
    data,
  })
}
//获得列表
export const getAdvertList = (data) => {
  return request('/api/control/module_config_list', {
    method: 'post',
    data,
  })
}

//发布 模块
export const publishModule = (data) => {
  return request('/api/control/publish_module', {
    method: 'post',
    data,
  })
}

//同步保存信息
export const postGuideInfo = (data) => {
  return request('/api/control/save_module_config', {
    method: 'post',
    data: data,
  })
}

//同步信息ota2（分包）
export const postAdvertInfo = (data) => {
  return request('/api/control/update_save_module_config', {
    method: 'post',
    data: data,
  })
}

//保存并发布给机器人（单个导览发布到多个机器人）
export const saveandPublish = (data) => {
  console.log('api save_and_publish_module0')
  return request('/api/control/save_and_publish_module', {
    method: 'post',
    data: data,
  })
}

//设置模块状态
export const setConfigStatus = (data) => {
  return request('/api/control/set_config_status', {
    method: 'post',
    data,
  })
}

//设置包在机器人上的显示隐藏
export const switchstatusInRobot = (data) => {
  return request('/api/control/set_module_pkg_status', {
    method: 'post',
    data,
  })
}
//广告分包（列表页的发布到多个机器人）
export const directPublishModule = (data) => {
  return request('/api/control/direct_publish_module', {
    method: 'post',
    data,
  })
}
