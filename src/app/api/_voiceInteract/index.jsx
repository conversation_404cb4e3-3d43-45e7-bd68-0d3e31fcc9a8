import request from '@utils/request'
import { RESTORE_LOGIN, RESET_LOGIN, UPDATE_USER_PROFILE } from '@redux/action-types'

/**************************************
 * Redux actions
 *************************************/

// TODO: promise login
// export const login = (data) => dispatch => dispatch({
//     type: USER_LOGIN,
//     data,
// })

export const getVoiceInteract = (data) => {
  let url = '/api/control/dialog_history_list'
  // url ='http://test-jiedai.ainirobot.com/api/control/dialog_history_list';
  return request(url, {
    method: 'get',
    data,
  })
}

// /api/control/direct_publish_module
export const downVoiceInteract = (data) => {
  return request('/api/control/dialog_history_export', {
    method: 'get',
    ext: 'download',
    data,
  })
}

// /api/control/dialog_history_labels
export const getDomainIntent = (data) => {
  return request('/api/control/dialog_history_labels', {
    method: 'get',
    data,
  })
}
///api/control/operation_log
// 获取用户操作日志
export const getOperLog = (data) => {
  let url = '/api/control/operation_log'
  return request(url, {
    method: 'get',
    data,
  })
}

// 操作日志获取页面分类
export const getDataPage = (data) => {
  return request('/api/control/operation_module', {
    method: 'get',
    data,
  })
}
