import request from '@utils/request'
//同步信息
export const getReceptionList = (data) => {
  return request('/api/control/module_config_list', {
    method: 'post',
    data,
  })
}
//上传图片
export const upload = (data) => {
  return request('/api/control/upload_config_resource', {
    method: 'post',
    data,
  })
}
//保存
export const saveModuleConfig = (data) => {
  return request('/api/control/save_module_config', {
    method: 'post',
    data,
  })
}
//获取模块信息
export const getModuleConfig = (data) => {
  console.log('详情- 8')
  return request('/api/control/module_config_detail', {
    method: 'post',
    data,
  })
}
//设置模块状态
export const setConfigStatus = (data) => {
  return request('/api/control/set_config_status', {
    method: 'post',
    data,
  })
}

//发布 模块
export const publishModule = (data) => {
  return request('/api/control/publish_module', {
    method: 'post',
    data,
  })
}
export const corpVisitReason = (data) => {
  return request('/api/control/corp_visit_reason', {
    method: 'post',
    data,
  })
}

//单保存访问目的

export const updateSaveModuleConfig = (data) => {
  return request('/api/control/update_save_module_config', {
    method: 'post',
    data,
  })
}
//改变状态
export const setPkgModule = (data) => {
  return request('/api/control/set_module_pkg_status', {
    method: 'post',
    data,
  })
}
//删除
export const deleteModule = (data) => {
  return request('/api/control/set_config_status', {
    method: 'post',
    data,
  })
}
export const directPublishModule = (data) => {
  return request('/api/control/direct_publish_module', {
    method: 'post',
    data,
  })
}

//获取列表
export const getList = (data) => {
  return request('/api/control/module_config_list', {
    method: 'POST',
    data: data,
  })
}

//保存并发布
export const saveAndPublish = (data) => {
  console.log('api save_and_publish_module8')
  return request('/api/control/save_and_publish_module', {
    method: 'POST',
    data: data,
  })
}
//获取详细信息
export const getDetail = (data) => {
  console.log('详情- 9')
  return request('/api/control/module_config_detail', {
    method: 'POST',
    data,
  })
}
//列表发布
export const publish = (data) => {
  return request('/api/control/direct_publish_module', {
    method: 'POST',
    data: data,
  })
}
//删除
export const changeItemStatus = (data) => {
  return request('/api/control/set_module_pkg_status', {
    method: 'POST',
    data: data,
  })
}

//转tts
export const textToTts = (data) => {
  return request('/api/control/upload_config_texts', {
    method: 'POST',
    data: data,
  })
}
