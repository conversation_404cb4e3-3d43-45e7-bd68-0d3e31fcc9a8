import {
  GET_BGUIDE_OBJ,
  ADD_BGUIDE_ENTERPRISE,
  ADD_BGUIDE_GUIDE,
  DEL_BGUIDE_ENTERPRISE,
  DEL_BGUIDE_GUIDE,
  MODIFIED_BGUIDE_ENTERPRISE,
  MODIFIED_BGUIDE_GUIDE,
  SET_SELECTED_INDEX,
} from '@redux/action-types'

const initialState = {
  businessGuide: [],
  pIndexSelected: -1,
  cIndexSelected: -1,
}

export default function (state = initialState, { type, ...obj }) {
  switch (type) {
    case GET_BGUIDE_OBJ: {
      return Object.assign({}, state, obj.data)
    }
    case ADD_BGUIDE_ENTERPRISE: {
      let businessGuide = []
      let new_enterprise = {
        isRoot: true,
        title: '部门名称',
        ctime: Date.parse(new Date()) / 1000,
        guideInfo: [],
      }
      state.businessGuide.map((enterprise, index) => {
        businessGuide.push(enterprise)
      })
      businessGuide.unshift(new_enterprise)
      return Object.assign({}, state, { businessGuide: businessGuide })
    }
    case DEL_BGUIDE_ENTERPRISE: {
      console.log(obj.data.idx)
      let businessGuide = []
      state.businessGuide.map((enterprise, index) => {
        businessGuide.push(enterprise)
      })
      businessGuide.splice(obj.data.idx, 1)
      return Object.assign({}, state, { businessGuide: businessGuide })
    }
    case MODIFIED_BGUIDE_ENTERPRISE: {
      let businessGuide = []
      state.businessGuide.map((enterprise, index) => {
        businessGuide.push(enterprise)
      })
      let enterprise = Object.assign({}, businessGuide[obj.data.idx], { title: obj.data.title })
      businessGuide.splice(obj.data.idx, 1, enterprise)
      return Object.assign({}, state, { businessGuide: businessGuide })
    }
    case ADD_BGUIDE_GUIDE: {
      let businessGuide = []
      let new_guide = {
        title: '事项名称',
        guideText: '',
        guidePic: '',
        ctime: Date.parse(new Date()) / 1000,
      }
      state.businessGuide.map((enterprise, index) => {
        if (obj.data.idx != index) {
          businessGuide.push(enterprise)
        } else {
          let guide_info = []
          enterprise.guideInfo.map((guideInfo, index) => {
            guide_info.push(guideInfo)
          })
          guide_info.unshift(new_guide)
          businessGuide.push(Object.assign({}, enterprise, { guideInfo: guide_info }))
        }
      })
      return Object.assign({}, state, { businessGuide: businessGuide })
    }
    case DEL_BGUIDE_GUIDE: {
      let businessGuide = []
      state.businessGuide.map((enterprise, index) => {
        if (obj.data.pIdx != index) {
          businessGuide.push(enterprise)
        } else {
          enterprise.guideInfo.splice(obj.data.cIdx, 1)
          businessGuide.push(enterprise)
        }
      })
      return Object.assign({}, state, { businessGuide: businessGuide })
    }
    case MODIFIED_BGUIDE_GUIDE: {
      let businessGuide = []
      state.businessGuide.map((enterprise, index) => {
        if (index != obj.data.pIdx) {
          businessGuide.push(enterprise)
        } else {
          enterprise.guideInfo.map((guideInfo, index) => {
            if (index == obj.data.cIdx) {
              console.log(obj.data.content)
              guideInfo[obj.data.type] = obj.data.content
            }
          })
          businessGuide.push(enterprise)
        }
      })
      return Object.assign({}, state, { businessGuide: businessGuide })
    }
    case SET_SELECTED_INDEX: {
      return Object.assign({}, state, {
        pIndexSelected: obj.data.pIndexSelected,
        cIndexSelected: obj.data.cIndexSelected,
      })
    }
    default:
      return state
  }
}
