import request from '@utils/request'
import { CLEAR_MAP_LIST, GET_MAP_INFO } from '@redux/action-types'

// 获取地图列表
export const getMapList = () => {
  return request('/api/control/map_pkg_list', {
    method: 'GET',
    data: {},
  })
}

// 删除地图列表
export const clearMapList = () => (dispatch) => {
  dispatch({
    type: CLEAR_MAP_LIST,
    data: {
      mapList: [],
    },
  })
}

// 复制地图
export const copyMap = (data) => {
  return request('/api/control/copy_map_pkg', {
    method: 'POST',
    data,
  })
}

// 删除地图
export const deleteMap = (data) => {
  return request('/api/control/delete_map_pkg', {
    method: 'POST',
    data,
  })
}

// 更新地图详情
export const updateMap = (data) => {
  return request('/api/control/update_map_pkg', {
    method: 'POST',
    data,
  })
}

// 获取地图详情
export const getMapInfo = (map_id) => {
  return request('/api/control/map_pkg_info', {
    method: 'GET',
    data: { map_id },
  })
}
