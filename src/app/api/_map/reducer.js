import { GET_MAP_LIST, GET_MAP_INFO, CLEAR_MAP_LIST } from '@redux/action-types'

const initialState = {
  mapList: [],
  mapInfo: {},
}

export default function (state = initialState, { type, ...data }) {
  switch (type) {
    case GET_MAP_LIST: {
      return Object.assign({}, state, data.data)
    }
    case GET_MAP_INFO: {
      return Object.assign({}, state, data.data)
    }
    case CLEAR_MAP_LIST: {
      return Object.assign({}, state, data.data)
    }
  }
  return state
}
