import { GET_GROUP_LIST, SET_CURRENT, SET_CURRENTROBOT, SET_BASIC, SET_INSPIRE } from '@redux/action-types'
import getStore from '@utils/store'

const initialState = {
  groups: [],
  current: null,
  curRobot: null,
  basic: [],
}

export default function (state = initialState, { type, ...data }) {
  switch (type) {
    case GET_GROUP_LIST: {
      if (data.groups[1] && data.groups[1].attr.group_id == 'BASIC' && data.corpId == 201) {
        data.groups.splice(1, 1)
      }
      if (data.corpId) {
        delete data.corpId
      }
      return Object.assign({}, state, data)
    }
    case SET_CURRENT: {
      return Object.assign({}, state, data)
    }
    case SET_CURRENTROBOT: {
      return Object.assign({}, state, data)
    }
    case SET_BASIC: {
      return Object.assign({}, state, data)
    }
    case SET_INSPIRE: {
      return Object.assign({}, state, data)
    }
  }
  return state
}
