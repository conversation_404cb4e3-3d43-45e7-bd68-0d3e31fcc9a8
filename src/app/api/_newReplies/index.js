import request from '@utils/request'
import {
  GET_REPLIES_LIST,
  GET_REPLIES_EMPTYTIPS,
  GET_CONFIGURATION,
  GET_GROUP_LIST,
  SET_BASIC,
  SET_INSPIRE,
} from '@redux/action-types'

import getStore from '@utils/store'

export const getRepliesEmptytips = (data) => (dispatch) =>
  dispatch({
    type: GET_REPLIES_EMPTYTIPS,
    data,
  })

//获取自定义场景文件名
export const getConfiguration = (robotId) => (dispatch) => {
  request('/api/user/meui_configuration', {
    method: 'GET',
    data: {
      robot_id: robotId,
    },
  })
    .then((res) => {
      let filename = res.file_name ? decodeURI(res.file_name) : ''
      dispatch({
        type: GET_CONFIGURATION,
        data: {
          fileName: filename,
        },
      })
    })
    .catch(() => {
      dispatch({
        type: GET_CONFIGURATION,
        data: {
          fileName: '',
        },
      })
    })
}

//new new
//上传图片
export const uploadImageInfo = (data) => {
  return request('/api/control/qa_image', {
    method: 'POST',
    data,
  })
}

//上传问答视频
export const uploadVideoInfo = (data) => {
  return request('/api/control/qa_video', {
    method: 'POST',
    data,
  })
}
//上传问答视频封面
export const uploadVideoCover = (data) => {
  return request('/api/control/qa_videocover', {
    method: 'POST',
    data,
  })
}
// 问答批量上传
export const uploadQAFile = (data) => {
  return request('/api/control/qa_file', {
    method: 'POST',
    data,
  })
}
//下载批量模版
export const downLoadQAFile = (data) => {
  return request('/api/control/qa_file', {
    method: 'GET',
    data,
  })
}

//获取企业问答
export const getRepliesList = (data) => {
  return request('/api/control/qa', {
    method: 'GET',
    data,
  })
}
//添加企业问答/编辑企业问答
export const uploadQa = (data) => {
  return request('/api/control/qa', {
    method: 'POST',
    data,
  })
}

//删除一列
export const deleteQA = (data) => {
  return request('/api/control/qa_delete', {
    method: 'POST',
    data,
  })
}

//获取tag列表
export const getTagLists = (data) => {
  return request('/api/control/corp_qa_tags', {
    method: 'GET',
    data,
  })
}
//增加tag
export const addTag = (data) => {
  return request('/api/control/corp_qa_tags', {
    method: 'POST',
    data,
  })
}
//删除tag
export const deleteTag = (data) => {
  return request('/api/control/delete_corp_qa_tag', {
    method: 'POST',
    data,
  })
}

//问答改版
//获取企业问答
export const getQaList = (data) => {
  return request('/api/control/qa/search', {
    method: 'GET',
    data,
  })
}

//分类列表
export const groupsSearch = (data) => (dispatch) => {
  return request('/api/control/qa/groups/search', {
    method: 'POST',
    data,
  }).then((res) => {
    dispatch({
      type: GET_GROUP_LIST,
      groups: res,
      corpId: getStore().getState().user.corp.corp_cate,
    })
    return res
  })
}

export const groupsAdd = (data) => {
  return request('/api/control/qa/groups/add', {
    method: 'POST',
    data,
  })
}
export const groupsEditName = (data) => {
  return request('/api/control/qa/groups/edit_name', {
    method: 'POST',
    data,
  })
}
export const groupsDel = (data) => {
  return request('/api/control/qa/groups/delete', {
    method: 'POST',
    data,
  })
}
export const editThreshold = (data) => {
  return request('/api/control/qa/groups/edit_threshold', {
    method: 'POST',
    data,
  })
}

export const chgroup = (data) => {
  return request('/api/control/qa/chgroup', {
    method: 'POST',
    data,
  })
}

//添加企业问答/编辑企业问答
export const qaAdd = (data) => {
  return request('/api/control/qa/add', {
    method: 'POST',
    data,
  })
}
//删除
export const delQA = (data) => {
  return request('/api/control/qa/v1/delete', {
    method: 'POST',
    data,
  })
}
//导入
export const uploadFile = (data) => {
  return request('/api/control/qa/v1/file', {
    method: 'POST',
    data,
  })
}
//下载
export const downLoadFile = (data) => {
  return request('/api/control/qa/v1/file', {
    method: 'GET',
    data,
  })
}

//获取机器级列表
export const devicesList = (data) => {
  return request('/api/control/qa/devices_list', {
    method: 'GET',
    data,
  })
}

//获取机器级问答
export const devices = (data) => {
  return request('/api/control/qa/devices', {
    method: 'GET',
    data,
  })
}

//添加机器级问答
export const addDevices = (data) => {
  return request('/api/control/qa/devices', {
    method: 'POST',
    data,
  })
}

//编辑机器级问答
export const deviceEdit = (data) => {
  return request('/api/control/qa/device_edit', {
    method: 'POST',
    data,
  })
}

//移动复制机器级问答
export const changeDevices = (data) => {
  return request('/api/control/qa/change_devices', {
    method: 'POST',
    data,
  })
}
//导出模版

export const getDeviceFile = (data) => {
  return request('/api/control/qa/device_file', {
    method: 'GET',
    data,
  })
}
//导入机器级问答
export const uploadDeviceFile = (data) => {
  return request('/api/control/qa/device_file', {
    method: 'POST',
    data,
  })
}

//获取关键词问答
export const getKeywordsList = (data) => {
  return request('/api/control/keywords', {
    method: 'GET',
    data,
  })
}
//编辑关键词问答
export const keywordsEdit = (data) => {
  return request('/api/control/edit_qa_keywords', {
    method: 'POST',
    data,
  })
}
//添加关键词问答
export const keywordsAdd = (data) => {
  return request('/api/control/keywords', {
    method: 'POST',
    data,
  })
}
//删除
export const keywordsDel = (data) => {
  return request('/api/control/del_qa_keywords', {
    method: 'POST',
    data,
  })
}
//下载关键词模版
export const downLoadKeywordsFile = (data) => {
  return request('/media/keywords_qa/关键词问答批量导入模板.xlsx', {
    method: 'GET',
    data,
  })
}
//导入
export const uploadKeywordsFile = (data) => {
  return request('/api/control/keywords_qa_in', {
    method: 'POST',
    data,
  })
}

//获取必配库列表

export const qaBasic = (data) => (dispatch) => {
  return request('/api/control/qa/basic', {
    method: 'GET',
    data,
  }).then((res) => {
    const data = res.data.map((el) => {
      el.query = '["' + el.query + '"]'
      el.placeholder = el.answer
      el.media_info = '[]'
      el.device_id = '[]'
      el.uuid = el.id
      el.group_id = 'BASIC'
      el.group_name = '高频必配问答库'
      return el
    })
    dispatch({
      type: SET_BASIC,
      basic: data,
    })

    const insdata = res.enlighten_qa.map((el) => {
      el.query = '["' + el.query + '"]'
      el.placeholder = el.answer
      el.media_info = '[]'
      el.device_id = '[]'
      el.uuid = el.id
      el.group_id = 'INSPIRE'
      el.group_name = '高频必配问答库'
      return el
    })
    dispatch({
      type: SET_INSPIRE,
      inspire: insdata,
    })
  })
}

//获得qa 启发问答

export const qaInspire = (data) => (dispatch) => {
  return request('/api/control/qa/basic', {
    method: 'GET',
    data,
  }).then((res) => {
    const data = res.data.map((el) => {
      el.query = '["' + el.query + '"]'
      el.placeholder = el.answer
      el.media_info = '[]'
      el.device_id = '[]'
      el.uuid = el.id
      el.group_id = 'INSPIRE'
      el.group_name = '高频必配问答库'
      return el
    })
    dispatch({
      type: SET_INSPIRE,
      inspire: data,
    })
  })
}

//获取QAAPI
export const qaapiList = (data) => {
  return request('/api/control/qa_api/search', {
    method: 'GET',
    data,
  })
}
//添加QAAPI
export const qaapiAdd = (data) => {
  return request('/api/control/qa_api/add', {
    method: 'POST',
    data,
  })
}
//编辑QAAPI
export const qaapiEdit = (data) => {
  return request('/api/control/qa_api/edit', {
    method: 'POST',
    data,
  })
}
//删除QAAPI
export const qaapiDel = (data) => {
  return request('/api/control/qa_api/v1/delete', {
    method: 'POST',
    data,
  })
}
//下载QAAPI模版
export const downLoadQaapiFile = (data) => {
  return request('/media/qaapi/问答批量导入模板.xlsx', {
    method: 'GET',
    data,
  })
}
//导入QAAPI
export const uploadQaapiFile = (data) => {
  return request('/api/control/qa_api_in', {
    method: 'POST',
    data,
  })
}

export const downLoadKWExcel = (data) => {
  return request('/api/control/keywords/template', {
    method: 'GET',
    data,
  })
}
