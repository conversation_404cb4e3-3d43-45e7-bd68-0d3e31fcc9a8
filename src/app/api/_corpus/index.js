// import {
//     // INIT_LIST
// } from '@redux/action-types'

import {} from '@redux/action-types'
// import getStore from '@utils/store'
import request from '@utils/request'
/**
 * Redux action: 示例
 * @param {String} arg1
 */
// export const sample = (arg1) => dispatch => dispatch({
//     type: INIT_ROBOTS,
//     data: arg1
// })

//获取欢迎语列表
export const getList = (data) => {
  return request('/api/control/module_config_list', {
    method: 'POST',
    data: data,
  })
}

//保存并发布
export const saveAndPublish = (data) => {
  console.log('api save_and_publish_module2')
  return request('/api/control/save_and_publish_module', {
    method: 'POST',
    data: data,
  })
}
//获取详细信息
export const getDetail = (data) => {
  console.log('详情- 3')
  return request('/api/control/module_config_detail', {
    method: 'POST',
    data,
  })
}
//列表发布
export const publish = (data) => {
  return request('/api/control/direct_publish_module', {
    method: 'POST',
    data: data,
  })
}
//删除
export const changeItemStatus = (data) => {
  return request('/api/control/set_module_pkg_status', {
    method: 'POST',
    data: data,
  })
}

//转tts
export const textToTts = (data) => {
  return request('/api/control/upload_config_texts', {
    method: 'POST',
    data: data,
  })
}

//查询人脸
export const findByImage = (data) => {
  return request('/api/user/find_by_image', {
    method: 'POST',
    data: data,
  })
}
//添加贵宾
export const vipVisitor = (data) => {
  return request('/api/user/vip_visitor', {
    method: 'POST',
    data: data,
  })
}
//获取VIP人脸以及姓名
export const selectVip = (data) => {
  return request('/capi/v1/corp/vip_person_list', {
    method: 'GET',
    data: data,
  })
}
