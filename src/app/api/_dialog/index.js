import request from '@utils/request'

//liebiao -
export const getKeyWordsList = (data) => {
  return request('/api/control/keyword_recommend_list', {
    method: 'GET',
    data,
  })
}

//保存关键词 -
export const saveKeyword = (data) => {
  return request('/api/control/keyword_recommend_add', {
    method: 'POST',
    data,
  })
}
//获取关键词详情 -
export const getKeywordDetail = (data) => {
  return request('/api/control/keyword_recommend_detail', {
    method: 'GET',
    data,
  })
}
//编辑关键词 -
export const editKeyword = (data) => {
  return request('/api/control/keyword_recommend_modify', {
    method: 'POST',
    data,
  })
}

//删除关键词 -
export const delKeyword = (data) => {
  return request('/api/control/keyword_recommend_delete', {
    method: 'POST',
    data,
  })
}
//开关状态
export const switchStatus = (data) => {
  return request('/api/control/keyword_recommend_status_modify', {
    method: 'POST',
    data,
  })
}
//获取单条播报次数
export const recommendCount = (data) => {
  return request('/api/control/recommend_count_list', {
    method: 'GET',
    data,
  })
}

//无条件
export const getUnconditionalList = (data) => {
  return request('/api/control/unconditional_recommend_list', {
    method: 'GET',
    data,
  })
}
//添加无条件推荐
export const addUnconditional = (data) => {
  return request('/api/control/unconditional_recommend_add', {
    method: 'POST',
    data,
  })
}
//编辑无条件推荐
export const editUnconditional = (data) => {
  return request('/api/control/unconditional_recommend_modify', {
    method: 'POST',
    data,
  })
}
//删除无条件
export const delUnconditional = (data) => {
  return request('/api/control/unconditional_recommend_delete', {
    method: 'POST',
    data,
  })
}

//条件
export const setUnconditional = (data) => {
  return request('/api/control/unconditional_recommend_setting_modify', {
    method: 'POST',
    data,
  })
}
//条件
export const getUnconditional = (data) => {
  return request('/api/control/unconditional_recommend_setting', {
    method: 'GET',
    data,
  })
}
