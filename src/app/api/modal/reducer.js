import {
  MODAL_OPEN,
  MODAL_CLOSE,
  MODAL_CLOSE_TOP,
  MODAL_CLOSE_ALL,
  // MODAL_UPDATE
} from '@redux/action-types'

const defaults = {
  modals: [],
  // closeOnMask: false,
}

export default (state = defaults, action) => {
  const removed = (id) => {
    let index
    state.modals.some((modal, i) => {
      if (modal[0] === id) index = i
      return typeof index !== 'undefined'
    })
    if (typeof index === 'undefined') return state.modals
    const modals = [...state.modals]
    modals.splice(index, 1)
    return modals
  }

  switch (action.type) {
    case MODAL_OPEN: {
      const { id = Date.now(), component } = action
      if (!component) return state
      return Object.assign({}, state, {
        modals: [[id, component]].concat(state.modals),
      })
    }

    case MODAL_CLOSE: {
      if (typeof action.id === 'undefined') return state
      return Object.assign({}, state, {
        modals: removed(action.id),
      })
    }

    case MODAL_CLOSE_TOP: {
      if (!state.modals.length) return state
      return Object.assign({}, state, {
        modals: removed(state.modals[0][0]),
      })
    }

    case MODAL_CLOSE_ALL: {
      return Object.assign({}, state, {
        modals: [],
      })
    }

    // case MODAL_UPDATE: {
    //     const {
    //         type, ...args
    //     } = action
    //     return Object.assign({}, state, args)
    // }
  }

  return state
}
