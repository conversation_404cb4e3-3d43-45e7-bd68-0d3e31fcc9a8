import React from 'react'
import {
  MODAL_OPEN,
  MODAL_CLOSE,
  MODAL_CLOSE_TOP,
  MODAL_CLOSE_ALL,
  // MODAL_UPDATE,
} from '@redux/action-types'

export const open = (id, component) => {
  if (React.isValidElement(id)) {
    component = id
    id = component.props && component.props.id ? component.props.id : Date.now()
  }
  return (dispatch) =>
    dispatch({
      type: MODAL_OPEN,
      id,
      component,
    })
}

export const close = (id) => (dispatch) =>
  dispatch({
    type: MODAL_CLOSE,
    id,
  })

export const closeTop = () => (dispatch) =>
  dispatch({
    type: MODAL_CLOSE_TOP,
  })

export const closeAll = () => (dispatch) =>
  dispatch({
    type: MODAL_CLOSE_ALL,
  })
