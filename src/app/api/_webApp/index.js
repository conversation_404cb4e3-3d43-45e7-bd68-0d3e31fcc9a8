import request from '@utils/request'
import { GET_REPLIES_LIST, GET_REPLIES_EMPTYTIPS, GET_CONFIGURATION, GET_GROUP_LIST } from '@redux/action-types'

export const getRepliesEmptytips = (data) => (dispatch) =>
  dispatch({
    type: GET_REPLIES_EMPTYTIPS,
    data,
  })

//获取自定义场景文件名
export const getConfiguration = (robotId) => (dispatch) => {
  request('/api/user/meui_configuration', {
    method: 'GET',
    data: {
      robot_id: robotId,
    },
  })
    .then((res) => {
      let filename = res.file_name ? decodeURI(res.file_name) : ''
      dispatch({
        type: GET_CONFIGURATION,
        data: {
          fileName: filename,
        },
      })
    })
    .catch(() => {
      dispatch({
        type: GET_CONFIGURATION,
        data: {
          fileName: '',
        },
      })
    })
}

//new new
//上传图片
export const uploadImageInfo = (data) => {
  return request('/api/control/qa_image', {
    method: 'POST',
    data,
  })
}

//上传问答视频
export const uploadVideoInfo = (data) => {
  return request('/api/control/qa_video', {
    method: 'POST',
    data,
  })
}
//上传问答视频封面
export const uploadVideoCover = (data) => {
  return request('/api/control/qa_videocover', {
    method: 'POST',
    data,
  })
}
// 问答批量上传
export const uploadQAFile = (data) => {
  return request('/api/control/qa_file', {
    method: 'POST',
    data,
  })
}
//下载批量模版
export const downLoadQAFile = () => {
  return request('/api/control/qa_file', {
    method: 'GET',
  })
}

//获取企业问答
export const getRepliesList = (data) => {
  return request('/api/control/qa', {
    method: 'GET',
    data,
  })
}
//添加企业问答/编辑企业问答  (编辑)
export const uploadQa = (data) => {
  // return request('/api/control/qa', {
  return request('/api/control/web_app_modify', {
    method: 'POST',
    data,
  })
}

//删除一列
export const deleteQA = (data) => {
  return request('/api/control/qa_delete', {
    method: 'POST',
    data,
  })
}

//获取tag列表
export const getTagLists = (data) => {
  return request('/api/control/corp_qa_tags', {
    method: 'GET',
    data,
  })
}
//增加tag
export const addTag = (data) => {
  return request('/api/control/corp_qa_tags', {
    method: 'POST',
    data,
  })
}
//删除tag
export const deleteTag = (data) => {
  return request('/api/control/delete_corp_qa_tag', {
    method: 'POST',
    data,
  })
}

//问答改版
//获取企业问答 （问答列表）
export const getQaList = (data) => {
  // return request('/api/control/qa/search', {
  return request('/api/control/web_app_list', {
    method: 'GET',
    data,
  })
}

//分类列表
export const groupsSearch = (data) => (dispatch) => {
  return request('/api/control/qa/groups/search', {
    method: 'POST',
    data,
  }).then((res) => {
    dispatch({
      type: GET_GROUP_LIST,
      groups: res,
    })
    return res
  })
}

export const groupsAdd = (data) => {
  return request('/api/control/qa/groups/add', {
    method: 'POST',
    data,
  })
}
export const groupsEditName = (data) => {
  return request('/api/control/qa/groups/edit_name', {
    method: 'POST',
    data,
  })
}
export const groupsDel = (data) => {
  return request('/api/control/qa/groups/delete', {
    method: 'POST',
    data,
  })
}
export const editThreshold = (data) => {
  return request('/api/control/qa/groups/edit_threshold', {
    method: 'POST',
    data,
  })
}

export const chgroup = (data) => {
  return request('/api/control/qa/chgroup', {
    method: 'POST',
    data,
  })
}

//添加企业问答/编辑企业问答 (添加轻应用)
export const qaAdd = (data) => {
  // return request('/api/control/qa/add', {
  return request('/api/control/web_app_add', {
    method: 'POST',
    data,
  })
}
//删除
export const delQA = (data) => {
  // return request('/api/control/qa/v1/delete', {
  return request('/api/control/web_app_delete', {
    method: 'POST',
    data,
  })
}
//导入
export const uploadFile = (data) => {
  return request('/api/control/qa/v1/file', {
    method: 'POST',
    data,
  })
}
//下载
export const downLoadFile = () => {
  return request('/api/control/qa/v1/file', {
    method: 'GET',
  })
}

//验证appurl
export const verifyAppUrl = (data) => {
  return request('/api/control/web_app_check_url', {
    method: 'POST',
    data: data,
  })
}

//获取轻应用列表(顶部)
export const getAppList = (data) => {
  return request('/api/control/web_app_template_list', {
    method: 'GET',
    data: data,
  })
}
