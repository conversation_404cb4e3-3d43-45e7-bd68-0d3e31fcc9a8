import request from '@utils/request'
import { UPDATE_LINE_INTRO, EDIT_THANKS_WORD } from '@redux/action-types'

/**************************************
 * Redux actions
 *************************************/

// TODO: promise login
// export const login = (data) => dispatch => dispatch({
//     type: USER_LOGIN,
//     data,
// })
//素材库列表
export const expostorRoute = (data) => {
  // return request('/api/control/module_config_list', {
  return request('/api/control/v2/module_config_list', {
    method: 'get',
    data,
  })
}
//复制
export const copyExpostorRoute = (data) => {
  return request('/api/control/clone_module_config', {
    method: 'post',
    data,
  })
}
//发布
export const directPublishModule = (data) => {
  return request('/api/control/direct_publish_module', {
    method: 'post',
    data,
  })
}

//保存并发布
export const saveAndPublishModule = (data) => {
  console.log('api save_and_publish_module3')
  return request('/api/control/save_and_publish_module', {
    method: 'post',
    data,
  })
}

//获取机器人列表
export const getModuleConfigRobotList = (data) => {
  return request('/api/control/module_config_robot_list', {
    method: 'post',
    data,
  })
}
//获取机器人上的线路图
export const getModulePkgStatus = (data) => {
  return request('/api/control/get_module_pkg_status', {
    method: 'post',
    data,
  })
}

export const publishModule = (data) => {
  return request('/api/control/publish_module', {
    method: 'post',
    data,
  })
}

export const setModule = (data) => {
  return request('/api/control/set_config_status', {
    method: 'post',
    data,
  })
}

export const deleteModule = (data) => {
  return request('/api/control/set_config_status', {
    method: 'post',
    data,
  })
}

//删除素材(已发布的)
export const setPkgModule = (data) => {
  return request('/api/control/set_module_pkg_status', {
    method: 'post',
    data,
  })
}

//素材在机器人上的排序(已发布的)
export const modifyRobotConfigSequence = (data) => {
  return request('/api/control/modify_robot_config_sequence', {
    method: 'post',
    data,
  })
}

//搜索地图
export const searchMap = (data) => {
  //robot/map_site_name/list
  // return request('/api/control/robot/map_site_name/list', {
  return request('/api/control/corp_map_pkg_list', {
    method: 'get',
    data,
  })
}
//地图详情
export const mapDetail = (data) => {
  return request('/api/control/map_pkg_info', {
    method: 'get',
    data,
  })
}

//保存线路简介
export const updataLineIntro = (data) => (dispatch) => {
  dispatch({
    type: UPDATE_LINE_INTRO,
    data,
  })
  return Promise.resolve()
}

//保存感谢回复
export const updataThanks = (data) => (dispatch) => {
  dispatch({
    type: EDIT_THANKS_WORD,
    data,
  })
  return Promise.resolve()
}

//批量转tts
export const textToTTS = (data) => {
  return request('/api/control/upload_config_texts', {
    method: 'post',
    data,
  })
}

//上传配置文件里的资源
export const uploadResource = (data) => {
  return request('/api/control/upload_config_resource', {
    method: 'post',
    data,
  })
}

//获取讲解员json配置
export const getExpostorConfig = (data) => {
  return request('/api/control/module_config_obj_detail', {
    method: 'get',
    data: data,
  })
}

//保存讲解员json配置
export const saveExpostorConfig = (data) => {
  return request('/api/control/save_module_config', {
    method: 'post',
    data: data,
  })
}

//更新保存讲解员json配置
export const updateExpostorConfig = (data) => {
  return request('/api/control/update_save_module_config', {
    method: 'post',
    data: data,
  })
}
