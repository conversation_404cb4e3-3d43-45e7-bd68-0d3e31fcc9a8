import {
  GET_EXPOSTOR_OBJ,
  CLEAR_EXPOSTOR,
  UPDATA_DURATION,
  POINT_ADD,
  POINT_MODIFY,
  POINT_BATCH_ADD,
  POINT_MOVE,
  POINT_DEL,
  UPDATE_LINE_INTRO,
  EDIT_MAP_EXPLANATION,
  EDIT_THANKS_WORD,
  EDIT_RETURN_POINT,
  UPDATE_FREE_QUIZ,
  EDIT_POINTS_ENABLED,
  EDIT_ENDPOINT_ENABLED,
  UPDATE_TRANSFORM_WORD,
  UPDATE_TRANSFORM_WORD421,
  COPY_TRANSFORM_WORD421,
  UPDATE_PROLOGUE,
  EXCHANGE_MAP_EXPLANATION,
  DELETE_MAP_EXPLANATION,
  UPDATE_BROADCAST_CONTENT,
  UPDATE_CROSS_TALK,
  UPDATE_BC_TTS,
  UPDATE_CT_TTS,
  UPDATA_ROUTE_CONFIG,
} from '@redux/action-types'
import { initPoint } from './guideConfig'

const initialState = {}

export default function (state = initialState, { type, ...obj }) {
  switch (type) {
    case CLEAR_EXPOSTOR: {
      return Object.assign({}, obj.data)
    }
    case GET_EXPOSTOR_OBJ: {
      return Object.assign({}, state, obj.data)
    }
    case UPDATA_DURATION: {
      return Object.assign({}, state, { duration: obj.data.duration })
    }
    case POINT_ADD: {
      let newAddPoint = JSON.parse(JSON.stringify(initPoint))
      newAddPoint.point_name = obj.data.point_name
      let newPointList = []
      state.tour_config.points.map((item, i) => {
        newPointList.push(item)
      })
      let pointPosAdd = obj.data.pos_add != -1 ? obj.data.pos_add : state.tour_config.points.length
      newPointList.splice(pointPosAdd, 0, newAddPoint)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: newPointList }),
      })
    }
    case POINT_MODIFY: {
      let oldPoint = state.tour_config.points[obj.data.pos_modify]
      oldPoint.point_name = obj.data.point_name
      let newPointList = state.tour_config.points
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: newPointList }),
      })
    }
    case POINT_BATCH_ADD: {
      let newPointList = []
      if (state.tour_config.points.length > obj.data.points.length) {
        //切换地图少位置点的覆盖多位置点
        let remainderPoint = state.tour_config.points.splice(0, obj.data.points.length)
        remainderPoint.map((item, i) => {
          item.point_name = obj.data.points[i].point_name
          newPointList.push(item)
        })
      } else {
        //切换地图多位置点的覆盖少位置点
        let remainderPoint = []
        state.tour_config.points.map((item, i) => {
          item.point_name = obj.data.points[i].point_name
          remainderPoint.push(item)
        })
        obj.data.points.splice(0, state.tour_config.points.length)
        newPointList = remainderPoint.concat(obj.data.points)
      }
      return Object.assign({}, state, {
        // tour_config: Object.assign({}, state.tour_config, { points: obj.data.points })
        tour_config: Object.assign({}, state.tour_config, { points: newPointList }),
      })
    }
    case POINT_MOVE: {
      //地点上下移动
      let newPointList = [],
        moveItem = null
      state.tour_config.points.map((item, i) => {
        if (obj.data.pointIdx != i) {
          newPointList.push(item)
        } else {
          moveItem = item
        }
      })
      let newPointIdx = obj.data.direction == 'up' ? obj.data.pointIdx - 1 : obj.data.pointIdx + 1
      newPointList.splice(newPointIdx, 0, moveItem)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: newPointList }),
      })
    }
    case POINT_DEL: {
      //删除地点
      let newPointList = []
      state.tour_config.points.map((item, i) => {
        if (obj.data.pointIdx != i) {
          newPointList.push(item)
        }
      })
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: newPointList }),
      })
    }
    case UPDATE_LINE_INTRO: {
      let newLineIntro = obj.data
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, {
          start_point: Object.assign({}, state.tour_config.start_point, { line_intro: newLineIntro }),
        }),
      })
    }
    case EDIT_MAP_EXPLANATION: {
      // let newMapExplanation = state.tour_config.start_point.map_explanation;
      // newMapExplanation["map_id"] = obj.data.map_id;
      // newMapExplanation["map_name"] = obj.data.map_name;
      // newMapExplanation["point_list"] = obj.data.point_list;
      // newMapExplanation["finish"] = obj.data.finish;
      let newMapExplanation = Object.assign({}, state.tour_config.start_point.map_explanation, obj.data)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, {
          start_point: Object.assign({}, state.tour_config.start_point, { map_explanation: newMapExplanation }),
        }),
      })
    }
    case EDIT_THANKS_WORD: {
      let newThanksWord = obj.data.content
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, {
          end_point: Object.assign({}, state.tour_config.end_point, { thanks_word: newThanksWord }),
        }),
      })
    }
    case EDIT_RETURN_POINT: {
      let newReturnPoint = state.tour_config.end_point.return_point
      newReturnPoint['point_name'] = obj.data.point_name
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, {
          end_point: Object.assign({}, state.tour_config.end_point, { return_point: newReturnPoint }),
        }),
      })
    }
    case UPDATE_FREE_QUIZ: {
      let newData = state.tour_config.points[obj.data.index]
      newData.free_ask.question = obj.data.res
      newData.free_ask.finish = obj.data.finish
      state.tour_config.points[obj.data.index] = Object.assign({}, state.tour_config.points[obj.data.index], newData)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }

    case UPDATE_TRANSFORM_WORD: {
      let newData = state.tour_config.points[obj.data.index]
      newData.transition_word.wait_time = obj.data.wait_time
      // newData.transition_word.content[0] = Object.assign({},{action: obj.data.content.action,tts_text:obj.data.content.tts_text,tts_url:obj.data.content.tts_url,tts_track:obj.data.content.tts_track});
      newData.transition_word.content[0] = obj.data.content
      newData.transition_word.finish = obj.data.finish
      state.tour_config.points[obj.data.index] = Object.assign({}, state.tour_config.points[obj.data.index], newData)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }
    case UPDATE_TRANSFORM_WORD421: {
      let newData = state.tour_config.points[obj.data.index]
      if (newData.transition_word421 == undefined) {
        newData.transition_word421 = Object.assign({}, newData.transition_word)
      }
      newData.transition_word421.wait_time = obj.data.wait_time
      newData.transition_word421.content[0] = Object.assign(
        {},
        {
          action: obj.data.content.action,
          tts_text: obj.data.content.tts_text,
          tts_url: obj.data.content.tts_url,
          tts_track: obj.data.content.tts_track,
        },
      )
      newData.transition_word.finish = obj.data.finish
      state.tour_config.points[obj.data.index] = Object.assign({}, state.tour_config.points[obj.data.index], newData)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }
    case COPY_TRANSFORM_WORD421: {
      let newData = state.tour_config.points[obj.data.index]

      newData.transition_word = JSON.parse(JSON.stringify(newData.transition_word421))
      // debugger;
      newData.transition_word.wait_time = newData.transition_word421.wait_time
      newData.transition_word421.content[0] = Object.assign(
        {},
        {
          action: newData.transition_word421.content[0].action,
          tts_text: newData.transition_word421.content[0].tts_text,
          tts_url: newData.transition_word421.content[0].tts_url,
          tts_track: newData.transition_word421.content[0].tts_track,
        },
      )
      newData.transition_word.finish = newData.transition_word421.finish
      state.tour_config.points[obj.data.index] = Object.assign({}, state.tour_config.points[obj.data.index], newData)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }
    case UPDATE_PROLOGUE: {
      let newData = state.tour_config.start_point
      newData.prologue[0] = Object.assign({}, obj.data.content, { finish: obj.data.finish })
      state.tour_config.start_point = Object.assign({}, state.tour_config.start_point, newData)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { start_point: state.tour_config.start_point }),
      })
    }
    case EDIT_POINTS_ENABLED: {
      let edit_point = state.tour_config.points[obj.data.idx]
      edit_point[obj.data.type].enabled = obj.data.checked
      state.tour_config.points.splice(obj.data.idx, 1, edit_point)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }
    case EDIT_ENDPOINT_ENABLED: {
      console.log(obj.data)
      let newCrossTalk = state.tour_config.end_point.cross_talk
      newCrossTalk['enabled'] = obj.data.checked
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, {
          end_point: Object.assign({}, state.tour_config.end_point, { cross_talk: newCrossTalk }),
        }),
      })
    }
    case EXCHANGE_MAP_EXPLANATION: {
      console.log('获取的拖动前后位置', obj.data.dragBefore, '=>', obj.data.dragAfter)
      let newPointList = []
      let moveItem = null
      state.tour_config.start_point.map_explanation.point_list.map((el, index) => {
        if (index != obj.data.dragBefore) {
          newPointList.push(el)
        } else {
          moveItem = el
        }
      })
      newPointList.splice(obj.data.dragAfter, 0, moveItem)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, {
          start_point: Object.assign({}, state.tour_config.start_point, {
            map_explanation: Object.assign({}, state.tour_config.start_point.map_explanation, {
              point_list: newPointList,
            }),
          }),
        }),
      })
    }
    case DELETE_MAP_EXPLANATION: {
      let newPointList = state.tour_config.start_point.map_explanation.point_list
      newPointList.splice(obj.data.idx, 1)
      console.log(newPointList)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, {
          start_point: Object.assign({}, state.tour_config.start_point, {
            map_explanation: Object.assign({}, state.tour_config.start_point.map_explanation, {
              point_list: newPointList,
            }),
          }),
        }),
      })
    }
    // UPDATE_BROADCAST_CONTENT
    case UPDATE_BROADCAST_CONTENT: {
      let newData = state.tour_config.points[obj.data.index]
      newData.broadcast = obj.data.content
      state.tour_config.points[obj.data.index] = Object.assign({}, state.tour_config.points[obj.data.index], newData)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }
    // UPDATE_CROSS_TALK
    case UPDATE_CROSS_TALK: {
      let newData = state.tour_config.points[obj.data.index]
      newData.cross_talk = obj.data.content
      state.tour_config.points[obj.data.index] = Object.assign({}, state.tour_config.points[obj.data.index], newData)
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }
    case UPDATE_BC_TTS: {
      let newData = state.tour_config.points[obj.data.index].broadcast[obj.data.tabKey - 1]

      newData.text[0] = Object.assign({}, obj.data.content.text)
      state.tour_config.points[obj.data.index].broadcast[obj.data.tabKey - 1] = Object.assign(
        {},
        state.tour_config.points[obj.data.index].broadcast[obj.data.tabKey - 1],
        newData,
      )
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }
    case UPDATE_CT_TTS: {
      let newData = state.tour_config.points[obj.data.index].cross_talk.broadcast[obj.data.tabKey - 1]
      newData.text[0] = Object.assign({}, obj.data.content.text)
      state.tour_config.points[obj.data.index].cross_talk.broadcast[obj.data.tabKey - 1] = Object.assign(
        {},
        state.tour_config.points[obj.data.index].cross_talk.broadcast[obj.data.tabKey - 1],
        newData,
      )
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, { points: state.tour_config.points }),
      })
    }
    case UPDATA_ROUTE_CONFIG: {
      let newRouteConfig = obj.data
      return Object.assign({}, state, {
        tour_config: Object.assign({}, state.tour_config, {
          start_point: Object.assign({}, state.tour_config.start_point, { route_config: newRouteConfig }),
        }),
      })
    }
  }
  return state
}
