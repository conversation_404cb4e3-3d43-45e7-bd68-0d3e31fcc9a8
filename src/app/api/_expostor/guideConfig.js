const guideConfig = {
  tour_id: '10001',
  duration: 6, //单位分钟
  timestamp: 0,
  version: '4.9',
  tour_config: {
    start_point: {
      //起始点
      line_intro: {
        //线路简介
        name: '',
        similar_name: [],
        is_hot: true,
        content: '',
        cover: '',
        bgm: '',
      },
      map_explanation: {
        //地图讲解点
        map_id: '',
        map_name: '',
        point_list: [],
      },
      prologue: [],
    },
    points: [
      {
        //中间位置点
        point_name: '',
        cross_talk: {
          //途中串词
          enabled: false,
          broadcast: [],
        },
        broadcast: [],
        free_ask: {
          //自由问答
          enabled: true,
          question: [],
        },
        transition_word: {
          //过渡语
          enabled: true,
          wait_time: 0, //单位分钟
          content: [],
        },
        // transition_word421: { //过渡语
        //     enabled: true,
        //     wait_time: 0,  //单位分钟
        //     content: []
        // }
      },
    ],
    end_point: {
      //结束点
      thanks_word: [
        {
          tts_text: __('navigationIntroduction.gladToHelp'),
          tts_url: '',
          tts_track: '',
        },
        {
          tts_text: __('navigationIntroduction.continueService'),
          tts_url: '',
          tts_track: '',
        },
        {
          tts_text: __('navigationIntroduction.seeYouNextTime'),
          tts_url: '',
          tts_track: '',
        },
        {
          tts_text: '谢谢，为您服务是我的荣幸',
          tts_url: '',
          tts_track: '',
        },
      ], //感谢回复
      return_point: {
        //返回位置点
        point_name: '',
        tts_text: '',
        tts_url: '',
        tts_track: '',
      },
    },
  },
  sound: {
    speed: 6,
    soundType: 'woman',
    soundVal: __('navigationIntroduction.female'),
  },
}

const initPoint = {
  //中间位置点
  point_name: '',
  cross_talk: {
    //途中串词
    enabled: false,
    broadcast: [],
  },
  broadcast: [],
  free_ask: {
    //自由问答
    enabled: true,
    question: [],
    finish: 'new_add',
  },
  transition_word: {
    //过渡语
    enabled: true,
    wait_time: 0,
    content: [],
    finish: 'new_add',
  },
}

export { initPoint }
export default guideConfig
