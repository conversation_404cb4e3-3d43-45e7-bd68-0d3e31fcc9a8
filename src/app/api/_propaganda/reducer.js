import { GET_PROPAGANDA_OBJ } from '@redux/action-types'

const initialState = {
  propaganda: {
    mTaskName: '',
    propaganda: [
      {
        res_mode: '', //1、图片加tts 2、图片加语音 3、视频
        title: '',
        voice_url: '',
        image_url: '',
        video_url: '',
        img_name: '',
        voice_name: '',
        video_name: '',
        voice_str: '',
      },
    ],
  },
  initPropaganda: {
    mTaskName: null,
    propaganda: [
      {
        res_mode: 1, //1、图片加tts 2、图片加语音 3、视频
        title: '',
        voice_url: '',
        image_url: '',
        video_url: '',
        img_name: '',
        voice_name: '',
        video_name: '',
        voice_str: '',
      },
    ],
  },
  robotidArray: [],
}

export default function (state = initialState, { type, ...obj }) {
  switch (type) {
    /*
        case SAMPLE: {
            return Object.assign({}, state, data)
        }
        */
    case GET_PROPAGANDA_OBJ: {
      return Object.assign({}, state, obj.data)
    }
  }

  return state
}
