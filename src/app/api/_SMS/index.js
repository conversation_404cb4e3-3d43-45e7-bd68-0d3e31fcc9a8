import request from '@utils/request'
//获取信息模板
export const getSMStemplate = (data) => {
  return request('/api/control/corp_sms_template', {
    method: 'get',
    data,
  })
}
//保存信息
export const saveSMStemplate = (data) => {
  return request('/api/control/corp_sms_template', {
    method: 'post',
    data,
  })
}

// 获取企业地点
// export const getCompanyAddress=()=>{
//     return request('/api/control/corp_sites',{
//         method:'get'
//     })
// }
export const getCompanyAddress = () => {
  return request('/api/user/visitor_base_info', {
    method: 'get',
  })
}

// 获取excel模板下载的url
export const downLoadSMSExcel = () => {
  return request('/api/control/visit_batch', {
    method: 'get',
  })
}

// 上传excel表格 uploadSMSFile
export const uploadSMSFile = (data) => {
  return request('/api/control/visit_batch_file_check', {
    method: 'post',
    data,
  })
}

// 个人发短信
export const singleSMS = (data) => {
  return request('/api/user/visitor', {
    method: 'post',
    data,
  })
}

// 团队
export const teamSMS = (data) => {
  return request('/api/user/visit_group', {
    method: 'post',
    data,
  })
}

// 个人-批量
export const singleExcelSMS = (data) => {
  return request('/api/control/visit_batch', {
    method: 'post',
    data,
  })
}
