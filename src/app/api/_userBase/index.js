import {} from '@redux/action-types'
import request from '@utils/request'
/**
 * Redux action: 示例
 * @param {String} arg1
 */
// export const sample = (arg1) => dispatch => dispatch({
//     type: INIT_ROBOTS,
//     data: arg1
// })

//获取用户列表
export const getPersonList = (data) => {
  return request('/capi/v1/corp/person_list', {
    method: 'GET',
    data: data,
  })
}

//上传照片
export const uploadImg = (data) => {
  return request('/capi/v1/corp/face_upload', {
    method: 'POST',
    data: data,
  })
}

//第一张照片检查是否有此人照片
export const searchPersonFace = (data) => {
  return request('/capi/v1/corp/search_person_by_face', {
    method: 'POST',
    data: data,
  })
}

//判断是否为同一人

export const faceSimilar = (data) => {
  return request('/capi/v1/corp/person_face_similar', {
    method: 'POST',
    data: data,
  })
}
//产看人员详情
export const personProfile = (data) => {
  return request('/capi/v1/corp/person_profile', {
    method: 'GET',
    data: data,
  })
}
//添加人员
export const addPerson = (data) => {
  return request('/capi/v1/corp/add_person', {
    method: 'POST',
    data: data,
  })
}
//修改人员
export const modifyPerson = (data) => {
  return request('/capi/v1/corp/modify_person', {
    method: 'POST',
    data: data,
  })
}
//删除

export const deletePerson = (data) => {
  return request('/capi/v1/corp/delete_person', {
    method: 'POST',
    data: data,
  })
}

// 图片批量上传
// export const uploadImages = (data) => {
//     return request('/api/user/vip_batch_upload', {
//         method: 'POST',
//         data: data
//     })
// }

// 人脸检测
export const faceCheck = (data) => {
  return request('/api/user/find_by_image', {
    method: 'POST',
    data: data,
  })
}

// 企业添加vip

export const addVipVisitor = (data) => {
  return request('/api/user/vip_visitor', {
    method: 'POST',
    data: data,
  })
}
