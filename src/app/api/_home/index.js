import request from '@utils/request'

//获取robot_id
export const getModuleConfigList = (data) => {
  return request('/api/control/module_config_list', {
    method: 'post',
    data: data,
  })
}
//获取json配置
export const getModuleConfig = (data) => {
  return request('/api/control/module_config_detail', {
    method: 'post',
    data,
  })
}

//上传图片
export const uploadResource = (data) => {
  return request('/api/control/upload_config_resource', {
    method: 'post',
    data: data,
  })
}

//保存并发布模块
export const publishModule = (data) => {
  return request('/api/control/save_and_publish_module', {
    method: 'post',
    data: data,
  })
}

export const addAsrData = (data) => {
  return request('/api/control/asr_data/sync', {
    method: 'post',
    data: data,
  })
}

//未改动时保存并发布模块
export const directPublishModule = (data) => {
  return request('/api/control/direct_publish_module', {
    method: 'post',
    data: data,
  })
}

//获取同步进度
export const getModuleStatus = (data) => {
  return request('/api/control/get_module_pkg_status', {
    method: 'post',
    data: data,
  })
}

//恢复默认配置
export const recoverDefaultStatus = (data) => {
  return request('/api/control/set_module_pkg_status', {
    method: 'post',
    data: data,
  })
}

//获取轻应用推荐问法
export const getWebQuery = (data) => {
  return request('/api/control/web_app_list', {
    method: 'get',
    data: data,
  })
}

//判断query是否在问答库中
export const searchAnswer = (data) => {
  return request('/api/control/qa_search_answer', {
    method: 'get',
    data: data,
  })
}

//5.13添加企业问答/编辑企业问答
export const qaKewordAdd = (data) => {
  return request('/api/control/keyword_qa', {
    method: 'POST',
    data,
  })
}

// 自动生成翻译接口
export const translateJson = (data) => {
  const formData = new FormData();
  
  // 如果已经是 FormData 则直接使用
  if (data instanceof FormData) {
    return request('/capi/v1/corp/translate_json', {
      method: 'post',
      data,
    });
  }
  // 将对象转换为 FormData
  Object.keys(data).forEach(key => {
    formData.append(key, data[key]);
  });

  return request('/capi/v1/corp/translate_json', {
    method: 'post',
    data: formData,
  }).catch(err =>{
    console.log("translateJson 请求失败，错误信息:", err);
    throw err;
  });
};

// Redux action: 设置通用翻译文本
export const setCommonTranslations = (translations) => (dispatch) =>
  dispatch({
    type: 'SET_COMMON_TRANSLATIONS',
    data: { commonTranslations: translations }
  });