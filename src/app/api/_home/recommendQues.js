export const recommendQues = {
  funcConfig: [
    {
      key: 'interview',
      name: __('customHome.functionList.reception'),
      list_icon1: 'module_public/module_skill_home/List1_interview.png',
      list_icon2: 'module_public/module_skill_home/List2_interview.png',
      card_icon: 'module_public/module_skill_home/Card_interview.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_interview.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_interview.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'take_me_there',
      name: __('customHome.functionList.guide'),
      list_icon1: 'module_public/module_skill_home/List1_take_me_there.png',
      list_icon2: 'module_public/module_skill_home/List2_take_me_there.png',
      card_icon: 'module_public/module_skill_home/Card_take_me_there.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_take_me_there.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_take_me_there.png',
      buttons_icon: '/module_public/module_skill_home/button_dark_blue.png',
    },
    {
      key: 'cruise',
      name: __('customHome.functionList.patrol'),
      list_icon1: 'module_public/module_skill_home/List1_cruise.png',
      list_icon2: 'module_public/module_skill_home/List2_cruise.png',
      card_icon: 'module_public/module_skill_home/Card_cruise.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_cruise.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_cruise.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      list_icon2: 'module_public/module_skill_home/List2_chat1.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      buttons_icon: '/module_public/module_skill_home/button_green.png',
      label: 'chat1',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.chat'),
      list_icon1: 'module_public/module_skill_home/List1_chat2.png',
      list_icon2: 'module_public/module_skill_home/List2_chat2.png',
      card_icon: 'module_public/module_skill_home/Card_chat2.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat2.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat2.png',
      buttons_icon: '/module_public/module_skill_home/button_green.png',
      label: 'chat2',
    },
    {
      key: 'weather',
      name: __('customHome.functionList.weather'),
      list_icon1: 'module_public/module_skill_home/List1_weather.png',
      list_icon2: 'module_public/module_skill_home/List2_weather.png',
      card_icon: 'module_public/module_skill_home/Card_weather.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_weather.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_weather.png',
      buttons_icon: '/module_public/module_skill_home/button_purple.png',
    },
    {
      key: 'calendar',
      name: __('customHome.functionList.calendar'),
      list_icon1: 'module_public/module_skill_home/List1_calendar.png',
      list_icon2: 'module_public/module_skill_home/List2_calendar.png',
      card_icon: 'module_public/module_skill_home/Card_calendar.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_calendar.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_calendar.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'guide',
      name: __('customHome.functionList.visit'),
      list_icon1: 'module_public/module_skill_home/List1_guide.png',
      list_icon2: 'module_public/module_skill_home/List2_guide.png',
      card_icon: 'module_public/module_skill_home/Card_guide.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_guide.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_guide.png',
      buttons_icon: '/module_public/module_skill_home/button_purple.png',
    },
    {
      key: 'dance',
      name: __('customHome.functionList.dance'),
      list_icon1: 'module_public/module_skill_home/List1_dance.png',
      list_icon2: 'module_public/module_skill_home/List2_dance.png',
      card_icon: 'module_public/module_skill_home/Card_dance.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_dance.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_dance.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'group_photo',
      name: __('customHome.functionList.groupPhoto'),
      list_icon1: 'module_public/module_skill_home/List1_group_photo.png',
      list_icon2: 'module_public/module_skill_home/List2_group_photo.png',
      card_icon: 'module_public/module_skill_home/Card_group_photo.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_group_photo.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_group_photo.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'web',
      name: __('customHome.functionList.miniAppAdress'),
      list_icon1: 'module_public/module_skill_home/List1_web.png',
      list_icon2: 'module_public/module_skill_home/List2_web.png',
      card_icon: 'module_public/module_skill_home/Card_web.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_web.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_web.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'open_app',
      name: __('customHome.functionList.miniApp'),
      list_icon1: 'module_public/module_skill_home/List1_open_app.png',
      list_icon2: 'module_public/module_skill_home/List2_open_app.png',
      card_icon: 'module_public/module_skill_home/Card_open_app.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_open_app.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_open_app.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'cafe_distributefood',
      name: '小食分发', //__('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      list_icon2: 'module_public/module_skill_home/List2_chat1.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'cafe_welcome',
      name: '餐厅揽客', //__('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      list_icon2: 'module_public/module_skill_home/List2_chat1.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'cafe_meal',
      name: '送餐', //__('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      list_icon2: 'module_public/module_skill_home/List2_chat1.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },

    // {
    // 	key: 'customer_services',
    // 	name: '人工客服',
    // 	list_icon1:"module_public/module_skill_home/List1_customer_services.png",
    // 	list_icon2:"module_public/module_skill_home/List2_customer_services.png",
    // 	card_icon:"module_public/module_skill_home/Card_customer_services.png",
    // 	standard1_icon:"module_public/module_skill_home/Standard1_customer_services.png",
    // 	standard2_icon:"module_public/module_skill_home/Standard2_customer_services.png"
    // }
  ],
  funcConfigEng: [
    {
      key: 'guide',
      name: __('customHome.functionList.visit'),
      list_icon1: 'module_public/module_skill_home/List1_guide.png',
      list_icon2: 'module_public/module_skill_home/List2_guide.png',
      card_icon: 'module_public/module_skill_home/Card_guide.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_guide.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_guide.png',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      // list_icon2: "module_public/module_skill_home/List2_chat1.png",
      list_icon2: 'module_public/module_skill_home/visit_bg_img.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      label: 'chat1',
    },
    {
      key: 'take_me_there',
      name: __('customHome.functionList.guide'),
      list_icon1: 'module_public/module_skill_home/List1_take_me_there.png',
      // list_icon2: "module_public/module_skill_home/List2_take_me_there.png", // 需要替换得图片
      list_icon2: 'module_public/module_skill_home/visit_bg_img.png',
      card_icon: 'module_public/module_skill_home/Card_take_me_there.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_take_me_there.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_take_me_there.png',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.chat'),
      list_icon1: 'module_public/module_skill_home/List1_chat2.png',
      list_icon2: 'module_public/module_skill_home/List2_chat2.png',
      card_icon: 'module_public/module_skill_home/Card_chat2.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat2.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat2.png',
      label: 'chat2',
    },
  ],
  funcConfigArabic: [
    {
      key: 'chat',
      name: __('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      // list_icon2: "module_public/module_skill_home/List2_chat1.png",
      list_icon2: 'module_public/module_skill_home/visit_bg_img.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      label: 'chat1',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.chat'),
      list_icon1: 'module_public/module_skill_home/List1_chat2.png',
      list_icon2: 'module_public/module_skill_home/List2_chat2.png',
      card_icon: 'module_public/module_skill_home/Card_chat2.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat2.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat2.png',
      label: 'chat2',
    },
  ],
  recommendQues: {
    interview: ['我来找人', '我来面试', '我来登记', '我是访客', '我来拜访', '我来开会'],
    take_me_there: ['带我去'],
    guide: ['我要参观', '带我参观', '我想参观', '带我参观一下', '我想参观一下', '我要参观一下'],
    cruise: ['开始巡逻', '开始巡航', '结束巡逻', '结束巡航', '停止巡逻', '停止巡航'],
    chat: ['你有什么功能', '你会干什么', '你多大了', '你有男朋友吗', '你爸爸妈妈是谁', '豹小秘的硬件配置'],
    weather: ['今天有雨吗', '明天天气怎么样', '今天天气怎么样', '明天有雨吗', '天气怎么样'],
    calendar: ['现在几点了', '今天是星期几', '几点了', '今天是几号', '春节是哪一天'],
    dance: ['跳个舞吧', '来跳个舞', '跳一个舞', '开始跳舞'],
    group_photo: [
      '我们合个影吧',
      '能和我拍照吗',
      '我们一起拍个照吧',
      '跟我拍个照',
      '合影',
      '合照',
      '拍照',
      '开启合影模式',
      '开始合影',
    ],
    web: [],
    open_app: [],
    cafe_meal: ['开始送餐', '我要送餐', '帮我送餐', '去送餐', '帮我送一下餐', '送餐去'],
    cafe_distributefood: ['开始分发', '帮我分发', '小食分发', '小食配送', '分发小食', '配送小食'],
    cafe_welcome: ['开始揽客', '去揽客', '揽客去吧'],
    // customer_services:['人工客服','转人工客服','转人工','转人工前台'],
  },
  recommendQuesEng: {
    interview: ['我来找人', '我来面试', '我来登记', '我是访客', '我来拜访', '我来开会'],
    take_me_there: ['Take me to toilet'],
    guide: ['I want to visit xxx'],
    cruise: ['开始巡逻', '开始巡航', '结束巡逻', '结束巡航', '停止巡逻', '停止巡航'],
    chat: ['What functions do you have'],
    weather: ['今天有雨吗', '明天天气怎么样', '今天天气怎么样', '明天有雨吗', '天气怎么样'],
    calendar: ['现在几点了', '今天是星期几', '几点了', '今天是几号', '春节是哪一天'],
    dance: ['跳个舞吧', '来跳个舞', '跳一个舞', '开始跳舞'],
    group_photo: [
      '我们合个影吧',
      '能和我拍照吗',
      '我们一起拍个照吧',
      '跟我拍个照',
      '合影',
      '合照',
      '拍照',
      '开启合影模式',
      '开始合影',
    ],
    web: [],
    open_app: [],
    // customer_services:['人工客服','转人工客服','转人工','转人工前台'],
  },
  recommendQuesArabic: {
    interview: ['我来找人', '我来面试', '我来登记', '我是访客', '我来拜访', '我来开会'],
    take_me_there: ['Take me to toilet'],
    guide: ['我要参观', '带我参观', '我想参观', '带我参观一下', '我想参观一下', '我要参观一下'],
    cruise: ['开始巡逻', '开始巡航', '结束巡逻', '结束巡航', '停止巡逻', '停止巡航'],
    chat: ['What functions do you have'],
    weather: ['今天有雨吗', '明天天气怎么样', '今天天气怎么样', '明天有雨吗', '天气怎么样'],
    calendar: ['现在几点了', '今天是星期几', '几点了', '今天是几号', '春节是哪一天'],
    dance: ['跳个舞吧', '来跳个舞', '跳一个舞', '开始跳舞'],
    group_photo: [
      '我们合个影吧',
      '能和我拍照吗',
      '我们一起拍个照吧',
      '跟我拍个照',
      '合影',
      '合照',
      '拍照',
      '开启合影模式',
      '开始合影',
    ],
    web: [],
    open_app: [],
    // customer_services:['人工客服','转人工客服','转人工','转人工前台'],
  },
  allQuesList: [
    '我来找人',
    '我来面试',
    '我来登记',
    '我是访客',
    '我来拜访',
    '我来开会',
    '带我去XXX',
    '我要参观',
    '带我参观',
    '我想参观',
    '带我参观一下',
    '我想参观一下',
    '我要参观一下',
    '开始巡逻',
    '开始巡航',
    '结束巡逻',
    '结束巡航',
    '停止巡逻',
    '停止巡航',
    '你有什么功能',
    '你会干什么',
    '你多大了',
    '你有男朋友吗',
    '你爸爸妈妈是谁',
    '豹小秘的硬件配置',
    '今天有雨吗',
    '明天天气怎么样',
    '今天天气怎么样',
    '明天有雨吗',
    '天气怎么样',
    '现在几点了',
    '今天是星期几',
    '几点了',
    '今天是几号',
    '春节是哪一天',
    '跳个舞吧',
    '来跳个舞',
    '跳一个舞',
    '开始跳舞',
  ],

  queries: [
    {
      query: ['你可以对我说：明天天气呢', '你可以对我说：后天有雨吗'],
      name: 'weather',
    },
    {
      query: ['你可以对我说：什么时候过年', '你可以对我说：今天是几号'],
      name: 'calendar',
    },
    {
      query: [
        __('customHome.modalMsg.sayToMes') + '1',
        __('customHome.modalMsg.globalBoot'),
        __('customHome.modalMsg.multiple'),
      ],
      name: 'mainPage',
    },
    {
      query: ['你可以对我说：带我去xxx', '你可以对我说：带我去xxx'],
      name: 'query_locations',
    },
    {
      query: ['你可以对我说：你会干什么', '你可以对我说：你男朋友是谁'],
      name: 'chat',
    },
    {
      query: ['你可以对我说：别跳了', '你可以对我说：退出'],
      name: 'dance',
    },
    {
      query: ['你可以对我说：退出', '你可以对我说：关闭'],
      name: 'web',
    },
    {
      query: ['你可以对我说：退出', '你可以对我说：关闭'],
      name: 'video',
    },
  ],
}

export const recommendQuesMiniIn = {
  funcConfig: [
    {
      key: 'interview',
      name: __('customHome.functionList.reception'),
      list_icon1: 'module_public/module_skill_home/List1_interview.png',
      list_icon2: 'module_public/module_skill_home/List2_interview.png',
      card_icon: 'module_public/module_skill_home/Card_interview.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_interview.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_interview.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'take_me_there',
      name: __('customHome.functionList.guide'),
      list_icon1: 'module_public/module_skill_home/List1_take_me_there.png',
      list_icon2: 'module_public/module_skill_home/List2_take_me_there.png',
      card_icon: 'module_public/module_skill_home/Card_take_me_there.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_take_me_there.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_take_me_there.png',
      buttons_icon: '/module_public/module_skill_home/button_dark_blue.png',
    },
    {
      key: 'cruise',
      name: __('customHome.functionList.patrol'),
      list_icon1: 'module_public/module_skill_home/List1_cruise.png',
      list_icon2: 'module_public/module_skill_home/List2_cruise.png',
      card_icon: 'module_public/module_skill_home/Card_cruise.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_cruise.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_cruise.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      list_icon2: 'module_public/module_skill_home/List2_chat1.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      buttons_icon: '/module_public/module_skill_home/button_green.png',
      label: 'chat1',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.chat'),
      list_icon1: 'module_public/module_skill_home/List1_chat2.png',
      list_icon2: 'module_public/module_skill_home/List2_chat2.png',
      card_icon: 'module_public/module_skill_home/Card_chat2.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat2.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat2.png',
      buttons_icon: '/module_public/module_skill_home/button_green.png',
      label: 'chat2',
    },
    {
      key: 'weather',
      name: __('customHome.functionList.weather'),
      list_icon1: 'module_public/module_skill_home/List1_weather.png',
      list_icon2: 'module_public/module_skill_home/List2_weather.png',
      card_icon: 'module_public/module_skill_home/Card_weather.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_weather.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_weather.png',
      buttons_icon: '/module_public/module_skill_home/button_purple.png',
    },
    {
      key: 'calendar',
      name: __('customHome.functionList.calendar'),
      list_icon1: 'module_public/module_skill_home/List1_calendar.png',
      list_icon2: 'module_public/module_skill_home/List2_calendar.png',
      card_icon: 'module_public/module_skill_home/Card_calendar.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_calendar.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_calendar.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'guide',
      name: __('customHome.functionList.visit'),
      list_icon1: 'module_public/module_skill_home/List1_guide.png',
      list_icon2: 'module_public/module_skill_home/List2_guide.png',
      card_icon: 'module_public/module_skill_home/Card_guide.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_guide.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_guide.png',
      buttons_icon: '/module_public/module_skill_home/button_purple.png',
    },
    {
      key: 'dance',
      name: __('customHome.functionList.dance'),
      list_icon1: 'module_public/module_skill_home/List1_dance.png',
      list_icon2: 'module_public/module_skill_home/List2_dance.png',
      card_icon: 'module_public/module_skill_home/Card_dance.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_dance.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_dance.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'group_photo',
      name: __('customHome.functionList.groupPhoto'),
      list_icon1: 'module_public/module_skill_home/List1_group_photo.png',
      list_icon2: 'module_public/module_skill_home/List2_group_photo.png',
      card_icon: 'module_public/module_skill_home/Card_group_photo.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_group_photo.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_group_photo.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'web',
      name: __('customHome.functionList.miniAppAdress'),
      list_icon1: 'module_public/module_skill_home/List1_web.png',
      list_icon2: 'module_public/module_skill_home/List2_web.png',
      card_icon: 'module_public/module_skill_home/Card_web.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_web.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_web.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    {
      key: 'open_app',
      name: __('customHome.functionList.miniApp'),
      list_icon1: 'module_public/module_skill_home/List1_open_app.png',
      list_icon2: 'module_public/module_skill_home/List2_open_app.png',
      card_icon: 'module_public/module_skill_home/Card_open_app.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_open_app.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_open_app.png',
      buttons_icon: '/module_public/module_skill_home/button_blue.png',
    },
    // mini机器人不需要招财机器人的功能
    // {
    //   key: 'cafe_distributefood',
    //   name: '小食分发', //__('customHome.functionList.freeQA'),
    //   list_icon1: 'module_public/module_skill_home/List1_chat1.png',
    //   list_icon2: 'module_public/module_skill_home/List2_chat1.png',
    //   card_icon: 'module_public/module_skill_home/Card_chat1.png',
    //   standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
    //   standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
    //   buttons_icon: '/module_public/module_skill_home/button_blue.png',
    // },
    // {
    //   key: 'cafe_welcome',
    //   name: '餐厅揽客', //__('customHome.functionList.freeQA'),
    //   list_icon1: 'module_public/module_skill_home/List1_chat1.png',
    //   list_icon2: 'module_public/module_skill_home/List2_chat1.png',
    //   card_icon: 'module_public/module_skill_home/Card_chat1.png',
    //   standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
    //   standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
    //   buttons_icon: '/module_public/module_skill_home/button_blue.png',
    // },
    // {
    //   key: 'cafe_meal',
    //   name: '送餐', //__('customHome.functionList.freeQA'),
    //   list_icon1: 'module_public/module_skill_home/List1_chat1.png',
    //   list_icon2: 'module_public/module_skill_home/List2_chat1.png',
    //   card_icon: 'module_public/module_skill_home/Card_chat1.png',
    //   standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
    //   standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
    //   buttons_icon: '/module_public/module_skill_home/button_blue.png',
    // },
  ],
  funcConfigEng: [
    {
      key: 'guide',
      name: __('customHome.functionList.visit'),
      list_icon1: 'module_public/module_skill_home/List1_guide.png',
      list_icon2: 'module_public/module_skill_home/List2_guide.png',
      card_icon: 'module_public/module_skill_home/Card_guide.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_guide.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_guide.png',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      // list_icon2: "module_public/module_skill_home/List2_chat1.png",
      list_icon2: 'module_public/module_skill_home/visit_bg_img.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      label: 'chat1',
    },
    {
      key: 'take_me_there',
      name: __('customHome.functionList.guide'),
      list_icon1: 'module_public/module_skill_home/List1_take_me_there.png',
      // list_icon2: "module_public/module_skill_home/List2_take_me_there.png", // 需要替换得图片
      list_icon2: 'module_public/module_skill_home/visit_bg_img.png',
      card_icon: 'module_public/module_skill_home/Card_take_me_there.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_take_me_there.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_take_me_there.png',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.chat'),
      list_icon1: 'module_public/module_skill_home/List1_chat2.png',
      list_icon2: 'module_public/module_skill_home/List2_chat2.png',
      card_icon: 'module_public/module_skill_home/Card_chat2.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat2.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat2.png',
      label: 'chat2',
    },
  ],
  funcConfigArabic: [
    {
      key: 'chat',
      name: __('customHome.functionList.freeQA'),
      list_icon1: 'module_public/module_skill_home/List1_chat1.png',
      // list_icon2: "module_public/module_skill_home/List2_chat1.png",
      list_icon2: 'module_public/module_skill_home/visit_bg_img.png',
      card_icon: 'module_public/module_skill_home/Card_chat1.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat1.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat1.png',
      label: 'chat1',
    },
    {
      key: 'chat',
      name: __('customHome.functionList.chat'),
      list_icon1: 'module_public/module_skill_home/List1_chat2.png',
      list_icon2: 'module_public/module_skill_home/List2_chat2.png',
      card_icon: 'module_public/module_skill_home/Card_chat2.png',
      standard1_icon: 'module_public/module_skill_home/Standard1_chat2.png',
      standard2_icon: 'module_public/module_skill_home/Standard2_chat2.png',
      label: 'chat2',
    },
  ],
  recommendQues: {
    interview: ['我来找人', '我来面试', '我来登记', '我是访客', '我来拜访', '我来开会'],
    take_me_there: ['带我去'],
    guide: ['我要参观', '带我参观', '我想参观', '带我参观一下', '我想参观一下', '我要参观一下'],
    cruise: ['开始巡逻', '开始巡航', '结束巡逻', '结束巡航', '停止巡逻', '停止巡航'],
    chat: ['你有什么功能', '你会干什么', '你多大了', '你有男朋友吗', '你爸爸妈妈是谁', '豹小秘的硬件配置'],
    weather: ['今天有雨吗', '明天天气怎么样', '今天天气怎么样', '明天有雨吗', '天气怎么样'],
    calendar: ['现在几点了', '今天是星期几', '几点了', '今天是几号', '春节是哪一天'],
    dance: ['跳个舞吧', '来跳个舞', '跳一个舞', '开始跳舞'],
    group_photo: [
      '我们合个影吧',
      '能和我拍照吗',
      '我们一起拍个照吧',
      '跟我拍个照',
      '合影',
      '合照',
      '拍照',
      '开启合影模式',
      '开始合影',
    ],
    web: [],
    open_app: [],
    cafe_meal: ['开始送餐', '我要送餐', '帮我送餐', '去送餐', '帮我送一下餐', '送餐去'],
    cafe_distributefood: ['开始分发', '帮我分发', '小食分发', '小食配送', '分发小食', '配送小食'],
    cafe_welcome: ['开始揽客', '去揽客', '揽客去吧'],
    // customer_services:['人工客服','转人工客服','转人工','转人工前台'],
  },
  recommendQuesEng: {
    interview: ['我来找人', '我来面试', '我来登记', '我是访客', '我来拜访', '我来开会'],
    take_me_there: ['Take me to toilet'],
    guide: ['I want to visit xxx'],
    cruise: ['开始巡逻', '开始巡航', '结束巡逻', '结束巡航', '停止巡逻', '停止巡航'],
    chat: ['What functions do you have'],
    weather: ['今天有雨吗', '明天天气怎么样', '今天天气怎么样', '明天有雨吗', '天气怎么样'],
    calendar: ['现在几点了', '今天是星期几', '几点了', '今天是几号', '春节是哪一天'],
    dance: ['跳个舞吧', '来跳个舞', '跳一个舞', '开始跳舞'],
    group_photo: [
      '我们合个影吧',
      '能和我拍照吗',
      '我们一起拍个照吧',
      '跟我拍个照',
      '合影',
      '合照',
      '拍照',
      '开启合影模式',
      '开始合影',
    ],
    web: [],
    open_app: [],
    // customer_services:['人工客服','转人工客服','转人工','转人工前台'],
  },
  recommendQuesArabic: {
    interview: ['我来找人', '我来面试', '我来登记', '我是访客', '我来拜访', '我来开会'],
    take_me_there: ['Take me to toilet'],
    guide: ['我要参观', '带我参观', '我想参观', '带我参观一下', '我想参观一下', '我要参观一下'],
    cruise: ['开始巡逻', '开始巡航', '结束巡逻', '结束巡航', '停止巡逻', '停止巡航'],
    chat: ['What functions do you have'],
    weather: ['今天有雨吗', '明天天气怎么样', '今天天气怎么样', '明天有雨吗', '天气怎么样'],
    calendar: ['现在几点了', '今天是星期几', '几点了', '今天是几号', '春节是哪一天'],
    dance: ['跳个舞吧', '来跳个舞', '跳一个舞', '开始跳舞'],
    group_photo: [
      '我们合个影吧',
      '能和我拍照吗',
      '我们一起拍个照吧',
      '跟我拍个照',
      '合影',
      '合照',
      '拍照',
      '开启合影模式',
      '开始合影',
    ],
    web: [],
    open_app: [],
    // customer_services:['人工客服','转人工客服','转人工','转人工前台'],
  },
  allQuesList: [''],

  queries: [
    {
      query: ['你可以对我说：明天天气呢', '你可以对我说：后天有雨吗'],
      name: 'weather',
    },
    {
      query: ['你可以对我说：什么时候过年', '你可以对我说：今天是几号'],
      name: 'calendar',
    },
    {
      query: [
        __('customHome.modalMsg.sayToMes') + '1',
        __('customHome.modalMsg.globalBoot'),
        __('customHome.modalMsg.multiple'),
      ],
      name: 'mainPage',
    },
    {
      query: ['你可以对我说：带我去xxx', '你可以对我说：带我去xxx'],
      name: 'query_locations',
    },
    {
      query: ['你可以对我说：你会干什么', '你可以对我说：你男朋友是谁'],
      name: 'chat',
    },
    {
      query: ['你可以对我说：别跳了', '你可以对我说：退出'],
      name: 'dance',
    },
    {
      query: ['你可以对我说：退出', '你可以对我说：关闭'],
      name: 'web',
    },
    {
      query: ['你可以对我说：退出', '你可以对我说：关闭'],
      name: 'video',
    },
  ],
}

export default recommendQues
