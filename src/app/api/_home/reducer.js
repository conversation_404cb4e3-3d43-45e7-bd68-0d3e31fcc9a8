import {
  GET_EDIT_LIST,
  GET_HOME_MODULE,
  OR<PERSON>IN_HOME_MODULE,
  CH<PERSON>GE_MODULE_SKILL,
  CHANGE_MODULE_QUESTION,
  <PERSON>AN<PERSON>_SKILL_SORT,
  <PERSON>AN<PERSON>_QUESTIONEXT_SORT,
  REMOVE_HOMEMODULE_SKILL,
  ADD_HOMEMODULE_SKILL,
  CHANGE_INFO,
  ADD_NEW_SKILL,
  UPDATE_SKILL,
  UPDATE_QUESTIONEXT,
  CHANGE_QUERY_SORT,
  REMOVE_HOMEMODULE_QUERY,
  IS_MODULE_CHANGE,
  <PERSON>AN<PERSON>_MODULE_QUERIES,
  UPDATE_MODULE_QUERY,
  ADD_NEW_QUERIES,
  ADD_NEW_QUESTIONEXT,
  EDIT_QUERIES,
  PUSH_QUERIES,
  NEW_PUSH_QUERIES,
  MODIFY_QUERIES_SORT,
  CHANGE_HOME_LANG,
  SET_COMMON_TRANSLATIONS,
} from '@redux/action-types'

const initialState = {}

export default function (
  state = {
    isModuleChange: true,
    commonTranslations: {
      askMe: '你还可以问我', // 默认中文
    },
  },
  { type, ...obj },
) {
  switch (type) {
    case GET_HOME_MODULE: {
      let originModule = JSON.parse(JSON.stringify(obj.data.homeModule))
      return Object.assign({}, state, obj.data, { originHomeModule: originModule })
    }
    case ORIGIN_HOME_MODULE: {
      return Object.assign({}, state, { originHomeModule: obj.data.originHomeModule })
    }
    case CHANGE_HOME_LANG: {
      // let lang = JSON.parse(JSON.stringify(obj.data.lang))
      // return Object.assign({}, state, obj.data, {lang: lang})
    }
    case GET_EDIT_LIST: {
      return Object.assign({}, state, { editIndex: obj.data.index })
    }
    case CHANGE_INFO: {
      let newtemplateInfo = state.homeModule.templateInfo
      newtemplateInfo[obj.data.type] = obj.data.content
      return Object.assign({}, state, {
        homeModule: Object.assign({}, state.homeModule, { templateInfo: newtemplateInfo }),
      })
    }
    case CHANGE_MODULE_SKILL: {
      let newSkills = []
      state.homeModule.skills.map((item, index) => {
        if (index != obj.data.index) {
          newSkills.push(item)
        } else {
          if (obj.data.type == 'take_me_there_title') {
            item['title'] = obj.data.content
          } else {
            item[obj.data.type] = obj.data.content[obj.data.type]
          }
          newSkills.push(item)
        }
      })
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { skills: newSkills }) })
    }
    case CHANGE_MODULE_QUESTION: {
      let newques = []
      state.homeModule.questionExt.map((item, index) => {
        if (index != obj.data.index) {
          newques.push(item)
        } else {
          // if (obj.data.type == 'take_me_there_title') {
          //     item['title'] = obj.data.content;
          // } else { }
          if (!obj.data.type) {
            item = obj.data.content
          } else {
            item[obj.data.type] = obj.data.content[obj.data.type]
          }

          newques.push(item)
        }
      })
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { questionExt: newques }) })
    }
    case CHANGE_SKILL_SORT: {
      let newSkills = []
      let moveItem = null
      console.log('切换-2', state.homeModule.skills)
      state.homeModule.skills.map((skill, index) => {
        if (index != obj.data.dragBefore) {
          newSkills.push(skill)
        } else {
          moveItem = skill
        }
      })
      newSkills.splice(obj.data.dragAfter, 0, moveItem)
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { skills: newSkills }) })
    }

    case CHANGE_QUESTIONEXT_SORT: {
      let newSkills = []
      let moveItem = null
      console.log('question', state.homeModule.skills)
      state.homeModule.questionExt &&
        state.homeModule.questionExt.map((skill, index) => {
          if (index != obj.data.dragBefore) {
            newSkills.push(skill)
          } else {
            moveItem = skill
          }
        })
      newSkills.splice(obj.data.dragAfter, 0, moveItem)
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { questionExt: newSkills }) })
    }
    case REMOVE_HOMEMODULE_SKILL: {
      //转移homeModule中skills的skill项
      let newSkills = []
      let toggleSkill = null
      let showLen = 0
      console.log('切换-3', state.homeModule.skills)
      state.homeModule.skills.map((skill, index) => {
        if (skill.display) {
          showLen++
        }
        if (index != obj.data.index) {
          newSkills.push(skill)
        } else {
          toggleSkill = skill
        }
      })
      toggleSkill.display = obj.data.display
      newSkills.splice(showLen - 1, 0, toggleSkill)
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { skills: newSkills }) })
    }
    case ADD_HOMEMODULE_SKILL: {
      let newSkills = []
      let newSkills_1 = []
      console.log('切换-4', state.homeModule.skills)
      state.homeModule.skills.map((skill, index) => {
        if (index == obj.data.index) {
          skill.display = obj.data.display
        }
        if (skill.display) {
          newSkills.push(skill)
        } else {
          newSkills_1.push(skill)
        }
      })

      // newSkills.splice(obj.data.index, 1);
      // if (obj.data.modalStyle == 'List') {
      //     newSkills.splice(2, 0, toggleSkill);
      // } else if (obj.data.modalStyle == 'Standard' || obj.data.modalStyle == 'government') {
      //     newSkills.splice(1, 0, toggleSkill);
      // } else {
      //     newSkills.splice(0, 0, toggleSkill);
      // }
      return Object.assign({}, state, {
        homeModule: Object.assign({}, state.homeModule, { skills: newSkills.concat(newSkills_1) }),
      })
    }
    case ADD_NEW_SKILL: {
      let newSkills = []
      console.log('切换-5', state.homeModule.skills)
      state.homeModule.skills.map((skill, index) => {
        newSkills.push(skill)
      })
      newSkills.splice(obj.data.index, 0, obj.data.skill)
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { skills: newSkills }) })
    }
    case ADD_NEW_QUESTIONEXT: {
      let newques = []
      console.log('切换-5', state.homeModule.questionExt)
      state.homeModule.questionExt &&
        state.homeModule.questionExt.map((ques, index) => {
          newques.push(ques)
        })
      newques.splice(obj.data.index, 0, obj.data.content)
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { questionExt: newques }) })
    }
    case UPDATE_SKILL: {
      let newSkills = []
      obj.data.skills.map((skill, index) => {
        newSkills.push(skill)
      })

      // newSkills.splice(obj.data.index, 0, obj.data.skill);
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { skills: newSkills }) })
    }

    case UPDATE_QUESTIONEXT: {
      let newQuestionsExt = []
      obj.data.questionExt.map((skill, index) => {
        newQuestionsExt.push(skill)
      })

      // newSkills.splice(obj.data.index, 0, obj.data.skill);
      return Object.assign({}, state, {
        homeModule: Object.assign({}, state.homeModule, { questionExt: newQuestionsExt }),
      })
    }
    case CHANGE_QUERY_SORT: {
      let queries = [],
        oldStandardQuery = {},
        oldStandardQueryIndex = 0,
        modalStyle = ''
      if (obj.data.bottomQueries) {
        modalStyle = obj.data.bottomQueries
      } else {
        if (obj.data.modalStyle == 'government') {
          modalStyle = 'governmentMain'
        } else {
          modalStyle = 'standardMain'
        }
      }
      state.homeModule.queries.map((query, index) => {
        if (query.name == modalStyle) {
          oldStandardQueryIndex = index
          oldStandardQuery = query
        }
        queries.push(query)
      })
      let newStandardQueryArr = []
      let moveItem = null
      oldStandardQuery.query.map((query, index) => {
        if (index != obj.data.dragBefore) {
          newStandardQueryArr.push(query)
        } else {
          moveItem = query
        }
      })
      newStandardQueryArr.splice(obj.data.dragAfter, 0, moveItem)
      let newStandardQuery = Object.assign({}, oldStandardQuery, { query: newStandardQueryArr })
      queries.splice(oldStandardQueryIndex, 1, newStandardQuery)
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: queries }) })
    }
    case UPDATE_MODULE_QUERY: {
      let queries = [],
        oldStandardQuery = {},
        oldStandardQueryIndex = 0,
        modalStyle = ''
      let newStandardQueryArr = []
      if (obj.data.bottomQueries) {
        modalStyle = obj.data.bottomQueries
      } else {
        if (obj.data.modalStyle == 'government') {
          modalStyle = 'governmentMain'
        } else {
          modalStyle = 'standardMain'
        }
      }
      let oldquey = JSON.parse(JSON.stringify(state.homeModule.queries))
      oldquey.map((query, index) => {
        if (query.name == modalStyle) {
          query.query = obj.data.query
          queries.push(query)
        } else {
          queries.push(query)
        }
      })

      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: queries }) })
    }
    case REMOVE_HOMEMODULE_QUERY: {
      let queries = [],
        oldStandardQuery = {},
        oldStandardQueryIndex = 0,
        modalStyle = ''
      if (obj.data.bottomQueries) {
        modalStyle = obj.data.bottomQueries
      } else {
        if (obj.data.modalStyle == 'government') {
          modalStyle = 'governmentMain'
        } else {
          modalStyle = 'standardMain'
        }
      }
      state.homeModule.queries.map((query, index) => {
        if (query.name == modalStyle) {
          oldStandardQueryIndex = index
          oldStandardQuery = query
        }
        queries.push(query)
      })
      let newStandardQueryArr = []
      oldStandardQuery.query.map((query, index) => {
        if (index != obj.data.index) {
          newStandardQueryArr.push(query)
        }
      })
      let newStandardQuery = Object.assign({}, oldStandardQuery, { query: newStandardQueryArr })
      queries.splice(oldStandardQueryIndex, 1, newStandardQuery)
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: queries }) })
    }
    case CHANGE_MODULE_QUERIES: {
      let newQueries = []
      let modalStyle = ''
      if (obj.data.modalStyle == 'government') {
        modalStyle = 'governmentMain'
      } else {
        modalStyle = 'standardMain'
      }
      state.homeModule.queries.map((item, index) => {
        if (item.name == modalStyle) {
          item.query[obj.data.index] = obj.data.content
          newQueries.push(item)
        } else {
          newQueries.push(item)
        }
      })
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: newQueries }) })
    }
    case ADD_NEW_QUERIES: {
      let newQueries = []
      let modalStyle = ''
      if (obj.data.bottomQueries) {
        modalStyle = obj.data.bottomQueries
      } else {
        if (obj.data.modalStyle == 'government') {
          modalStyle = 'governmentMain'
        } else {
          modalStyle = 'standardMain'
        }
      }
      state.homeModule.queries.map((item, index) => {
        if (item.name == modalStyle) {
          item.query.unshift(obj.data.content)
          newQueries.push(item)
        } else {
          newQueries.push(item)
        }
      })
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: newQueries }) })
    }
    case PUSH_QUERIES: {
      let newQueries = []
      let flag = true
      let modalStyle = obj.data.bottomQueries
      let json = {}
      json['query'] = obj.data.content
      json['name'] = obj.data.bottomQueries
      state.homeModule &&
        state.homeModule.queries.map((item, index) => {
          if (item.name == modalStyle) {
            flag = false
          }
          newQueries.push(item)
        })
      if (flag) {
        newQueries.push(json)
      }
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: newQueries }) })
    }
    case NEW_PUSH_QUERIES: {
      let newQueries = []
      let flag = true
      let modalStyle = obj.data.bottomQueries
      let json = {}
      json['query'] = obj.data.content
      json['name'] = obj.data.bottomQueries
      json['sort'] = obj.data.sort
      state.homeModule.queries.map((item, index) => {
        if (item.name == modalStyle) {
          flag = false
        }
        newQueries.push(item)
      })
      if (flag) {
        newQueries.push(json)
      }
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: newQueries }) })
    }
    case MODIFY_QUERIES_SORT: {
      let newQueries = []
      let modalStyle = obj.data.bottomQueries
      let json = {}
      json['query'] = obj.data.content
      json['name'] = obj.data.bottomQueries
      json['sort'] = obj.data.sort
      state.homeModule.queries.map((item, index) => {
        if (item.name == modalStyle) {
          newQueries.push(json)
        } else {
          newQueries.push(item)
        }
      })
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: newQueries }) })
    }
    case EDIT_QUERIES: {
      let newQueries = []
      let modalStyle = ''
      if (obj.data.bottomQueries) {
        modalStyle = obj.data.bottomQueries
      }
      state.homeModule.queries.map((item, index) => {
        if (item.name == modalStyle) {
          item.query[obj.data.index] = obj.data.content
          newQueries.push(item)
        } else {
          newQueries.push(item)
        }
      })
      return Object.assign({}, state, { homeModule: Object.assign({}, state.homeModule, { queries: newQueries }) })
    }
    case IS_MODULE_CHANGE: {
      return Object.assign({}, state, { isModuleChange: true })
    }
    case SET_COMMON_TRANSLATIONS: {
      return Object.assign({}, state, { commonTranslations: obj.data.commonTranslations })
    }
    default:
      return state
  }

  return state
}
