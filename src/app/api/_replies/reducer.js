import { GET_REPLIES_LIST, GET_REPLIES_EMPTYTIPS, GET_CONFIGURATION } from '@redux/action-types'

const initialState = {
  emptyStyle: 'none',
  fileName: '',
}

export default function (state = initialState, { type, ...data }) {
  switch (type) {
    case GET_REPLIES_LIST: {
      return Object.assign({}, state, obj.data)
    }

    case GET_REPLIES_EMPTYTIPS: {
      return Object.assign({}, state, data)
    }

    case GET_CONFIGURATION: {
      return Object.assign({}, state, data.data)
    }
  }

  return state
}
