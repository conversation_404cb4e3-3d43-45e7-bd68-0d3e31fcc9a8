import request from '@utils/request'
import { GET_REPLIES_LIST, GET_REPLIES_EMPTYTIPS, GET_CONFIGURATION } from '@redux/action-types'

export const getRepliesEmptytips = (data) => (dispatch) =>
  dispatch({
    type: GET_REPLIES_EMPTYTIPS,
    data,
  })

//获取企业问答
export const getRepliesList = (page) => {
  return request('/api/control/qa', {
    method: 'GET',
    data: {
      page_num: page || 1,
      page_size: 200,
    },
  })
}

//删除一列
export const deleteAll = (data) => {
  return request('/api/control/qa_delete', {
    method: 'POST',
    data,
  })
}

//删除问题
//export const deleteQuestion = (data) => {
//  return request('/api/control/qa/question_delete', {
//      method: 'POST',
//      data
//  })
//}

//删除答案
//export const deleteAnswer = (data) => {
//  return request('/api/control/qa/answer_delete', {
//      method: 'POST',
//      data
//  })
//}

//上传图片
export const uploadImageInfo = (data) => {
  return request('/api/control/qa_image', {
    method: 'POST',
    data,
  })
}

//获取问答模板

export const addQuestionReplay = (data) => {
  console.log('data---addQuestionReplay', data)

  return request('/api/control/qa', {
    method: 'POST',
    data,
  })
}

export const saveQuestionReplay = (data) => {
  console.log('saveQuestionReplay', data)
  return request('/api/control/qa', {
    method: 'POST',
    data,
  })
}

//同步数据

export const underway = (data) => {
  return request('/api/control/qa_sync', {
    method: 'POST',
    data,
  })
}
//获取自定义场景文件名
export const getConfiguration = (robotId) => (dispatch) => {
  request('/api/user/meui_configuration', {
    method: 'GET',
    data: {
      robot_id: robotId,
    },
  })
    .then((res) => {
      let filename = res.file_name ? decodeURI(res.file_name) : ''
      dispatch({
        type: GET_CONFIGURATION,
        data: {
          fileName: filename,
        },
      })
    })
    .catch(() => {
      dispatch({
        type: GET_CONFIGURATION,
        data: {
          fileName: '',
        },
      })
    })
}
