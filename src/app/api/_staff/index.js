// import {
//     // INIT_LIST
// } from '@redux/action-types'

import {} from '@redux/action-types'
// import getStore from '@utils/store'
import request from '@utils/request'
/**
 * Redux action: 示例
 * @param {String} arg1
 */
// export const sample = (arg1) => dispatch => dispatch({
//     type: INIT_ROBOTS,
//     data: arg1
// })

//上传照片检测是否符合条件
export const uploadPic = (data) => {
  return request('/api/user/images', {
    method: 'post',
    data: data,
  })
}
//确认照片
export const ensurePic = (data) => {
  return request('/api/user/images_verification', {
    method: 'post',
    data: data,
  })
}
export const ensureStaffPic = (data) => {
  return request('/capi/v1/corp/user_images_verification', {
    method: 'post',
    data: data,
  })
}
