import request from '@utils/request'

export const getRoote = () => {
  // return request('/test_static/js/index.json', {
  return request('/api/control/corp_menu_setting', {
    method: 'get',
    // data: data
  })
}
export const getNewWindow = () => {
  // return request('/test_static/js/index.json', {
  return request('/capi/v1/corp/sso_bdcp', {
    method: 'get',
    // data: data
  })
}
export const getNewWindows = (url, path) => {
  let winUrl = window.open('about:blank')
  return request(url, {
    method: 'get',
    data: {
      tpart_redirect: path || '',
    },
  })
    .then((res) => {
      console.log(res)
      const url = res.redirect_url
      winUrl.location.href = url
      console.log('获取跳转页面成功')
    })
    .catch((err) => {
      console.log(err)
    })
}
export const goToUrl = (idStr) => {
  // const idStr = '1030'
  const pathData = this.props.routePair.filter((el) => el.id == idStr)
  console.log()
  const url = pathData[0].path
  // historyReplace(url)
  return console.log(idStr)
}
