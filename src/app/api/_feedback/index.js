import request from '@utils/request'

//获取列表
export const getFeedbackList = (data) => {
  return request('/api/control/module_config_list', {
    method: 'post',
    data: data,
  })
}
//获取详情
export const getFeedback = (data) => {
  console.log('详情- 4')
  return request('/api/control/module_config_detail', {
    method: 'post',
    data,
  })
}
export const uploadResource = (data) => {
  return request('/api/control/upload_config_resource', {
    method: 'post',
    data: data,
  })
}
//保存并发布给机器人（单个导览发布到多个机器人）
export const saveandPublish = (data) => {
  console.log('api save_and_publish_module4')
  return request('/api/control/save_and_publish_module', {
    method: 'post',
    data: data,
  })
}
