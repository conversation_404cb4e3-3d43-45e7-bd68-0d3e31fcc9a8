import request from '@utils/request'
import {
  RESTORE_LOGIN,
  RESET_LOGIN,
  UPDATE_USER_PROFILE,
  UPDATE_CORP_PROFILE,
  INIT_ROBOTS_DATA,
} from '@redux/action-types'
import Cookies from 'js-cookie'

/**************************************
 * 标准方法
 *************************************/

/**
 * 设置登录 Cookie
 * @param {Object} data
 * @param {Boolean} isSaveSession 是否记住登录
 */
export const setCookies = (data, isSaveSession) => {
  for (let key in data) {
    Cookies.set(key, data[key], {
      expires: isSaveSession ? 30 : undefined,
      domain: __DEV__ ? '' : document.domain,
      secure: location.protocol == 'https:' ? true : false,
    })
  }
}

/**************************************
 * Redux actions
 *************************************/

// TODO: promise login
// export const login = (data) => dispatch => dispatch({
//     type: USER_LOGIN,
//     data,
// })
//重置初次登陆
export const resetLoginStatus = () => {
  return request('/capi/v1/corp/reset_user_login_status', {
    method: 'GET',
  })
}
//

export const loginByCode = (mobile, code, token) => {
  return request('/capi/v1/corp/admin_code_auth', {
    method: 'GET',
    data: {
      auth_mobile: mobile,
      auth_code: code,
      auth_token: token,
    },
  })
}

export const getLoginCode = (mobile) => {
  return request('/capi/v1/corp/send_admin_auth_code', {
    method: 'GET',
    data: {
      auth_mobile: mobile,
    },
  })
}

export const loginByPassword = (account, password) => {
  return request('/capi/v1/corp/admin_password_auth', {
    method: 'GET',
    data: {
      auth_account: account,
      auth_password: password,
    },
  })
}

export const modifyUser = (data) => {
  return request('/capi/v1/corp/modify_user', {
    method: 'POST',
    data,
  })
}

export const getResetCode = (mobile) => {
  return request('/capi/v1/corp/send_admin_rpwd_code', {
    method: 'GET',
    data: {
      rpwd_account: mobile,
    },
  })
}

export const checkResetCode = (mobile, code, token) => {
  return request('/capi/v1/corp/admin_rpwd_code_check', {
    method: 'GET',
    data: {
      rpwd_account: mobile,
      rpwd_code: code,
      rpwd_token: token,
    },
  })
}

export const resetPassword = (mobile, code, token, password) => {
  return request('/capi/v1/corp/admin_reset_password', {
    method: 'GET',
    data: {
      rpwd_account: mobile,
      rpwd_code: code,
      rpwd_token: token,
      rpwd_password: password,
    },
  })
}

export const getUserProfile = (userId) => {
  return request('/capi/v1/corp/user_profile', {
    method: 'GET',
    data: {
      user_uuid: userId,
    },
  })
}
//获取企业语言
export const getLangList = (data) => {
  return request('/api/control/lang/list', {
    method: 'GET',
    data,
  })
}

/**
 * Redux action: 恢复登录进程
 * @param {String} token
 * @param {String} uuid
 */
export const restoreLogin = (token, uuid) => (dispatch) =>
  dispatch({
    type: RESTORE_LOGIN,
    token,
    uuid,
  })

/**
 * Redux action: 重置登录和当前用户数据
 */
export const resetLogin = () => (dispatch) => {
  Cookies.remove('token', { domain: __DEV__ ? '' : document.domain })
  Cookies.remove('uuid', { domain: __DEV__ ? '' : document.domain })
  localStorage.removeItem('currentRobotId')
  localStorage.removeItem('remoteRobot')
  localStorage.removeItem('call_id')
  localStorage.removeItem('top_qa')

  // localStorage.clear()
  window.location.reload()

  // dispatch({type:INIT_ROBOTS_DATA});
  // return dispatch({
  //     type: RESET_LOGIN,
  // })
}

/**
 * Redux action: 更新当前用户信息
 * @param {Object} profile
 */
export const updateUserProfile =
  (profile = {}) =>
  (dispatch) =>
    dispatch({
      type: UPDATE_USER_PROFILE,
      profile,
    })

export const updateCorpProfile =
  (corp = {}) =>
  (dispatch) =>
    dispatch({
      type: UPDATE_CORP_PROFILE,
      corp,
    })

// async example
// export const login = (mobile) => dispatch =>
//     getLoginCode(mobile).then((res) => {
//         dispatch({
//             type: USER_LOGIN,
//             data: {
//                 token: res.token
//             },
//         })
//     })

//更改机器人姓名
export const changeRobotName = (data) => {
  return request('/api/control/modify_robot_profile', {
    method: 'post',
    data: data,
  })
}

//获取企业配置状态统计
export const getConfigStatus = (data) => {
  return request('/api/control/config_status_statistics', {
    method: 'GET',
    data,
  })
}
