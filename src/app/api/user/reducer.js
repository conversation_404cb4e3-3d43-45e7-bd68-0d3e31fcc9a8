import {
  GET_LOGIN_CODE,
  LOGIN_BY_CODE,
  MODIFY_USER,
  LOGIN_BY_PASSWORD,
  GET_RESET_CODE,
  CHECK_RESET_CODE,
  RESET_PASSWORD,
  RESTORE_LOGIN,
  RESET_LOGIN,
  UPDATE_USER_PROFILE,
  UPDATE_CORP_PROFILE,
  GET_USER_PROFILE,
  SET_LANG_DATA,
  SET_CUR_LANG,
} from '@redux/action-types'

const initialState = {
  logged: false,
  detail: {},
  langData: [],
  langDataActive: [],
  curLang: {},
  userLocaleId: '',
}

export default function (state = initialState, { type, ...obj }) {
  switch (type) {
    case GET_LOGIN_CODE: {
      if (__DEV__) console.log(obj)
      return Object.assign({}, state, obj.data)
    }

    case LOGIN_BY_CODE: {
      return Object.assign({}, state, obj.data)
    }

    case MODIFY_USER: {
      return Object.assign({}, state, obj.data)
    }

    case LOGIN_BY_PASSWORD: {
      return Object.assign({}, state, obj.data)
    }

    case GET_RESET_CODE: {
      return Object.assign({}, state, obj.data)
    }

    case CHECK_RESET_CODE: {
      return Object.assign({}, state, obj.data)
    }

    case RESET_PASSWORD: {
      return Object.assign({}, state, obj.data)
    }

    case GET_USER_PROFILE: {
      return Object.assign({}, state, obj.data)
    }

    case RESET_LOGIN: {
      return initialState
    }

    case RESTORE_LOGIN: {
      return Object.assign({}, state, obj, {
        logged: true,
      })
    }

    case UPDATE_USER_PROFILE: {
      return Object.assign({}, state, {
        detail: Object.assign({}, state.detail, obj.profile),
      })
    }
    case UPDATE_CORP_PROFILE: {
      return Object.assign({}, state, {
        corp: Object.assign({}, obj.corp),
      })
    }
    case SET_LANG_DATA: {
      return Object.assign({}, state, obj.data)
    }
    case SET_CUR_LANG: {
      return Object.assign({}, state, obj.data)
    }
  }

  return state
}
