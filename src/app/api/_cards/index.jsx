import request from '@utils/request'
import { RESTORE_LOGIN, RESET_LOGIN, UPDATE_USER_PROFILE } from '@redux/action-types'

/**************************************
 * Redux actions
 *************************************/

// TODO: promise login
// export const login = (data) => dispatch => dispatch({
//     type: USER_LOGIN,
//     data,
// })

export const addCard = (data) => {
  let url = '/api/control/knowledge_card'
  return request(url, {
    method: 'post',
    data,
  })
}

// /api/control/direct_publish_module
export const getCards = (data) => {
  // '/api/control/dialog_history_list'+'?start_time=1547049600&end_time=1547650800'
  let url = '/api/control/knowledge_card'
  return request(url, {
    method: 'get',
    data,
  })
}
// /api/control/knowledge_card_details
export const getCardDetail = (data) => {
  let url = '/api/control/knowledge_card_details'
  return request(url, {
    method: 'get',
    data,
  })
}

export const deleteCard = (data) => {
  return request('/api/control/knowledge_card_delete', {
    method: 'get',
    data,
  })
}

// /api/control/dialog_history_labels
export const searchCard = (data) => {
  return request('/api/control/knowledge_card_search', {
    method: 'get',
    data,
  })
}
