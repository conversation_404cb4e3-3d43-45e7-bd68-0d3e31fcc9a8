// import {
//     // INIT_LIST
// } from '@redux/action-types'

import {} from '@redux/action-types'
// import getStore from '@utils/store'
import request from '@utils/request'
/**
 * Redux action: 示例
 * @param {String} arg1
 */
// export const sample = (arg1) => dispatch => dispatch({
//     type: INIT_ROBOTS,
//     data: arg1
// })

//获取访客数据列表
export const getVisitorListData = () => {
  return request('/api/user/company_visitor_list', {
    method: 'GET',
    data: {
      page: 1,
      size: 12,
    },
  })
}

//获取游客搜索列表
export const getVisitorSearchList = (data) => {
  return request('/api/user/company_visitor_search', {
    method: 'post',
    data,
  })
}

//删除游客记录
export const getDelTourist = (data) => {
  return request('/capi/v1/corp/delete_guest_person', {
    method: 'get',
    data,
  })
}

//获取访客详情
export const getVisitorInfo = (id) => {
  return request('/api/user/visit_info', {
    method: 'get',
    data: {
      task_id: id,
    },
  })
}

//提交访客详情编辑的表单
export const postEditVisitorInfo = (id) => {
  return request('/api/user/visit_info', {
    method: 'get',
    data: {
      task_id: id,
    },
  })
}

//删除用户
export const getDelUser = (data) => {
  return request('/capi/v1/corp/delete_user', {
    method: 'post',
    data,
  })
}

//修改游客信息
export const editTouristInfo = (data) => {
  return request('/capi/v1/corp/modify_guest_person', {
    method: 'post',
    data: data,
  })
}

//调整游客为VIP
export const setTouristToVIP = (data) => {
  return request('/api/user/promoted_to_vip', {
    method: 'post',
    data: data,
  })
}
