import { INIT_LIST, GET_VISITORS_OBJ } from '@redux/action-types'

const initialState = {
  // INIT_LIST
  list: {},
}

export default function (
  state = initialState,

  { type, ...obj },
) {
  switch (type) {
    /*
        case SAMPLE: {
            return Object.assign({}, state, data)
        }
        */
    case INIT_LIST: {
      return Object.assign({}, state, {
        list: Object.assign({}, state, obj.data),
      })
    }
    // case GET_VISITORS_OBJ: {
    //     return Object.assign({}, state, {
    //         list: Object.assign({}, state, obj.data)
    //     })
    // }
    case GET_VISITORS_OBJ: {
      return Object.assign({}, state, obj.data)
    }
  }

  return state
}
