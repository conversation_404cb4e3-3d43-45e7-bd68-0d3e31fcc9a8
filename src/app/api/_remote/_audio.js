//请求麦克风
navigator.getUserMedia =
  navigator.mediaDevices.getUserMedia ||
  navigator.getUserMedia ||
  navigator.webkitGetUserMedia ||
  navigator.mozGetUserMedia ||
  navigator.msGetUserMedia

let Recorder = function (stream) {
  this.stream = stream
  this.audioContext = null
  this.mediaNode = null
  this.jsNode = null

  //开始录音
  this.beginRecord = (stream) => {
    this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
    this.mediaNode = this.audioContext.createMediaStreamSource(stream)
    // 创建一个jsNode
    this.jsNode = createJSNode(this.audioContext)
    // 需要连到扬声器消费掉outputBuffer，process回调才能触发
    // 并且由于不给outputBuffer设置内容，所以扬声器不会播放出声音
    this.jsNode.connect(this.audioContext.destination)
    this.jsNode.onaudioprocess = onAudioProcess
    // 把mediaNode连接到jsNode
    this.mediaNode.connect(this.jsNode)
  }
  // this.close = () => {
  //     this.audioContext.close();   //关闭AudioContext否则录音多次会报错。
  // }

  //停止录音
  this.close = () => {
    this.stream.getAudioTracks()[0].stop()
    this.mediaNode.disconnect()
    this.jsNode.disconnect()
    console.log(leftDataList, rightDataList)
    let leftData = mergeArray(leftDataList),
      rightData = mergeArray(rightDataList)
    let allData = interleaveLeftAndRight(leftData, rightData) // 交叉合并左右声道的数据
    let wavBuffer = createWavFile(allData)
    return wavBuffer
  }
}

//获取录音机
Recorder.get = (callback) => {
  window.navigator.mediaDevices
    .getUserMedia({
      audio: {
        sampleRate: 44100, // 采样率
        channelCount: 2, // 声道
        volume: 1.0, // 音量
      },
    })
    .then((mediaStream) => {
      console.log(mediaStream)
      let rec = new Recorder(mediaStream)
      callback(rec)
    })
    .catch((err) => {
      // 如果用户电脑没有麦克风设备或者用户拒绝了，或者连接出问题了等
      // 这里都会抛异常，并且通过err.name可以知道是哪种类型的错误
      console.error(err)
    })
}

function createJSNode(audioContext) {
  const BUFFER_SIZE = 4096
  const INPUT_CHANNEL_COUNT = 2
  const OUTPUT_CHANNEL_COUNT = 2
  // createJavaScriptNode已被废弃
  let creator = audioContext.createScriptProcessor || audioContext.createJavaScriptNode
  creator = creator.bind(audioContext)
  return creator(BUFFER_SIZE, INPUT_CHANNEL_COUNT, OUTPUT_CHANNEL_COUNT)
}

let leftDataList = [],
  rightDataList = []
function onAudioProcess(event) {
  let audioBuffer = event.inputBuffer
  let leftChannelData = audioBuffer.getChannelData(0),
    rightChannelData = audioBuffer.getChannelData(1)
  // 需要克隆一下
  leftDataList.push(leftChannelData.slice(0))
  rightDataList.push(rightChannelData.slice(0))
}

function mergeArray(list) {
  let length = list.length * list[0].length
  let data = new Float32Array(length),
    offset = 0
  for (let i = 0; i < list.length; i++) {
    data.set(list[i], offset)
    offset += list[i].length
  }
  return data
}

// 交叉合并左右声道的数据
function interleaveLeftAndRight(left, right) {
  let totalLength = left.length + right.length
  let data = new Float32Array(totalLength)
  for (let i = 0; i < left.length; i++) {
    let k = i * 2
    data[k] = left[i]
    data[k + 1] = right[i]
  }
  return data
}

function createWavFile(audioData) {
  const WAV_HEAD_SIZE = 44
  let buffer = new ArrayBuffer(audioData.length + WAV_HEAD_SIZE),
    // 需要用一个view来操控buffer
    view = new DataView(buffer)
  // 写入wav头部信息
  let writeUTFBytes = function (offset, string) {
    let lng = string.length
    for (let i = 0; i < lng; i++) {
      view.setUint8(offset + i, string.charCodeAt(i))
    }
  }
  // RIFF chunk descriptor/identifier
  writeUTFBytes(0, 'RIFF')
  // RIFF chunk length
  view.setUint32(4, 44 + audioData.length * 2, true)
  // RIFF type
  writeUTFBytes(8, 'WAVE')
  // format chunk identifier
  // FMT sub-chunk
  writeUTFBytes(12, 'fmt ')
  // format chunk length
  view.setUint32(16, 16, true)
  // sample format (raw)
  view.setUint16(20, 1, true)
  // stereo (2 channels)
  view.setUint16(22, 2, true)
  // sample rate
  view.setUint32(24, 44100, true)
  // byte rate (sample rate * block align)
  view.setUint32(28, 44100 * 2, true)
  // block align (channel count * bytes per sample)
  view.setUint16(32, 2 * 2, true)
  // bits per sample
  view.setUint16(34, 16, true)
  // data sub-chunk
  // data chunk identifier
  writeUTFBytes(36, 'data')
  // data chunk length
  view.setUint32(40, audioData.length * 2, true)

  // 写入PCM数据
  let length = audioData.length
  let index = 44
  let volume = 1
  for (let i = 0; i < length; i++) {
    view.setInt16(index, audioData[i] * (0x7fff * volume), true)
    index += 2
  }
  return buffer
}

export default Recorder
