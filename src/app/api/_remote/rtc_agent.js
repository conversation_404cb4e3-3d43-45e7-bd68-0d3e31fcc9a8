import request from '@utils/request'
import AgoraRTC from 'agora-rtc-sdk'
var heart = '', // 判断心跳变量
  // 房间信息
  roomInfo = {
    uid: 0,
    channelName: '',
  }

// 事件
var av_event = {
  onInvite: function () {}, // 来电
  onConnect: function () {}, // 接通
  onDisconnect: function () {}, // 挂断
}

function av_register(options) {
  let url = '/vapi/v2/conf/register'
  if (options.liveme) {
    url = '/vapi/v3/conf/register'
  }
  //'/vapi/v2/conf/register'/
  //post  nodata
  console.log('注册请求')
  request(url, {
    method: 'GET',
    data: {
      // call_uid: options.data.call_uid
    },
  })
    .then((res) => {
      console.log('注册成功', res)
      options.success && options.success(res)
    })
    .catch((err) => {
      console.log('注册失败：', err)
      options.fail && options.fail(err)
    })
}

function av_invite(options) {
  let url = '/vapi/v2/conf/invite'
  if (options.liveme) {
    url = '/vapi/v3/conf/invite'
  }
  // '/vapi/v2/conf/invite'
  console.log('监听invite')
  request(url, {
    method: 'GET',
    data: {
      called_uid: options.data.called_uid,
      invite_type: options.data.invite_type,
    },
  })
    .then((res) => {
      console.log('invite成功', res)
      options.success && options.success(res)
    })
    .catch((err) => {
      console.log('invite失败', err)
      options.fail && options.fail(err)
    })
}

/**
 * [av_setListener 设置监听事件]
 * @param {options}
 *  onInvite,		  // 来电
 *  onRinging,		// 振铃
 *  onAnswer,		  // 应答
 *  onConnect,    // 接通
 *  onDisconnect, // 挂断
 *  onRecvMsg,		// 收到消息
 *  onKicked      // 被踢
 */
// function av_setListener(options) {
//   if (!options) { console.log('av_setListener参数错误', options); return; }
//   av_event.onInvite = options.onInvite || function () { };		 // 来电
//   av_event.onConnect = options.onConnect || function () { };  // 接通
//   av_event.onDisconnect = options.onDisconnect || function () { }; // 挂断
// }

/**
 * [av_heartbeat 心跳请求]
 * @param {options}
 *   success: 成功回调
 *   fail: 失败回调
 */
// function av_heartbeat(options) {
//   console.log('心跳请求');
//   request({
//     url: 'agent_heartbeat',
//     data: {
//       uid: accountInfo.uid,
//       token: accountInfo.token,
//       call_id: roomInfo.roomID,
//       hb_type: options.type
//     },
//     success: function (ret) {
//       if (ret.ret != "0") {
//         console.log('心跳失败：', ret);
//         return;
//       }
// 			console.log('心跳成功', ret);
// 			options.success(parseInt(ret.data.hb_interval))
//       // return parseInt(ret.data.hb_interval);
//     },
//     fail: function (ret) {
//       console.log('心跳失败：', ret);
//     }
//   });
// }

/**
 * [av_connect 连接已建立]
 * @param {options}
 *   uid       : 本机uid,
 *   token     : 当前登录会话 token
 *   call_id   : 现有的call_id. 若无可传空串或不传. 用于邀请第三方加入
 *   success: 成功回调
 *   fail: 失败回调
 */
// function av_connect(options) {
//   console.log('通话建立请求');
//   request({
//     url: 'agent_call_connect',
//     data: {
//       uid: options.uid,
//       token: options.token,
//       call_id: roomInfo.roomID
//     },
//     success: function (ret) {
//       console.log(ret);
//       if (ret.ret != "0") {
//         console.log('通话建立失败：', ret);
//         options.fail && options.fail(ret);
//         return;
//       }
//       console.log('通话建立成功', ret);
// //    var data = ret.data.data;
//       // roomInfo.state   =  data.state;
//       options.success && options.success(roomInfo);
//     },
//     fail: function (ret) {
//       console.log('通话建立失败：', ret);
//       options.fail && options.fail(ret);
//     }
//   });
// }

/**
 * [av_disconnect 连接已断开]
 * @param {options}
 *   uid       : 本机uid,
 *   token     : 当前登录会话 token
 *   call_id   : 现有的call_id. 若无可传空串或不传. 用于邀请第三方加入
 *   reason    : 断开原因.
 *   success: 成功回调
 *   fail: 失败回调
 */
// function av_disconnect(options) {
//   console.log('挂断请求');
//   request({
//     url: 'agent_call_disconnect',
//     data: {
//       uid: options.uid,
//       token: options.token,
//       call_id: roomInfo.roomID,
//       reason: options.reason
//     },
//     success: function (ret) {
//       if (ret.ret != "0") {
//         console.log('挂断失败：', ret);
//         options.fail && options.fail(ret);
//         return;
//       }
//       console.log('挂断成功', ret);

//       options.success && options.success();
//     },
//     fail: function (ret) {
//       console.log('挂断失败：', ret);
//       options.fail && options.fail(ret);
//     }
//   });
// }

var rtc = {
  av_register: av_register,
  av_invite: av_invite,
  // av_heartbeat: av_heartbeat,
  // av_connect: av_connect,
  // av_disconnect: av_disconnect,
  // av_setListener: av_setListener
}

export default rtc
