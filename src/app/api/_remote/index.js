import request from '@utils/request'

//获取机器人信息
export const getRobotInfo = (data) => {
  return request('/api/control/robot_statistics', {
    method: 'GET',
    data: {
      robot_id: data,
    },
  })
}
//获取地图信息
export const getRobotMapInfo = (data) => {
  return request('/api/control/robot_map_pgm', {
    method: 'GET',
    data: {
      robot_id: data,
    },
  })
}
// //获取地图pgm
// export const fetchPgmMap = (url, data) => {
//     return request(url, {
//         method: 'GET',
//         data: data
//     })
// }

//文字上屏相关
//获取robot_id
export const captureSwitch = (data) => {
  return request('/api/screen/capture_switch', {
    method: 'post',
    data: data,
  })
}
//实时上屏消息心跳
export const onScreenHeartbeat = (data) => {
  return request('/api/screen/capture_alive', {
    method: 'post',
    data: data,
  })
}
//获取asr接口token
export const getAsrToken = (data) => {
  return request('/api/screen/speech_token', {
    method: 'post',
    data: data,
  })
}
//获取机器人截屏
export const getRobotScreen = (data) => {
  return request('/capi/v1/corp/robot_capture_screen', {
    method: 'GET',
    data,
  })
}

//远程监控相关
//获取视频监控token
export const getAgoraToken = (data) => {
  return request('/vapi/v2/conf/agora_token', {
    method: 'get',
    data: data,
  })
}
//视频监控心跳
export const monitorHeartbeat = (data, isliveme) => {
  let url = isliveme ? '/vapi/v3/conf/heartbeat' : '/vapi/v2/conf/heartbeat'
  //'/vapi/v2/conf/heartbeat'
  return request(url, {
    method: 'post',
    data: data,
  })
}
//发送disconnet事件
export const monitorDisconnet = (data, isliveme) => {
  let url = isliveme ? '/vapi/v3/conf/disconnect' : '/vapi/v2/conf/disconnect'
  //'/vapi/v2/conf/disconnect'
  return request(url, {
    method: 'post',
    data: data,
  })
}
// /vapi/v1/conf/robot_appinfo 获取机器人
export const get_robot_appinfo = (data) => {
  return request('/vapi/v1/conf/robot_appinfo', {
    method: 'get',
    data: data,
  })
}
