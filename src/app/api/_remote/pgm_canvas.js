var PgmCanvas = {
  arr: ['#F8FAFA', '#C6EDFF', '#72C8F4'],
  ch: 0,
  ox: 2,
  oy: 3,
  idNum: '',
  dotNaV_x: 0,
  dotNaV_y: 0,
  robot: {
    addressX: 0,
    addressY: 0,
  },
  canvas: null,
  _offScreenDrawPgm: function (data, canvas) {
    var offscreenCanvas = document.createElement('canvas'),
      offscreenContext = offscreenCanvas.getContext('2d')
    this.offscreenCanvas = offscreenCanvas
    this.offscreenContext = offscreenContext
    this.canvas = canvas
    this.ctx = this.canvas.getContext('2d')
    var header = this._findPgmHeader(data)
    if (!header['size']) {
      return
    }
    var boom = data.slice(header.size)
    // console.log(boom.length, 'drawPgm')
    // 画布尺寸
    this.canvas.width = header.width
    this.canvas.height = header.height
    this.offscreenCanvas.width = header.width
    this.offscreenCanvas.height = header.height

    for (var i = 0; i < boom.length; i++) {
      if (this._getColor(boom[i]) != '#F8FAFA') {
        this._offScreenDraw(this._getColor(boom[i]), i)
      } else {
        var qrr = []
        if (i % this.canvas.width == 0 && i != 0) {
          qrr.push(i)
          if (this.ch <= this.canvas.height - 1) {
            this.ch = this.ch + qrr.length
          } else {
            this.ch = this.ch + qrr.length - (this.canvas.height - 1)
          }
        }
      }
    }
    var w = canvas.width,
      h = canvas.height

    this.ctx.drawImage(
      offscreenCanvas,
      0,
      0,
      offscreenCanvas.width,
      offscreenCanvas.height,
      -w / 2 + w / 2,
      -h / 2 + h / 2,
      w,
      h,
    )
  },
  drawLinePGM: function (pgmdataArray, canvas) {
    var pgmdata = pgmdataArray.buffer
    this.canvas = canvas
    //const colors = ['#72C8F4', '#F8FAFA', '#C6EDFF']  // 0, other, 255
    function parsePGMFile(pgmdata) {
      var pgm = {}
      var data = new Uint8Array(pgmdata)
      var s = 0,
        ftype = [],
        width = [],
        height = [],
        colors = [],
        size = []
      while (data[s] != 10 && s < 40) ftype.push(String.fromCharCode(data[s++]))
      pgm['ftype'] = ftype.join('')
      s++
      while (data[s] != 32 && s < 40) width.push(String.fromCharCode(data[s++]))
      pgm['width'] = width.join('')
      s++
      while (data[s] != 10 && s < 40) height.push(String.fromCharCode(data[s++]))
      pgm['height'] = height.join('')
      s++
      while (data[s] != 10 && s < 40) colors.push(String.fromCharCode(data[s++]))
      pgm['colors'] = colors.join('')
      s++
      pgm['header_size'] = s
      pgm['data_pos'] = s
      pgm['data_size'] = pgm['width'] * pgm['height']
      pgm['data'] = data
      pgm['tail_pos'] = pgm['data_pos'] + pgm['data_size']
      pgm['tail_size'] = data.length - pgm['header_size'] - pgm['data_size']

      // assert(pgm['ftype'] == 'P5');
      // assert(pgm['colors'] == 255);
      // assert(pgm['tail_size'] == 16);

      let resolution = new Float64Array(pgmdata.slice(pgm['tail_pos'], pgm['tail_pos'] + 8))[0]
      let origin_x = new Float32Array(pgmdata.slice(pgm['tail_pos'] + 8, pgm['tail_pos'] + 12))[0]
      let origin_y = new Float32Array(pgmdata.slice(pgm['tail_pos'] + 12, pgm['tail_pos'] + 16))[0]

      return pgm
    }
    function toColorPgm(c) {
      // if (typeof (c) != 'number') return '#0';
      // if (c == 0) return '#000000';
      // let a = c.toString(16);
      // if (a.length == 1) a = '0' + a;
      //return '#' + a + a + a;

      return PgmCanvas._getColor(c)
    }
    function toColorMap(c) {
      //if (typeof (c) != 'number') return '#0';
      // let colors = ["#EFF1F2", "#FFFFFF", "#E1E7EA", "#99A3A8"]; // 254 0 250 other
      // let colors = ["#EFF1F2", "#E8F6FD", "#72C8F4", "#99A3A8"]; // 254 0 250 other
      // let colors = ["#969696", "#FFFFFF", "#000000", "#ff0000"]; // 254 0 250 other

      // if (c == 254) return colors[0];
      // else if (c == 0) return colors[1];
      // else if (c == 250) return colors[2];
      // else return colors[3];
      return PgmCanvas._getColor(c)
    }

    let data = []
    let ftype = [],
      width = [],
      height = [],
      colors = [],
      size = []
    let s = 0
    let toColor
    if (pgmdata instanceof ArrayBuffer) {
      toColor = toColorPgm
      data = new Uint8Array(pgmdata)

      var pgm = parsePGMFile(pgmdata)

      while (data[s] != 10 && s <= 50) ftype.push(String.fromCharCode(data[s++]))
      s++
      while (data[s] != 32 && s <= 50) width.push(String.fromCharCode(data[s++]))
      s++
      while (data[s] != 10 && s <= 50) height.push(String.fromCharCode(data[s++]))
      s++
      while (data[s] != 10 && s <= 50) colors.push(String.fromCharCode(data[s++]))
      s++

      ftype = ftype.join('')
      width = parseInt(width.join(''))
      height = parseInt(height.join(''))
      colors = colors.join('')
      size = width * height
    } else {
      toColor = toColorMap
      var res_list = getMap(pgmdata)
      width = res_list[0]
      height = res_list[1]
      ftype = ''
      colors = ''
      data = res_list[2]
      size = res_list[3]
    }

    console.log('ftype=', ftype, ',width=', width, ',height=', height, ',colors=', colors, ',size=', size)

    // var header = this._findPgmHeader(data);
    // width = header.width;
    // height = header.height;
    this.canvas.width = width
    this.canvas.height = height
    //var ctx = wx.createCanvasContext(canvasId);
    var ctx = canvas.getContext('2d')
    var fillc = data[s] // #96（150）
    // if (!this.scale) this.scale =  1.0 * (this.sysInfo.windowWidth) / width;
    // var scale = 1.0*(this.scale||0.5);
    // console.log("scale=", scale);
    // ctx.scale(scale * this.data.canvasWidth / width, scale*this.data.canvasHeight/height);
    //ctx.fillStyle = this._getColor(fillc);
    ctx.fillStyle = toColor(fillc)
    ctx.fillRect(0, 0, width, height)
    // ctx.draw();
    for (let w = 0; w <= width; w++) {
      let c = -1
      let sh = -1
      let v = -1
      for (let h = 0; h <= height; h++) {
        v = data[w * width + h + s]
        if (w * width + h >= size) break
        if (typeof v != 'number') {
          console.log('img data is not number. idx=', w * width + h + s, ',size=', size, ',v=', v)
          break
        }
        if (v != c) {
          // 遇到不同颜色了
          if (sh == -1) {
            // 记录起始点
            sh = h
            c = v
          } // 找到结束点了，处理，且记录下一个起始点
          else {
            if (c != fillc) {
              // 如果不是背景色，则画出此线条
              // ctx.setStrokeStyle(toColor(c));
              // ctx.moveTo(sh, w);
              // ctx.lineTo(h-1, w);
              // ctx.stroke();

              ctx.fillStyle = toColor(c)
              ctx.fillRect(sh, w, h - sh, 1)
              // ctx.fill();
              // console.log("fillRect(", sh, ",", w, ",", h-sh, ",", 1, "); color=", toColor(c))

              //  ctx.setStrokeStyle(toColor(c));
              //  ctx.strokeRect(sh, w, h - 1 - sh, 1);
              //  ctx.stroke();

              // console.log("moveTo(", sh, ",", w, "); lineTo(", h - 1, ",", w, "); color=", toColor(c))
            }

            sh = h
            c = v
          }
        } // 如果到行尾了，则结束此行，画出最后一段
        else {
          if (h == height - 1 && c != fillc) {
            // 如果不是背景色，则画出此线条
            // ctx.setStrokeStyle(toColor(c));
            // ctx.moveTo(sh, w);
            // ctx.lineTo(h - 1, w);
            // ctx.stroke();

            ctx.fillStyle = toColor(c)
            ctx.fillRect(sh, w, h - sh, 1)
            // ctx.fill();
            // console.log("fillRect(", sh, ",", w, ",", h-sh, ",", 1, "); color=", toColor(c))

            // ctx.setStrokeStyle(toColor(c));
            // ctx.strokeRect(sh, w, h - 1 - sh, 1);
            // ctx.stroke();
            // console.log("moveTo(", sh, ",", w, "); lineTo(", h - 1, ",", w, "); color=", toColor(c))
          }
        }
      }
      // ctx.draw(true);
      if (w * width >= size || typeof v != 'number' || v == -1) break
    }
    //    ctx.draw();
    this.pgmdata = pgmdata
    var that = this
    // ctx.draw(false, function (res) {
    //   console.log("on draw callback()");
    //   wx.canvasToTempFilePath({
    //     x: 0, y: 0, widht: width, height: height,
    //     canvasId: 'testImage',
    //     success: function (res) {
    //       console.log("filename=", res.tempFilePath);
    //       that.setData({ mapfile: res.tempFilePath, hiddencanvas: true });
    //     }
    //     , fail: function (err) {
    //       console.log(err);
    //     }
    //   });
    // });
    console.log('ctx.draw() finish')
  },
  drawPGM: function (pgmdata, canvasId) {
    //const colors = ['#72C8F4', '#F8FAFA', '#C6EDFF']  // 0, other, 255
    function parsePGMFile(pgmdata) {
      var pgm = {}
      var data = new Uint8Array(pgmdata)
      var s = 0,
        ftype = [],
        width = [],
        height = [],
        colors = [],
        size = []
      while (data[s] != 10 && s < 40) ftype.push(String.fromCharCode(data[s++]))
      pgm['ftype'] = ftype.join('')
      s++
      while (data[s] != 32 && s < 40) width.push(String.fromCharCode(data[s++]))
      pgm['width'] = width.join('')
      s++
      while (data[s] != 10 && s < 40) height.push(String.fromCharCode(data[s++]))
      pgm['height'] = height.join('')
      s++
      while (data[s] != 10 && s < 40) colors.push(String.fromCharCode(data[s++]))
      pgm['colors'] = colors.join('')
      s++
      pgm['header_size'] = s
      pgm['data_pos'] = s
      pgm['data_size'] = pgm['width'] * pgm['height']
      pgm['data'] = data
      pgm['tail_pos'] = pgm['data_pos'] + pgm['data_size']
      pgm['tail_size'] = data.length - pgm['header_size'] - pgm['data_size']

      // assert(pgm['ftype'] == 'P5');
      // assert(pgm['colors'] == 255);
      // assert(pgm['tail_size'] == 16);

      let resolution = new Float64Array(pgmdata.slice(pgm['tail_pos'], pgm['tail_pos'] + 8))[0]
      let origin_x = new Float32Array(pgmdata.slice(pgm['tail_pos'] + 8, pgm['tail_pos'] + 12))[0]
      let origin_y = new Float32Array(pgmdata.slice(pgm['tail_pos'] + 12, pgm['tail_pos'] + 16))[0]

      return pgm
    }
    function toColorPgm(c) {
      if (typeof c != 'number') return '#0'
      if (c == 0) return '#000000'
      let a = c.toString(16)
      if (a.length == 1) a = '0' + a
      return '#' + a + a + a
    }
    function toColorMap(c) {
      if (typeof c != 'number') return '#0'
      // let colors = ["#EFF1F2", "#FFFFFF", "#E1E7EA", "#99A3A8"]; // 254 0 250 other
      // let colors = ["#EFF1F2", "#E8F6FD", "#72C8F4", "#99A3A8"]; // 254 0 250 other
      // let colors = ["#969696", "#FFFFFF", "#000000", "#ff0000"]; // 254 0 250 other
      // if (c == 254) return colors[0];
      // else if (c == 0) return colors[1];
      // else if (c == 250) return colors[2];
      // else return colors[3];

      return this._getColor(c)
    }

    let data = []
    let ftype = [],
      width = [],
      height = [],
      colors = [],
      size = []
    let s = 0
    let toColor
    if (pgmdata instanceof ArrayBuffer) {
      toColor = toColorPgm
      data = new Uint8Array(pgmdata)

      var pgm = parsePGMFile(pgmdata)

      while (data[s] != 10 && s <= 50) ftype.push(String.fromCharCode(data[s++]))
      s++
      while (data[s] != 32 && s <= 50) width.push(String.fromCharCode(data[s++]))
      s++
      while (data[s] != 10 && s <= 50) height.push(String.fromCharCode(data[s++]))
      s++
      while (data[s] != 10 && s <= 50) colors.push(String.fromCharCode(data[s++]))
      s++

      ftype = ftype.join('')
      width = parseInt(width.join(''))
      height = parseInt(height.join(''))
      colors = colors.join('')
      size = width * height
    } else {
      toColor = toColorMap
      var res_list = getMap(pgmdata)
      width = res_list[0]
      height = res_list[1]
      ftype = ''
      colors = ''
      data = res_list[2]
      size = res_list[3]
    }

    console.log('ftype=', ftype, ',width=', width, ',height=', height, ',colors=', colors, ',size=', size)

    this.setData({
      canvasWidth: width,
      canvasHeight: height,
    })

    var ctx = wx.createCanvasContext(canvasId)
    var fillc = data[s] // #96（150）
    // if (!this.scale) this.scale =  1.0 * (this.sysInfo.windowWidth) / width;
    // var scale = 1.0*(this.scale||0.5);
    // console.log("scale=", scale);
    // ctx.scale(scale * this.data.canvasWidth / width, scale*this.data.canvasHeight/height);
    ctx.setFillStyle(toColor(fillc))
    ctx.fillRect(0, 0, width, height)
    // ctx.draw();
    for (let w = 0; w <= width; w++) {
      let c = -1
      let sh = -1
      let v = -1
      for (let h = 0; h <= height; h++) {
        v = data[w * width + h + s]
        if (w * width + h >= size) break
        if (typeof v != 'number') {
          console.log('img data is not number. idx=', w * width + h + s, ',size=', size, ',v=', v)
          break
        }
        if (v != c) {
          // 遇到不同颜色了
          if (sh == -1) {
            // 记录起始点
            sh = h
            c = v
          } // 找到结束点了，处理，且记录下一个起始点
          else {
            if (c != fillc) {
              // 如果不是背景色，则画出此线条
              // ctx.setStrokeStyle(toColor(c));
              // ctx.moveTo(sh, w);
              // ctx.lineTo(h-1, w);
              // ctx.stroke();

              ctx.setFillStyle(toColor(c))
              ctx.fillRect(sh, w, h - sh, 1)
              // ctx.fill();
              // console.log("fillRect(", sh, ",", w, ",", h-sh, ",", 1, "); color=", toColor(c))

              //  ctx.setStrokeStyle(toColor(c));
              //  ctx.strokeRect(sh, w, h - 1 - sh, 1);
              //  ctx.stroke();

              // console.log("moveTo(", sh, ",", w, "); lineTo(", h - 1, ",", w, "); color=", toColor(c))
            }

            sh = h
            c = v
          }
        } // 如果到行尾了，则结束此行，画出最后一段
        else {
          if (h == height - 1 && c != fillc) {
            // 如果不是背景色，则画出此线条
            // ctx.setStrokeStyle(toColor(c));
            // ctx.moveTo(sh, w);
            // ctx.lineTo(h - 1, w);
            // ctx.stroke();

            ctx.setFillStyle(toColor(c))
            ctx.fillRect(sh, w, h - sh, 1)
            // ctx.fill();
            // console.log("fillRect(", sh, ",", w, ",", h-sh, ",", 1, "); color=", toColor(c))

            // ctx.setStrokeStyle(toColor(c));
            // ctx.strokeRect(sh, w, h - 1 - sh, 1);
            // ctx.stroke();
            // console.log("moveTo(", sh, ",", w, "); lineTo(", h - 1, ",", w, "); color=", toColor(c))
          }
        }
      }
      // ctx.draw(true);
      if (w * width >= size || typeof v != 'number' || v == -1) break
    }
    //    ctx.draw();
    this.pgmdata = pgmdata
    var that = this
    ctx.draw(false, function (res) {
      console.log('on draw callback()')
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        widht: width,
        height: height,
        canvasId: 'testImage',
        success: function (res) {
          console.log('filename=', res.tempFilePath)
          that.setData({ mapfile: res.tempFilePath, hiddencanvas: true })
        },
        fail: function (err) {
          console.log(err)
        },
      })
    })
    console.log('ctx.draw() finish')
  },
  drawPgm: function (data, canvas) {
    this.ch = 0
    // console.log(data)
    this.canvas = canvas
    this.ctx = this.canvas.getContext('2d')
    var header = this._findPgmHeader(data)
    if (!header['size']) {
      return
    }
    var boom = data.slice(header.size)
    // console.log(boom.length, 'drawPgm')
    // 画布尺寸
    this.canvas.width = header.width
    this.canvas.height = header.height

    for (var i = 0; i < boom.length; i++) {
      if (this._getColor(boom[i]) != '#F8FAFA') {
        this._draw(this._getColor(boom[i]), i)
      } else {
        var qrr = []
        if (i % this.canvas.width == 0 && i != 0) {
          qrr.push(i)
          if (this.ch <= this.canvas.height - 1) {
            this.ch = this.ch + qrr.length
          } else {
            this.ch = this.ch + qrr.length - (this.canvas.height - 1)
          }
        }
      }
    }
    // postData = data;
  },
  canvas2Pgm: function (data, canvas) {
    var header = this._findPgmHeader(data)
    var footer = data.slice(data.length - 16, data.length)
    if (!header['size']) {
      return
    }
    var boom = []
    var imageData = canvas.getContext('2d').getImageData(0, 0, canvas.width, canvas.height)
    // for (var i = 0; i < imageData.data.length; i += 4) {
    //   var color = this._rgbToHex('('+imageData.data[i]+','+imageData.data[i+1]+','+imageData.data[i+2]+')')
    //   if (color == '#f8fafa') {
    //     boom.push(150);
    //   } else if (color == '#c6edff') {
    //     boom.push(255);
    //   } else {
    //     boom.push(0);
    //   }
    // }
    for (var m = 0; m < imageData.data.length / canvas.width / 4; m++) {
      var start = m * canvas.width * 4
      for (var n = start + canvas.width * 4 - 1; n >= start; n -= 4) {
        var color = this._rgbToHex(
          '(' + imageData.data[n - 3] + ',' + imageData.data[n - 2] + ',' + imageData.data[n - 1] + ')',
        )
        if (color == '#f8fafa') {
          boom.push(150)
        } else if (color == '#c6edff') {
          boom.push(255)
        } else {
          boom.push(0)
        }
      }
    }
    // console.log(boom.length)
    var postData = data.slice(0, header.size).concat(boom).concat(footer)
    // console.log(postData)
    var ar = new Uint8Array(postData)
    var blob = new Blob([ar.buffer], {
      type: 'image/pgm',
    })
    // var reader = new FileReader();

    return blob
  },
  canvas2Zip: function (data, canvas) {
    var header = this._findPgmHeader(data)
    var footer = data.slice(data.length - 16, data.length)
    if (!header['size']) {
      return
    }
    var boom = []
    var imageData = canvas.getContext('2d').getImageData(0, 0, canvas.width, canvas.height)
    // for (var i = 0; i < imageData.data.length; i += 4) {
    //   var color = this._rgbToHex('('+imageData.data[i]+','+imageData.data[i+1]+','+imageData.data[i+2]+')')
    //   if (color == '#f8fafa') {
    //     boom.push(150);
    //   } else if (color == '#c6edff') {
    //     boom.push(255);
    //   } else {
    //     boom.push(0);
    //   }
    // }
    // for (var m = 0; m < imageData.data.length/canvas.width/4; m++) {
    //   var start = m*canvas.width*4
    //   for (var n = start+canvas.width*4-1; n >= start; n -= 4) {
    //     var color = this._rgbToHex('('+imageData.data[n-3]+','+imageData.data[n-2]+','+imageData.data[n-1]+')')
    //     if (color == '#f8fafa') {
    //       boom.push(150);
    //     } else if (color == '#c6edff') {
    //       boom.push(255);
    //     } else {
    //       boom.push(0);
    //     }
    //   };
    // };

    for (var m = 0; m < imageData.data.length / canvas.width / 4; m++) {
      var start = m * canvas.width * 4
      for (var n = start; n < start + canvas.width * 4; n += 4) {
        //       for (var n = start+canvas.width*4-1; n >= start; n -= 4) {
        var color = this._rgbToHex(
          '(' + imageData.data[n] + ',' + imageData.data[n + 1] + ',' + imageData.data[n + 2] + ')',
        )
        //         var color = this._rgbToHex('('+imageData.data[n-3]+','+imageData.data[n-2]+','+imageData.data[n-1]+')')
        //      if (color == '#f8fafa') {	//灰色
        //        boom.push(150);
        //      } else if (color == '#c6edff') {	//浅蓝色
        //        boom.push(255);
        //      } else if (color == '#fd713c' || color == '#72c8f4' || color == '#ff713c' || color == '#ddbbb1' || color == '#fbc3ae') {	//深蓝色
        //        boom.push(0);
        //      } else {
        //        boom.push(255);
        //      }
        if (color == '#f8fafa' || color == '#000000') {
          //背景浅灰色
          boom.push(150)
          //      } else if (color == '#c6edff' || color == '#c6ecff' || color == '#c5edff' || color == '#c5ecff') {	//可行区浅蓝色
        } else if (color >= '#c1ecff' && color <= '#c9eeff') {
          boom.push(255)
        } else {
          boom.push(0)
        }
      }
    }
    // console.log(boom.length)
    var postData = data.slice(0, header.size).concat(boom).concat(footer)

    var zip = new Zlib.Zip()
    zip.addFile(postData, {
      filename: stringToByteArray('map.pgm'),
      compressionMethod: Zlib.Zip.CompressionMethod.DEFLATE,
    })
    var compressed = zip.compress()
    function stringToByteArray(str) {
      var array = new (window.Uint8Array !== void 0 ? Uint8Array : Array)(str.length)
      var i
      var il

      for (i = 0, il = str.length; i < il; ++i) {
        array[i] = str.charCodeAt(i) & 0xff
      }

      return array
    }

    var blob = new Blob([compressed.buffer], {
      type: 'application/zip',
    })
    // var reader = new FileReader();
    var zipData = { md5: md5(compressed), file: blob }
    return zipData
  },

  _findPgmHeader: function (data) {
    var line = 0
    var lineString = ''
    var header = {}
    for (var i = 0; i < 40; i++) {
      if (data[i] == 10) {
        line++
        if (line == 3) {
          header['size'] = i + 1
          header['bin'] = data.slice(0, header.size)
          break
        } else if (line == 2) {
          var ar = lineString.split(' ')
          header['width'] = parseInt(ar[0])
          header['height'] = parseInt(ar[1])
        }
        lineString = ''
      } else {
        lineString += String.fromCharCode(data[i])
      }
    }
    return header
  },
  _getColor: function (value) {
    // if (value > 250) {
    //     return arr[1];
    // } else if (value >= 0 && value <= 150) {
    //     return arr[2];
    // } else if (value > 150 && value <= 250) {
    //     return arr[0];
    // }
    // return "#99A3A8";

    if (value == 255) {
      return this.arr[1]
    } else if (value == 0) {
      return this.arr[2]
    }
    return this.arr[0]
  },
  _rgbToHex: function (rgb) {
    var color = rgb.toString().match(/\d+/g) // 把 x,y,z 推送到 color 数组里
    var hex = '#'

    for (var i = 0; i < 3; i++) {
      hex += ('0' + Number(color[i]).toString(16)).slice(-2)
    }
    return hex
  },
  _draw: function (color, i) {
    var qrr = []
    if (i % this.canvas.width == 0 && i != 0) {
      qrr.push(i)
      if (this.ch <= this.canvas.height - 1) {
        this.ch = this.ch + qrr.length
      } else {
        this.ch = this.ch + qrr.length - (this.canvas.height - 1)
      }
    }
    this.ctx.fillStyle = color
    //this.ctx.fillRect(this.canvas.width - i % this.canvas.width, this.ch, 1, 1);
    this.ctx.fillRect(i % this.canvas.width, this.ch, 1, 1)
  },
  _offScreenDraw: function (color, i) {
    //offscreenContext
    var qrr = []
    if (i % this.canvas.width == 0 && i != 0) {
      qrr.push(i)
      if (this.ch <= this.canvas.height - 1) {
        this.ch = this.ch + qrr.length
      } else {
        this.ch = this.ch + qrr.length - (this.canvas.height - 1)
      }
    }
    this.offscreenContext.fillStyle = color
    //this.ctx.fillRect(this.canvas.width - i % this.canvas.width, this.ch, 1, 1);
    this.offscreenContext.fillRect(i % this.canvas.width, this.ch, 1, 1)
  },
}

export default PgmCanvas
