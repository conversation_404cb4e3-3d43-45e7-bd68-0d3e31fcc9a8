export default function (data) {
  let boom = data.slice(data.length - 16)
  const dv = new DataView(boom.buffer)
  this.tailbin = boom
  this.res = dv.getFloat64(0, true)
  this.px = dv.getFloat32(8, true)
  this.py = dv.getFloat32(12, true)
  this.width = 0
  this.height = 0
  //Math.round((item.top/20)*10000)/10000
  //  canvas 位置到 pgm 地图坐标转换
  ;(this.pixX2dx = function (px) {
    return px * this.res + this.px
  }),
    //pgm 地图坐标转换 为canvas 位置
    (this.dx2pixX = function (px) {
      return (px - this.px) / this.res
    }),
    (this.pixY2dy = function (py) {
      return py * this.res + this.py
    }),
    //pgm 地图坐标转换 为canvas 位置
    (this.dy2PixY = function (dy) {
      return (dy - this.py) / this.res
    })
}
