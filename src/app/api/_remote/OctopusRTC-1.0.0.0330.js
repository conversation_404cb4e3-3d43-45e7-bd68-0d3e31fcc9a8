/*! For license information please see OctopusRTC-1.0.0.js.LICENSE */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.OctopusRTC=t():e.OctopusRTC=t()}(window,(function(){return function(e){var t={};function r(i){if(t[i])return t[i].exports;var n=t[i]={i:i,l:!1,exports:{}};return e[i].call(n.exports,n,n.exports,r),n.l=!0,n.exports}return r.m=e,r.c=t,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(i,n,function(t){return e[t]}.bind(null,n));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=10)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=1,n=function(){function e(e){this.debug=null,this.info=null,this.warn=null,this.error=null,void 0!==e&&void 0!==e.level&&(i=e.level),this.debug=this.proxyConsole.bind(this,"log"),this.info=this.proxyConsole.bind(this,"info"),this.warn=this.proxyConsole.bind(this,"warn"),this.error=this.proxyConsole.bind(this,"error")}return e.prototype.proxyConsole=function(e){if(10==i)return console.log(),void console.info();var t="",r=Array.prototype.slice.call(arguments);r.shift();for(var n=0,s=r;n<s.length;n++){var o=s[n];t+="object"==typeof o?JSON.stringify(o)+" ":o+" "}var a={time:this.dateFormat(),content:"octopusSDK:"+t};switch(e){case"info":console.info("%c"+JSON.stringify(a),"color: #311078");break;case"error":console.error(JSON.stringify(a));break;case"warn":console.warn(JSON.stringify(a));break;default:console.log(JSON.stringify(a))}},e.prototype.dateFormat=function(){var e=new Date;return e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()+"."+e.getMilliseconds()},e}();t.default=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={VENDORURL:"https://rtc-backend-orion.ksmobile.net",CENTERURL:"https://orion-rtc-center.ksmobile.net",IPURL:"https://geoip.linkv.fun/ip_geo",SDKVERSION:1010010,OS:"web",ipInfo:{iso_cc:"CN"}}},function(e,t,r){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var s=n(r(5)),o=n(r(1));t.default=i(i(i({},s.default),{USERSOURCE:"pc_center",SIGNALUSERSOURCE:"pc",bitRate:{start:900,min:600,max:1800}}),o.default)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={isObject:function(e){return"[object Object]"===Object.prototype.toString.call(e)},isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},toHump:function(e,t){return 1==t?e.replace(/(^|_)(\w)/g,(function(e,t,r){return r.toUpperCase()})):e.replace(/\_(\w)/g,(function(e,t){return t.toUpperCase()}))},formatKey:function(e,t,r){var i=this,n=t||{},s=function(t){if(o.isArray(e[t])){var s=o.toHump(t,r);n[s]=[],e[t].forEach((function(e){i.isObject(e)?n[s].push(i.formatKey(e)):n[s].push(e)}))}else if(o.isObject(e[t])){var a=o.toHump(t,r);n[a]=o.formatKey(e[t])}else{a=o.toHump(t,r);n[a]=e[t]}},o=this;for(var a in e)s(a);return n},uniqueArr:function(e,t){for(var r={},i=[],n=0;n<e.length;n++)r[t]=e[n];for(var s in r)i.push(r[s]);return i},wxResquest:function(e,t,r,i){return new Promise((function(n,s){wx.request({url:e,data:t,header:r,method:i,success:function(e){200===e.statusCode?n(e):s(e)},fail:function(e){s(e)}})}))},deepClone:function(e){var t=Array.isArray(e)?[]:{};if(e&&"object"==typeof e){var r="";for(r in e)e.hasOwnProperty(r)&&(e[r]&&"object"==typeof e[r]?t[r]=this.deepClone(e[r]):t[r]=e[r])}return t},computeAverage:function(e,t){return e.reduce((function(e,r){return e+r[t]}),0)/e.length}};t.default=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={VENDORURL:"https://qa-rtc-backend-orion.ksmobile.net",CENTERURL:"https://qa-orion-rtc-center.ksmobile.net",IPURL:"https://geoip.linkv.fun/ip_geo",APPID:"1391174876",SDKVERSION:1010010,OS:"web",ipInfo:{iso_cc:"CN"}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={CLIENTIP:"",PROTO:"https"}},function(e,t,r){var i;e.exports=function e(t,r,n){function s(a,c){if(!r[a]){if(!t[a]){if(!c&&"function"==typeof i&&i)return i(a,!0);if(o)return o(a,!0);var u=new Error("Cannot find module '"+a+"'");throw u.code="MODULE_NOT_FOUND",u}var l=r[a]={exports:{}};t[a][0].call(l.exports,(function(e){return s(t[a][1][e]||e)}),l,l.exports,e,t,r,n)}return r[a].exports}for(var o="function"==typeof i&&i,a=0;a<n.length;a++)s(n[a]);return s}({1:[function(e,t,r){"use strict";var i=Object.prototype.hasOwnProperty,n="~";function s(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,r,i,s){if("function"!=typeof r)throw new TypeError("The listener must be a function");var a=new o(r,i||e,s),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],a]:e._events[c].push(a):(e._events[c]=a,e._eventsCount++),e}function c(e,t){0==--e._eventsCount?e._events=new s:delete e._events[t]}function u(){this._events=new s,this._eventsCount=0}Object.create&&(s.prototype=Object.create(null),(new s).__proto__||(n=!1)),u.prototype.eventNames=function(){var e,t,r=[];if(0===this._eventsCount)return r;for(t in e=this._events)i.call(e,t)&&r.push(n?t.slice(1):t);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},u.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,s=r.length,o=new Array(s);i<s;i++)o[i]=r[i].fn;return o},u.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},u.prototype.emit=function(e,t,r,i,s,o){var a=n?n+e:e;if(!this._events[a])return!1;var c,u,l=this._events[a],h=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),h){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,r),!0;case 4:return l.fn.call(l.context,t,r,i),!0;case 5:return l.fn.call(l.context,t,r,i,s),!0;case 6:return l.fn.call(l.context,t,r,i,s,o),!0}for(u=1,c=new Array(h-1);u<h;u++)c[u-1]=arguments[u];l.fn.apply(l.context,c)}else{var d,p=l.length;for(u=0;u<p;u++)switch(l[u].once&&this.removeListener(e,l[u].fn,void 0,!0),h){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,t);break;case 3:l[u].fn.call(l[u].context,t,r);break;case 4:l[u].fn.call(l[u].context,t,r,i);break;default:if(!c)for(d=1,c=new Array(h-1);d<h;d++)c[d-1]=arguments[d];l[u].fn.apply(l[u].context,c)}}return!0},u.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},u.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},u.prototype.removeListener=function(e,t,r,i){var s=n?n+e:e;if(!this._events[s])return this;if(!t)return c(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||i&&!o.once||r&&o.context!==r||c(this,s);else{for(var a=0,u=[],l=o.length;a<l;a++)(o[a].fn!==t||i&&!o[a].once||r&&o[a].context!==r)&&u.push(o[a]);u.length?this._events[s]=1===u.length?u[0]:u:c(this,s)}return this},u.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&c(this,t)):(this._events=new s,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=n,u.EventEmitter=u,void 0!==t&&(t.exports=u)},{}]},{},[1])(1)},function(e,t,r){"use strict";var i=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},n=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=s(r(0)),a={},c=function(){function e(e){this._userId="",this._logger=new o.default,this._userId=e.userId}return e.enumerateDevices=function(e,t){return i(this,void 0,void 0,(function(){var r,i,s,o,a,c,u,l;return n(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,navigator.mediaDevices.enumerateDevices()];case 1:for(r=n.sent(),i=[],s=[],o=[],a=0,c=r;a<c.length;a++)switch((u=c[a]).kind){case"audioinput":i.push(u);break;case"audiooutput":s.push(u);break;case"videoinput":o.push(u)}return"function"==typeof e&&e({microphones:i,speakers:s,cameras:o}),[2,Promise.resolve({microphones:i,speakers:s,cameras:o})];case 2:return l=n.sent(),"function"==typeof t&&t(l),[2,Promise.reject(l)];case 3:return[2]}}))}))},e.prototype.startPreview=function(e,t,r,s){return i(this,void 0,void 0,(function(){var i,o;return n(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),this._logger.debug("constraints:::",t),[4,navigator.mediaDevices.getUserMedia(t)];case 1:return i=n.sent(),a["s_"+this._userId]=i,e.srcObject=i,"function"==typeof r&&r(i.id),[2,Promise.resolve(i.id)];case 2:return o=n.sent(),"function"==typeof s&&s(o),[2,Promise.reject(o)];case 3:return[2]}}))}))},e.prototype.stopPreview=function(e){return i(this,void 0,void 0,(function(){var t,r,i;return n(this,(function(n){if(!e)return[2];if(t=e.srcObject){for(r=0,i=t.getTracks();r<i.length;r++)i[r].stop();e.srcObject=null}return[2]}))}))},e.prototype.enumerateDevices=function(t,r){return i(this,void 0,void 0,(function(){return n(this,(function(i){return[2,e.enumerateDevices(t,r)]}))}))},e.prototype.publishMedia=function(e){var t=this;a["s_"+this._userId]&&a["s_"+this._userId].getTracks().forEach((function(r){e.addTrack(r,a["s_"+t._userId])}))},e.prototype.getDeviceCapabilities=function(){if(a["s_"+this._userId])return a["s_"+this._userId].getVideoTracks()[0].getCapabilities()},e.prototype.changeConstraints=function(e){var t=this;a["s_"+this._userId]&&a["s_"+this._userId].getVideoTracks()[0].applyConstraints(e).then((function(e){t._logger.debug("applyConstraint success",e)})).catch((function(e){t._logger.error("constraintChange",e)}))},e}();t.default=c},,,function(e,t,r){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},n=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},s=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var a=r(11),c=o(r(12)),u=o(r(13)),l=o(r(1)),h=o(r(4)),d=o(r(14)),p=o(r(33)),f=o(r(34)),g=o(r(35)),m=function(){function e(e){this._mode="auto",this._preferredMode="octopus",this._roomId="",this._userId="",this._logLevel=1,this._role=0,this._userName="",this._octopusConfig=null,this._octopusClient=null,this._zegoConfig=null,this._zegoClient=null,this._appId="",this._defaultOctopusConfig=i(i({},l.default),{APPID:""}),this._api=null,this._env="prod",this.onPublishStateUpdate=null,this.onPublishQualityUpdate=null,this.onPlayStateUpdate=null,this.onPlayQualityUpdate=null,this.onStreamUpdated=null,this.onDisconnect=null,this.onKickOut=null,this._userId=e.userId,void 0!==e&&(void 0!==e.mode&&(this._mode=e.mode),void 0!==e.userName&&(this._userName=e.userName),void 0!==e.logLevel&&(this._logLevel=e.logLevel),void 0!==e.env&&(this._env=e.env,"test"!=e.env&&"dev"!=e.env||(this._defaultOctopusConfig=h.default,console.log("octopusConfig:::",this._defaultOctopusConfig))),void 0!==e.appId&&(this._appId=this._defaultOctopusConfig.APPID=e.appId),void 0!==e.edgeUrl&&(this._defaultOctopusConfig.EDGEURL=e.edgeUrl)),this._api=new g.default(this._defaultOctopusConfig),this.onPublishStateUpdate=function(e,t,r){},this.onPublishQualityUpdate=function(e,t){},this.onPlayStateUpdate=function(e,t,r){},this.onPlayQualityUpdate=function(e,t){},this.onStreamUpdated=function(e,t){},this.onDisconnect=function(e){},this.onKickOut=function(e){}}return Object.defineProperty(e.prototype,"mode",{get:function(){return this._mode},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"preferredMode",{get:function(){return this._preferredMode},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"roomId",{get:function(){return this._roomId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"userId",{get:function(){return this._userId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"userName",{get:function(){return this._userName},enumerable:!0,configurable:!0}),e.prototype.init=function(){this.destory(),this.getIpInfo(),this._octopusConfig={env:this._env,userId:this._appId+"-"+this._userId,userName:this._userName,logLevel:this._logLevel,octopusConfig:this._defaultOctopusConfig},this._octopusClient=new d.default(this._octopusConfig)},e.prototype.destory=function(){this._octopusClient=null,this._zegoClient=null},e.prototype.getIpInfo=function(){return n(this,void 0,void 0,(function(){var e;return s(this,(function(t){switch(t.label){case 0:return this._api?(e=this._defaultOctopusConfig,[4,this._api.getLocalInfo()]):[2];case 1:return e.ipInfo=t.sent(),console.log("getLocalInfo::",this._defaultOctopusConfig.ipInfo),[2]}}))}))},e.prototype.getPreferredMode=function(){return n(this,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return this._api?[4,this._api.getVendor({appId:this._appId,room_id:this._roomId,sdk_version:this._defaultOctopusConfig.SDKVERSION,os:this._defaultOctopusConfig.OS})]:[2,"octopus"];case 1:return[2,e.sent().vendor]}}))}))},e.prototype.on=function(e,t,r){this._octopusClient&&this._octopusClient.on(e,t,this._octopusClient)},e.prototype.initVendor=function(){return n(this,void 0,void 0,(function(){var e;return s(this,(function(t){switch(t.label){case 0:return"octopus"!==this._mode&&"zego"!==this._mode?[3,1]:(this._preferredMode=this._mode,[3,3]);case 1:return e=this,[4,this.getPreferredMode()];case 2:e._preferredMode=t.sent(),t.label=3;case 3:return"zego"==this._preferredMode&&(this._octopusClient=null,"test"==this._env?this._zegoConfig=i(i({},u.default),{idName:this._userId,nickName:this._userName}):this._zegoConfig=i(i({},c.default),{idName:this._userId,nickName:this._userName}),this._zegoClient=new a.ZegoClient,this._zegoClient.config(this._zegoConfig)),this.callbackFunctions(),[2]}}))}))},e.prototype.login=function(e,t,r){return n(this,void 0,void 0,(function(){var i=this;return s(this,(function(o){switch(o.label){case 0:return this._role=t,this._roomId=e,[4,this.initVendor()];case 1:return o.sent(),[2,new Promise((function(e,o){return n(i,void 0,void 0,(function(){var i=this;return s(this,(function(n){return this._octopusClient?(this._octopusClient.login(this._appId+"-"+this._roomId,t,r,(function(r){var n=[];if(r){for(var s=0,o=r;s<o.length;s++){var a=o[s];n.push({streamId:a.streamId,userId:a.userId,roomId:a.roomId})}1==t&&i._api&&i._api.updateRoom({appId:i._appId,roomId:i._roomId,status:"1"}),new f.default({appId:i._appId,role:t,roomId:i._roomId}),e(n)}}),(function(e){o(e)})),[2]):(this._zegoClient&&this._zegoClient.login(this._roomId,t,r,(function(r){for(var n=[],s=0,o=r;s<o.length;s++){var a=o[s];n.push({streamId:a.stream_id,userId:a.anchor_id_name,roomId:""}),1==t&&i._api&&i._api.updateRoom({appId:i._appId,roomId:i._roomId,status:"1"})}e(n)}),(function(e){o(e)})),[2])}))}))}))]}}))}))},e.prototype.loginout=function(){1==this._role&&this._api&&this._api.updateRoom({appId:this._appId,roomId:this._roomId,status:"3"}),this._octopusClient?this._octopusClient.loginout():this._zegoClient&&this._zegoClient.logout()},e.prototype.devicesList=function(){return n(this,void 0,void 0,(function(){var e;return s(this,(function(t){switch(t.label){case 0:return e=p.default.bind,[4,navigator.mediaDevices.enumerateDevices()];case 1:return[2,new(e.apply(p.default,[void 0,t.sent()]))]}}))}))},e.prototype.getCamerasList=function(){return n(this,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,this.devicesList()];case 1:return[2,e.sent().cameras]}}))}))},e.prototype.getMicrophonesList=function(){return n(this,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,this.devicesList()];case 1:return[2,e.sent().microphones]}}))}))},e.prototype.getSpeakersList=function(){return n(this,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,this.devicesList()];case 1:return[2,e.sent().speakers]}}))}))},e.prototype.startPreview=function(e,t){return n(this,void 0,void 0,(function(){var r=this;return s(this,(function(i){return[2,new Promise((function(i,n){r._octopusClient?r._octopusClient.startPreview(e,t,(function(e){if(i(e),r._octopusClient&&t.video){var n=r._octopusClient.getDevicesSettings();if(n){var s=n.height&&n.height.max,o=t.height&&t.height;if(o&&s&&o>s){var a={height:s,width:544*s/720};r._octopusClient.changeConstrains(a)}}}}),n):r._zegoClient&&r._zegoClient.startPreview(e,t,(function(e){i(e)}),n)}))]}))}))},e.prototype.stopPreview=function(e){return n(this,void 0,void 0,(function(){return s(this,(function(t){return this._octopusClient?(this._octopusClient.stopPreview(e),[2]):(this._zegoClient&&this._zegoClient.stopPreview(e),[2])}))}))},e.prototype.startPublishingStream=function(e,t,r,i){this._octopusClient?this._octopusClient.startPublishingStream(e,t,r,i):this._zegoClient&&this._zegoClient.startPublishingStream(e,t,r,i)},e.prototype.stopPublishingStream=function(e,t){this._octopusClient?this._octopusClient.stopPublishingStream(e):this._zegoClient&&(t&&this._zegoClient.stopPreview(t),this._zegoClient.stopPublishingStream(e))},e.prototype.startPlayingStream=function(e,t,r,i){this._octopusClient?this._octopusClient.startPlayingStream(e,t,r,i):this._zegoClient&&this._zegoClient.startPlayingStream(e,t,r,i)},e.prototype.stopPlayingStream=function(e){this._octopusClient?this._octopusClient.stopPlayingStream(e):this._zegoClient&&this._zegoClient.stopPlayingStream(e)},e.prototype.updateMixStream=function(e){var t=this;return new Promise((function(r,i){t._octopusClient?t._octopusClient.updateMixStream(e,(function(e){r(e)}),(function(e){i(e)})):t._zegoClient&&t._zegoClient.updateMixStream(e,(function(e){r(e)}),(function(e){i(e)}))}))},e.prototype.changeConstrains=function(e){this._octopusClient&&this._octopusClient.changeConstrains(e)},e.prototype.muted=function(e){this._octopusClient&&this._octopusClient.muted(e)},e.prototype.callbackFunctions=function(){var e=this;if(this._octopusClient)return this._octopusClient.onPublishStateUpdate=function(t,r,i){1!=t&&1==e._role&&e._api&&e._api.updateRoom({appId:e._appId,roomId:e._roomId,status:"2"}),e.onPublishStateUpdate(t,r,i)},this._octopusClient.onPlayStateUpdate=function(t,r,i){e.onPlayStateUpdate(t,r,i)},this._octopusClient.onDisconnect=function(t){e.onDisconnect(t)},this._octopusClient.onStreamUpdated=function(t,r){e.onStreamUpdated(t,r)},void(this._octopusClient.onKickOut=function(t){e.onKickOut(t)});this._zegoClient&&(this._zegoClient.onPublishStateUpdate=function(t,r,i){1!=t&&1==e._role&&e._api&&e._api.updateRoom({appId:e._appId,roomId:e._roomId,status:"2"}),e.onPublishStateUpdate(t,r,i)},this._zegoClient.onPublishQualityUpdate=function(t,r){e.onPublishQualityUpdate(t,r)},this._zegoClient.onPlayStateUpdate=function(t,r,i){e.onPlayStateUpdate(t,r,i)},this._zegoClient.onPlayQualityUpdate=function(t,r){e.onPlayQualityUpdate(t,r)},this._zegoClient.onPlayQualityUpdate=function(t,r){e.onPlayQualityUpdate(t,r)},this._zegoClient.onDisconnect=function(t){e.onDisconnect(t)},this._zegoClient.onKickOut=function(t){e.onKickOut(t)},this._zegoClient.onStreamUpdated=function(t,r){e.onStreamUpdated(t,r)})},e}();t.default=m},function(e,t,r){"undefined"!=typeof self&&self,e.exports=function(e){var t={};function r(i){if(t[i])return t[i].exports;var n=t[i]={i:i,l:!1,exports:{}};return e[i].call(n.exports,n,n.exports,r),n.l=!0,n.exports}return r.m=e,r.c=t,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(i,n,function(t){return e[t]}.bind(null,n));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=5)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PROTO_VERSION="1.4.0",t.ROOMVERSION="V1",function(e){e[e.debug=0]="debug",e[e.info=1]="info",e[e.warn=2]="warn",e[e.error=3]="error",e[e.report=99]="report",e[e.disable=100]="disable"}(t.ENUM_LOG_LEVEL||(t.ENUM_LOG_LEVEL={})),function(e){e[e.disable=0]="disable",e[e.websocket=1]="websocket",e[e.https=2]="https"}(t.ENUM_REMOTE_TYPE||(t.ENUM_REMOTE_TYPE={}));var i=function(){function e(e,t){void 0===e&&(e=null),void 0===t&&(t=null),this._id=null,this.next=null,this.prev=null,this._id=e,this._data=t}return Object.defineProperty(e.prototype,"id",{get:function(){return this._id},set:function(e){this._id=e},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"data",{get:function(){return this._data},set:function(e){this._data=e},enumerable:!0,configurable:!0}),e.prototype.hasNext=function(){return this.next&&this.next.id},e.prototype.hasPrev=function(){return this.prev&&this.prev.id},e}();t.ListNode=i;var n=function(){function e(){this.start=new i,this.end=new i,this._idCounter=0,this._numNodes=0,this.start.next=this.end,this.start.prev=null,this.end.prev=this.start,this.end.next=null}return e.prototype.insertBefore=function(e,t){var r=new i(this._idCounter,t);return r.next=e,r.prev=e.prev,e.prev.next=r,e.prev=r,++this._idCounter,++this._numNodes,r},e.prototype.addLast=function(e){return this.insertBefore(this.end,e)},e.prototype.add=function(e){return this.addLast(e)},e.prototype.getFirst=function(){return 0===this._numNodes?null:this.start.next},e.prototype.getLast=function(){return 0===this._numNodes?null:this.end.prev},e.prototype.size=function(){return this._numNodes},e.prototype.getFromFirst=function(e){var t=0,r=this.start.next;if(e>=0)for(;t<e&&null!==r;)r=r.next,++t;else r=null;if(null===r)throw"Index out of bounds.";return r},e.prototype.get=function(e){return 0===e?this.getFirst():e===this._numNodes-1?this.getLast():this.getFromFirst(e)},e.prototype.remove=function(e){return e.prev.next=e.next,e.next.prev=e.prev,--this._numNodes,e},e.prototype.removeFirst=function(){var e=null;return this._numNodes>0&&(e=this.remove(this.start.next)),e},e.prototype.removeLast=function(){var e=null;return this._numNodes>0&&(e=this.remove(this.end.prev)),e},e.prototype.removeAll=function(){this.start.next=this.end,this.end.prev=this.start,this._numNodes=0,this._idCounter=0},e.prototype.each=function(e){for(var t=this.start;t.hasNext();)e(t=t.next)},e.prototype.find=function(e){for(var t=this.start,r=!1,i=null;t.hasNext()&&!r;)e(t=t.next)&&(i=t,r=!0);return i},e.prototype.map=function(e){for(var t=this.start,r=[];t.hasNext();)e(t=t.next)&&r.push(t);return r},e.prototype.push=function(e){return this.addLast(e)},e.prototype.unshift=function(e){this._numNodes>0?this.insertBefore(this.start.next,e):this.insertBefore(this.end,e)},e.prototype.pop=function(){return this.removeLast()},e.prototype.shift=function(){return this.removeFirst()},e}();t.LinkedList=n,t.sdkErrorList={SUCCESS:{code:"ZegoClient.Success",msg:"success."},PARAM:{code:"ZegoClient.Error.Param",msg:"input error."},HEARTBEAT_TIMEOUT:{code:"ZegoClient.Error.Timeout",msg:"heartbeat timeout."},LOGIN_TIMEOUT:{code:"ZegoClient.Error.Timeout",msg:"login timeout."},SEND_MSG_TIMEOUT:{code:"ZegoClient.Error.Timeout",msg:"send customsg timeout."},RESET_QUEUE:{code:"ZegoClient.Error.Timeout",msg:"msg waiting ack is clear when reset."},LOGIN_DISCONNECT:{code:"ZegoClient.Error.Network",msg:"network is broken and login fail."},KICK_OUT:{code:"ZegoClient.Error.Kickout",msg:"kickout reason="},UNKNOWN:{code:"ZegoClient.Error.Unknown",msg:"unknown error."},FREQ_LIMITED:{code:"ZegoClient.Error.requencyLimited",msg:"Frequency Limited."}},function(e){e[e.disconnected=0]="disconnected",e[e.connecting=1]="connecting",e[e.connected=2]="connected"}(t.ENUM_SIGNAL_STATE||(t.ENUM_SIGNAL_STATE={})),t.ENUM_RESOLUTION_TYPE={LOW:{width:240,height:320,frameRate:15,bitRate:300},MEDIUM:{width:480,height:640,frameRate:15,bitRate:800},HIGH:{width:720,height:1280,frameRate:20,bitRate:1500}},t.ENUM_RETRY_STATE={didNotStart:0,retrying:1,finished:2},t.ENUM_PUBLISH_STATE={start:0,waitingSessionRsp:1,waitingOffserRsp:2,waitingServerAnswer:3,waitingServerICE:4,connecting:5,publishing:6,stop:7,didNotStart:8},t.ENUM_PLAY_STATE={start:0,waitingSessionRsp:1,waitingOffserRsp:2,waitingServerAnswer:3,waitingServerICE:4,connecting:5,playing:6,stop:7,didNotStart:8},t.ENUM_CONNECT_STATE={disconnect:0,connecting:1,connected:2},t.MAX_TRY_CONNECT_COUNT=3,t.SEND_MSG_RESET=2,t.SEND_MSG_TIMEOUT=1,t.MAX_TRY_HEARTBEAT_COUNT=5,t.ENUM_PUBLISH_STREAM_STATE={waiting_url:1,tryPublish:2,update_info:3,publishing:4,stop:5},t.ENUM_STREAM_SUB_CMD={liveNone:0,liveBegin:2001,liveEnd:2002,liveUpdate:2003},t.ENUM_STREAM_UPDATE_TYPE={added:0,deleted:1},function(e){e[e.logout=0]="logout",e[e.trylogin=1]="trylogin",e[e.login=2]="login"}(t.ENUM_RUN_STATE||(t.ENUM_RUN_STATE={})),t.ENUM_PUBLISH_STATE_UPDATE={start:0,error:1,retry:2},t.ENUM_PLAY_STATE_UPDATE={start:0,error:1,retry:2},t.MAX_TRY_LOGIN_COUNT=5,t.TRY_LOGIN_INTERVAL=[2e3,2e3,3e3,3e3,4e3],t.MINIUM_HEARTBEAT_INTERVAL=3e3,t.ENUM_STREAM_UPDATE_CMD={added:12001,deleted:12002,updated:12003},t.SERVER_ERROR_CODE=1e4,t.MIXSTREAM_ERROR_CODE=1e4,function(e){e[e.low=1]="low",e[e.stantard=2]="stantard",e[e.hight=3]="hight",e[e.custome=4]="custome"}(t.QUALITYLEVEL||(t.QUALITYLEVEL={})),t.ENUM_SIGNAL_SUB_CMD={none:0,joinLiveRequest:1001,joinLiveResult:1002,joinLiveInvite:1003,joinLiveStop:1004},t.ENUM_PUSH_SIGNAL_SUB_CMD={none:0,pushJoinLiveRequest:11001,pushJoinLiveResult:11002,pushJoinLiveInvite:11003,pushJoinLiveStop:11004},function(e){e[e.auto=0]="auto",e[e.ultra=1]="ultra"}(t.ENUM_PLAY_SOURCE_TYPE||(t.ENUM_PLAY_SOURCE_TYPE={})),function(e){e[e.cdn=0]="cdn",e[e.ultra=1]="ultra",e[e.customUrl=2]="customUrl"}(t.ENUM_DISPATCH_TYPE||(t.ENUM_DISPATCH_TYPE={})),function(e){e[e.ClientType_None=0]="ClientType_None",e[e.ClientType_H5=1]="ClientType_H5",e[e.ClientType_SmallPragram=2]="ClientType_SmallPragram",e[e.ClientType_Webrtc=3]="ClientType_Webrtc"}(t.E_CLIENT_TYPE||(t.E_CLIENT_TYPE={}))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(){}return e.checkConfigParam=function(e,t){return e.appid&&"number"==typeof e.appid?e.server?!(!e.idName||"string"!=typeof e.idName)||(t.error("ccp.0 idName must be string and not empty"),!1):(t.error("ccp.0 server must be string and not empty"),!1):(t.error("ccp.0 appid must be number"),!1)},e.checkLoginParam=function(e,t){return!0},e.registerCallback=function(e,t,r){var i,n;t.success&&(i=t.success),t.error&&(n=t.error),r[e+"SuccessCallback"]=i,r[e+"ErrorCallback"]=n},e.actionErrorCallback=function(e,t){return t[e+"ErrorCallback"]},e.actionSuccessCallback=function(e,t){return t[e+"SuccessCallback"]},e.getServerError=function(e){var t={1:"parse json error.",1001:"login is processing.",1002:"liveroom request error.",1003:"zpush connect fail.",1004:"zpush handshake fail.",1005:"zpush login fail.",1006:"user login state is wrong.",1007:"got no zpush addr",1008:"token error",1009:"dispatch error",2002:"biz channel error",1e9:"liveroom cmd error, result="};if(0===e)return{code:"ZegoClient.Success",msg:"success"};var r={code:"ZegoClient.Error.Server",msg:""};return r.msg=e>1e9?t[1e9]+e:t[e]?"unknown error code:"+e:t[e],r},e.isKeepTryLogin=function(e){switch(e){case 1002:case 1003:return!0;default:return!1}},e.mergeStreamList=function(e,t,r,i,n){e.debug("msl.0 call");var s,o=[],a=[],c=[];i||(i=[]);for(var u=0;u<i.length;u++)if(i[u].anchor_id_name!=t){s=!1;for(var l=0;l<r.length;l++)if(i[u].stream_id===r[l].stream_id){i[u].extra_info!==r[l].extra_info&&c.push(i[u]),s=!0;break}s||o.push(i[u])}else e.debug("msl.0 have self stream added");for(var h=0;h<r.length;h++){s=!1;for(var d=0;d<i.length;d++)if(r[h].stream_id===i[d].stream_id){s=!0;break}s||a.push(r[h])}for(r.splice(0),u=0;u<i.length;u++)r.push(i[u]);n(o,a,c),e.debug("msl.0 call success")},e.checkCustomCommandParam=function(e){return!0},e.generateRandumNumber=function(e){return parseInt(Math.random()*(e+1)+"",10)},e.uuid=function(e,t){var r,i="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),n=[];if(t=t||i.length,e)for(r=0;r<e;r++)n[r]=i[0|Math.random()*t];else{var s=void 0;for(n[8]=n[13]=n[18]=n[23]="-",n[14]="4",r=0;r<36;r++)n[r]||(s=0|16*Math.random(),n[r]=i[19==r?3&s|8:s])}return n.join("")},e.isSupportWebrtc=function(){var e=window.RTCPeerConnection||window.mozRTCPeerConnection||window.webkitRTCPeerConnection,t=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.msGetUserMedia||navigator.mozGetUserMedia||navigator.mediaDevices&&navigator.mediaDevices.getUserMedia,r=window.WebSocket;return!!e&&!!t&&!!r},e.isSupportH264=function(e,t){var r=!1;new RTCPeerConnection(null).createOffer({offerToReceiveAudio:1,offerToReceiveVideo:1}).then((function(t){if(t&&t.sdp){r=!0,clearTimeout(i);var n=t.sdp.split("\r\n").some((function(e){return e.startsWith("a=rtpmap:")&&e.indexOf("H264/")>-1}));e&&e(n)}}),(function(e){r=!0,clearTimeout(i),t&&t(e)}));var i=setTimeout((function(){0==r&&t(!1)}),200)},e.supportDetection=function(e,t,r){var i={webRtc:!1,capture:!1,videoDecodeType:{H264:!1,VP8:!1},screenSharing:e};(window.RTCPeerConnection||window.mozRTCPeerConnection||window.webkitRTCPeerConnection)&&(i.webRtc=!0),navigator&&(navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.msGetUserMedia||navigator.mozGetUserMedia||navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)&&(i.capture=!0),this.supportVideoCodeType((function(e){i.videoDecodeType.H264=e.H264,i.videoDecodeType.VP8=e.VP8,t&&t(i)}),(function(e){r&&r(e)}))},e.compareVersion=function(e,t){e=e.split("."),t=t.split(".");for(var r=Math.max(e.length,t.length);e.length<r;)e.push("0");for(;t.length<r;)t.push("0");for(var i=0;i<r;i++){var n=parseInt(e[i]),s=parseInt(t[i]);if(n>s)return 1;if(n<s)return-1}return 0},e.isSupportLive=function(e,t){var r=wx.getSystemInfoSync().SDKVersion,i={code:-1,msg:""};this.compareVersion(r,"1.7.0")<0&&(i={code:10001,msg:"当前微信版本过低，无法使用相关组件"},e&&e(i)),wx.getSetting({success:function(t){var r=t.authSetting;r["scope.camera"]&&r["scope.record"]||(i={code:10002,msg:"需要摄像头和录音功能的授权"}),e&&e(i)},fail:function(e){t&&t(e)}})},e.supportVideoCodeType=function(e,t){var r=!1;new RTCPeerConnection(null).createOffer({offerToReceiveAudio:1,offerToReceiveVideo:1}).then((function(t){if(t&&t.sdp){r=!0;var i=t.sdp.split("\r\n"),n=i.some((function(e){return e.startsWith("a=rtpmap:")&&e.indexOf("H264/")>-1})),s=i.some((function(e){return e.startsWith("a=rtpmap:")&&e.indexOf("VP8/")>-1})),o=i.some((function(e){return e.startsWith("a=rtpmap:")&&e.indexOf("VP9/")>-1})),a=i.some((function(e){return e.startsWith("a=rtpmap:")&&e.indexOf("H264/")>-1}));e&&e({H264:n,VP8:s,VP9:o,H265:a})}}),(function(e){r=!0,clearTimeout(i),t&&t(e)}));var i=setTimeout((function(){0==r&&t(!1)}),200)},e.inlineWorker=function(e){if(Worker){var t=e.toString().trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1],r=URL.createObjectURL(new window.Blob([t],{type:"text/javascript"}));return new Worker(r)}return null},e}();t.ClientUtil=i},function(e,t,r){"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.playErrorList={DISPATCH_ERROR:{code:"ZegoPlayWeb.Error.Dispatch",msg:"dispatch request error"},DISPATCH_TIMEOUT:{code:"ZegoPlayWeb.Timeout.Dispatch",msg:"dispatch request timeout"},TOKEN_ERROR:{code:"ZegoPlayWeb.Error.Token",msg:"login token error"},SEND_SESSION_TIMEOUT:{code:"ZegoPlayWeb.Timeout.Session",msg:"send session request timeout"},CREATE_SESSION_ERROR:{code:"ZegoPlayWeb.Error.Session",msg:"create session error"},CREATE_OFFER_ERROR:{code:"ZegoPublish.Error.CreateOffer",msg:"create offer error"},SERVER_MEDIA_DESC_TIMEOUT:{code:"ZegoPlayWeb.Timeout.RemoteOffer",msg:"wating server mediaDesc timeout"},SET_REMOTE_DESC_ERROR:{code:"ZegoPlayWeb.Error.RemoteOffer",msg:"other side offer error"},CREATE_ANSWER_ERROR:{code:"ZegoPlayWeb.Error.CreateAnswer",msg:"create offer error"},SET_LOCAL_DESC_ERROR:{code:"ZegoPlayWeb.Error.LocalDesc",msg:"setLocalDescription error"},SEND_MEDIA_DESC_TIMEOUT:{code:"ZegoPlayWeb.Timeout.Desc",msg:"send mediaDesc timeout"},SEND_CANDIDATE_ERROR:{code:"ZegoPlayWeb.Error.Candidate",msg:"send candidate error"},SEND_CANDIDATE_TIMEOUT:{code:"ZegoPlayWeb.Timeout.Candidate",msg:"send candidate timeout"},SERVER_CANDIDATE_TIMEOUT:{code:"ZegoPlayWeb.Timeout.ServerCandidate",msg:"waiting candidate timeout"},SERVER_CANDIDATE_ERROR:{code:"ZegoPlayWeb.Error.ServerCandidate",msg:"recv candidate error"},MEDIA_CONNECTION_FAILED:{code:"ZegoPlayWeb.Error.ConnectionFailed",msg:"ice Connection state failed"},MEDIA_CONNECTION_CLOSED:{code:"ZegoPlayWeb.Error.ConnectionClosed",msg:"ice connection state closed"},SESSION_CLOSED:{code:"ZegoPlayWeb.Error.SessionClosed",msg:"server session closed"},WEBSOCKET_ERROR:{code:"ZegoPlayWeb.Error.SocketError",msg:"network error"}},t.publishErrorList={DISPATCH_ERROR:{code:"ZegoPublish.Error.Dispatch",msg:"dispatch request error"},DISPATCH_TIMEOUT:{code:"ZegoPublish.Timeout.Dispatch",msg:"dispatch request timeout"},TOKEN_ERROR:{code:"ZegoPublish.Error.Token",msg:"login token error"},SEND_SESSION_TIMEOUT:{code:"ZegoPublish.Timeout.Session",msg:"send session request timeout"},CREATE_SESSION_ERROR:{code:"ZegoPublish.Error.Session",msg:"create session error"},CREATE_OFFER_ERROR:{code:"ZegoPublish.Error.CreateOffer",msg:"create offer error"},SET_LOCAL_DESC_ERROR:{code:"ZegoPublish.Error.LocalDesc",msg:"setLocalDescription error"},SEND_MEDIA_DESC_TIMEOUT:{code:"ZegoPublish.Timeout.Desc",msg:"send mediaDesc timeout"},SERVER_MEDIA_DESC_TIMEOUT:{code:"ZegoPublish.Timeout.ServerAnswer",msg:"waiting server mediaDesc timeout"},SERVER_MEDIA_DESC_ERROR:{code:"ZegoPublish.Error.ServerAnswer",msg:"server mediaDesc type error"},SET_REMOTE_DESC_ERROR:{code:"ZegoPublish.Error.RemoteDesc",msg:"other side offer error"},SEND_CANDIDATE_TIMEOUT:{code:"ZegoPublish.Timeout.Candidate",msg:"sendIceCandidate error"},SERVER_CANDIDATE_TIMEOUT:{code:"ZegoPublish.Timeout.ServerCandidate",msg:"waiting candidate timeout"},SERVER_CANDIDATE_ERROR:{code:"ZegoPublish.Error.ServerCandidate",msg:"recv candidate error"},SESSION_CLOSED:{code:"ZegoPublish.Error.SessionClosed",msg:"server session closed"},MEDIA_CONNECTION_FAILED:{code:"ZegoPublish.Error.IConnectionFailed",msg:"Iice Connection state failed"},MEDIA_CONNECTION_CLOSED:{code:"ZegoPublish.Error.ConnectionClosed",msg:"ice connection state closed"},WEBSOCKET_ERROR:{code:"ZegoPublish.Error.SocketError",msg:"network error"}},t.ENUM_PUBLISH_STATE_UPDATE={start:0,error:1,retry:2},t.ENUM_PLAY_STATE_UPDATE={start:0,error:1,retry:2,stop:3},t.ENUM_RETRY_STATE={didNotStart:0,retrying:1,finished:2},t.getSeq=(i=1,function(){return i++})},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e){this.loop=!1,this.replace=!1,this.effectEndedCallBack=null,this.effectEndedListener=null,this.startTimes=0,this.startOffset=0,this.pauseTimes=0,this.resumeOffset=0,this.isMixAudio=!1,this.logger=e}return e.prototype.preloadEffect=function(e,t){var r=this;this.logger.info("amu.pe.0 start preload effect");var i=new("undefined"!=typeof webkitAudioContext?webkitAudioContext:AudioContext),n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){if(200==n.status||304==n.status){var e=n.response;i.decodeAudioData(e,(function(e){r.logger.info("amu.pe.0 effect preload success"),t("",e)}),(function(e){t(e)}))}else{var s=n.statusText;t(s)}},n.send()},e.prototype.playEffect=function(e,t,r,i,n){var s=this;!0!==this.isMixAudio?this.audioBuffer?(this.startOffset=e||0,this.loop=t||!1,this.replace=r||!1,this.effectEndedCallBack=n,this.mixEffect(this.audioBuffer,(function(){s.buffSource.loop=!!t,e?s.buffSource.start(0,e/1e3):s.buffSource.start(0),s.startTimes=Date.now(),s.effectEndedListener=s.effectEndedHandler.bind(s),s.buffSource.addEventListener("ended",s.effectEndedListener),i&&i()}))):this.logger.error("amu.pe.1 no audio buffer found"):this.logger.error("amu.pe.1 audio is mixing")},e.prototype.pauseEffect=function(){this.stopMixingAudio(),this.resumeOffset=(this.pauseTimes-this.startTimes+this.startOffset)%(1e3*this.audioBuffer.duration)},e.prototype.resumeEffect=function(){this.playEffect(this.resumeOffset,this.loop,this.replace,null,this.effectEndedCallBack),this.startOffset=this.resumeOffset},e.prototype.mixEffect=function(e,t){this.localStream?(this.ac=new("undefined"!=typeof webkitAudioContext?webkitAudioContext:AudioContext),this.gainNode=this.ac.createGain(),this.buffSource=this.ac.createBufferSource(),this.buffSource.buffer=e,this.buffSource.connect(this.gainNode),this.replaceTrack()&&t()):this.logger.error("amu.me.0 localStream can not be found")},e.prototype.startMixingAudio=function(e,t){return this.replace=t||!1,this.isMixAudio?(this.logger.error("amu.sma.0 audio is mixing"),!1):this.localStream?(e.captureStream=e.captureStream||e.mozCaptureStream||e.webkitCaptureStream,this.ac=new("undefined"!=typeof webkitAudioContext?webkitAudioContext:AudioContext),this.gainNode=this.ac.createGain(),this.mixAudio=this.ac.createMediaStreamSource(e.captureStream()),this.mixAudio.connect(this.gainNode),this.replaceTrack()):(this.logger.error("amu.sma.0 localStream can not be found"),!1)},e.prototype.replaceTrack=function(){this.streamSource=this.ac.createMediaStreamSource(this.localStream.clone()),this.destination=this.ac.createMediaStreamDestination(),!this.replace&&this.streamSource.connect(this.destination),this.gainNode.connect(this.destination);var e=this.destination.stream.getAudioTracks()[0],t=this.peerConnection.getSenders().find((function(t){return t.track.kind===e.kind}));return t?(this.micTrack=this.localStream.getAudioTracks()[0],t.replaceTrack(e),this.localStream.removeTrack(this.micTrack),this.localStream.addTrack(e),this.isMixAudio=!0,!0):(this.logger.error("amu.rt.0 no sender"),!1)},e.prototype.stopMixingAudio=function(){var e=this;return this.isMixAudio?this.localStream?(this.peerConnection.getSenders().find((function(t){return t.track.kind===e.micTrack.kind})).replaceTrack(this.micTrack),this.localStream.removeTrack(this.localStream.getAudioTracks()[0]),this.localStream.addTrack(this.micTrack),this.mixAudio?(this.mixAudio.disconnect(this.gainNode),this.mixAudio=null):this.buffSource&&(this.buffSource.removeEventListener("ended",this.effectEndedListener),this.buffSource.stop(),this.pauseTimes=Date.now(),this.buffSource.disconnect(this.gainNode),this.buffSource=null),this.gainNode.disconnect(this.destination),this.micTrack=null,this.ac=null,this.isMixAudio=!1,!0):(this.logger.error("amu.sma.1 localStream can not be found"),!1):(this.logger.error("amu.sma.1 no mixing audio found"),!1)},e.prototype.setMixingAudioVolume=function(e){if(!this.gainNode)return this.logger.error("amu.sma.2 no mixing audio found"),!1;this.gainNode.gain.value=e},e.prototype.effectEndedHandler=function(){this.stopMixingAudio(),this.effectEndedCallBack&&this.effectEndedCallBack()},e}();t.audioMixUtil=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(){}return e.zegoSdp=function(e){var t=e.split("\r\n"),r=[],i=[];t.forEach((function(e){var t=e.match(/a=rtpmap:(\d+)\s+((H264\/90000)|(opus\/48000\/2))/);t&&t[1]&&t[2]&&("H264/90000"===t[2]&&r.push(t[1]),"opus/48000/2"===t[2]&&i.push(t[1]))}));var n=[];return t.map((function(e){var t=!0,s=e.match(/((a=rtcp-fb:)|(a=rtpmap:)|(a=fmtp:))(\d+)/);if(s&&s[5]&&(r.concat(i).some((function(e){return e==s[5]}))||(t=!1)),e.indexOf("m=video")>-1){var o=e.split(" ");e=[o[0],o[1],o[2]].concat(r).join(" ")}else e.indexOf("m=audio")>-1&&(o=e.split(" "),e=[o[0],o[1],o[2]].concat(i).join(" "));t&&n.push(e)})),n.join("\r\n")},e.getSDPByVideDecodeType=function(e,t){var r={str:"",arr:[],obj:{H264:[],H265:[],VP8:[],VP9:[],OHTER:[]}};if(!e.includes("m=video"))return e;var i=/m=video.+/.exec(e)[0];i=i.match(/[\s|\d]+/g)[1].replace(" ",""),r.str=i,r.arr=r.str.split(" "),r.arr.forEach((function(t){var i=new RegExp("a=rtpmap:"+t+".+").exec(e)[0];i.includes("H264")?r.obj.H264.push(t):i.includes("H265")?r.obj.H265.push(t):i.includes("VP8")?r.obj.VP8.push(t):i.includes("VP9")?r.obj.VP9.push(t):r.obj.OHTER.push(t)})),r.obj.OHTER.forEach((function(t){var i=new RegExp("a=fmtp:"+t+".+apt=(\\d+)").exec(e),n=i&&i[1];n&&(r.obj.H264.includes(n)?r.obj.H264.push(t):r.obj.H265.includes(n)?r.obj.H265.push(t):r.obj.VP8.includes(n)?r.obj.VP8.push(t):r.obj.VP9.includes(n)&&r.obj.VP9.push(t))}));var n=[];return"VP9"===t?n=r.obj.H265.concat(r.obj.H264,r.obj.VP8):"VP8"===t?n=r.obj.H265.concat(r.obj.H264,r.obj.VP9):"H264"===t?n=r.obj.H265.concat(r.obj.VP8,r.obj.VP9):"H265"===t&&(n=r.obj.VP8.concat(r.obj.H264,r.obj.VP9)),n.forEach((function(t){var i=r.arr.indexOf(t);r.arr.splice(i,1);var n=new RegExp("a=rtpmap:"+t+".+\\s\\n","g"),s=new RegExp("a=rtcp-fb:"+t+".+\\s\\n","g"),o=new RegExp("a=fmtp:"+t+".+\\s\\n","g");e=(e=(e=e.replace(n,"")).replace(s,"")).replace(o,"")})),e=e.replace(i,r.arr.join(" "))},e}();t.sdpUtil=i},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__assign||Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e};Object.defineProperty(t,"__esModule",{value:!0});var o=r(0),a=r(2),c=r(6),u=r(8),l=r(1),h=r(3),d=r(17),p=r(25),f=r(26),g=function(e){function t(){var t=this,r=new c.LoggerWeb,i=new p.StateCenter,n=new u.ZegoStreamCenterWeb(r,i);return(t=e.call(this)||this).streamCenter=n,t.logger=r,t.stateCenter=i,t.audioMixing=new h.audioMixUtil(r),t.init(),t.bindWindowListener(),t}return n(t,e),t.prototype.getSocket=function(e){return new WebSocket(e)},t.prototype.enableCamera=function(e,t){return this.logger.debug("zc.p.ec.0 call"),"boolean"!=typeof t?(this.logger.error("zc.p.ec.0 argument is not bool"),!1):this.streamCenter.enableCamera(e,t)},t.prototype.enableMicrophone=function(e,t){return this.logger.debug("zc.p.em.0 call"),"boolean"!=typeof t?(this.logger.error("zc.p.em.0 argument is not bool"),!1):this.streamCenter.enableMicrophone(e,t)},t.prototype.setLocalAudioOutput=function(e,t){return this.logger.debug("zc.p.slao call"),"string"!=typeof t?(console.error("audiooutput is not string"),!1):this.streamCenter.setStreamAudioOutput(e,t)},t.prototype.setPlayAudioOutput=function(e,t){return this.logger.debug("zc.p.spao call"),"string"!=typeof t?(console.error("audiooutput is not string"),!1):this.streamCenter.setPlayStreamAudioOutput(e,t)},t.prototype.setCustomSignalUrl=function(e){return this.logger.debug("zc.p.scs.0 call: "+e),e&&0!=e.length?0!=e.indexOf("wss://")?(this.logger.error("zc.p.scs.0 url is not correct"),!1):void(this.stateCenter.customUrl=e):(this.logger.error("zc.p.scs.0 param error"),!1)},t.prototype.setQualityMonitorCycle=function(e){"number"==typeof e&&e>=1e3&&this.streamCenter.setQualityMonitorCycle(e)},t.prototype.startPlayingStream=function(e,t,r,i){var n=this;if(this.logger.debug("zc.p.sps.0 call"),!e||""===e)return this.logger.error("zc.p.sps.0 param error"),!1;if(!t)return this.logger.error("zc.p.sps.0 don't have remoteVideo"),!1;if(this.stateCenter.customUrl)return this.streamCenter.setPlayStateStart(e,t,r,i)?this.streamCenter.startPlayingStream(e,[this.stateCenter.customUrl]):(this.logger.error("zc.p.sps.0 cannot start play"),!1);if(!this.stateCenter.isLogin())return this.logger.error("zc.p.sps.0 not login"),!1;for(var s=!1,c=0;c<this.stateCenter.streamList.length;c++)if(this.stateCenter.streamList[c].stream_id===e){s=!0;break}if(0==s&&this.logger.info("zc.p.sps.0 cannot find stream"),this.stateCenter.pullLimited||(e=NaN+e),!this.streamCenter.setPlayStateStart(e,t,r,i))return this.logger.info("zc.p.sps.0 cannot start play"),!1;var u={stream_id:e,ptype:"pull",signals:this.streamCenter.getAllInUseUrl()};return this.socketCenter.registerRouter("webrtc_url",(function(e){n.handleFetchWebRtcUrlRsp(e)})),this.socketCenter.sendMessage("webrtc_url",u,void 0,(function(t,r){t==o.sdkErrorList.SEND_MSG_TIMEOUT?n.onPlayStateUpdate(o.ENUM_PLAY_STATE_UPDATE.error,e,a.playErrorList.DISPATCH_TIMEOUT):n.onPlayStateUpdate(o.ENUM_PLAY_STATE_UPDATE.error,e,a.playErrorList.DISPATCH_ERROR),n.streamCenter.stopPlayingStream(e)})),!0},t.prototype.stopPlayingStream=function(e){if(this.logger.debug("zc.p.sps.1.0 call"),!e||""===e)return this.logger.info("zc.p.sps.1.0 param error"),!1;for(var t in this.streamCenter.stopPlayingStream(e),this.stateCenter.streamUrlMap)if(this.stateCenter.streamUrlMap[t]===e){delete this.stateCenter.streamUrlMap[t];break}return this.logger.debug("zc.p.sps.1.0 call success"),!0},t.prototype.startPreview=function(e,t,r,i){if(this.logger.debug("zc.p.sp.0 call"),!e)return this.logger.error("zc.p.sp.0 no localVideo"),!1;if(t.audioBitRate){if("number"!=typeof t.audioBitRate)return void this.logger.error("zc.p.sp.0 audioBitRate must be number");if(t.audioBitRate<48e3)return void this.logger.error("zc.p.sp.0 audioBitRate cannot less 48000");this.stateCenter.audioBitRate=t.audioBitRate}return this.streamCenter.startPreview(e,t,r,i)},t.prototype.stopPreview=function(e){return this.logger.debug("zc.p.sp.1 call"),e?this.streamCenter.stopPreview(e):(this.logger.info("zc.p.sp.1 param error"),!1)},t.prototype.startPublishingStream=function(e,t,r,i){var n=this;if(this.logger.debug("zc.p.sps.1 call"),!e)return this.logger.error("zc.p.sps.1 param error"),!1;if(i||(i={}),i.audioBitRate=this.stateCenter.audioBitRate,this.stateCenter.customUrl&&0!=this.stateCenter.customUrl.length)return this.stateCenter.publishStreamList[e]={state:o.ENUM_PUBLISH_STREAM_STATE.tryPublish,extra_info:r},this.streamCenter.setPublishStateStart(e,t,i)?this.streamCenter.startPublishingStream(e,[this.stateCenter.customUrl]):(this.logger.info("zc.p.sps.1 cannot start publish"),!1);if(!this.stateCenter.isLogin())return this.logger.error("zc.p.sps.1 not login"),!1;if(this.stateCenter.publishStreamList[e]={state:o.ENUM_PUBLISH_STREAM_STATE.tryPublish,extra_info:r},!this.streamCenter.setPublishStateStart(e,t,i))return this.logger.error("zc.p.sps.1 cannot start publish"),!1;this.logger.info("zc.p.sps.1 start publish");var s={stream_id:e,ptype:"push",signals:this.streamCenter.getAllInUseUrl(),header_kvs:[{key:"grpc-metadata-push",value:i&&i.cdnUrl||""}]};return this.socketCenter.registerRouter("webrtc_url",(function(e){n.handleFetchWebRtcUrlRsp(e)})),this.socketCenter.sendMessage("webrtc_url",s,void 0,(function(t,r){t==o.sdkErrorList.SEND_MSG_TIMEOUT?n.onPublishStateUpdate(o.ENUM_PUBLISH_STATE_UPDATE.error,e,a.publishErrorList.DISPATCH_TIMEOUT):n.onPublishStateUpdate(o.ENUM_PUBLISH_STATE_UPDATE.error,e,a.publishErrorList.DISPATCH_ERROR),n.streamCenter.stopPublishingStream(e)})),!0},t.prototype.stopPublishingStream=function(e){return this.logger.debug("zc.p.sps.1.1 call"),e?(this.streamCenter.stopPublishingStream(e),this.stateCenter.publishStreamList[e]&&(this.stateCenter.publishStreamList[e].state>=o.ENUM_PUBLISH_STREAM_STATE.update_info&&this.streamHandler.updateStreamInfo(e,o.ENUM_STREAM_SUB_CMD.liveEnd),delete this.stateCenter.publishStreamList[e]),!0):(this.logger.info("zc.p.sps.1.1 param error"),!1)},t.prototype.preloadEffect=function(e,t,r){var i=this;e&&"number"==typeof e&&t&&"string"==typeof t?this.stateCenter.audioEffectBuffer[e]?this.logger.error("zc.pe.0 audio buffer already exists"):this.audioMixing.preloadEffect(t,(function(t,n){if(t)return i.logger.error("zc.pe.0 effect preload fail "+t),void(r&&r(t));n&&(i.stateCenter.audioEffectBuffer[e]=n,r&&r())})):this.logger.error("zc.pe.0 params error")},t.prototype.playEffect=function(e,t,r){if(e.streamId&&"string"==typeof e.streamId&&e.effectId&&"number"==typeof e.effectId)if(this.stateCenter.audioEffectBuffer[e.effectId]){var i=this.stateCenter.audioEffectBuffer[e.effectId],n=this.getPublisher(e.streamId);n?i?n.playEffect(e,i,t,r):this.logger.error("zc.pe.1 no audio buffer found"):this.logger.error("zc.pe.1 publisher doesn't exist")}else this.logger.error("zc,pe.1 audio buffer dosesn't exists");else this.logger.error("zc.pe.1 params error")},t.prototype.pauseEffect=function(e){if(e&&"string"==typeof e){var t=this.getPublisher(e);t?t.pauseEffect():this.logger.error("zc.pe.2 publisher doesn't exist")}else this.logger.error("zc.pe.2 streamid format error")},t.prototype.resumeEffect=function(e){if(e&&"string"==typeof e){var t=this.getPublisher(e);t?t.resumeEffect():this.logger.error("zc.re.0 publisher doesn't exist")}else this.logger.error("zc.re.0 streamid format error")},t.prototype.unloadEffect=function(e){return e&&"number"==typeof e?(delete this.stateCenter.audioEffectBuffer[e],!0):(this.logger.error("zc.ue.0 params error"),!1)},t.prototype.startMixingAudio=function(e,t,r){if(this.logger.debug("zc.sma.0 call"),!e||"string"!=typeof e)return this.logger.error("zc.sma.0 stream id error"),!1;if(!t)return this.logger.error("zc.sma.0 no audio"),!1;var i=this.getPublisher(e);return i?i.startMixingAudio(t,r):(this.logger.error("zc.sma.0 publisher doesn't exist"),!1)},t.prototype.stopMixingAudio=function(e){if(!e||"string"!=typeof e)return this.logger.error("zc.sma.1 param streamid format error"),!1;var t=this.getPublisher(e);return t?t.stopMixingAudio():(this.logger.error("zc.sma.1 publisher doesn't exist"),!1)},t.prototype.setMixingAudioVolume=function(e,t){if(this.logger.debug("zc.sma.2 call"),!e||"string"!=typeof e||"number"!=typeof t||t<0||t>100)return this.logger.error("zc.sma.2 param error"),!1;var r=this.getPublisher(e);return r?r.audioMixing.setMixingAudioVolume(t/100):(this.logger.error("zc.sma.2 publisher doesn't exist"),!1)},t.prototype.getPublisher=function(e){var t=null,r=this.streamCenter.getTotalStreamId(e);return this.streamCenter.publisherList[r]&&this.streamCenter.publisherList[r].publisher&&(t=this.streamCenter.publisherList[r].publisher),t},t.prototype.startScreenShotChrome=function(e){if(!t.screenShotReady)return this.logger.error('zc.b.ss Please install the extension:1. Go to chrome://extensions  2. Check: "Enable Developer mode   3. Click: "Load the unpacked extension... 4. Choose "extension" folder from the repository 5. Reload this page'),!1;window.postMessage({type:"SS_UI_REQUEST",text:"start"},"*"),l.ClientUtil.registerCallback("screenShare",{success:e},this.stateCenter.callbackList)},t.prototype.startScreenSharingChrome=function(e,t){var r=this;"getDisplayMedia"in navigator.mediaDevices?navigator.mediaDevices.getDisplayMedia({audio:e}).then((function(e){r.stateCenter.screenShotStream=e,t(!0,e)})).catch((function(e){r.logger.error("zc.b.sss "+e),t(!1,null,e)})):this.logger.error("zc.b.sss brower does not support getDisplayMedia")},t.prototype.startScreenShotFirFox=function(e,t,r){var i=this,n={video:{},audio:t};n.video.mediaSource=e,navigator.mediaDevices.getUserMedia(n).then((function(e){i.stateCenter.screenShotStream=e,r(!0,e)})).catch((function(e){i.logger.error("zc.b.ssf "+e),r(!1,null)}))},t.prototype.stopScreenShot=function(){this.stateCenter.screenShotStream.getTracks().forEach((function(e){e.stop()})),window.postMessage({type:"SS_UI_CANCEL",text:"start"},"*")},t.prototype.switchDevice=function(e,t,r,i,n){var s=this;"audio"!==e&&"video"!==e||"string"!=typeof r?this.logger.error("zg.sd.0 param error"):this.enumDevices((function(o){var a=o.cameras,c=o.microphones;a.find((function(e){return e.deviceId==r}))||c.find((function(e){return e.deviceId==r}))?s.streamCenter.switchDevice(e,t,r,i,n):s.logger.error("zg.sd.0 can not switch device")}),(function(e){return n&&n(e)}))},t.prototype.WebrtcOnPublishStateUpdateHandle=function(e,t,r){this.stateCenter.publishStreamList[t].state==o.ENUM_PUBLISH_STREAM_STATE.publishing&&this.onPublishStateUpdate(e,t,r)},t.prototype.setCDNInfo=function(e,t){e.urls_flv=t.urls_flv,e.urls_hls=t.urls_m3u8,e.urls_https_flv=t.urls_https_flv,e.urls_https_hls=t.urls_https_m3u8,e.urls_rtmp=t.urls_rtmp},t.prototype.loginBodyData=function(){return{id_name:this.stateCenter.idName,nick_name:this.stateCenter.nickName,role:this.stateCenter.role,token:this.stateCenter.token,version:o.PROTO_VERSION,room_name:this.stateCenter.roomid,user_state_flag:this.stateCenter.userStateUpdate?1:0,room_create_flag:this.stateCenter.roomCreateFlag,client_type:o.E_CLIENT_TYPE.ClientType_Webrtc,third_token:this.stateCenter.third_token}},t.prototype.screenStreamFrom=function(e,t,r){var i=this,n={};n.audio={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:e}},n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:e,maxWidth:window.screen.width,maxHeight:window.screen.height}},!t&&(n.audio=!1),navigator.mediaDevices.getUserMedia(n).then((function(e){i.stateCenter.screenShotStream=e,r(!0,e)})).catch((function(e){i.logger.error("zc.b.ssf "+e),r(!1,null,e)}))},t.prototype.filterStreamList=function(e){var t={},r={},i={},n=[],s=0;for(var o in this.stateCenter.streamList.forEach((function(t,r){t.stream_id==e&&(s=r)})),this.stateCenter.streamList[s])"urls_flv"!=o&&"urls_https_flv"!=o||(t[o]=this.stateCenter.streamList[s][o]),"urls_m3u8"!=o&&"urls_https_m3u8"!=o||(r[o]=this.stateCenter.streamList[s][o]),"urls_rtmp"==o&&(i[o]=this.stateCenter.streamList[s][o]);var a=window.location.protocol,c=window.navigator.userAgent;if(/Safari/.test(c)&&!/Chrome/.test(c))for(var o in r)r[o]&&r[o].forEach((function(e){-1!==e.indexOf(a)&&n.push(e)}));else if("http:"==a)for(var o in t)t[o]&&t[o].forEach((function(e){-1===e.indexOf("http")&&-1===e.indexOf("https")||n.push(e)}));else if("https:"==a)for(var o in t)t[o]&&t[o].forEach((function(e){-1!==e.indexOf(a)&&n.push(e)}));else if("rtmp:"==a)for(var o in i)i[o]&&i[o].forEach((function(e){-1!==e.indexOf(a)&&n.push(e)}));return n.filter((function(e,t,r){return r.indexOf(e)==t}))},t.prototype.voiceChange=function(e,t){return e&&"number"==typeof e?t&&"string"==typeof t?this.getPublisher(t).voiceChange(e):(this.logger.error("zc.vc.0 stream id error"),!1):(this.logger.error("zc.vc.0 mult error"),!1)},t.prototype.voiceBack=function(e){return this.getPublisher(e).voiceBack()},t.isSupportWebrtc=function(){return l.ClientUtil.isSupportWebrtc()},t.isSupportH264=function(e,t){l.ClientUtil.isSupportH264(e,t)},t.supportDetection=function(e,t){navigator&&navigator.mediaDevices&&(this.screenShotReady||"getDisplayMedia"in navigator.mediaDevices)?l.ClientUtil.supportDetection(!0,e,t):l.ClientUtil.supportDetection(!1,e,t)},t.prototype.enumDevices=function(e,r){t.enumDevices(e,r)},t.enumDevices=function(e,t){void 0!==navigator.mediaDevices&&void 0!==navigator.mediaDevices.enumerateDevices?navigator.mediaDevices.enumerateDevices().then((function(t){for(var r=[],i=[],n=[],s=0;s<t.length;s++){var o=t[s];"audioinput"===o.kind&&r.push({label:o.label,deviceId:o.deviceId}),"audiooutput"===o.kind&&i.push({label:o.label,deviceId:o.deviceId}),"videoinput"===o.kind&&n.push({label:o.label,deviceId:o.deviceId})}e&&e({microphones:r,speakers:i,cameras:n})})).catch((function(e){t&&t(e)})):t&&t("browser don't support enumerate devices")},t.getAudioInfo=function(e,t,r){if(!e.srcObject)return console.error("srcObject is empty!"),!1;var i=s({},r);return new f.MediaUtil(i).connectToSource(e.srcObject,(function(e){t(e)}))},t.handleDataAvailable=function(e){e.data&&e.data.size>0&&t.recordedBlobs.push(e.data)},t.startRecord=function(e){var r=e.captureStream();t.recordedBlobs=[];var i={mimeType:"video/webm;codecs=vp9"};MediaRecorder.isTypeSupported(i.mimeType)||(i={mimeType:"video/webm;codecs=vp8"},MediaRecorder.isTypeSupported(i.mimeType)||(i={mimeType:"video/webm"},MediaRecorder.isTypeSupported(i.mimeType)||(i={mimeType:""})));try{t.mediaRecorder=new MediaRecorder(r,i)}catch(e){return void console.error("Exception while creating MediaRecorder:",e)}t.mediaRecorder.onstop=function(e){console.log("Recorder stopped: ",e)},t.mediaRecorder.ondataavailable=t.handleDataAvailable,t.mediaRecorder.start(10)},t.stopRecord=function(){t.mediaRecorder?t.mediaRecorder.stop():console.warn("please invoke startRecord first")},t.resumeRecord=function(){t.mediaRecorder?t.mediaRecorder.resume():console.warn("please invoke startRecord first")},t.pauseRecord=function(){t.mediaRecorder?t.mediaRecorder.pause():console.warn("please invoke startRecord first")},t.saveRecord=function(e){if(t.mediaRecorder&&t.recordedBlobs){var r=new Blob(t.recordedBlobs,{type:"video/webm"}),i=window.URL.createObjectURL(r),n=document.createElement("a");n.style.display="none",n.href=i,n.download=e+".webm",document.body.appendChild(n),n.click(),setTimeout((function(){document.body.removeChild(n),window.URL.revokeObjectURL(i)}),100)}else console.warn("please invoke startRecord first")},t.takeSnapShot=function(e,t){if(e&&0!==e.videoHeight){var r=document.createElement("canvas");r.width=e.videoWidth,r.height=e.videoHeight,r.getContext("2d").drawImage(e,0,0,r.width,r.height),t.src=r.toDataURL("image/jpeg")}else console.error("video can not empty")},t.saveSnapShot=function(e,t){if(e&&0!==e.videoHeight){var r=document.createElement("canvas");r.width=e.videoWidth,r.height=e.videoHeight,r.getContext("2d").drawImage(e,0,0,r.width,r.height),r.toBlob((function(e){var r=window.URL.createObjectURL(e),i=document.createElement("a");i.style.display="none",i.href=r,i.download=t+".jpeg",document.body.appendChild(i),i.click(),setTimeout((function(){document.body.removeChild(i),window.URL.revokeObjectURL(r)}),100)}))}else console.error("video can not empty")},t.prototype.bindWindowListener=function(){var e=this,t=navigator.userAgent.match(/iPad/i)||navigator.userAgent.match(/iPhone/i)?"pagehide":"beforeunload";window.addEventListener(t,(function(t){for(var r in window.event.cancelBubble=!0,e.streamCenter.publisherList)e.stopPublishingStream(r);for(var r in e.streamCenter.playerList)e.stopPublishingStream(r);console.log(e.streamCenter.playerList),console.log(e.streamCenter.publisherList),e.logout()})),window.addEventListener("message",(function(t){var r=t.data,i=r.type,n=r.streamId,s=r.canRequestAudioTrack;t.origin,"SS_DIALOG_SUCCESS"===i&&e.screenStreamFrom(n,s,l.ClientUtil.actionSuccessCallback("screenShare",e.stateCenter.callbackList)),"SS_DIALOG_CANCEL"===i&&(e.logger.error("zc.b.ss "+i),l.ClientUtil.actionSuccessCallback("screenShare",e.stateCenter.callbackList)(!1,null,i))}))},t.screenShotReady=!1,t}(d.BaseCenter);t.ZegoClient=g,window.addEventListener("message",(function(e){var t=e.data,r=t.type,i=(t.streamId,e.origin);i!==window.location.origin&&console.warn("ScreenStream: you should discard foreign event from origin:",i),"SS_PING"===r&&(g.screenShotReady=!0)}))},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var s=r(7),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.openWebSocketLogServer=function(e){if(this.url!=e){if(this.url=e,!e)return;if(null!=this.websocket&&2!=this.websocket.readyState&&3!=this.websocket.readyState)return;this.stopWebSocketServer(),this.websocket=new WebSocket(e),this.websocket.onopen=function(e){},this.websocket.onclose=function(e){console.error("onclose   websocket error:",e)},this.websocket.onmessage=function(e){},this.websocket.onerror=function(e){console.error("open log websocket error:"+e)}}},t.prototype.SendHttpsLog=function(){var e=this;if(0!=this.logCacheSend.length){var t=this.logCacheSend.join("\n"),r=new XMLHttpRequest;r.onreadystatechange=function(){if(4==r.readyState)if(200==r.status){if(0==r.responseText.length)return;try{var t=JSON.parse(r.responseText).interval;"number"==typeof t&&e.logUploadInterval!==t&&(e.timeInterval=t,e.openHttpsLogServer(e.url))}catch(e){console.log("send result failed "+e)}}else console.log("send failed "+r.status)},r.open("POST",this.url,!0),r.send(t),this.logCacheSend=[]}},t.prototype.logReportParamList=function(e,t){var r=new Date,i=r.getFullYear()+"/";return i+=(s.D[r.getMonth()+1]||r.getMonth()+1)+"/",i+=(s.D[r.getDate()]||r.getDate())+" ",i+=(s.D[r.getHours()]||r.getHours())+":",i+=(s.D[r.getMinutes()]||r.getMinutes())+":",i+=s.D[r.getSeconds()]||r.getSeconds(),i+="."+r.getTime()%1e3,t.time=i,t.level=e,t.console="rtc",t.appid=this.appid,t.roomid=this.roomid,t.userid=this.userid,t.id_name=this.userid,t.userName=this.userName,t.sessionid=this.sessionid,t.version=this.version,[JSON.stringify(t)]},t}(s.Logger);t.LoggerWeb=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0);t.D=["00","01","02","03","04","05","06","07","08","09"];var n=function(){function e(){this.logUploadTimer=null,this.logUploadInterval=1e4,this.logCache=[],this.logCacheSend=[],this.logCacheMax=100}return e.prototype.setLogLevel=function(e){this.logLevel<i.ENUM_LOG_LEVEL.debug||this.logLevel>i.ENUM_LOG_LEVEL.report?this.logLevel=i.ENUM_LOG_LEVEL.disable:this.logLevel=e},e.prototype.setRemoteLogLevel=function(e){this.logRemoteLevel<i.ENUM_LOG_LEVEL.debug||this.logRemoteLevel>i.ENUM_LOG_LEVEL.report?this.logRemoteLevel=i.ENUM_LOG_LEVEL.disable:this.logRemoteLevel=e},e.prototype.setSessionInfo=function(e,t,r,i,n,s){this.appid=e,this.roomid=t,this.sessionid=r,this.userid=i,this.userName=n,this.version=s},e.prototype.openLogServer=function(e){try{e.startsWith("wss:")?(this.logType=i.ENUM_REMOTE_TYPE.websocket,this.openWebSocketLogServer(e)):e.startsWith("https:")?(this.logType=i.ENUM_REMOTE_TYPE.https,this.openHttpsLogServer(e)):this.logType=i.ENUM_REMOTE_TYPE.disable}catch(e){this.error(JSON.stringify(e))}},e.prototype.stopLogServer=function(){this.logType==i.ENUM_REMOTE_TYPE.websocket?this.stopWebSocketServer():this.logType==i.ENUM_REMOTE_TYPE.https&&(this.SendHttpsLog(),this.stopHttpsServer()),this.logType=i.ENUM_REMOTE_TYPE.disable},e.prototype.stopWebSocketServer=function(){this.websocket&&(this.websocket.onclose=null,this.websocket.onerror=null,this.websocket.close(),this.websocket=null)},e.prototype.openHttpsLogServer=function(e){var t=this;this.url=e,e&&(this.stopHttpsServer(),this.logUploadTimer||(this.logUploadTimer=setInterval((function(){t.SendHttpsLog()}),this.logUploadInterval)))},e.prototype.stopHttpsServer=function(){this.logUploadTimer&&(clearInterval(this.logUploadTimer),this.logUploadTimer=null)},e.prototype.report=function(e){var t=this.logReportParamList(i.ENUM_LOG_LEVEL.report,e);this.logLevel!==i.ENUM_LOG_LEVEL.disable&&this.logLevel<=i.ENUM_LOG_LEVEL.report&&console.debug.apply(console,t),this.RemoteLog(i.ENUM_LOG_LEVEL.report,t,!0)},e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=this.logParamList(i.ENUM_LOG_LEVEL.debug,e.join(""));this.logLevel!==i.ENUM_LOG_LEVEL.disable&&this.logLevel<=i.ENUM_LOG_LEVEL.debug&&console.debug.apply(console,r),this.log(i.ENUM_LOG_LEVEL.debug,r)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=this.logParamList(i.ENUM_LOG_LEVEL.info,e.join(""));this.logLevel!==i.ENUM_LOG_LEVEL.disable&&this.logLevel<=i.ENUM_LOG_LEVEL.info&&console.info.apply(console,r),this.log(i.ENUM_LOG_LEVEL.info,r)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=this.logParamList(i.ENUM_LOG_LEVEL.warn,e.join(""));this.logLevel!==i.ENUM_LOG_LEVEL.disable&&this.logLevel<=i.ENUM_LOG_LEVEL.warn&&console.warn.apply(console,r),this.log(i.ENUM_LOG_LEVEL.warn,r)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=this.logParamList(i.ENUM_LOG_LEVEL.error,e.join(""));this.logLevel!==i.ENUM_LOG_LEVEL.disable&&this.logLevel<=i.ENUM_LOG_LEVEL.error&&console.error.apply(console,r),this.log(i.ENUM_LOG_LEVEL.error,r)},e.prototype.log=function(e,t){this.logRemoteLevel!==i.ENUM_LOG_LEVEL.disable&&this.logRemoteLevel<=e&&this.RemoteLog(e,t)},e.prototype.RemoteLog=function(e,t,r){if(void 0===r&&(r=!1),""!=this.url)if(this.logType==i.ENUM_REMOTE_TYPE.websocket)this.RemoteWebSocketLog(e,t);else if(this.logType==i.ENUM_REMOTE_TYPE.https)this.RemoteHttpsLog(e,t,r);else if(this.logLevel!==i.ENUM_LOG_LEVEL.disable&&this.logLevel<=e)for(this.logCacheSend.push(t);this.logCacheSend.length>this.logCacheMax;)this.logCacheSend.shift()},e.prototype.RemoteWebSocketLog=function(e,t){if(null==this.websocket||2==this.websocket.readyState||3==this.websocket.readyState){var r=this.url;this.url="",this.openLogServer(r),this.logCacheSend.length<this.logCacheMax&&this.logCacheSend.push(t)}else if(0==this.websocket.readyState)this.logCacheSend.length<this.logCacheMax&&this.logCacheSend.push(t);else if(1==this.websocket.readyState)if(this.logCacheSend.length>0){for(var i="",n=0;n<this.logCacheSend.length;n++)(i+this.logCacheSend[n]).length>4e3&&(this.websocket.send(i),i=""),i=i+this.logCacheSend[n]+"\n";t=i+t,this.logCacheSend=[],this.websocket.send(t)}else this.websocket.send(t);else console.warn("wrong socket state:"+this.websocket.readyState),this.logCacheSend.length<this.logCacheMax&&this.logCacheSend.push(t)},e.prototype.RemoteHttpsLog=function(e,t,r){this.logCacheSend.push(t),(this.logCacheSend.length>=this.logCacheMax||!0===r)&&this.SendHttpsLog()},e.prototype.logParamList=function(e,r){var i=new Date,n=i.getFullYear()+"/";n+=(t.D[i.getMonth()+1]||i.getMonth()+1)+"/",n+=(t.D[i.getDate()]||i.getDate())+" ",n+=(t.D[i.getHours()]||i.getHours())+":",n+=(t.D[i.getMinutes()]||i.getMinutes())+":",n+=t.D[i.getSeconds()]||i.getSeconds(),n+="."+i.getTime()%1e3;var s=r.substr(0,r.indexOf(" "));0==s.length&&(s=r);var o=r.substr(r.indexOf(" ")+1,4500);0==o.length&&(o="");var a={time:n,level:e,action:s,content:o,appid:this.appid,roomid:this.roomid,userid:this.userid,userName:this.userName,sessionid:this.sessionid};return[JSON.stringify(a)]},e}();t.Logger=n},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var s=r(9),o=r(10),a=r(11),c=r(2),u=r(0),l=r(14),h=r(15),d=function(e){function t(t,r){var i=e.call(this,t,r)||this;return i.testEnvironment=!1,i.heartbeatTimer=null,i.heartbeatInterval=1e4,i.qualityTimerInterval=3e3,i.maxRetryCount=5,i.previewVideoList=[],i.signalList={},i.checkMessageTimeout=function(){for(var e in i.signalList)i.signalList[e].signal&&i.signalList[e].signal.checkMessageTimeout()},i.getAllInUseUrl=function(){var e=[];for(var t in i.signalList)e.push(t);return e},i.onDisconnectHandle=function(e){if(i.logger.info("zsc.od.0 call"),i.signalList[e]){for(var t=i.signalList[e],r=0;r<t.publishConnectedList.length;r++){var n=i.publisherList[t.publishConnectedList[r]];n&&n.publisher&&n.publisher.onDisconnect()}for(r=0;r<t.playConnectedList.length;r++){var s=i.playerList[t.playConnectedList[r]];s&&s.player&&s.player.onDisconnect()}delete i.signalList[e],i.stopSignalHeartbeat()}},i.logger=t,i.stateCenter=r,i.dataReport=new s.ZegoDataReport(i.logger),i}return n(t,e),t.prototype.onSignalDisconnected=function(e){},t.prototype.setQualityMonitorCycle=function(e){this.logger.debug("zsc.qmc.0 timeInterval "+e),this.qualityTimerInterval=e},t.prototype.setSessionInfo=function(e,t,r,i){this.logger.debug("zsc.ssi.0 called"),this.appid=e,this.userid=t,this.token=r,this.testEnvironment=i},t.prototype.onPlayStateUpdate=function(e,t,r){},t.prototype.onPlayQualityUpdate=function(e,t){},t.prototype.onPublishStateUpdate=function(e,t,r){},t.prototype.onPublishQualityUpdate=function(e,t){},t.prototype.onUpdateHeartBeartIntervalHandle=function(e){e!=this.heartbeatInterval&&(this.logger.debug("zsc.uhb.0 update "+e),this.heartbeatTimer&&(clearTimeout(this.heartbeatTimer),this.heartbeatTimer=null),this.heartbeatInterval=e,this.startSignalHeartbeat())},t.prototype.switchDevice=function(e,t,r,i,n){var s,o,a,c,u,l,h=this,d=null,p=/Safari/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent);if(s=this.checkPreview(t)){o=s.mediaStreamConfig,"video"===e?(o.videoInput=r,c=t.srcObject.getVideoTracks()[0]):(o.audioInput=r,c=t.srcObject.getAudioTracks()[0]),!p&&c.stop();var f=s.getMediaStreamConstraints(o);navigator.mediaDevices.getUserMedia(f).then((function(r){for(var n in"video"===e?(u=r.getVideoTracks()[0],s.localStream.removeTrack(s.localStream.getVideoTracks()[0])):(u=r.getAudioTracks()[0],s.localStream.removeTrack(s.localStream.getAudioTracks()[0])),h.publisherList)h.publisherList[n].localVideo===t&&(l=n);l&&(d=h.publisherList[h.getTotalStreamId(l)].publisher,(a=d.peerConnection.getSenders().find((function(e){return e.track.kind===u.kind})))?a.replaceTrack(u):h.logger.warn("zg.sd.0 no sender found, only swithcing device on localMediaElement")),s.localStream.addTrack(u),i&&i()}),(function(e){return n&&n(e)}))}else this.logger.error("zg.sd.0 no preview found")},t.prototype.enableMicrophone=function(e,t){var r=this.checkPreview(e);return r?r.enableMicrophone(t):(this.logger.info("zsc.em.0 no preview"),!1)},t.prototype.enableCamera=function(e,t){var r=this.checkPreview(e);return r?r.enableCamera(t):(this.logger.error("zsc.ec.0 no preview"),!1)},t.prototype.startPreview=function(e,t,r,i){if(!e)return this.logger.error("zsc.sp.0 localVideo null"),!1;var n=this.checkPreview(e);return n?(this.logger.warn("zsc.sp.0 localvideo already exist"),!0):(n=new o.ZegoPreview(this.logger),this.previewVideoList.push(n),n.startPreview(e,t,r,i),this.logger.debug("zsc.sp.0 call success"),!0)},t.prototype.stopPreview=function(e){if(!e)return this.logger.warn("zsc.sp.0 localVideo null"),!1;for(var t in this.publisherList)this.publisherList[t].localVideo===e&&(this.publisherList[t].localVideo=null);var r=this.checkPreview(e);return r?(r.previewSuc&&(r.stopPreview(),this.removePreview(r)),!0):(this.logger.warn("zsc.sp.0 no preview"),!1)},t.prototype.setPublishStateStart=function(e,t,r){var i=this,n=this.getTotalStreamId(e);if(this.publisherList[n])return this.logger.error("zsc.pss.0 publisher already exist"),!1;var s=new a.ZegoPublish(this.logger,null,this.dataReport,this.qualityTimerInterval);return s.onPublishStateUpdate=function(t,r,n){var s=i.publisherList[r];s?i.onPublishStateUpdate(t,s.streamId,n):i.logger.error("zsc.psuh.0 cannot find publish "+e)},s.onPublishQualityUpdate=function(t,r){var n=i.publisherList[t];n?i.onPublishQualityUpdate(n.streamId,r):i.logger.error("zsc.psuh.0 cannot find publish "+e)},this.publisherList[n]={localVideo:t,publisher:s,serverUrls:[],retryCount:0,streamId:e,playOption:r},this.dataReport.eventStart(s.reportSeq,"GetSignalUrl"),!0},t.prototype.getTotalStreamId=function(e){if(this.testEnvironment){var t="zegotest-"+this.appid+"-"+e;return this.logger.info("zsc.gts.0 test streamid "+t),t}return e},t.prototype.startPublishingStream=function(e,t,r){this.logger.info("zsc.sps.0 call");var i=this.getTotalStreamId(e),n=this.publisherList[i];if(!n)return this.logger.error("zsc.sps.0 publisher don't exist"),!1;var s=n.publisher;if(this.dataReport.eventEndWithMsg(s.reportSeq,"GetSignalUrl",{urls:t}),!t||0===t.length)return this.onPublishStateUpdate(c.ENUM_PUBLISH_STATE_UPDATE.error,e,c.publishErrorList.DISPATCH_ERROR),this.logger.info("zsc.sps.0 server don't have signal url"),!1;var o=this.server||t[0];return n.serverUrls=n.serverUrls.concat(t),this.connectPublishServer(i,o)},t.prototype.updateWaitingList=function(e,t,r,i,n){t?e.publishWaitingList.push({streamId:r,success:i,error:n}):e.playWaitingList.push({streamId:r,success:i,error:n})},t.prototype.publishStream=function(e){var t=this.publisherList[e].publisher;if(t){var r=null,i=null,n=this.publisherList[e].playOption,s=this.checkPreview(this.publisherList[e].localVideo);s&&(r=s.localStream,i=s.videoInfo),r||this.logger.info("zsc.ps.0 no localStream"),this.logger.debug("zsc.ps.0 call success"),t.startPublish(e,r,i,s.mediaStreamConfig,n)}else this.logger.info("zsc.ps.0 publisher don't exist")},t.prototype.connectPublishServer=function(e,t){var r=this,i=this.publisherList[e];return i?(this.dataReport.eventStart(i.publisher.reportSeq,"ConnectServer"),this.connetWithReuseSignalServer(e,!0,t,(function(e,i){var n=r.publisherList[e];if(n){var s=n.publisher;if(s){r.dataReport.eventEndWithMsg(s.reportSeq,"ConnectServer",{result:0,server:t});var o=i.tokenInfo;r.logger.info("zsc.cps.0 update token success"),o&&o.report&&(s.qualityUpload=o.report,s.qualityUploadInterval=o.report_interval),s.signal=i.signal,n.retryCount=0,r.server=t,r.publishStream(e),r.getTokenSuccess()}else r.logger.info("zsc.cps.1 check publisher don't exist")}else r.logger.info("zsc.cps.0 after connect publisher don't exist")}),(function(e,t){r.logger.error("zsc.cps.0 update token failed "+t);var i=r.publisherList[e];if(i)if(r.shouldRetry(i,t)){r.logger.info("zsc.cps.1 retry connect");var n=i.serverUrls[0];i.serverUrls.splice(0,1),i.retryCount+=1,r.connectPublishServer(e,n)}else r.onPublishStateUpdate(c.ENUM_PUBLISH_STATE_UPDATE.error,e,c.publishErrorList.TOKEN_ERROR);else r.logger.info("zsc.cps.0 after connect publisher don't exist")})),!0):(this.logger.error("zsc.cps.0 publisher don't exist"),!1)},t.prototype.shouldRetry=function(e,t){return 0!=e.serverUrls.length&&!(e.retryCount>=this.maxRetryCount)&&3==t},t.prototype.getTokenSuccess=function(){this.logger.debug("zsc.gts.0 call")},t.prototype.stopPublishingStream=function(e){var t=this.getTotalStreamId(e),r=this.publisherList[t];r?(r.publisher&&(r.publisher.stopPublish(),delete r.publisher),this.removeStreamFromSignal(!0,t),this.stopSignalHeartbeat(),delete this.publisherList[t],this.logger.debug("zsc.sps.0.1 call success")):this.logger.warn("zsc.sps.0.1 publisher don't exist")},t.prototype.setPlayStreamAudioOutput=function(e,t){var r=this.getTotalStreamId(e);if(null!=t&&0!=t.length){this.logger.debug("zsc.psao.1 device "+t);var i=this.playerList[r];return i?i.player?i.player.setAudioDestination(t):(this.logger.info("zsc.psao.1 player don't exist"),!1):(this.logger.info("zsc.psao.1 play don't exist"),!1)}return!1},t.prototype.setStreamAudioOutput=function(e,t){var r=this;return!(null==t||0==t.length||!e||(this.logger.debug("zsc.ssao.0 device "+t),e?"undefined"!==e.sinkId?(e.setSinkId(t).then((function(){r.logger.info("zsc.ssao.0 success device: "+t)})).catch((function(e){r.logger.info("zsc.ssao.0 "+e.name)})),0):(this.logger.error("zsc.ssao.0 browser does not suppport"),1):(this.logger.error("zsc.ssao.0 no localVideo"),1)))},t.prototype.connetWithReuseSignalServer=function(e,t,r,i,n){var s=this;this.logger.info("zsc.crss.0 begin "+r);var o=null;if(this.signalList[r])(o=this.signalList[r]).state==u.ENUM_SIGNAL_STATE.connected?(this.logger.info("zsc.crss.0 already connected "+r+" streamId: "+e),t?o.publishConnectedList.push(e):o.playConnectedList.push(e),i(e,o)):o.state==u.ENUM_SIGNAL_STATE.connecting&&(this.logger.debug("zsc.crss.0 signal is connecting "+r+" streamId: "+e),this.updateWaitingList(o,t,e,i,n));else{this.logger.info("zsc.crss.0 new signal "+r+" streamId: "+e);var a=new l.ZegoSignal(this.logger,this.stateCenter);a.setSessionInfo(this.appid,this.userid),a.onUpdateHeartBeartInterval=this.onUpdateHeartBeartIntervalHandle,a.onDisconnect=this.onDisconnectHandle,this.signalList[r]={signal:a,state:u.ENUM_SIGNAL_STATE.connecting,publishWaitingList:[],playWaitingList:[],publishConnectedList:[],playConnectedList:[],tokenInfo:null},this.updateWaitingList(this.signalList[r],t,e,i,n),a.connectServer(this.token,r,(function(e,t,i){o=s.signalList[r];var n,a,c=0;if(0!=e){for(s.logger.debug("zsc.crss.0 connect failed "+t),c=0;c<o.publishWaitingList.length;c++)(n=o.publishWaitingList[c]).error&&n.error(n.streamId,e);for(c=0;c<o.playWaitingList.length;c++)(a=o.playWaitingList[c]).error&&a.error(a.streamId,e);delete s.signalList[r]}else{for(s.logger.debug("zsc.crss.0 connected success "+t),o.state=u.ENUM_SIGNAL_STATE.connected,o.tokenInfo=i,c=0;c<o.publishWaitingList.length;c++)(n=o.publishWaitingList[c]).success&&n.success(n.streamId,o),o.publishConnectedList.push(n.streamId);for(c=0;c<o.playWaitingList.length;c++)(a=o.playWaitingList[c]).success&&a.success(a.streamId,o),o.playConnectedList.push(a.streamId);o.publishWaitingList=[],o.playWaitingList=[],null==s.heartbeatTimer&&s.startSignalHeartbeat()}}))}},t.prototype.setPlayStateStart=function(e,t,r,i){var n=this.getTotalStreamId(e);if(this.playerList[n])return this.logger.warn("zsc.pss.1 player already exist"),!1;var s=new h.ZegoPlayWeb(this.logger,null,this.dataReport,this.qualityTimerInterval);return s.onPlayStateUpdate=this.onPlayStateUpdate,s.onPlayQualityUpdate=this.onPlayQualityUpdate,s.onVideoSizeChanged=this.onVideoSizeChanged,this.playerList[n]={player:s,remoteVideo:t,audioOutput:r,signal:null,serverUrls:[],retryCount:0,playOption:i},this.dataReport.eventStart(s.reportSeq,"GetSignalUrl"),!0},t.prototype.startPlayingStream=function(e,t,r){this.logger.info("zsc.sps.1 start play called");var i=this.getTotalStreamId(e),n=this.playerList[i];if(!n)return this.logger.error("zsc.sps.1 player don't exist"),!1;var s=n.player;return this.dataReport.eventEndWithMsg(s.reportSeq,"GetSignalUrl",{urls:t}),0==t.length?(this.onPlayStateUpdate(c.ENUM_PLAY_STATE_UPDATE.error,e,c.playErrorList.DISPATCH_ERROR),this.logger.info("zsc.sps.1 server don't have signal url"),!1):(n.serverUrls=n.serverUrls.concat(t),this.connectPlayServer(i,this.server||t[0]))},t.prototype.connectPlayServer=function(e,t){var r=this,i=this.playerList[e];return i?(this.dataReport.eventStart(i.player.reportSeq,"ConnectServer"),this.connetWithReuseSignalServer(e,!1,t,(function(e,i){var n=r.playerList[e];if(n){var s=n.player;if(s){r.dataReport.eventEndWithMsg(s.reportSeq,"ConnectServer",{result:0,server:t});var o=i.tokenInfo;r.logger.info("zsc.cps.1 update token success"),o&&o.report&&(s.qualityUpload=o.report,s.qualityUploadInterval=o.report_interval),s.signal=i.signal,n.retryCount=0,r.server=t,r.playStream(e),r.getTokenSuccess()}else r.logger.error("zsc.cps.1 checkplayer don't exist")}else r.logger.error("zsc.cps.1 after connect player don't exist")}),(function(e,t){var i=r.playerList[e];if(i)if(r.shouldRetry(i,t)){r.logger.info("zsc.cps.1 retry connect");var n=i.serverUrls[0];i.serverUrls.splice(0,1),i.retryCount+=1,r.connectPlayServer(e,n)}else r.onPlayStateUpdate(c.ENUM_PLAY_STATE_UPDATE.error,e,c.playErrorList.TOKEN_ERROR);else r.logger.error("zsc.cps.1 after connect player don't exist")})),!0):(this.logger.error("zsc.cps.1 player don't exist"),!1)},t.prototype.playStream=function(e){var t=this.playerList[e].player;t?(this.logger.info("zsc.ps.1 call success"),t.startPlay(e,this.playerList[e].remoteVideo,this.playerList[e].audioOutput,this.playerList[e].playOption)):this.logger.warn("zsc.ps.1 player don't exist")},t.prototype.removeStreamFromSignal=function(e,t){var r=[];for(var i in this.signalList){var n=this.signalList[i];if(e){for(var s=0;s<n.publishConnectedList.length;s++)if(n.publishConnectedList[s]===t){this.logger.debug("zsc.rsfs.0 found from publish"),n.publishConnectedList.splice(s,1);break}}else for(var o=0;o<n.playConnectedList.length;o++)if(n.playConnectedList[o]===t){this.logger.debug("zsc.rsfs.0 found from play"),n.playConnectedList.splice(o,1);break}0==n.publishConnectedList.length&&0==n.playConnectedList.length&&(n.signal.disconnectServer(),r.push(i))}for(var a=0;a<r.length;a++)delete this.signalList[r[a]]},t.prototype.stopSignalHeartbeat=function(){this.logger.debug("zsc.ssh.1 call");var e=0;for(var t in this.signalList)e+=1;this.heartbeatTimer&&0==e&&(this.logger.info("zsc.ssh.1 stop"),clearTimeout(this.heartbeatTimer),this.heartbeatTimer=null)},t.prototype.stopPlayingStream=function(e){var t=this.getTotalStreamId(e),r=this.playerList[t];r?(r.player&&(r.player.stopPlay(),delete r.player),this.removeStreamFromSignal(!1,t),this.stopSignalHeartbeat(),delete this.playerList[t],this.logger.debug("zsc.sps.1.1 call success")):this.logger.info("zsc.sps.1.1 player don't exist")},t.prototype.reset=function(){for(var e in this.publisherList)this.publisherList[e].publisher&&this.publisherList[e].publisher.stopPublish();for(var t in this.playerList)this.playerList[t].player&&this.playerList[t].player.stopPlay();for(var r in this.signalList)this.signalList[r].signal&&this.signalList[r].signal.disconnectServer();this.playerList={},this.publisherList={},this.signalList={},this.server="",this.heartbeatTimer&&(clearTimeout(this.heartbeatTimer),this.heartbeatTimer=null)},t.prototype.startSignalHeartbeat=function(){var e=this;this.logger.debug("zsc.ssh.0 call"),this.heartbeatTimer&&(clearTimeout(this.heartbeatTimer),this.heartbeatTimer=null),this.heartbeatTimer=setTimeout((function(){e.checkSignalHeartbeat()}),this.heartbeatInterval)},t.prototype.checkSignalHeartbeat=function(){for(var e in this.logger.debug("zsc.csh.0 call"),this.signalList)this.signalList[e].signal&&this.signalList[e].signal.sendHeartbeat();this.heartbeatTimer&&this.startSignalHeartbeat()},t.prototype.checkPreview=function(e){for(var t=0;t<this.previewVideoList.length;t++)if(this.previewVideoList[t].localVideo===e)return this.previewVideoList[t];return null},t.prototype.removePreview=function(e){for(var t=0;t<this.previewVideoList.length;t++)if(this.previewVideoList[t]===e){this.previewVideoList.splice(t,1);break}},t.prototype.onPlayerStreamUrlUpdate=function(e,t,r){},t.prototype.onVideoSizeChanged=function(e,t,r){},t}(r(16).ZegoStreamCenter);t.ZegoStreamCenterWeb=d},function(e,t,r){"use strict";var i=this&&this.__assign||Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e};Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e){this.log=e,this.dataStatistics={},this.logger=e}return e.prototype.newReport=function(e){this.dataStatistics[e]={abs_time:Date.now(),time_consumed:0,error:0,events:[]}},e.prototype.addMsgExt=function(e,t){this.dataStatistics[e]?this.dataStatistics[e].msg_ext=t:console.warn(e+" not exist")},e.prototype.eventStart=function(e,t){this.dataStatistics[e]?null!=this.dataStatistics[e].events?this.dataStatistics[e].events.push({event:t,abs_time:Date.now(),time_consumed:0}):this.logger.warn("zd.es.0 no events"):this.logger.warn("zd.es.0 no seq match")},e.prototype.eventEnd=function(e,t,r){if(this.dataStatistics[e]){var i=this.dataStatistics[e].events;if(i&&0!==i.length){for(var n=i.length-1;n>=0;n--)if(i[n].event==t&&i[n].time_consumed){i[n].time_consumed=Date.now()-i[n].abs_time;break}}else this.logger.info("zd.ee.0 no events")}else this.logger.info("zd.ee.0 no seq match")},e.prototype.eventEndWithMsg=function(e,t,r){if(this.dataStatistics[e]){var n=this.dataStatistics[e].events;if(n){for(var s=n.length-1;s>=0;s--)if(n[s].event==t&&n[s].time_consumed){n[s].time_consumed=Date.now()-n[s].abs_time,null==n[s].msg_ext&&(n[s].msg_ext={}),n[s].msg_ext=i({},r);break}}else this.logger.warn("zd.ee.0 no events")}else this.logger.warn("zd.ee.0 no seq match")},e.prototype.addEventInfo=function(e,t,r,i){if(this.dataStatistics[e]){var n=this.dataStatistics[e].events;if(null!=n){for(var s=n.length-1;s>=0;s--)if(n[s].event==t&&null!=n[s].time_consumed&&n[s].event==t&&null!=n[s].time_consumed){null==n[s].msg_ext&&(n[s].msg_ext={}),n[s].msg_ext[r]=i;break}}else this.logger.warn("zd.aei.0 no events")}else this.logger.warn("zd.aei.0 no seq match")},e.prototype.addEvent=function(e,t,r){this.dataStatistics[e]?this.dataStatistics[e].events&&(r?this.dataStatistics[e].events.push({event:t,abs_time:Date.now(),msg_ext:r}):this.dataStatistics[e].events.push({event:t,abs_time:Date.now()})):this.logger.warn("zd.ae.0 no seq match")},e.prototype.uploadReport=function(e,t){var r=this.dataStatistics[e];null!=r&&(r.itemtype=t,r.time_consumed=Date.now()-r.abs_time,this.logger.report(r),delete this.dataStatistics[e])},e}();t.ZegoDataReport=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=function(){function e(e){var t=this;this.log=e,this.localVideo=null,this.localStream=null,this.videoInfo={},this.previewSuc=!1,this.enableMicrophone=function(e){return t.localStream?(t.localStream.getAudioTracks().forEach((function(t){t.enabled=e})),t.logger.debug("zp.em.2 call success"),!0):(t.logger.error("zp.em.2 no localStream"),!1)},this.enableCamera=function(e){return t.localStream?(t.localStream.getVideoTracks().forEach((function(t){t.enabled=e})),t.logger.debug("zp.ec.2 call success"),!0):(t.logger.error("zp.ec.2 no localStream"),!1)},this.setAudioDestination=function(e){return t.localVideo?"undefined"!==t.localVideo.sinkId?(t.localVideo.setSinkId(e).then((function(){t.logger.info("zp.sad.2 success device: "+e)})).catch((function(e){t.logger.info("zp.sad.2 "+e.name)})),!0):(t.logger.error("zp.sad.2 browser does not suppport"),!1):(t.logger.error("zp.sad.2 no localVideo"),!1)},this.logger=e}return e.prototype.getMediaStreamConstraints=function(e){var t={audio:null,video:null};if(t.audio=!1,t.video=!1,console.log("mediaStreamConfig",e),e.audio&&(void 0===e.audioInput&&void 0===e.noiseSuppression&&void 0===e.autoGainControl&&void 0===e.echoCancellation?(t.audio={},t.audio.noiseSuppression=!0,t.audio.autoGainControl=!0,t.audio.echoCancellation=!0):(t.audio={},void 0!==e.audioInput&&null!==e.audioInput&&(t.audio.deviceId=e.audioInput),void 0!==e.noiseSuppression&&(t.audio.noiseSuppression=e.noiseSuppression),void 0!==e.autoGainControl&&(t.audio.autoGainControl=e.autoGainControl),void 0!==e.echoCancellation&&(t.audio.echoCancellation=e.echoCancellation))),e.video){var r=640,n=480,s=15,o=800;if(1===e.videoQuality?(r=i.ENUM_RESOLUTION_TYPE.LOW.width,n=i.ENUM_RESOLUTION_TYPE.LOW.height,s=i.ENUM_RESOLUTION_TYPE.LOW.frameRate,o=i.ENUM_RESOLUTION_TYPE.LOW.bitRate):2===e.videoQuality?(r=i.ENUM_RESOLUTION_TYPE.MEDIUM.width,n=i.ENUM_RESOLUTION_TYPE.MEDIUM.height,s=i.ENUM_RESOLUTION_TYPE.MEDIUM.frameRate,o=i.ENUM_RESOLUTION_TYPE.MEDIUM.bitRate):3===e.videoQuality?(r=i.ENUM_RESOLUTION_TYPE.HIGH.width,n=i.ENUM_RESOLUTION_TYPE.HIGH.height,s=i.ENUM_RESOLUTION_TYPE.HIGH.frameRate,o=i.ENUM_RESOLUTION_TYPE.HIGH.bitRate):4===e.videoQuality?(r=e.width,n=e.height,s=e.frameRate,o=e.bitRate||800):this.logger.info("zp.gmsc.2 user default"),!0===e.horizontal){var a=n;n=r,r=a}t.video={width:r,height:n,frameRate:s,bitRate:o},null!=e.facingMode?t.video.facingMode=e.facingMode:null!=e.videoInput&&null!=e.videoInput&&(t.video.deviceId={exact:e.videoInput}),this.logger.info("zp.gmsc.2 width: "+r+" height: "+n+" rate: "+s)}return t},e.prototype.startPreview=function(e,t,r,i){var n=this;if(this.logger.debug("zp.sv.2 called"),this.localVideo=e,this.mediaStreamConfig=t,void 0!==navigator.mediaDevices&&null!=navigator.mediaDevices.getUserMedia){if(t.externalMediaStream instanceof MediaStream)return this.logger.debug("zp.sv.2 use external media stream"),this.previewSuc=!0,this.localStream=t.externalMediaStream,this.videoInfo={width:t.width,height:t.height,frameRate:t.frameRate,bitRate:t.bitRate},void(r&&r());if(t.externalCapture)this.captureStream(e)?(this.previewSuc=!0,r&&r()):i&&i("browser don't support");else{var s=this.getMediaStreamConstraints(t);this.videoInfo=s.video,this.logger.info("zp.sv.2 ",JSON.stringify(s)),navigator.mediaDevices.getUserMedia(s).then((function(e){if(n.logger.info("zp.sv.2 success"),!n.localVideo)return n.logger.info("zp.sv.2 no localVideo"),void(i&&i("no localVideo"));n.localVideo.srcObject=e,n.localStream=e,n.previewSuc=!0,r&&r()}),(function(e){n.logger.info("zp.sv.2 failed"),i&&i(e.name)}))}}else i&&i("browser don't support")},e.prototype.captureStream=function(e){if(!e)return this.logger.info("zp.cs.2 no local video"),!1;if(e.captureStream)this.localStream=e.captureStream(),this.logger.debug("zp.cs.2 captureStream");else{if(!e.mozCaptureStream)return this.logger.info("zp.cs.2 don't support"),!1;this.localStream=e.mozCaptureStream(),this.logger.debug("zp.cs.2 mozCaptureStream")}return this.videoInfo={width:e.videoWidth,height:e.videoHeight,frameRate:0,bitRate:0},this.logger.debug("zp.cs.2 called success"),!0},e.prototype.stopPreview=function(){if(this.logger.info("zp.sv.2.1 called"),this.localStream){var e=this.localStream.getTracks();e.reverse(),e.forEach((function(e){e.stop()})),this.localStream=null,this.localVideo.srcObject=null,this.localVideo=null,this.videoInfo={}}},e}();t.ZegoPreview=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=r(2),s=r(12),o=r(3),a=r(4),c=r(13),u=function(){function e(e,t,r,s){this.state=i.ENUM_PUBLISH_STATE.stop,this.sessionId=0,this.waitingICETimeInterval=5e3,this.waitingAnswerTimeInterval=5e3,this.candidateInfo=[],this.waitingICETimer=null,this.waitingAnswerTimer=null,this.qualityTimer=null,this.publishQualityList=[],this.maxQualityListCount=10,this.lastPublishStats={},this.reportSeq=n.getSeq(),this.qualityUpload=!1,this.qualityUploadInterval=3e4,this.qualityUploadLastTime=0,this.qualitySeq=0,this.maxRetryCount=3,this.currentRetryCount=0,this.retryState=i.ENUM_RETRY_STATE.didNotStart,this.waitingServerTimerInterval=3e3,this.waitingServerTimer=null,this.videoInfo={width:0,height:0,frameRate:0,bitRate:0},this.mediaStreamConfig=null,this.offerSeq=0,this.qualityCount=0,this.closeSessionSignal=!1,this.audioBitRate=48e3,this.localSdpRevert=!1,this.videoDecodeType="H264",this.logger=e,this.signal=t,this.dataReport=r,this.qualityTimeInterval=s,this.audioMixing=new o.audioMixUtil(e),r.newReport(this.reportSeq)}return e.prototype.publishStateUpdateError=function(e){0!=this.sessionId&&this.shouldSendCloseSession(e)&&(this.signal.sendCloseSession(n.getSeq(),this.sessionId,1),this.closeSessionSignal=!0),this.state=i.ENUM_PUBLISH_STATE.stop,this.onPublishStateUpdate(n.ENUM_PUBLISH_STATE_UPDATE.error,this.streamId,e),this.resetPublish()},e.prototype.resetPublish=function(){this.logger.info("zp.rp.0 call"),this.streamId=null,this.state=i.ENUM_PUBLISH_STATE.stop,null==this.peerConnection&&null==this.peerConnection||(this.peerConnection.close(),this.peerConnection=null),null!=this.waitingAnswerTimer&&(clearTimeout(this.waitingAnswerTimer),this.waitingAnswerTimer=null),null!=this.waitingICETimer&&(clearTimeout(this.waitingICETimer),this.waitingICETimer=null),this.clearPublishQualityTimer(),this.signal&&(this.signal.unregisterPushCallback("CandidateInfoPush",this.sessionId),this.signal.unregisterPushCallback("MediaDescPush",this.sessionId),this.signal.unregisterPushCallback("CloseSessionPush",this.sessionId)),this.sessionSeq=0,this.offerSeq=0,this.candidateInfo=[],this.publishQualityList=[],this.qualityUploadLastTime=0,this.currentRetryCount=0,this.retryState=i.ENUM_RETRY_STATE.didNotStart,this.clearTryPublishTimer()},e.prototype.clearTryPublishTimer=function(){null!=this.waitingServerTimer&&(clearTimeout(this.waitingServerTimer),this.waitingServerTimer=null)},e.prototype.clearPublishQualityTimer=function(){null!=this.qualityTimer&&(clearInterval(this.qualityTimer),this.qualityTimer=null),this.lastPublishStats={},this.qualityCount=0},e.prototype.shouldSendCloseSession=function(e){return this.state!=i.ENUM_PUBLISH_STATE.stop&&this.state!=i.ENUM_PUBLISH_STATE.waitingSessionRsp},e.prototype.startPublish=function(e,t,r,s,o){var a=this;this.logger.info("zp.sp.0 called"),e?(this.streamId=e,this.localStream=t,this.mediaStreamConfig=s,navigator.userAgent.toLowerCase().indexOf("firefox")>-1&&(s.externalCapture||s.externalMediaStream)&&(this.localStream.onaddtrack=function(){a.logger.info("zp.sp.0 Track added");var e=a.localStream.getVideoTracks(),t=a.localStream.getAudioTracks();e.length>1?(a.peerConnection.getSenders().find((function(t){return t.track.kind===e[1].kind})).replaceTrack(e[1]),a.localStream.removeTrack(e[0])):t.length>1&&(a.peerConnection.getSenders().find((function(e){return e.track.kind===t[1].kind})).replaceTrack(t[1]),a.localStream.removeTrack(t[0]))}),r&&(this.videoInfo=r),o&&o.audioBitRate&&(this.audioBitRate=o.audioBitRate),o&&o.videoDecodeType&&(this.videoDecodeType=o.videoDecodeType),this.sessionSeq=n.getSeq(),this.dataReport.eventStart(this.reportSeq,"CreateSession"),this.signal.createSession(this.sessionSeq,0,0,e,o&&o.streamParams,(function(e,t,r){a.dataReport.eventEndWithMsg(a.reportSeq,"CreateSession",{sessionId:r.session_id}),a.logger.info("zp.sp.0 sessionId:"+r.session_id),a.sessionSeq==e?0!==r.result?(a.logger.error("zp.sp.0 create session failed "+r.result),a.publishStateUpdateError(n.publishErrorList.CREATE_SESSION_ERROR)):(a.sessionId=r.session_id,a.logger.debug("zp.sp.0 create session success "+a.sessionId),a.onCreatePublishSessionSuccess(r)):a.logger.error("zp.sp.0 seq is not match.")}),(function(e,t){a.dataReport.eventEndWithMsg(a.reportSeq,"CreateSession",{error:e}),a.publishStateUpdateError(n.publishErrorList.SEND_SESSION_TIMEOUT)})),this.state=i.ENUM_PUBLISH_STATE.waitingSessionRsp,this.logger.debug("zp.sp.0 called success")):this.logger.error("zp.sp.0 streamId is null")},e.prototype.onCreatePublishSessionSuccess=function(e){var t=this;this.logger.info("zp.ops.0 called");var r=[];e.turn_server&&r.push(e.turn_server),e.stun_server&&r.push(e.stun_server);var i={iceTransportPolicy:"relay",iceServers:[{urls:r,username:e.turn_username,credential:e.turn_auth_key}]};this.logger.info("zp.ops.0 username: "+e.turn_username),this.logger.info("zp.ops.0 credential: "+e.turn_auth_key),this.peerConnection=new RTCPeerConnection(i),this.peerConnection.onicecandidate=function(e){t.onIceCandidate(e)},this.peerConnection.onsignalingstatechange=function(e){t.onConnectionStateChange(e)},this.peerConnection.oniceconnectionstatechange=function(e){t.onIceConnectionStateChange(e)};var s=[],o=[];this.localStream&&(this.localStream.getTracks().forEach((function(e){t.peerConnection.addTrack(e,t.localStream)})),s=this.localStream.getVideoTracks(),o=this.localStream.getAudioTracks(),console.warn("getConstraints",o&&o[0]&&o[0].getConstraints&&o[0].getConstraints()),s.length>0&&this.logger.info("zp.ops.0 video device: "+s[0].label),o.length>0&&this.logger.info("zp.ops.0 audio device: "+o[0].label));var a={offerToReceiveAudio:o.length>0?1:0,offerToReceiveVideo:s.length>0?1:0};this.logger.info("zp.ops.0 createOffer: "+a),this.dataReport.eventStart(this.reportSeq,"CreateOffer"),this.peerConnection.createOffer(a).then((function(e){t.dataReport.eventEnd(t.reportSeq,"CreateOffer"),t.onCreateOfferSuccess(e)}),(function(e){t.dataReport.eventEndWithMsg(t.reportSeq,"CreateOffer",{error:e.toString()}),t.logger.error("zp.ops.0 create offer error "+e.toString()),t.publishStateUpdateError(n.publishErrorList.CREATE_OFFER_ERROR)})),this.signal.registerPushCallback("CandidateInfoPush",this.sessionId,(function(e,r,i){t.onRecvCandidateInfo(e,r,i)})),this.signal.registerPushCallback("CloseSessionPush",this.sessionId,(function(e,r,i){t.onRecvCloseSession(e,r,i)})),this.signal.registerPushCallback("MediaDescPush",this.sessionId,(function(e,r,i){t.onRecvMediaDescription(e,r,i)})),this.signal.registerPushCallback("SessionResetPush",this.sessionId,(function(e,r,i){t.onRecvResetSession(e,r,i)})),this.logger.debug("zp.ops.0 call success")},e.prototype.onCreateOfferSuccess=function(e){var t=this;0!=this.videoInfo.bitRate&&(e.sdp=this.updateBandwidthRestriction(e.sdp,this.videoInfo.bitRate)),e.sdp=e.sdp.replace(/sendrecv/g,"sendonly"),e.sdp=e.sdp.replace(/useinbandfec=\d+/,"maxaveragebitrate="+this.audioBitRate),/m=video[\s\S]*m=audio/.test(e.sdp)&&(this.localSdpRevert=!0),e.sdp=a.sdpUtil.getSDPByVideDecodeType(e.sdp,this.videoDecodeType),this.logger.info("zp.oco.0 localSdp1 "+e.sdp.substr(0,e.sdp.length/2)),this.logger.info("zp.oco.0 localSdp2 "+e.sdp.substr(e.sdp.length/2)),this.dataReport.eventStart(this.reportSeq,"SetLocalDescription"),this.peerConnection.setLocalDescription(e).then((function(){t.dataReport.eventEnd(t.reportSeq,"SetLocalDescription"),t.onSetLocalDescriptionSuccess(e)}),(function(e){t.dataReport.eventEndWithMsg(t.reportSeq,"SetLocalDescription",{error:e.toString()}),t.logger.error("zp.oco.0 error "+e.toString()),t.publishStateUpdateError(n.publishErrorList.SET_LOCAL_DESC_ERROR)}))},e.prototype.updateBandwidthRestriction=function(e,t){var r="AS";return"firefox"===s.browserDetails.browser&&(t=1e3*(t>>>0),r="TIAS"),-1===e.indexOf("b="+r+":")?(e=e.replace(/c=IN (.*)\r\n/g,"c=IN $1\r\nb="+r+":"+t+"\r\n")).replace("b="+r+":"+t+"\r\n",""):(e=e.replace(new RegExp("b="+r+":.*\r\n","g"),"b="+r+":"+t+"\r\n")).replace("b="+r+":"+t+"\r\n","")},e.prototype.onSetLocalDescriptionSuccess=function(e){var t=this;this.logger.info("zp.osd.0 success");var r={sdp:e.sdp,width:this.videoInfo.width,height:this.videoInfo.height,frameRate:this.videoInfo.frameRate,video_min_kpbs:this.videoInfo.bitRate,video_max_kpbs:this.videoInfo.bitRate,audio_kpbs:48};this.offerSeq=n.getSeq(),this.dataReport.eventStart(this.reportSeq,"SendMediaDesc"),this.signal.sendMediaDesc(this.offerSeq,this.sessionId,0,r,(function(e,r,s){t.offerSeq==e&&t.sessionId==r?(t.logger.info("zp.osd.0 send success"),t.dataReport.eventEnd(t.reportSeq,"SendMediaDesc"),t.waitingAnswerTimer=setTimeout((function(){t.state==i.ENUM_PUBLISH_STATE.waitingServerAnswer&&(t.logger.error("zp.osd.0 waiting timeout"),t.publishStateUpdateError(n.publishErrorList.SERVER_MEDIA_DESC_TIMEOUT))}),t.waitingAnswerTimeInterval),t.state=i.ENUM_PUBLISH_STATE.waitingServerAnswer):t.logger.error("zp.osd.0 seq or sessionId is not equal")}),(function(e,r){t.dataReport.eventEndWithMsg(t.reportSeq,"SendMediaDesc",{error:e}),t.publishStateUpdateError(n.publishErrorList.SEND_MEDIA_DESC_TIMEOUT)})),this.state=i.ENUM_PUBLISH_STATE.waitingOffserRsp,this.logger.debug("zp.osd.0 call success")},e.prototype.onRecvMediaDescription=function(e,t,r){this.logger.info("zp.ormd.0 received"),this.state==i.ENUM_PUBLISH_STATE.waitingServerAnswer?(null!=this.waitingAnswerTimer&&(clearTimeout(this.waitingAnswerTimer),this.waitingAnswerTimer=null),this.dataReport.addEvent(this.reportSeq,"RecvMediaDesc"),this.signal.sendMediaDescAck(e,this.sessionId,0),1==r.type?this.onGetRemoteOfferSucceses(r.sdp):this.publishStateUpdateError(n.publishErrorList.SERVER_MEDIA_DESC_ERROR)):this.logger.info("zp.ormd.0 current state "+this.state+" not allowed")},e.prototype.onGetRemoteOfferSucceses=function(e){var t=this;if(48e3!==this.audioBitRate&&(e=e.replace(/maxaveragebitrate=(\d+)/,"maxaveragebitrate="+this.audioBitRate)),this.localSdpRevert){var r=[/[\s\S]*m=audio/.exec(e)[0].replace("m=audio",""),/m=video[\s\S]*/.exec(e)[0],/m=audio[\s\S]*m=video/.exec(e)[0].replace("m=video","")],s=r[0],o=r[1],a=r[2],c=/a=group:BUNDLE\s+(\w+)\s+(\w+)/.exec(s);e=(s=s.replace(/a=group:BUNDLE\s+(\w+)\s+(\w+)/,"a=group:BUNDLE "+c[2]+" "+c[1]))+o+a}this.logger.info("zp.oro.0 remoteSdp:",e);var u={type:"answer",sdp:e,toJSON:function(){}};this.dataReport.eventStart(this.reportSeq,"SetRemoteDescription"),this.peerConnection.setRemoteDescription(new RTCSessionDescription(u)).then((function(){t.logger.info("zp.oro.0 set success"),t.dataReport.eventEnd(t.reportSeq,"SetRemoteDescription")}),(function(e){t.logger.error("zp.oro.0 failed: "+e.toString()),t.dataReport.eventEndWithMsg(t.reportSeq,"SetRemoteDescription",{error:e.toString()}),t.publishStateUpdateError(n.publishErrorList.SET_REMOTE_DESC_ERROR)})),this.sendCandidateInfo(this.candidateInfo),this.candidateInfo=[],this.state=i.ENUM_PUBLISH_STATE.waitingServerICE,this.waitingICETimer=setTimeout((function(){t.state==i.ENUM_PUBLISH_STATE.waitingServerICE&&(t.logger.error("zp.orod.0 waiting server timeout"),t.publishStateUpdateError(n.publishErrorList.SERVER_CANDIDATE_TIMEOUT))}),this.waitingICETimeInterval),this.logger.debug("zp.oro.0 call success")},e.prototype.onIceConnectionStateChange=function(e){this.state!=i.ENUM_PUBLISH_STATE.stop&&null!=this.peerConnection&&(this.logger.info("zp.oics.0 stateChanged "+this.peerConnection.iceConnectionState),"connected"===this.peerConnection.iceConnectionState?(this.logger.info("zp.oics.0 connected state "+this.state),this.dataReport.eventEnd(this.reportSeq,"IceConnected"),this.state!=i.ENUM_PUBLISH_STATE.publishing&&this.onPublishStateUpdate(n.ENUM_PUBLISH_STATE_UPDATE.start,this.streamId),this.state=i.ENUM_PUBLISH_STATE.publishing,this.retryState!=i.ENUM_RETRY_STATE.didNotStart&&(this.retryState=i.ENUM_RETRY_STATE.finished,this.currentRetryCount=0),this.dataReport.eventStart(this.reportSeq,"PublishState"),this.setPublishQualityTimer()):"closed"===this.peerConnection.iceConnectionState?(this.dataReport.addEvent(this.reportSeq,"IceClosed"),this.checkPublishConnectionFailedState(this.peerConnection.iceConnectionState)):"failed"===this.peerConnection.iceConnectionState&&(this.dataReport.addEvent(this.reportSeq,"IceFailed"),this.checkPublishConnectionFailedState(this.peerConnection.iceConnectionState)))},e.prototype.onIceCandidate=function(e){if(this.logger.info("zp.oic.0 candidate"+e.candidate),e.candidate)if(this.logger.info("zp.oic.0 candidate"+e.candidate.candidate),this.state<i.ENUM_PUBLISH_STATE.waitingServerICE||this.state==i.ENUM_PUBLISH_STATE.stop)this.candidateInfo.push({candidate:e.candidate.candidate,sdpMid:e.candidate.sdpMid,sdpMLineIndex:e.candidate.sdpMLineIndex});else{var t={candidate:e.candidate.candidate,sdpMid:e.candidate.sdpMid,sdpMLineIndex:e.candidate.sdpMLineIndex};this.sendCandidateInfo([t])}},e.prototype.sendCandidateInfo=function(e){var t=this;this.logger.info("zp.sci.0 called"),!(e=e.filter((function(e){return e.candidate.indexOf("relay")>0})))||e.length<1?this.logger.info("zp.sci.0 cancelled"):(this.dataReport.eventStart(this.reportSeq,"SendIceCandidate"),this.signal.sendCandidateInfo(n.getSeq(),this.sessionId,e,(function(e,r,i){t.logger.debug("zp.sci.0 send success"),t.dataReport.eventEnd(t.reportSeq,"SendIceCandidate")}),(function(e,r){t.logger.error("zp.sci.0 failed to send: "+e.toString()),t.dataReport.eventEndWithMsg(t.reportSeq,"SendIceCandidate",{error:e}),t.publishStateUpdateError(n.publishErrorList.SEND_CANDIDATE_TIMEOUT)})))},e.prototype.onConnectionStateChange=function(e){this.logger.info("zp.ocs.0 called "+e.target.signalingState)},e.prototype.onRecvCandidateInfo=function(e,t,r){var s=this;if(this.logger.debug("zp.oci.0 received "+r.infos.length),this.state==i.ENUM_PUBLISH_STATE.waitingServerICE){null!=this.waitingICETimer&&(clearTimeout(this.waitingICETimer),this.waitingICETimer=null),this.dataReport.addEvent(this.reportSeq,"RecvIceCandidate"),this.signal.sendCandidateInfoAck(e,this.sessionId,0);for(var o=0;o<r.infos.length;o++){var a={sdpMid:r.infos[o].sdpMid,sdpMLineIndex:r.infos[o].sdpMLineIndex,candidate:r.infos[o].candidate};this.logger.debug("zp.orci.0 candidate "+a.candidate),this.peerConnection.addIceCandidate(new RTCIceCandidate(a)).then((function(){s.logger.debug("zp.oci.0 add success")}),(function(e){s.logger.error("zp.oci.0 add error "+e.toString()),s.publishStateUpdateError(n.publishErrorList.SERVER_CANDIDATE_ERROR)}))}this.state=i.ENUM_PUBLISH_STATE.connecting,this.dataReport.eventStart(this.reportSeq,"IceConnected")}else this.logger.info("zp.oci.0 current state "+this.state+" not allowed")},e.prototype.onRecvCloseSession=function(e,t,r){this.logger.info("zp.orcs.0 reason: "+r.reason),this.dataReport.addEvent(this.reportSeq,"RecvCloseSession"),this.signal.sendCloseSessionAck(e,this.sessionId,0);var i=JSON.parse(JSON.stringify(n.publishErrorList.SESSION_CLOSED));i.msg+=r.reason,this.publishStateUpdateError(i)},e.prototype.onRecvResetSession=function(e,t,r){this.logger.info("zp.orrs.0 received "),t==this.sessionId?(this.dataReport.addEvent(this.reportSeq,"RecvResetSession"),this.shouldRetryPublish()&&this.startRetryPublish()):this.logger.error("zp.orrs.0 cannot find session")},e.prototype.shouldRetryPublish=function(){return this.retryState==i.ENUM_RETRY_STATE.didNotStart&&this.state!=i.ENUM_PUBLISH_STATE.publishing?(this.logger.info("zp.srp.0.0 connection didn't success"),!1):this.retryState==i.ENUM_RETRY_STATE.retrying?(this.logger.info("zp.srp.0.0 already retrying"),!1):this.currentRetryCount>this.maxRetryCount?(this.logger.info("zp.srp.0.0 beyond max"),!1):(this.logger.info("zp.srp.1.0 call success"),!0)},e.prototype.startRetryPublish=function(){this.logger.info("zp.srp.0 call");var e=this.streamId;e?(this.resetPublish(),this.tryStartPublish(e)):this.logger.info("zp.srp.0 no streamid")},e.prototype.tryStartPublish=function(e){var t=this;if(this.logger.info("zp.tsp.0 call"),this.clearTryPublishTimer(),this.streamId=e,this.currentRetryCount>this.maxRetryCount)return this.logger.info("zp.tsp.0 beyond max limit"),void this.publishStateUpdateError(n.publishErrorList.WEBSOCKET_ERROR);this.retryState=i.ENUM_RETRY_STATE.retrying,this.currentRetryCount+=1,this.signal.isServerConnected()?(this.logger.debug("zp.tsp.0 signal connected"),this.startPublish(e,this.localStream,this.videoInfo,this.mediaStreamConfig)):(this.logger.debug("zp.tsp.0 signal server not connected"),this.waitingAnswerTimer=setTimeout((function(){t.tryStartPublish(e)}),this.waitingAnswerTimeInterval))},e.prototype.checkPublishConnectionFailedState=function(e){var t=null;"failed"==e?t=n.publishErrorList.MEDIA_CONNECTION_FAILED:"closed"==e&&(t=n.publishErrorList.MEDIA_CONNECTION_CLOSED),null!=t&&(this.state!=i.ENUM_PUBLISH_STATE.publishing&&this.retryState==i.ENUM_PUBLISH_STATE.didNotStart?(this.logger.info("zp.oics.0  state "+this.state+" retryState "+this.retryState+" connectionState "+e),this.publishStateUpdateError(t)):this.shouldRetryPublish()?(this.onPublishStateUpdate(n.ENUM_PUBLISH_STATE_UPDATE.retry,this.streamId),this.startRetryPublish()):this.publishStateUpdateError(t))},e.prototype.setPublishQualityTimer=function(){var e=this;null==this.qualityTimer&&(this.logger.debug("zp.spq.0 called"),this.clearPublishQualityTimer(),this.qualityTimer=setInterval((function(){e.peerConnection&&e.peerConnection.getStats(null).then((function(t){e.getPublishStats(t)}),(function(t){e.logger.info("zp.spq.0 getStats error "+t.toString())}))}),this.qualityTimeInterval),this.lastPublishStats={time:0,audioBytesSent:0,videoBytesSent:0,framesEncoded:0,framesSent:0},this.qualitySeq=n.getSeq(),this.qualityCount=0,this.dataReport.newReport(this.qualitySeq))},e.prototype.getPublishStats=function(e){var t=this;if(e){var r={audioCodeType:"opus",audioBitrate:0,videoBitrate:0,videoFPS:0,nackCount:0,pliCount:0,frameHeight:0,frameWidth:0,videoTransferFPS:0,totalRoundTripTime:0,currentRoundTripTime:0},i=this.lastPublishStats.time;e.forEach((function(e){("outbound-rtp"==e.type||"ssrc"==e.type&&null!=e.bytesSent)&&"audio"==e.mediaType?(0!=i&&(r.audioBitrate=8*(e.bytesSent-t.lastPublishStats.audioBytesSent)/(e.timestamp-i)),r.audioBitrate<0&&(r.audioBitrate=0),t.lastPublishStats.audioBytesSent=e.bytesSent,t.lastPublishStats.time=e.timestamp):("outbound-rtp"==e.type||"ssrc"==e.type&&null!=e.bytesSent)&&"video"==e.mediaType?(0!=i&&(r.videoBitrate=8*(e.bytesSent-t.lastPublishStats.videoBytesSent)/(e.timestamp-i),r.videoFPS=1e3*(e.framesEncoded-t.lastPublishStats.framesEncoded)/(e.timestamp-i)),r.videoBitrate<0&&(r.videoBitrate=0),r.videoFPS<0&&(r.videoFPS=0),r.nackCount=e.nackCount,r.pliCount=e.pliCount,t.lastPublishStats.videoBytesSent=e.bytesSent,t.lastPublishStats.framesEncoded=e.framesEncoded,t.lastPublishStats.time=e.timestamp):"track"==e.type&&("video"==e.kind||e.id.indexOf("video")>=0)?(r.frameHeight=e.frameHeight,r.frameWidth=e.frameWidth,0!=i&&(r.videoTransferFPS=1e3*(e.framesSent-t.lastPublishStats.framesSent)/(e.timestamp-i)),r.videoTransferFPS<0&&(r.videoTransferFPS=0),t.lastPublishStats.framesSent=e.framesSent):"candidate-pair"==e.type&&(null!=e.totalRoundTripTime&&(r.totalRoundTripTime=e.totalRoundTripTime),null!=e.currentRoundTripTime&&(r.currentRoundTripTime=e.currentRoundTripTime))})),this.uploadPublishQuality(r),0!=i&&this.onPublishQualityUpdate(this.streamId,r)}},e.prototype.uploadPublishQuality=function(e){var t=this;if(this.qualityUpload){var r=Date.parse(new Date+"");(0==this.qualityUploadLastTime||r-this.qualityUploadLastTime>=this.qualityUploadInterval)&&(this.logger.debug("zp.upq.0 upload"),e.stream_type="publish",e.stream_id=this.streamId,e.timeStamp=r/1e3,this.signal.QualityReport(n.getSeq(),this.sessionId,e,(function(e,r,i){void 0!==i.report&&(t.qualityUpload=i.report,t.qualityUploadInterval=i.report_interval_ms)}),(function(e,r){t.logger.info("zp.upq.0 upload failed "+e)})),this.qualityUploadLastTime=r)}},e.prototype.stopPublish=function(){this.logger.debug("zp.sp.0.1 called"),this.sessionId&&!this.closeSessionSignal&&this.signal.sendCloseSession(n.getSeq(),this.sessionId,0),this.dataReport.eventEndWithMsg(this.reportSeq,"PublishState",{state:this.state+""}),this.dataReport.addEvent(this.reportSeq,"StopPublish"),this.dataReport.addMsgExt(this.reportSeq,{stream:this.streamId,sessionId:this.sessionId}),this.dataReport.uploadReport(this.reportSeq,"RTCPublishStream"),this.resetPublish()},e.prototype.onPublishStateUpdate=function(e,t,r){},e.prototype.onPublishQualityUpdate=function(e,t){},e.prototype.onDisconnect=function(){this.logger.info("zp.od.0 call"),this.logger.info("zp.od.0 websocket disconnect"),this.dataReport.addEvent(this.reportSeq,"OnDisconnect"),this.publishStateUpdateError(n.publishErrorList.WEBSOCKET_ERROR)},e.prototype.playEffect=function(e,t,r,i){this.audioMixing.localStream=this.localStream,this.audioMixing.peerConnection=this.peerConnection,this.audioMixing.audioBuffer=t,this.audioMixing.playEffect(e.playTime,e.loop,e.replace,r,i)},e.prototype.pauseEffect=function(){this.audioMixing.pauseEffect()},e.prototype.resumeEffect=function(){this.audioMixing.resumeEffect()},e.prototype.startMixingAudio=function(e,t){return this.audioMixing.localStream=this.localStream,this.audioMixing.peerConnection=this.peerConnection,this.audioMixing.startMixingAudio(e,t)},e.prototype.stopMixingAudio=function(){return this.audioMixing.stopMixingAudio()},e.prototype.voiceChange=function(e){var t=new("undefined"!=typeof webkitAudioContext?webkitAudioContext:AudioContext),r=null,i=null;if(this.pitchEffect||(this.pitchEffect=new c.pitchUtil(t),r=t.createMediaStreamSource(this.localStream.clone()),i=t.createMediaStreamDestination(),r.connect(this.pitchEffect.input),this.pitchEffect.output.connect(i)),this.pitchEffect.setPitchOffset(e),!this.micTrack){var n=i.stream.getAudioTracks()[0],s=this.peerConnection.getSenders().find((function(e){return e.track.kind===n.kind}));if(!s)return this.logger.error("zp.vc.0 no sender"),!1;this.micTrack=this.localStream.getAudioTracks()[0],s.replaceTrack(n),this.localStream.removeTrack(this.micTrack),this.localStream.addTrack(n)}},e.prototype.voiceBack=function(){var e=this;this.micTrack?(this.peerConnection.getSenders().find((function(t){return t.track.kind===e.micTrack.kind})).replaceTrack(this.micTrack),this.localStream.removeTrack(this.localStream.getAudioTracks()[0]),this.localStream.addTrack(this.micTrack),this.micTrack=null,this.pitchEffect=null):this.logger.error("zp.vb.0 mo mickTrack found")},e}();t.ZegoPublish=u},function(e,t,r){var i;e.exports=function e(t,r,n){function s(a,c){if(!r[a]){if(!t[a]){if(!c&&"function"==typeof i&&i)return i(a,!0);if(o)return o(a,!0);var u=new Error("Cannot find module '"+a+"'");throw u.code="MODULE_NOT_FOUND",u}var l=r[a]={exports:{}};t[a][0].call(l.exports,(function(e){return s(t[a][1][e]||e)}),l,l.exports,e,t,r,n)}return r[a].exports}for(var o="function"==typeof i&&i,a=0;a<n.length;a++)s(n[a]);return s}({1:[function(e,t,r){"use strict";var i=(0,e("./adapter_factory.js").adapterFactory)({window:window});t.exports=i},{"./adapter_factory.js":2}],2:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.adapterFactory=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).window,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0},r=i.log,u=i.detectBrowser(e),l={browserDetails:u,commonShim:c,extractVersion:i.extractVersion,disableLog:i.disableLog,disableWarnings:i.disableWarnings};switch(u.browser){case"chrome":if(!n||!n.shimPeerConnection||!t.shimChrome)return r("Chrome shim is not included in this adapter release."),l;r("adapter.js shimming chrome."),l.browserShim=n,n.shimGetUserMedia(e),n.shimMediaStream(e),n.shimPeerConnection(e),n.shimOnTrack(e),n.shimAddTrackRemoveTrack(e),n.shimGetSendersWithDtmf(e),n.shimGetStats(e),n.shimSenderReceiverGetStats(e),n.fixNegotiationNeeded(e),c.shimRTCIceCandidate(e),c.shimConnectionState(e),c.shimMaxMessageSize(e),c.shimSendThrowTypeError(e),c.removeAllowExtmapMixed(e);break;case"firefox":if(!o||!o.shimPeerConnection||!t.shimFirefox)return r("Firefox shim is not included in this adapter release."),l;r("adapter.js shimming firefox."),l.browserShim=o,o.shimGetUserMedia(e),o.shimPeerConnection(e),o.shimOnTrack(e),o.shimRemoveStream(e),o.shimSenderGetStats(e),o.shimReceiverGetStats(e),o.shimRTCDataChannel(e),c.shimRTCIceCandidate(e),c.shimConnectionState(e),c.shimMaxMessageSize(e),c.shimSendThrowTypeError(e);break;case"edge":if(!s||!s.shimPeerConnection||!t.shimEdge)return r("MS edge shim is not included in this adapter release."),l;r("adapter.js shimming edge."),l.browserShim=s,s.shimGetUserMedia(e),s.shimGetDisplayMedia(e),s.shimPeerConnection(e),s.shimReplaceTrack(e),c.shimMaxMessageSize(e),c.shimSendThrowTypeError(e);break;case"safari":if(!a||!t.shimSafari)return r("Safari shim is not included in this adapter release."),l;r("adapter.js shimming safari."),l.browserShim=a,a.shimRTCIceServerUrls(e),a.shimCreateOfferLegacy(e),a.shimCallbacksAPI(e),a.shimLocalStreamsAPI(e),a.shimRemoteStreamsAPI(e),a.shimTrackEventTransceiver(e),a.shimGetUserMedia(e),c.shimRTCIceCandidate(e),c.shimMaxMessageSize(e),c.shimSendThrowTypeError(e),c.removeAllowExtmapMixed(e);break;default:r("Unsupported browser!")}return l};var i=u(e("./utils")),n=u(e("./chrome/chrome_shim")),s=u(e("./edge/edge_shim")),o=u(e("./firefox/firefox_shim")),a=u(e("./safari/safari_shim")),c=u(e("./common_shim"));function u(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}},{"./chrome/chrome_shim":3,"./common_shim":6,"./edge/edge_shim":7,"./firefox/firefox_shim":11,"./safari/safari_shim":14,"./utils":15}],3:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=e("./getusermedia");Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return n.shimGetUserMedia}});var s=e("./getdisplaymedia");Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return s.shimGetDisplayMedia}}),r.shimMediaStream=function(e){e.MediaStream=e.MediaStream||e.webkitMediaStream},r.shimOnTrack=function(e){if("object"!==(void 0===e?"undefined":i(e))||!e.RTCPeerConnection||"ontrack"in e.RTCPeerConnection.prototype)o.wrapPeerConnectionEvent(e,"track",(function(e){return e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e}));else{Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var r=this;return this._ontrackpoly||(this._ontrackpoly=function(t){t.stream.addEventListener("addtrack",(function(i){var n;n=e.RTCPeerConnection.prototype.getReceivers?r.getReceivers().find((function(e){return e.track&&e.track.id===i.track.id})):{track:i.track};var s=new Event("track");s.track=i.track,s.receiver=n,s.transceiver={receiver:n},s.streams=[t.stream],r.dispatchEvent(s)})),t.stream.getTracks().forEach((function(i){var n;n=e.RTCPeerConnection.prototype.getReceivers?r.getReceivers().find((function(e){return e.track&&e.track.id===i.id})):{track:i};var s=new Event("track");s.track=i,s.receiver=n,s.transceiver={receiver:n},s.streams=[t.stream],r.dispatchEvent(s)}))},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}},r.shimGetSendersWithDtmf=function(e){if("object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){var t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};var r=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,i){var n=r.apply(this,arguments);return n||(n=t(this,e),this._senders.push(n)),n};var n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){n.apply(this,arguments);var t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}var s=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){var r=this;this._senders=this._senders||[],s.apply(this,[e]),e.getTracks().forEach((function(e){r._senders.push(t(r,e))}))};var o=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;this._senders=this._senders||[],o.apply(this,[e]),e.getTracks().forEach((function(e){var r=t._senders.find((function(t){return t.track===e}));r&&t._senders.splice(t._senders.indexOf(r),1)}))}}else if("object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){var a=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){var e=this,t=a.apply(this,[]);return t.forEach((function(t){return t._pc=e})),t},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}},r.shimGetStats=function(e){if(e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(e,r,i){var n=this,s=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof arguments[0]))return t.apply(this,[]);var o=function(e){var t={};return e.result().forEach((function(e){var r={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((function(t){r[t]=e.stat(t)})),t[r.id]=r})),t},a=function(e){return new Map(Object.keys(e).map((function(t){return[t,e[t]]})))};return arguments.length>=2?t.apply(this,[function(e){s[1](a(o(e)))},arguments[0]]):new Promise((function(e,r){t.apply(n,[function(t){e(a(o(t)))},r])})).then(r,i)}}},r.shimSenderReceiverGetStats=function(e){if("object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver){if(!("getStats"in e.RTCRtpSender.prototype)){var t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){var e=this,r=t.apply(this,[]);return r.forEach((function(t){return t._pc=e})),r});var r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){var e=this;return this._pc.getStats().then((function(t){return o.filterStats(t,e.track,!0)}))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){var n=e.RTCPeerConnection.prototype.getReceivers;n&&(e.RTCPeerConnection.prototype.getReceivers=function(){var e=this,t=n.apply(this,[]);return t.forEach((function(t){return t._pc=e})),t}),o.wrapPeerConnectionEvent(e,"track",(function(e){return e.receiver._pc=e.srcElement,e})),e.RTCRtpReceiver.prototype.getStats=function(){var e=this;return this._pc.getStats().then((function(t){return o.filterStats(t,e.track,!1)}))}}if("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype){var s=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){var t=arguments[0],r=void 0,i=void 0,n=void 0;return this.getSenders().forEach((function(e){e.track===t&&(r?n=!0:r=e)})),this.getReceivers().forEach((function(e){return e.track===t&&(i?n=!0:i=e),e.track===t})),n||r&&i?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):r?r.getStats():i?i.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return s.apply(this,arguments)}}}},r.shimAddTrackRemoveTrackWithNative=a,r.shimAddTrackRemoveTrack=function(e){if(e.RTCPeerConnection){var t=o.detectBrowser(e);if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return a(e);var r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){var e=this,t=r.apply(this);return this._reverseStreams=this._reverseStreams||{},t.map((function(t){return e._reverseStreams[t.id]}))};var i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){var r=this;if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach((function(e){if(r.getSenders().find((function(t){return t.track===e})))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[t.id]){var n=new e.MediaStream(t.getTracks());this._streams[t.id]=n,this._reverseStreams[n.id]=t,t=n}i.apply(this,[t])};var n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},n.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,r){var i=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find((function(e){return e===t})))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");var s=this.getSenders().find((function(e){return e.track===t}));if(s)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};var o=this._streams[r.id];if(o)o.addTrack(t),Promise.resolve().then((function(){i.dispatchEvent(new Event("negotiationneeded"))}));else{var a=new e.MediaStream([t]);this._streams[r.id]=a,this._reverseStreams[a.id]=r,this.addStream(a)}return this.getSenders().find((function(e){return e.track===t}))},["createOffer","createAnswer"].forEach((function(t){var r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){var e=this,t=arguments,i=arguments.length&&"function"==typeof arguments[0];return i?r.apply(this,[function(r){var i=u(e,r);t[0].apply(null,[i])},function(e){t[1]&&t[1].apply(null,e)},arguments[2]]):r.apply(this,arguments).then((function(t){return u(e,t)}))}}));var s=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=(e=this,t=arguments[0],r=t.sdp,Object.keys(e._reverseStreams||[]).forEach((function(t){var i=e._reverseStreams[t],n=e._streams[i.id];r=r.replace(new RegExp(i.id,"g"),n.id)})),new RTCSessionDescription({type:t.type,sdp:r})),s.apply(this,arguments)):s.apply(this,arguments);var e,t,r};var c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get:function(){var e=c.get.apply(this);return""===e.type?e:u(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){var t=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(e._pc!==this)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};var r=void 0;Object.keys(this._streams).forEach((function(i){t._streams[i].getTracks().find((function(t){return e.track===t}))&&(r=t._streams[i])})),r&&(1===r.getTracks().length?this.removeStream(this._reverseStreams[r.id]):r.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function u(e,t){var r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((function(t){var i=e._reverseStreams[t],n=e._streams[i.id];r=r.replace(new RegExp(n.id,"g"),i.id)})),new RTCSessionDescription({type:t.type,sdp:r})}},r.shimPeerConnection=function(e){if(!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection){["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}}));var t=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?t.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())}}},r.fixNegotiationNeeded=function(e){o.wrapPeerConnectionEvent(e,"negotiationneeded",(function(e){if("stable"===e.target.signalingState)return e}))};var o=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(e("../utils.js"));function a(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){var e=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((function(t){return e._shimmedLocalStreams[t][0]}))};var t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){if(!r)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};var i=t.apply(this,arguments);return this._shimmedLocalStreams[r.id]?-1===this._shimmedLocalStreams[r.id].indexOf(i)&&this._shimmedLocalStreams[r.id].push(i):this._shimmedLocalStreams[r.id]=[r,i],i};var r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){var t=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach((function(e){if(t.getSenders().find((function(t){return t.track===e})))throw new DOMException("Track already exists.","InvalidAccessError")}));var i=this.getSenders();r.apply(this,arguments);var n=this.getSenders().filter((function(e){return-1===i.indexOf(e)}));this._shimmedLocalStreams[e.id]=[e].concat(n)};var i=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],i.apply(this,arguments)};var n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){var t=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach((function(r){var i=t._shimmedLocalStreams[r].indexOf(e);-1!==i&&t._shimmedLocalStreams[r].splice(i,1),1===t._shimmedLocalStreams[r].length&&delete t._shimmedLocalStreams[r]})),n.apply(this,arguments)}}},{"../utils.js":15,"./getdisplaymedia":4,"./getusermedia":5}],4:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(r){return t(r).then((function(t){var i=r.video&&r.video.width,n=r.video&&r.video.height,s=r.video&&r.video.frameRate;return r.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:s||3}},i&&(r.video.mandatory.maxWidth=i),n&&(r.video.mandatory.maxHeight=n),e.navigator.mediaDevices.getUserMedia(r)}))}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}},{}],5:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimGetUserMedia=function(e){var t=e&&e.navigator;if(t.mediaDevices){var r=n.detectBrowser(e),o=function(e){if("object"!==(void 0===e?"undefined":i(e))||e.mandatory||e.optional)return e;var t={};return Object.keys(e).forEach((function(r){if("require"!==r&&"advanced"!==r&&"mediaSource"!==r){var n="object"===i(e[r])?e[r]:{ideal:e[r]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);var s=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];var o={};"number"==typeof n.ideal?(o[s("min",r)]=n.ideal,t.optional.push(o),(o={})[s("max",r)]=n.ideal,t.optional.push(o)):(o[s("",r)]=n.ideal,t.optional.push(o))}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[s("",r)]=n.exact):["min","max"].forEach((function(e){void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[s(e,r)]=n[e])}))}})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},a=function(e,n){if(r.version>=61)return n(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"===i(e.audio)){var a=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])};a((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),a(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=o(e.audio)}if(e&&"object"===i(e.video)){var c=e.video.facingMode;c=c&&("object"===(void 0===c?"undefined":i(c))?c:{ideal:c});var u=r.version<66;if(c&&("user"===c.exact||"environment"===c.exact||"user"===c.ideal||"environment"===c.ideal)&&(!t.mediaDevices.getSupportedConstraints||!t.mediaDevices.getSupportedConstraints().facingMode||u)){delete e.video.facingMode;var l=void 0;if("environment"===c.exact||"environment"===c.ideal?l=["back","rear"]:"user"!==c.exact&&"user"!==c.ideal||(l=["front"]),l)return t.mediaDevices.enumerateDevices().then((function(t){var r=(t=t.filter((function(e){return"videoinput"===e.kind}))).find((function(e){return l.some((function(t){return e.label.toLowerCase().includes(t)}))}));return!r&&t.length&&l.includes("back")&&(r=t[t.length-1]),r&&(e.video.deviceId=c.exact?{exact:r.deviceId}:{ideal:r.deviceId}),e.video=o(e.video),s("chrome: "+JSON.stringify(e)),n(e)}))}e.video=o(e.video)}return s("chrome: "+JSON.stringify(e)),n(e)},c=function(e){return r.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}};if(t.getUserMedia=function(e,r,i){a(e,(function(e){t.webkitGetUserMedia(e,r,(function(e){i&&i(c(e))}))}))}.bind(t),t.mediaDevices.getUserMedia){var u=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return a(e,(function(e){return u(e).then((function(t){if(e.audio&&!t.getAudioTracks().length||e.video&&!t.getVideoTracks().length)throw t.getTracks().forEach((function(e){e.stop()})),new DOMException("","NotFoundError");return t}),(function(e){return Promise.reject(c(e))}))}))}}}};var n=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(e("../utils.js")),s=n.log},{"../utils.js":15}],6:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimRTCIceCandidate=function(e){if(e.RTCIceCandidate&&!(e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)){var t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"===(void 0===e?"undefined":i(e))&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){var r=new t(e),n=s.default.parseCandidate(e.candidate),o=Object.assign(r,n);return o.toJSON=function(){return{candidate:o.candidate,sdpMid:o.sdpMid,sdpMLineIndex:o.sdpMLineIndex,usernameFragment:o.usernameFragment}},o}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,o.wrapPeerConnectionEvent(e,"icecandidate",(function(t){return t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t}))}},r.shimMaxMessageSize=function(e){if(!e.RTCSctpTransport&&e.RTCPeerConnection){var t=o.detectBrowser(e);"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get:function(){return void 0===this._sctp?null:this._sctp}});var r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,function(e){if(!e||!e.sdp)return!1;var t=s.default.splitSections(e.sdp);return t.shift(),t.some((function(e){var t=s.default.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")}))}(arguments[0])){var e=function(e){var t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;var r=parseInt(t[1],10);return r!=r?-1:r}(arguments[0]),i=(c=e,u=65536,"firefox"===t.browser&&(u=t.version<57?-1===c?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),u),n=function(e,r){var i=65536;"firefox"===t.browser&&57===t.version&&(i=65535);var n=s.default.matchPrefix(e.sdp,"a=max-message-size:");return n.length>0?i=parseInt(n[0].substr(19),10):"firefox"===t.browser&&-1!==r&&(i=2147483637),i}(arguments[0],e),o=void 0;o=0===i&&0===n?Number.POSITIVE_INFINITY:0===i||0===n?Math.max(i,n):Math.min(i,n);var a={};Object.defineProperty(a,"maxMessageSize",{get:function(){return o}}),this._sctp=a}var c,u;return r.apply(this,arguments)}}},r.shimSendThrowTypeError=function(e){if(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype){var t=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){var e=t.apply(this,arguments);return r(e,this),e},o.wrapPeerConnectionEvent(e,"datachannel",(function(e){return r(e.channel,e.target),e}))}function r(e,t){var r=e.send;e.send=function(){var i=arguments[0],n=i.length||i.size||i.byteLength;if("open"===e.readyState&&t.sctp&&n>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return r.apply(e,arguments)}}},r.shimConnectionState=function(e){if(e.RTCPeerConnection&&!("connectionState"in e.RTCPeerConnection.prototype)){var t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get:function(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get:function(){return this._onconnectionstatechange||null},set:function(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((function(e){var r=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=function(e){var t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;var r=new Event("connectionstatechange",e);t.dispatchEvent(r)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}}))}},r.removeAllowExtmapMixed=function(e){if(e.RTCPeerConnection){var t=o.detectBrowser(e);if(!("chrome"===t.browser&&t.version>=71)){var r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(e){return e&&e.sdp&&-1!==e.sdp.indexOf("\na=extmap-allow-mixed")&&(e.sdp=e.sdp.split("\n").filter((function(e){return"a=extmap-allow-mixed"!==e.trim()})).join("\n")),r.apply(this,arguments)}}}};var n,s=(n=e("sdp"))&&n.__esModule?n:{default:n},o=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(e("./utils"))},{"./utils":15,sdp:17}],7:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var i=e("./getusermedia");Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return i.shimGetUserMedia}});var n=e("./getdisplaymedia");Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return n.shimGetDisplayMedia}}),r.shimPeerConnection=function(e){var t=o.detectBrowser(e);if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)){var r=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set:function(e){r.set.call(this,e);var t=new Event("enabled");t.enabled=e,this.dispatchEvent(t)}})}!e.RTCRtpSender||"dtmf"in e.RTCRtpSender.prototype||Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}}),e.RTCDtmfSender&&!e.RTCDTMFSender&&(e.RTCDTMFSender=e.RTCDtmfSender);var i=(0,c.default)(e,t.version);e.RTCPeerConnection=function(e){return e&&e.iceServers&&(e.iceServers=(0,a.filterIceServers)(e.iceServers,t.version),o.log("ICE servers after filtering:",e.iceServers)),new i(e)},e.RTCPeerConnection.prototype=i.prototype},r.shimReplaceTrack=function(e){!e.RTCRtpSender||"replaceTrack"in e.RTCRtpSender.prototype||(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)};var s,o=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(e("../utils")),a=e("./filtericeservers"),c=(s=e("rtcpeerconnection-shim"))&&s.__esModule?s:{default:s}},{"../utils":15,"./filtericeservers":8,"./getdisplaymedia":9,"./getusermedia":10,"rtcpeerconnection-shim":16}],8:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.filterIceServers=function(e,t){var r=!1;return(e=JSON.parse(JSON.stringify(e))).filter((function(e){if(e&&(e.urls||e.url)){var t=e.urls||e.url;e.url&&!e.urls&&i.deprecated("RTCIceServer.url","RTCIceServer.urls");var n="string"==typeof t;return n&&(t=[t]),t=t.filter((function(e){if(0===e.indexOf("stun:"))return!1;var t=e.startsWith("turn")&&!e.startsWith("turn:[")&&e.includes("transport=udp");return t&&!r?(r=!0,!0):t&&!r})),delete e.url,e.urls=n?t[0]:t,!!t.length}}))};var i=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(e("../utils"))},{"../utils":15}],9:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(e){"getDisplayMedia"in e.navigator&&e.navigator.mediaDevices&&(e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator)))}},{}],10:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetUserMedia=function(e){var t=e&&e.navigator,r=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return r(e).catch((function(e){return Promise.reject(function(e){return{name:{PermissionDeniedError:"NotAllowedError"}[e.name]||e.name,message:e.message,constraint:e.constraint,toString:function(){return this.name}}}(e))}))}}},{}],11:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=e("./getusermedia");Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return n.shimGetUserMedia}});var s=e("./getdisplaymedia");Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return s.shimGetDisplayMedia}}),r.shimOnTrack=function(e){"object"===(void 0===e?"undefined":i(e))&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})},r.shimPeerConnection=function(e){var t=o.detectBrowser(e);if("object"===(void 0===e?"undefined":i(e))&&(e.RTCPeerConnection||e.mozRTCPeerConnection)){!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}}));var r=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())};var n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},s=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(e,r,i){return s.apply(this,[e||null]).then((function(e){if(t.version<53&&!r)try{e.forEach((function(e){e.type=n[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach((function(t,r){e.set(r,Object.assign({},t,{type:n[t.type]||t.type}))}))}return e})).then(r,i)}}},r.shimSenderGetStats=function(e){if("object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection&&e.RTCRtpSender&&!(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)){var t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){var e=this,r=t.apply(this,[]);return r.forEach((function(t){return t._pc=e})),r});var r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}},r.shimReceiverGetStats=function(e){if("object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection&&e.RTCRtpSender&&!(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)){var t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){var e=this,r=t.apply(this,[]);return r.forEach((function(t){return t._pc=e})),r}),o.wrapPeerConnectionEvent(e,"track",(function(e){return e.receiver._pc=e.srcElement,e})),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}},r.shimRemoveStream=function(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;o.deprecated("removeStream","removeTrack"),this.getSenders().forEach((function(r){r.track&&e.getTracks().includes(r.track)&&t.removeTrack(r)}))})},r.shimRTCDataChannel=function(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)};var o=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(e("../utils"))},{"../utils":15,"./getdisplaymedia":12,"./getusermedia":13}],12:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(r){if(!r||!r.video){var i=new DOMException("getDisplayMedia without video constraints is undefined");return i.name="NotFoundError",i.code=8,Promise.reject(i)}return!0===r.video?r.video={mediaSource:t}:r.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(r)})}},{}],13:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimGetUserMedia=function(e){var t=n.detectBrowser(e),r=e&&e.navigator,s=e&&e.MediaStreamTrack;if(r.getUserMedia=function(e,t,i){n.deprecated("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(e).then(t,i)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){var o=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},a=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(e){return"object"===(void 0===e?"undefined":i(e))&&"object"===i(e.audio)&&(e=JSON.parse(JSON.stringify(e)),o(e.audio,"autoGainControl","mozAutoGainControl"),o(e.audio,"noiseSuppression","mozNoiseSuppression")),a(e)},s&&s.prototype.getSettings){var c=s.prototype.getSettings;s.prototype.getSettings=function(){var e=c.apply(this,arguments);return o(e,"mozAutoGainControl","autoGainControl"),o(e,"mozNoiseSuppression","noiseSuppression"),e}}if(s&&s.prototype.applyConstraints){var u=s.prototype.applyConstraints;s.prototype.applyConstraints=function(e){return"audio"===this.kind&&"object"===(void 0===e?"undefined":i(e))&&(e=JSON.parse(JSON.stringify(e)),o(e,"autoGainControl","mozAutoGainControl"),o(e,"noiseSuppression","mozNoiseSuppression")),u.apply(this,[e])}}}};var n=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(e("../utils"))},{"../utils":15}],14:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimLocalStreamsAPI=function(e){if("object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){var t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){var r=this;this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getTracks().forEach((function(i){return t.call(r,i,e)}))},e.RTCPeerConnection.prototype.addTrack=function(e,r){return r&&(this._localStreams?this._localStreams.includes(r)||this._localStreams.push(r):this._localStreams=[r]),t.call(this,e,r)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;this._localStreams||(this._localStreams=[]);var r=this._localStreams.indexOf(e);if(-1!==r){this._localStreams.splice(r,1);var i=e.getTracks();this.getSenders().forEach((function(e){i.includes(e.track)&&t.removeTrack(e)}))}})}},r.shimRemoteStreamsAPI=function(e){if("object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(e){var t=this;this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach((function(e){if(t._remoteStreams||(t._remoteStreams=[]),!t._remoteStreams.includes(e)){t._remoteStreams.push(e);var r=new Event("addstream");r.stream=e,t.dispatchEvent(r)}}))})}});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach((function(t){if(e._remoteStreams||(e._remoteStreams=[]),!(e._remoteStreams.indexOf(t)>=0)){e._remoteStreams.push(t);var r=new Event("addstream");r.stream=t,e.dispatchEvent(r)}}))}),t.apply(e,arguments)}}},r.shimCallbacksAPI=function(e){if("object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype,r=t.createOffer,n=t.createAnswer,s=t.setLocalDescription,o=t.setRemoteDescription,a=t.addIceCandidate;t.createOffer=function(e,t){var i=arguments.length>=2?arguments[2]:arguments[0],n=r.apply(this,[i]);return t?(n.then(e,t),Promise.resolve()):n},t.createAnswer=function(e,t){var r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i};var c=function(e,t,r){var i=s.apply(this,[e]);return r?(i.then(t,r),Promise.resolve()):i};t.setLocalDescription=c,c=function(e,t,r){var i=o.apply(this,[e]);return r?(i.then(t,r),Promise.resolve()):i},t.setRemoteDescription=c,c=function(e,t,r){var i=a.apply(this,[e]);return r?(i.then(t,r),Promise.resolve()):i},t.addIceCandidate=c}},r.shimGetUserMedia=function(e){var t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){var r=t.mediaDevices,i=r.getUserMedia.bind(r);t.mediaDevices.getUserMedia=function(e){return i(s(e))}}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,r,i){t.mediaDevices.getUserMedia(e).then(r,i)}.bind(t))},r.shimConstraints=s,r.shimRTCIceServerUrls=function(e){var t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,r){if(e&&e.iceServers){for(var i=[],s=0;s<e.iceServers.length;s++){var o=e.iceServers[s];!o.hasOwnProperty("urls")&&o.hasOwnProperty("url")?(n.deprecated("RTCIceServer.url","RTCIceServer.urls"),(o=JSON.parse(JSON.stringify(o))).urls=o.url,delete o.url,i.push(o)):i.push(e.iceServers[s])}e.iceServers=i}return new t(e,r)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in e.RTCPeerConnection&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return t.generateCertificate}})},r.shimTrackEventTransceiver=function(e){"object"===(void 0===e?"undefined":i(e))&&e.RTCPeerConnection&&"receiver"in e.RTCTrackEvent.prototype&&!e.RTCTransceiver&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})},r.shimCreateOfferLegacy=function(e){var t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);var r=this.getTransceivers().find((function(e){return"audio"===e.receiver.track.kind}));!1===e.offerToReceiveAudio&&r?"sendrecv"===r.direction?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":"recvonly"===r.direction&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):!0!==e.offerToReceiveAudio||r||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);var i=this.getTransceivers().find((function(e){return"video"===e.receiver.track.kind}));!1===e.offerToReceiveVideo&&i?"sendrecv"===i.direction?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":"recvonly"===i.direction&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):!0!==e.offerToReceiveVideo||i||this.addTransceiver("video")}return t.apply(this,arguments)}};var n=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(e("../utils"));function s(e){return e&&void 0!==e.video?Object.assign({},e,{video:n.compactObject(e.video)}):e}},{"../utils":15}],15:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.extractVersion=o,r.wrapPeerConnectionEvent=function(e,t,r){if(e.RTCPeerConnection){var i=e.RTCPeerConnection.prototype,n=i.addEventListener;i.addEventListener=function(e,i){if(e!==t)return n.apply(this,arguments);var s=function(e){var t=r(e);t&&i(t)};return this._eventMap=this._eventMap||{},this._eventMap[i]=s,n.apply(this,[e,s])};var s=i.removeEventListener;i.removeEventListener=function(e,r){if(e!==t||!this._eventMap||!this._eventMap[r])return s.apply(this,arguments);var i=this._eventMap[r];return delete this._eventMap[r],s.apply(this,[e,i])},Object.defineProperty(i,"on"+t,{get:function(){return this["_on"+t]},set:function(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}},r.disableLog=function(e){return"boolean"!=typeof e?new Error("Argument type: "+(void 0===e?"undefined":i(e))+". Please use a boolean."):(n=e,e?"adapter.js logging disabled":"adapter.js logging enabled")},r.disableWarnings=function(e){return"boolean"!=typeof e?new Error("Argument type: "+(void 0===e?"undefined":i(e))+". Please use a boolean."):(s=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))},r.log=function(){if("object"===("undefined"==typeof window?"undefined":i(window))){if(n)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}},r.deprecated=function(e,t){s&&console.warn(e+" is deprecated, please use "+t+" instead.")},r.detectBrowser=function(e){var t=e.navigator,r={browser:null,version:null};if(void 0===e||!e.navigator)return r.browser="Not a browser.",r;if(t.mozGetUserMedia)r.browser="firefox",r.version=o(t.userAgent,/Firefox\/(\d+)\./,1);else if(t.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)r.browser="chrome",r.version=o(t.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(t.mediaDevices&&t.userAgent.match(/Edge\/(\d+).(\d+)$/))r.browser="edge",r.version=o(t.userAgent,/Edge\/(\d+).(\d+)$/,2);else{if(!e.RTCPeerConnection||!t.userAgent.match(/AppleWebKit\/(\d+)\./))return r.browser="Not a supported browser.",r;r.browser="safari",r.version=o(t.userAgent,/AppleWebKit\/(\d+)\./,1)}return r},r.compactObject=function e(t){return"object"!==(void 0===t?"undefined":i(t))?t:Object.keys(t).reduce((function(r,n){var s="object"===i(t[n]),o=s?e(t[n]):t[n],a=s&&!Object.keys(o).length;return void 0===o||a?r:Object.assign(r,function(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}({},n,o))}),{})},r.walkStats=a,r.filterStats=function(e,t,r){var i=r?"outbound-rtp":"inbound-rtp",n=new Map;if(null===t)return n;var s=[];return e.forEach((function(e){"track"===e.type&&e.trackIdentifier===t.id&&s.push(e)})),s.forEach((function(t){e.forEach((function(r){r.type===i&&r.trackId===t.id&&a(e,r,n)}))})),n};var n=!0,s=!0;function o(e,t,r){var i=e.match(t);return i&&i.length>=r&&parseInt(i[r],10)}function a(e,t,r){t&&!r.has(t.id)&&(r.set(t.id,t),Object.keys(t).forEach((function(i){i.endsWith("Id")?a(e,e.get(t[i]),r):i.endsWith("Ids")&&t[i].forEach((function(t){a(e,e.get(t),r)}))})))}},{}],16:[function(e,t,r){"use strict";var i=e("sdp");function n(e,t,r,n,s){var o=i.writeRtpDescription(e.kind,t);if(o+=i.writeIceParameters(e.iceGatherer.getLocalParameters()),o+=i.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":s||"active"),o+="a=mid:"+e.mid+"\r\n",e.rtpSender&&e.rtpReceiver?o+="a=sendrecv\r\n":e.rtpSender?o+="a=sendonly\r\n":e.rtpReceiver?o+="a=recvonly\r\n":o+="a=inactive\r\n",e.rtpSender){var a=e.rtpSender._initialTrackId||e.rtpSender.track.id;e.rtpSender._initialTrackId=a;var c="msid:"+(n?n.id:"-")+" "+a+"\r\n";o+="a="+c,o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+c,e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+c,o+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+i.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+i.localCName+"\r\n"),o}function s(e,t){var r={codecs:[],headerExtensions:[],fecMechanisms:[]},i=function(e,t){e=parseInt(e,10);for(var r=0;r<t.length;r++)if(t[r].payloadType===e||t[r].preferredPayloadType===e)return t[r]},n=function(e,t,r,n){var s=i(e.parameters.apt,r),o=i(t.parameters.apt,n);return s&&o&&s.name.toLowerCase()===o.name.toLowerCase()};return e.codecs.forEach((function(i){for(var s=0;s<t.codecs.length;s++){var o=t.codecs[s];if(i.name.toLowerCase()===o.name.toLowerCase()&&i.clockRate===o.clockRate){if("rtx"===i.name.toLowerCase()&&i.parameters&&o.parameters.apt&&!n(i,o,e.codecs,t.codecs))continue;(o=JSON.parse(JSON.stringify(o))).numChannels=Math.min(i.numChannels,o.numChannels),r.codecs.push(o),o.rtcpFeedback=o.rtcpFeedback.filter((function(e){for(var t=0;t<i.rtcpFeedback.length;t++)if(i.rtcpFeedback[t].type===e.type&&i.rtcpFeedback[t].parameter===e.parameter)return!0;return!1}));break}}})),e.headerExtensions.forEach((function(e){for(var i=0;i<t.headerExtensions.length;i++){var n=t.headerExtensions[i];if(e.uri===n.uri){r.headerExtensions.push(n);break}}})),r}function o(e,t,r){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(r)}function a(e,t){var r=e.getRemoteCandidates().find((function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type}));return r||e.addRemoteCandidate(t),!r}function c(e,t){var r=new Error(t);return r.name=e,r.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0}[e],r}t.exports=function(e,t){function r(t,r){r.addTrack(t),r.dispatchEvent(new e.MediaStreamTrackEvent("addtrack",{track:t}))}function u(t,r,i,n){var s=new Event("track");s.track=r,s.receiver=i,s.transceiver={receiver:i},s.streams=n,e.setTimeout((function(){t._dispatchEvent("track",s)}))}var l=function(r){var n=this,s=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach((function(e){n[e]=s[e].bind(s)})),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this._localDescription=null,this._remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.connectionState="new",this.iceGatheringState="new",r=JSON.parse(JSON.stringify(r||{})),this.usingBundle="max-bundle"===r.bundlePolicy,"negotiate"===r.rtcpMuxPolicy)throw c("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(r.rtcpMuxPolicy||(r.rtcpMuxPolicy="require"),r.iceTransportPolicy){case"all":case"relay":break;default:r.iceTransportPolicy="all"}switch(r.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:r.bundlePolicy="balanced"}if(r.iceServers=function(e,t){var r=!1;return(e=JSON.parse(JSON.stringify(e))).filter((function(e){if(e&&(e.urls||e.url)){var i=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var n="string"==typeof i;return n&&(i=[i]),i=i.filter((function(e){return 0!==e.indexOf("turn:")||-1===e.indexOf("transport=udp")||-1!==e.indexOf("turn:[")||r?0===e.indexOf("stun:")&&t>=14393&&-1===e.indexOf("?transport=udp"):(r=!0,!0)})),delete e.url,e.urls=n?i[0]:i,!!i.length}}))}(r.iceServers||[],t),this._iceGatherers=[],r.iceCandidatePoolSize)for(var o=r.iceCandidatePoolSize;o>0;o--)this._iceGatherers.push(new e.RTCIceGatherer({iceServers:r.iceServers,gatherPolicy:r.iceTransportPolicy}));else r.iceCandidatePoolSize=0;this._config=r,this.transceivers=[],this._sdpSessionId=i.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1};Object.defineProperty(l.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}}),Object.defineProperty(l.prototype,"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}}),l.prototype.onicecandidate=null,l.prototype.onaddstream=null,l.prototype.ontrack=null,l.prototype.onremovestream=null,l.prototype.onsignalingstatechange=null,l.prototype.oniceconnectionstatechange=null,l.prototype.onconnectionstatechange=null,l.prototype.onicegatheringstatechange=null,l.prototype.onnegotiationneeded=null,l.prototype.ondatachannel=null,l.prototype._dispatchEvent=function(e,t){this._isClosed||(this.dispatchEvent(t),"function"==typeof this["on"+e]&&this["on"+e](t))},l.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},l.prototype.getConfiguration=function(){return this._config},l.prototype.getLocalStreams=function(){return this.localStreams},l.prototype.getRemoteStreams=function(){return this.remoteStreams},l.prototype._createTransceiver=function(e,t){var r=this.transceivers.length>0,i={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};if(this.usingBundle&&r)i.iceTransport=this.transceivers[0].iceTransport,i.dtlsTransport=this.transceivers[0].dtlsTransport;else{var n=this._createIceAndDtlsTransports();i.iceTransport=n.iceTransport,i.dtlsTransport=n.dtlsTransport}return t||this.transceivers.push(i),i},l.prototype.addTrack=function(t,r){if(this._isClosed)throw c("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var i;if(this.transceivers.find((function(e){return e.track===t})))throw c("InvalidAccessError","Track already exists.");for(var n=0;n<this.transceivers.length;n++)this.transceivers[n].track||this.transceivers[n].kind!==t.kind||(i=this.transceivers[n]);return i||(i=this._createTransceiver(t.kind)),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(r)&&this.localStreams.push(r),i.track=t,i.stream=r,i.rtpSender=new e.RTCRtpSender(t,i.dtlsTransport),i.rtpSender},l.prototype.addStream=function(e){var r=this;if(t>=15025)e.getTracks().forEach((function(t){r.addTrack(t,e)}));else{var i=e.clone();e.getTracks().forEach((function(e,t){var r=i.getTracks()[t];e.addEventListener("enabled",(function(e){r.enabled=e.enabled}))})),i.getTracks().forEach((function(e){r.addTrack(e,i)}))}},l.prototype.removeTrack=function(t){if(this._isClosed)throw c("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof e.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var r=this.transceivers.find((function(e){return e.rtpSender===t}));if(!r)throw c("InvalidAccessError","Sender was not created by this connection.");var i=r.stream;r.rtpSender.stop(),r.rtpSender=null,r.track=null,r.stream=null,-1===this.transceivers.map((function(e){return e.stream})).indexOf(i)&&this.localStreams.indexOf(i)>-1&&this.localStreams.splice(this.localStreams.indexOf(i),1),this._maybeFireNegotiationNeeded()},l.prototype.removeStream=function(e){var t=this;e.getTracks().forEach((function(e){var r=t.getSenders().find((function(t){return t.track===e}));r&&t.removeTrack(r)}))},l.prototype.getSenders=function(){return this.transceivers.filter((function(e){return!!e.rtpSender})).map((function(e){return e.rtpSender}))},l.prototype.getReceivers=function(){return this.transceivers.filter((function(e){return!!e.rtpReceiver})).map((function(e){return e.rtpReceiver}))},l.prototype._createIceGatherer=function(t,r){var i=this;if(r&&t>0)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var n=new e.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});return Object.defineProperty(n,"state",{value:"new",writable:!0}),this.transceivers[t].bufferedCandidateEvents=[],this.transceivers[t].bufferCandidates=function(e){var r=!e.candidate||0===Object.keys(e.candidate).length;n.state=r?"completed":"gathering",null!==i.transceivers[t].bufferedCandidateEvents&&i.transceivers[t].bufferedCandidateEvents.push(e)},n.addEventListener("localcandidate",this.transceivers[t].bufferCandidates),n},l.prototype._gather=function(t,r){var n=this,s=this.transceivers[r].iceGatherer;if(!s.onlocalcandidate){var o=this.transceivers[r].bufferedCandidateEvents;this.transceivers[r].bufferedCandidateEvents=null,s.removeEventListener("localcandidate",this.transceivers[r].bufferCandidates),s.onlocalcandidate=function(e){if(!(n.usingBundle&&r>0)){var o=new Event("icecandidate");o.candidate={sdpMid:t,sdpMLineIndex:r};var a=e.candidate,c=!a||0===Object.keys(a).length;if(c)"new"!==s.state&&"gathering"!==s.state||(s.state="completed");else{"new"===s.state&&(s.state="gathering"),a.component=1,a.ufrag=s.getLocalParameters().usernameFragment;var u=i.writeCandidate(a);o.candidate=Object.assign(o.candidate,i.parseCandidate(u)),o.candidate.candidate=u,o.candidate.toJSON=function(){return{candidate:o.candidate.candidate,sdpMid:o.candidate.sdpMid,sdpMLineIndex:o.candidate.sdpMLineIndex,usernameFragment:o.candidate.usernameFragment}}}var l=i.getMediaSections(n._localDescription.sdp);l[o.candidate.sdpMLineIndex]+=c?"a=end-of-candidates\r\n":"a="+o.candidate.candidate+"\r\n",n._localDescription.sdp=i.getDescription(n._localDescription.sdp)+l.join("");var h=n.transceivers.every((function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}));"gathering"!==n.iceGatheringState&&(n.iceGatheringState="gathering",n._emitGatheringStateChange()),c||n._dispatchEvent("icecandidate",o),h&&(n._dispatchEvent("icecandidate",new Event("icecandidate")),n.iceGatheringState="complete",n._emitGatheringStateChange())}},e.setTimeout((function(){o.forEach((function(e){s.onlocalcandidate(e)}))}),0)}},l.prototype._createIceAndDtlsTransports=function(){var t=this,r=new e.RTCIceTransport(null);r.onicestatechange=function(){t._updateIceConnectionState(),t._updateConnectionState()};var i=new e.RTCDtlsTransport(r);return i.ondtlsstatechange=function(){t._updateConnectionState()},i.onerror=function(){Object.defineProperty(i,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:r,dtlsTransport:i}},l.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var r=this.transceivers[e].iceTransport;r&&(delete r.onicestatechange,delete this.transceivers[e].iceTransport);var i=this.transceivers[e].dtlsTransport;i&&(delete i.ondtlsstatechange,delete i.onerror,delete this.transceivers[e].dtlsTransport)},l.prototype._transceive=function(e,r,n){var o=s(e.localCapabilities,e.remoteCapabilities);r&&e.rtpSender&&(o.encodings=e.sendEncodingParameters,o.rtcp={cname:i.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(o.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(o)),n&&e.rtpReceiver&&o.codecs.length>0&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach((function(e){delete e.rtx})),e.recvEncodingParameters.length?o.encodings=e.recvEncodingParameters:o.encodings=[{}],o.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(o.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(o.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(o))},l.prototype.setLocalDescription=function(e){var t,r,n=this;if(-1===["offer","answer"].indexOf(e.type))return Promise.reject(c("TypeError",'Unsupported type "'+e.type+'"'));if(!o("setLocalDescription",e.type,n.signalingState)||n._isClosed)return Promise.reject(c("InvalidStateError","Can not set local "+e.type+" in state "+n.signalingState));if("offer"===e.type)t=i.splitSections(e.sdp),r=t.shift(),t.forEach((function(e,t){var r=i.parseRtpParameters(e);n.transceivers[t].localCapabilities=r})),n.transceivers.forEach((function(e,t){n._gather(e.mid,t)}));else if("answer"===e.type){t=i.splitSections(n._remoteDescription.sdp),r=t.shift();var a=i.matchPrefix(r,"a=ice-lite").length>0;t.forEach((function(e,t){var o=n.transceivers[t],c=o.iceGatherer,u=o.iceTransport,l=o.dtlsTransport,h=o.localCapabilities,d=o.remoteCapabilities;if(!(i.isRejected(e)&&0===i.matchPrefix(e,"a=bundle-only").length||o.rejected)){var p=i.getIceParameters(e,r),f=i.getDtlsParameters(e,r);a&&(f.role="server"),n.usingBundle&&0!==t||(n._gather(o.mid,t),"new"===u.state&&u.start(c,p,a?"controlling":"controlled"),"new"===l.state&&l.start(f));var g=s(h,d);n._transceive(o,g.codecs.length>0,!1)}}))}return n._localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?n._updateSignalingState("have-local-offer"):n._updateSignalingState("stable"),Promise.resolve()},l.prototype.setRemoteDescription=function(n){var l=this;if(-1===["offer","answer"].indexOf(n.type))return Promise.reject(c("TypeError",'Unsupported type "'+n.type+'"'));if(!o("setRemoteDescription",n.type,l.signalingState)||l._isClosed)return Promise.reject(c("InvalidStateError","Can not set remote "+n.type+" in state "+l.signalingState));var h={};l.remoteStreams.forEach((function(e){h[e.id]=e}));var d=[],p=i.splitSections(n.sdp),f=p.shift(),g=i.matchPrefix(f,"a=ice-lite").length>0,m=i.matchPrefix(f,"a=group:BUNDLE ").length>0;l.usingBundle=m;var v=i.matchPrefix(f,"a=ice-options:")[0];return l.canTrickleIceCandidates=!!v&&v.substr(14).split(" ").indexOf("trickle")>=0,p.forEach((function(o,c){var u=i.splitLines(o),p=i.getKind(o),v=i.isRejected(o)&&0===i.matchPrefix(o,"a=bundle-only").length,y=u[0].substr(2).split(" ")[2],_=i.getDirection(o,f),b=i.parseMsid(o),S=i.getMid(o)||i.generateIdentifier();if(v||"application"===p&&("DTLS/SCTP"===y||"UDP/DTLS/SCTP"===y))l.transceivers[c]={mid:S,kind:p,protocol:y,rejected:!0};else{var C,T,R,E,w,P,I,k,M;!v&&l.transceivers[c]&&l.transceivers[c].rejected&&(l.transceivers[c]=l._createTransceiver(p,!0));var L,U,O=i.parseRtpParameters(o);v||(L=i.getIceParameters(o,f),(U=i.getDtlsParameters(o,f)).role="client"),I=i.parseRtpEncodingParameters(o);var D=i.parseRtcpParameters(o),A=i.matchPrefix(o,"a=end-of-candidates",f).length>0,x=i.matchPrefix(o,"a=candidate:").map((function(e){return i.parseCandidate(e)})).filter((function(e){return 1===e.component}));if(("offer"===n.type||"answer"===n.type)&&!v&&m&&c>0&&l.transceivers[c]&&(l._disposeIceAndDtlsTransports(c),l.transceivers[c].iceGatherer=l.transceivers[0].iceGatherer,l.transceivers[c].iceTransport=l.transceivers[0].iceTransport,l.transceivers[c].dtlsTransport=l.transceivers[0].dtlsTransport,l.transceivers[c].rtpSender&&l.transceivers[c].rtpSender.setTransport(l.transceivers[0].dtlsTransport),l.transceivers[c].rtpReceiver&&l.transceivers[c].rtpReceiver.setTransport(l.transceivers[0].dtlsTransport)),"offer"!==n.type||v)"answer"!==n.type||v||(T=(C=l.transceivers[c]).iceGatherer,R=C.iceTransport,E=C.dtlsTransport,w=C.rtpReceiver,P=C.sendEncodingParameters,k=C.localCapabilities,l.transceivers[c].recvEncodingParameters=I,l.transceivers[c].remoteCapabilities=O,l.transceivers[c].rtcpParameters=D,x.length&&"new"===R.state&&(!g&&!A||m&&0!==c?x.forEach((function(e){a(C.iceTransport,e)})):R.setRemoteCandidates(x)),m&&0!==c||("new"===R.state&&R.start(T,L,"controlling"),"new"===E.state&&E.start(U)),!s(C.localCapabilities,C.remoteCapabilities).codecs.filter((function(e){return"rtx"===e.name.toLowerCase()})).length&&C.sendEncodingParameters[0].rtx&&delete C.sendEncodingParameters[0].rtx,l._transceive(C,"sendrecv"===_||"recvonly"===_,"sendrecv"===_||"sendonly"===_),!w||"sendrecv"!==_&&"sendonly"!==_?delete C.rtpReceiver:(M=w.track,b?(h[b.stream]||(h[b.stream]=new e.MediaStream),r(M,h[b.stream]),d.push([M,w,h[b.stream]])):(h.default||(h.default=new e.MediaStream),r(M,h.default),d.push([M,w,h.default]))));else{(C=l.transceivers[c]||l._createTransceiver(p)).mid=S,C.iceGatherer||(C.iceGatherer=l._createIceGatherer(c,m)),x.length&&"new"===C.iceTransport.state&&(!A||m&&0!==c?x.forEach((function(e){a(C.iceTransport,e)})):C.iceTransport.setRemoteCandidates(x)),k=e.RTCRtpReceiver.getCapabilities(p),t<15019&&(k.codecs=k.codecs.filter((function(e){return"rtx"!==e.name}))),P=C.sendEncodingParameters||[{ssrc:1001*(2*c+2)}];var N,z=!1;"sendrecv"===_||"sendonly"===_?(z=!C.rtpReceiver,w=C.rtpReceiver||new e.RTCRtpReceiver(C.dtlsTransport,p),z&&(M=w.track,b&&"-"===b.stream||(b?(h[b.stream]||(h[b.stream]=new e.MediaStream,Object.defineProperty(h[b.stream],"id",{get:function(){return b.stream}})),Object.defineProperty(M,"id",{get:function(){return b.track}}),N=h[b.stream]):(h.default||(h.default=new e.MediaStream),N=h.default)),N&&(r(M,N),C.associatedRemoteMediaStreams.push(N)),d.push([M,w,N]))):C.rtpReceiver&&C.rtpReceiver.track&&(C.associatedRemoteMediaStreams.forEach((function(t){var r,i,n=t.getTracks().find((function(e){return e.id===C.rtpReceiver.track.id}));n&&(r=n,(i=t).removeTrack(r),i.dispatchEvent(new e.MediaStreamTrackEvent("removetrack",{track:r})))})),C.associatedRemoteMediaStreams=[]),C.localCapabilities=k,C.remoteCapabilities=O,C.rtpReceiver=w,C.rtcpParameters=D,C.sendEncodingParameters=P,C.recvEncodingParameters=I,l._transceive(l.transceivers[c],!1,z)}}})),void 0===l._dtlsRole&&(l._dtlsRole="offer"===n.type?"active":"passive"),l._remoteDescription={type:n.type,sdp:n.sdp},"offer"===n.type?l._updateSignalingState("have-remote-offer"):l._updateSignalingState("stable"),Object.keys(h).forEach((function(t){var r=h[t];if(r.getTracks().length){if(-1===l.remoteStreams.indexOf(r)){l.remoteStreams.push(r);var i=new Event("addstream");i.stream=r,e.setTimeout((function(){l._dispatchEvent("addstream",i)}))}d.forEach((function(e){var t=e[0],i=e[1];r.id===e[2].id&&u(l,t,i,[r])}))}})),d.forEach((function(e){e[2]||u(l,e[0],e[1],[])})),e.setTimeout((function(){l&&l.transceivers&&l.transceivers.forEach((function(e){e.iceTransport&&"new"===e.iceTransport.state&&e.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))}))}),4e3),Promise.resolve()},l.prototype.close=function(){this.transceivers.forEach((function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()})),this._isClosed=!0,this._updateSignalingState("closed")},l.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",t)},l.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout((function(){if(t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t._dispatchEvent("negotiationneeded",e)}}),0))},l.prototype._updateIceConnectionState=function(){var e,t={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach((function(e){e.iceTransport&&!e.rejected&&t[e.iceTransport.state]++})),e="new",t.failed>0?e="failed":t.checking>0?e="checking":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0?e="connected":t.completed>0&&(e="completed"),e!==this.iceConnectionState){this.iceConnectionState=e;var r=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",r)}},l.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach((function(e){e.iceTransport&&e.dtlsTransport&&!e.rejected&&(t[e.iceTransport.state]++,t[e.dtlsTransport.state]++)})),t.connected+=t.completed,e="new",t.failed>0?e="failed":t.connecting>0?e="connecting":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0&&(e="connected"),e!==this.connectionState){this.connectionState=e;var r=new Event("connectionstatechange");this._dispatchEvent("connectionstatechange",r)}},l.prototype.createOffer=function(){var r=this;if(r._isClosed)return Promise.reject(c("InvalidStateError","Can not call createOffer after close"));var s=r.transceivers.filter((function(e){return"audio"===e.kind})).length,o=r.transceivers.filter((function(e){return"video"===e.kind})).length,a=arguments[0];if(a){if(a.mandatory||a.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==a.offerToReceiveAudio&&(s=!0===a.offerToReceiveAudio?1:!1===a.offerToReceiveAudio?0:a.offerToReceiveAudio),void 0!==a.offerToReceiveVideo&&(o=!0===a.offerToReceiveVideo?1:!1===a.offerToReceiveVideo?0:a.offerToReceiveVideo)}for(r.transceivers.forEach((function(e){"audio"===e.kind?--s<0&&(e.wantReceive=!1):"video"===e.kind&&--o<0&&(e.wantReceive=!1)}));s>0||o>0;)s>0&&(r._createTransceiver("audio"),s--),o>0&&(r._createTransceiver("video"),o--);var u=i.writeSessionBoilerplate(r._sdpSessionId,r._sdpSessionVersion++);r.transceivers.forEach((function(n,s){var o=n.track,a=n.kind,c=n.mid||i.generateIdentifier();n.mid=c,n.iceGatherer||(n.iceGatherer=r._createIceGatherer(s,r.usingBundle));var u=e.RTCRtpSender.getCapabilities(a);t<15019&&(u.codecs=u.codecs.filter((function(e){return"rtx"!==e.name}))),u.codecs.forEach((function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1"),n.remoteCapabilities&&n.remoteCapabilities.codecs&&n.remoteCapabilities.codecs.forEach((function(t){e.name.toLowerCase()===t.name.toLowerCase()&&e.clockRate===t.clockRate&&(e.preferredPayloadType=t.payloadType)}))})),u.headerExtensions.forEach((function(e){(n.remoteCapabilities&&n.remoteCapabilities.headerExtensions||[]).forEach((function(t){e.uri===t.uri&&(e.id=t.id)}))}));var l=n.sendEncodingParameters||[{ssrc:1001*(2*s+1)}];o&&t>=15019&&"video"===a&&!l[0].rtx&&(l[0].rtx={ssrc:l[0].ssrc+1}),n.wantReceive&&(n.rtpReceiver=new e.RTCRtpReceiver(n.dtlsTransport,a)),n.localCapabilities=u,n.sendEncodingParameters=l})),"max-compat"!==r._config.bundlePolicy&&(u+="a=group:BUNDLE "+r.transceivers.map((function(e){return e.mid})).join(" ")+"\r\n"),u+="a=ice-options:trickle\r\n",r.transceivers.forEach((function(e,t){u+=n(e,e.localCapabilities,"offer",e.stream,r._dtlsRole),u+="a=rtcp-rsize\r\n",!e.iceGatherer||"new"===r.iceGatheringState||0!==t&&r.usingBundle||(e.iceGatherer.getLocalCandidates().forEach((function(e){e.component=1,u+="a="+i.writeCandidate(e)+"\r\n"})),"completed"===e.iceGatherer.state&&(u+="a=end-of-candidates\r\n"))}));var l=new e.RTCSessionDescription({type:"offer",sdp:u});return Promise.resolve(l)},l.prototype.createAnswer=function(){var r=this;if(r._isClosed)return Promise.reject(c("InvalidStateError","Can not call createAnswer after close"));if("have-remote-offer"!==r.signalingState&&"have-local-pranswer"!==r.signalingState)return Promise.reject(c("InvalidStateError","Can not call createAnswer in signalingState "+r.signalingState));var o=i.writeSessionBoilerplate(r._sdpSessionId,r._sdpSessionVersion++);r.usingBundle&&(o+="a=group:BUNDLE "+r.transceivers.map((function(e){return e.mid})).join(" ")+"\r\n"),o+="a=ice-options:trickle\r\n";var a=i.getMediaSections(r._remoteDescription.sdp).length;r.transceivers.forEach((function(e,i){if(!(i+1>a)){if(e.rejected)return"application"===e.kind?"DTLS/SCTP"===e.protocol?o+="m=application 0 DTLS/SCTP 5000\r\n":o+="m=application 0 "+e.protocol+" webrtc-datachannel\r\n":"audio"===e.kind?o+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===e.kind&&(o+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),void(o+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+e.mid+"\r\n");var c;e.stream&&("audio"===e.kind?c=e.stream.getAudioTracks()[0]:"video"===e.kind&&(c=e.stream.getVideoTracks()[0]),c&&t>=15019&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1}));var u=s(e.localCapabilities,e.remoteCapabilities);!u.codecs.filter((function(e){return"rtx"===e.name.toLowerCase()})).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,o+=n(e,u,"answer",e.stream,r._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(o+="a=rtcp-rsize\r\n")}}));var u=new e.RTCSessionDescription({type:"answer",sdp:o});return Promise.resolve(u)},l.prototype.addIceCandidate=function(e){var t,r=this;return e&&void 0===e.sdpMLineIndex&&!e.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise((function(n,s){if(!r._remoteDescription)return s(c("InvalidStateError","Can not add ICE candidate without a remote description"));if(e&&""!==e.candidate){var o=e.sdpMLineIndex;if(e.sdpMid)for(var u=0;u<r.transceivers.length;u++)if(r.transceivers[u].mid===e.sdpMid){o=u;break}var l=r.transceivers[o];if(!l)return s(c("OperationError","Can not add ICE candidate"));if(l.rejected)return n();var h=Object.keys(e.candidate).length>0?i.parseCandidate(e.candidate):{};if("tcp"===h.protocol&&(0===h.port||9===h.port))return n();if(h.component&&1!==h.component)return n();if((0===o||o>0&&l.iceTransport!==r.transceivers[0].iceTransport)&&!a(l.iceTransport,h))return s(c("OperationError","Can not add ICE candidate"));var d=e.candidate.trim();0===d.indexOf("a=")&&(d=d.substr(2)),(t=i.getMediaSections(r._remoteDescription.sdp))[o]+="a="+(h.type?d:"end-of-candidates")+"\r\n",r._remoteDescription.sdp=i.getDescription(r._remoteDescription.sdp)+t.join("")}else for(var p=0;p<r.transceivers.length&&(r.transceivers[p].rejected||(r.transceivers[p].iceTransport.addRemoteCandidate({}),(t=i.getMediaSections(r._remoteDescription.sdp))[p]+="a=end-of-candidates\r\n",r._remoteDescription.sdp=i.getDescription(r._remoteDescription.sdp)+t.join(""),!r.usingBundle));p++);n()}))},l.prototype.getStats=function(t){if(t&&t instanceof e.MediaStreamTrack){var r=null;if(this.transceivers.forEach((function(e){e.rtpSender&&e.rtpSender.track===t?r=e.rtpSender:e.rtpReceiver&&e.rtpReceiver.track===t&&(r=e.rtpReceiver)})),!r)throw c("InvalidAccessError","Invalid selector.");return r.getStats()}var i=[];return this.transceivers.forEach((function(e){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach((function(t){e[t]&&i.push(e[t].getStats())}))})),Promise.all(i).then((function(e){var t=new Map;return e.forEach((function(e){e.forEach((function(e){t.set(e.id,e)}))})),t}))},["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach((function(t){var r=e[t];if(r&&r.prototype&&r.prototype.getStats){var i=r.prototype.getStats;r.prototype.getStats=function(){return i.apply(this).then((function(e){var t=new Map;return Object.keys(e).forEach((function(r){var i;e[r].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(i=e[r]).type]||i.type,t.set(r,e[r])})),t}))}}}));var h=["createOffer","createAnswer"];return h.forEach((function(e){var t=l.prototype[e];l.prototype[e]=function(){var e=arguments;return"function"==typeof e[0]||"function"==typeof e[1]?t.apply(this,[arguments[2]]).then((function(t){"function"==typeof e[0]&&e[0].apply(null,[t])}),(function(t){"function"==typeof e[1]&&e[1].apply(null,[t])})):t.apply(this,arguments)}})),(h=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach((function(e){var t=l.prototype[e];l.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]||"function"==typeof e[2]?t.apply(this,arguments).then((function(){"function"==typeof e[1]&&e[1].apply(null)}),(function(t){"function"==typeof e[2]&&e[2].apply(null,[t])})):t.apply(this,arguments)}})),["getStats"].forEach((function(e){var t=l.prototype[e];l.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]?t.apply(this,arguments).then((function(){"function"==typeof e[1]&&e[1].apply(null)})):t.apply(this,arguments)}})),l}},{sdp:17}],17:[function(e,t,r){"use strict";var i={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};i.localCName=i.generateIdentifier(),i.splitLines=function(e){return e.trim().split("\n").map((function(e){return e.trim()}))},i.splitSections=function(e){return e.split("\nm=").map((function(e,t){return(t>0?"m="+e:e).trim()+"\r\n"}))},i.getDescription=function(e){var t=i.splitSections(e);return t&&t[0]},i.getMediaSections=function(e){var t=i.splitSections(e);return t.shift(),t},i.matchPrefix=function(e,t){return i.splitLines(e).filter((function(e){return 0===e.indexOf(t)}))},i.parseCandidate=function(e){for(var t,r={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},i=8;i<t.length;i+=2)switch(t[i]){case"raddr":r.relatedAddress=t[i+1];break;case"rport":r.relatedPort=parseInt(t[i+1],10);break;case"tcptype":r.tcpType=t[i+1];break;case"ufrag":r.ufrag=t[i+1],r.usernameFragment=t[i+1];break;default:r[t[i]]=t[i+1]}return r},i.writeCandidate=function(e){var t=[];t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var r=e.type;return t.push("typ"),t.push(r),"host"!==r&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},i.parseIceOptions=function(e){return e.substr(14).split(" ")},i.parseRtpMap=function(e){var t=e.substr(9).split(" "),r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=3===t.length?parseInt(t[2],10):1,r.numChannels=r.channels,r},i.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},i.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},i.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},i.parseFmtp=function(e){for(var t,r={},i=e.substr(e.indexOf(" ")+1).split(";"),n=0;n<i.length;n++)r[(t=i[n].trim().split("="))[0].trim()]=t[1];return r},i.writeFmtp=function(e){var t="",r=e.payloadType;if(void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var i=[];Object.keys(e.parameters).forEach((function(t){e.parameters[t]?i.push(t+"="+e.parameters[t]):i.push(t)})),t+="a=fmtp:"+r+" "+i.join(";")+"\r\n"}return t},i.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},i.writeRtcpFb=function(e){var t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((function(e){t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},i.parseSsrcMedia=function(e){var t=e.indexOf(" "),r={ssrc:parseInt(e.substr(7,t-7),10)},i=e.indexOf(":",t);return i>-1?(r.attribute=e.substr(t+1,i-t-1),r.value=e.substr(i+1)):r.attribute=e.substr(t+1),r},i.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((function(e){return parseInt(e,10)}))}},i.getMid=function(e){var t=i.matchPrefix(e,"a=mid:")[0];if(t)return t.substr(6)},i.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1]}},i.getDtlsParameters=function(e,t){return{role:"auto",fingerprints:i.matchPrefix(e+t,"a=fingerprint:").map(i.parseFingerprint)}},i.writeDtlsParameters=function(e,t){var r="a=setup:"+t+"\r\n";return e.fingerprints.forEach((function(e){r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),r},i.getIceParameters=function(e,t){var r=i.splitLines(e);return{usernameFragment:(r=r.concat(i.splitLines(t))).filter((function(e){return 0===e.indexOf("a=ice-ufrag:")}))[0].substr(12),password:r.filter((function(e){return 0===e.indexOf("a=ice-pwd:")}))[0].substr(10)}},i.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},i.parseRtpParameters=function(e){for(var t={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=i.splitLines(e)[0].split(" "),n=3;n<r.length;n++){var s=r[n],o=i.matchPrefix(e,"a=rtpmap:"+s+" ")[0];if(o){var a=i.parseRtpMap(o),c=i.matchPrefix(e,"a=fmtp:"+s+" ");switch(a.parameters=c.length?i.parseFmtp(c[0]):{},a.rtcpFeedback=i.matchPrefix(e,"a=rtcp-fb:"+s+" ").map(i.parseRtcpFb),t.codecs.push(a),a.name.toUpperCase()){case"RED":case"ULPFEC":t.fecMechanisms.push(a.name.toUpperCase())}}}return i.matchPrefix(e,"a=extmap:").forEach((function(e){t.headerExtensions.push(i.parseExtmap(e))})),t},i.writeRtpDescription=function(e,t){var r="";r+="m="+e+" ",r+=t.codecs.length>0?"9":"0",r+=" UDP/TLS/RTP/SAVPF ",r+=t.codecs.map((function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType})).join(" ")+"\r\n",r+="c=IN IP4 0.0.0.0\r\n",r+="a=rtcp:9 IN IP4 0.0.0.0\r\n",t.codecs.forEach((function(e){r+=i.writeRtpMap(e),r+=i.writeFmtp(e),r+=i.writeRtcpFb(e)}));var n=0;return t.codecs.forEach((function(e){e.maxptime>n&&(n=e.maxptime)})),n>0&&(r+="a=maxptime:"+n+"\r\n"),r+="a=rtcp-mux\r\n",t.headerExtensions&&t.headerExtensions.forEach((function(e){r+=i.writeExtmap(e)})),r},i.parseRtpEncodingParameters=function(e){var t,r=[],n=i.parseRtpParameters(e),s=-1!==n.fecMechanisms.indexOf("RED"),o=-1!==n.fecMechanisms.indexOf("ULPFEC"),a=i.matchPrefix(e,"a=ssrc:").map((function(e){return i.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute})),c=a.length>0&&a[0].ssrc,u=i.matchPrefix(e,"a=ssrc-group:FID").map((function(e){return e.substr(17).split(" ").map((function(e){return parseInt(e,10)}))}));u.length>0&&u[0].length>1&&u[0][0]===c&&(t=u[0][1]),n.codecs.forEach((function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var i={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&t&&(i.rtx={ssrc:t}),r.push(i),s&&((i=JSON.parse(JSON.stringify(i))).fec={ssrc:c,mechanism:o?"red+ulpfec":"red"},r.push(i))}})),0===r.length&&c&&r.push({ssrc:c});var l=i.matchPrefix(e,"b=");return l.length&&(l=0===l[0].indexOf("b=TIAS:")?parseInt(l[0].substr(7),10):0===l[0].indexOf("b=AS:")?1e3*parseInt(l[0].substr(5),10)*.95-16e3:void 0,r.forEach((function(e){e.maxBitrate=l}))),r},i.parseRtcpParameters=function(e){var t={},r=i.matchPrefix(e,"a=ssrc:").map((function(e){return i.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute}))[0];r&&(t.cname=r.value,t.ssrc=r.ssrc);var n=i.matchPrefix(e,"a=rtcp-rsize");t.reducedSize=n.length>0,t.compound=0===n.length;var s=i.matchPrefix(e,"a=rtcp-mux");return t.mux=s.length>0,t},i.parseMsid=function(e){var t,r=i.matchPrefix(e,"a=msid:");if(1===r.length)return{stream:(t=r[0].substr(7).split(" "))[0],track:t[1]};var n=i.matchPrefix(e,"a=ssrc:").map((function(e){return i.parseSsrcMedia(e)})).filter((function(e){return"msid"===e.attribute}));return n.length>0?{stream:(t=n[0].value.split(" "))[0],track:t[1]}:void 0},i.generateSessionId=function(){return Math.random().toString().substr(2,21)},i.writeSessionBoilerplate=function(e,t,r){var n=void 0!==t?t:2;return"v=0\r\no="+(r||"thisisadapterortc")+" "+(e||i.generateSessionId())+" "+n+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},i.writeMediaSection=function(e,t,r,n){var s=i.writeRtpDescription(e.kind,t);if(s+=i.writeIceParameters(e.iceGatherer.getLocalParameters()),s+=i.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":"active"),s+="a=mid:"+e.mid+"\r\n",e.direction?s+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?s+="a=sendrecv\r\n":e.rtpSender?s+="a=sendonly\r\n":e.rtpReceiver?s+="a=recvonly\r\n":s+="a=inactive\r\n",e.rtpSender){var o="msid:"+n.id+" "+e.rtpSender.track.id+"\r\n";s+="a="+o,s+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+o,e.sendEncodingParameters[0].rtx&&(s+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+o,s+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return s+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+i.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(s+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+i.localCName+"\r\n"),s},i.getDirection=function(e,t){for(var r=i.splitLines(e),n=0;n<r.length;n++)switch(r[n]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[n].substr(2)}return t?i.getDirection(t):"sendrecv"},i.getKind=function(e){return i.splitLines(e)[0].split(" ")[0].substr(2)},i.isRejected=function(e){return"0"===e.split(" ",2)[1]},i.parseMLine=function(e){var t=i.splitLines(e)[0].substr(2).split(" ");return{kind:t[0],port:parseInt(t[1],10),protocol:t[2],fmt:t.slice(3).join(" ")}},i.parseOLine=function(e){var t=i.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:t[0],sessionId:t[1],sessionVersion:parseInt(t[2],10),netType:t[3],addressType:t[4],address:t[5]}},i.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var t=i.splitLines(e),r=0;r<t.length;r++)if(t[r].length<2||"="!==t[r].charAt(1))return!1;return!0},"object"==typeof t&&(t.exports=i)},{}]},{},[1])(1)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e){this.delayTime=.1,this.fadeTime=.05,this.startTime=.1,this.previousPitch=-1,this.context=e,this.input=e.createGain(),this.output=e.createGain(),this.mod1=e.createBufferSource(),this.mod2=e.createBufferSource(),this.mod3=e.createBufferSource(),this.mod4=e.createBufferSource(),this.shiftDownBuffer=this.createDelayTimeBuffer(e,this.startTime,this.fadeTime,!1),this.shiftUpBuffer=this.createDelayTimeBuffer(e,this.startTime,this.fadeTime,!0),this.mod1.buffer=this.shiftDownBuffer,this.mod2.buffer=this.shiftDownBuffer,this.mod3.buffer=this.shiftUpBuffer,this.mod4.buffer=this.shiftUpBuffer,this.mod1.loop=!0,this.mod2.loop=!0,this.mod3.loop=!0,this.mod4.loop=!0,this.mod1Gain=e.createGain(),this.mod2Gain=e.createGain(),this.mod3Gain=e.createGain(),this.mod4Gain=e.createGain(),this.mod3Gain.gain.value=0,this.mod4Gain.gain.value=0,this.mod1.connect(this.mod1Gain),this.mod2.connect(this.mod2Gain),this.mod3.connect(this.mod3Gain),this.mod4.connect(this.mod4Gain),this.modGain1=e.createGain(),this.modGain2=e.createGain(),this.delay1=e.createDelay(),this.delay2=e.createDelay(),this.mod1Gain.connect(this.modGain1),this.mod2Gain.connect(this.modGain2),this.mod3Gain.connect(this.modGain1),this.mod4Gain.connect(this.modGain2),this.modGain1.connect(this.delay1.delayTime),this.modGain2.connect(this.delay2.delayTime),this.fade1=e.createBufferSource(),this.fade2=e.createBufferSource(),this.fadeBuffer=this.createFadeBuffer(e,this.startTime,this.fadeTime),this.fade1.buffer=this.fadeBuffer,this.fade2.buffer=this.fadeBuffer,this.fade1.loop=!0,this.fade2.loop=!0,this.mix1=e.createGain(),this.mix2=e.createGain(),this.mix1.gain.value=0,this.mix2.gain.value=0,this.fade1.connect(this.mix1.gain),this.fade2.connect(this.mix2.gain),this.input.connect(this.delay1),this.input.connect(this.delay2),this.delay1.connect(this.mix1),this.delay2.connect(this.mix2),this.mix1.connect(this.output),this.mix2.connect(this.output);var t=e.currentTime+.05,r=t+this.startTime-this.fadeTime;this.mod1.start(t),this.mod2.start(r),this.mod3.start(t),this.mod4.start(r),this.fade1.start(t),this.fade2.start(r),this.setDelay(this.delayTime)}return e.prototype.createFadeBuffer=function(e,t,r){for(var i=t*e.sampleRate,n=i+(t-2*r)*e.sampleRate,s=e.createBuffer(1,n,e.sampleRate),o=s.getChannelData(0),a=r*e.sampleRate,c=i-a,u=0;u<i;++u)o[u]=u<a?Math.sqrt(u/a):u>=c?Math.sqrt(1-(u-c)/a):1;for(u=i;u<length;++u)o[u]=0;return s},e.prototype.createDelayTimeBuffer=function(e,t,r,i){for(var n=t*e.sampleRate,s=n+(t-2*r)*e.sampleRate,o=e.createBuffer(1,s,e.sampleRate),a=o.getChannelData(0),c=0;c<n;++c)a[c]=i?(n-c)/s:c/n;for(c=n;c<s;++c)a[c]=0;return o},e.prototype.setDelay=function(e){this.modGain1.gain.setTargetAtTime(.5*e,0,.01),this.modGain2.gain.setTargetAtTime(.5*e,0,.01)},e.prototype.setPitchOffset=function(e){e>0?(this.mod1Gain.gain.value=0,this.mod2Gain.gain.value=0,this.mod3Gain.gain.value=1,this.mod4Gain.gain.value=1):(this.mod1Gain.gain.value=1,this.mod2Gain.gain.value=1,this.mod3Gain.gain.value=0,this.mod4Gain.gain.value=0),this.setDelay(this.delayTime*Math.abs(e)),this.previousPitch=e},e}();t.pitchUtil=i},function(e,t,r){"use strict";var i=this&&this.__assign||Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e};Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),s=r(2),o=function(){function e(e,t){this.sendDataMap={},this.sendDataList=new n.LinkedList,this.sendDataCheckOnceCount=100,this.signalSeq=0,this.pushCallback={},this.sessionInfos={},this.tryHeartbeatCount=0,this.heartbeatInterval=1e4,this.sendDataTimeout=5e3,this.sendDataDropTimeout=1e4,this.tryConnectCount=1,this.tryConnectTimer=null,this.tryConnectInterval=3e3,this.state=n.ENUM_CONNECT_STATE.disconnect,this.tokenType=0,this.browser=this.getBrowserAndVersion(),this.platform=navigator.platform,this.logger=e,this.stateCenter=t}return e.prototype.getBrowserAndVersion=function(){var e,t=navigator.userAgent,r=t.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*([\d\.]+)/i)||[];return/trident/i.test(r[1])?{name:"IE",version:(e=/\brv[ :]+([\d\.]+)/g.exec(t)||[])[1]||""}:"Chrome"===r[1]&&null!=(e=t.match(/\bOPR|Edge\/([\d\.]+)/))?{name:"Opera",version:e[1]}:(r=r[2]?[r[1],r[2]]:[navigator.appName,navigator.appVersion,"-?"],null!=(e=t.match(/version\/([\d+\.]+)/i))&&r.splice(1,1,e[1]),{name:r[0],version:r[1]})},e.prototype.setSessionInfo=function(e,t){this.logger.debug("zs.ssi.0 call"),this.appid=e+"",this.userid=t},e.prototype.onDisconnect=function(e){},e.prototype.onUpdateHeartBeartInterval=function(e){},e.prototype.resetConnectTimer=function(){this.logger.info("zs.rct.0 call"),clearTimeout(this.tryConnectTimer),this.tryConnectTimer=null,this.tryConnectCount=0},e.prototype.bindWebSocketHandle=function(){var e=this;this.websocket.onmessage=function(t){var r=JSON.parse(t.data);e.logger.debug("zs.bsh.0 signmsg= ",r.header.cmd),r.header.appid==e.appid&&r.header.user_id===e.userid?e.handleServerPush(r):e.logger.warn("zs.bsh.0 check header failed")},this.websocket.onclose=function(t){e.logger.info("zs.bsh.0 close msg = "+JSON.stringify(t)),e.state!=n.ENUM_CONNECT_STATE.disconnect&&(e.resetConnectTimer(),e.startConnectTimer(),e.resetCheckMessage())},this.websocket.onerror=function(t){e.logger.error("zs.bsh.0 msg = "+JSON.stringify(t))}},e.prototype.resetCheckMessage=function(){this.logger.debug("zs.rcm.0 call");for(var e=this.sendDataList.getFirst();null!=e;)this.sendDataList.remove(e),e._data.error&&e._data.error(n.SEND_MSG_RESET,e._data.seq),e=this.sendDataList.getFirst();this.sendDataMap={}},e.prototype.handleServerPush=function(e){switch(e.header.cmd){case"LoginRsp":this.handleRespondData("LoginReq",e);break;case"CreateSessionRsp":this.handleRespondData("CreateSessionReq",e),0===e.body.result&&this.addSession(e.header.session_id,e.body.session_token);break;case"MediaDescRsp":this.handleRespondData("MediaDescReq",e);break;case"CandidateInfoRsp":this.handleRespondData("CandidateInfoReq",e);break;case"CloseSessionRsp":this.handleRespondData("CloseSessionReq",e),this.removeSession(e.header.session_id);break;case"ClientHBRsp":this.handleRespondData("ClientHBReq",e);break;case"MediaDescPush":case"CandidateInfoPush":this.handlePushData(e);break;case"CloseSessionPush":this.handlePushData(e),this.removeSession(e.header.session_id);break;case"QualityReportRsp":this.handleRespondData("QualityReportReq",e);break;case"SessionResetPush":this.handlePushResetSessionData(e)}},e.prototype.disconnectCallback=function(){this.connectCallback&&(this.connectCallback(-1,this.server,void 0),this.connectCallback=null);var e=this.server;this.disconnectServer(),this.onDisconnect(e)},e.prototype.updateToken=function(){var e=this;this.logger.info("zs.ut.0 call");var t={token:this.token,tokenType:this.tokenType,roomid:this.stateCenter.roomid,anchorname:this.stateCenter.anchor_info.anchor_id,sdkversion:n.PROTO_VERSION,osinfo:navigator.appVersion};if(0!=Object.keys(this.sessionInfos).length){var r=[];for(var i in this.sessionInfos){var o=parseInt(i);r.push({session_id:o,session_token:this.sessionInfos[o].token})}t.sessions=r}this.sendMessageWithCallback("LoginReq",s.getSeq(),0,t,(function(t,r,i){if(0==i.result){e.token=i.token,e.tokenType=i.tokenType;var n={report:i.report,report_interval:i.report_interval_ms};null!=e.connectCallback&&(e.connectCallback(0,e.server,n),e.connectCallback=null)}else{var s={error:i.strError};null!=e.connectCallback&&(e.connectCallback(i.result,e.server,s),e.connectCallback=null)}}),(function(t,r){null!=e.connectCallback&&(e.connectCallback(-1,e.server,void 0),e.connectCallback=null)}))},e.prototype.sendMessageWithCallback=function(e,t,r,i,s,o){if(this.logger.debug("zs.smwc.0 call "+e),!this.websocket||1!==this.websocket.readyState)return this.logger.error("zs.smwc.0 connect not establish"),void(o&&o(n.SEND_MSG_TIMEOUT,t));var a={header:this.getHeader(e,t,r),body:i};null==s&&(s=null),null==o&&(o=null);var c={seq:t,deleted:!1,cmd:e,time:Date.parse(new Date+""),success:s,error:o},u=this.sendDataList.push(c);this.sendDataMap[c.seq]=u;var l=JSON.stringify(a);this.websocket.send(l),this.logger.debug("zs.smwc.0 success")},e.prototype.getHeader=function(e,t,r){return this.globalHeader={version:"1.0.1",cmd:e,appid:this.appid+"",seq:t,user_id:this.userid,session_id:r},this.globalHeader},e.prototype.connectServer=function(e,t,r){var i=this;if(this.token=e,this.server=t,this.state=n.ENUM_CONNECT_STATE.connecting,this.connectCallback=r,this.websocket&&1===this.websocket.readyState)this.resetConnectTimer(),this.state=n.ENUM_CONNECT_STATE.connected;else{this.logger.debug("zs.cs.0 need new websocket");try{this.websocket&&(this.logger.warn("zs.cs.0 close error websocket"),this.websocket.onclose=null,this.websocket.onerror=null,this.websocket.close(),this.websocket=null),this.websocket=new WebSocket(this.server),this.websocket.onopen=function(){i.resetConnectTimer(),i.logger.info("zs.cs.0 websocket open call"),i.bindWebSocketHandle(),i.updateToken(),i.state=n.ENUM_CONNECT_STATE.connected}}catch(e){this.logger.error("zs.cs.0 websocket error "+e)}}this.tryConnectTimer=setTimeout((function(){i.startConnectTimer(r)}),this.tryConnectInterval)},e.prototype.startConnectTimer=function(e){if(this.logger.info("zs.sct.0 call"),this.tryConnectCount>=n.MAX_TRY_CONNECT_COUNT)return this.logger.error("zs.sct.0 beyond max limit"),void this.disconnectCallback();this.websocket&&1===this.websocket.readyState?this.resetConnectTimer():(this.tryConnectCount+=1,this.connectServer(this.token,this.server,e))},e.prototype.disconnectServer=function(){this.logger.debug("zs.ds.0 call"),this.server=null,this.connectCallback=null,this.resetCheckMessage(),this.resetConnectTimer(),this.websocket&&(this.websocket.onclose=null,this.websocket.onerror=null,this.websocket.close(),this.websocket=null),this.token="",this.sessionInfos={},this.tokenType=0,this.tryHeartbeatCount=0,this.tryConnectCount=0,this.state=n.ENUM_CONNECT_STATE.disconnect},e.prototype.isServerConnected=function(){return!(!this.websocket||1!==this.websocket.readyState)},e.prototype.createSession=function(e,t,r,i,n,s,o){void 0===n&&(n=""),this.logger.debug("zs.cs.1 call: ",i);var a={type:t,stream_id:i,platform:this.platform,browser:this.browser.name,version:this.browser.version,app_id:this.appid,negotiate_mode:r,strAuthParam:n};this.sendMessageWithCallback("CreateSessionReq",e,0,a,s,o)},e.prototype.removeSession=function(e){this.logger.info("zs.rs.0 call"),this.sessionInfos[e]&&delete this.sessionInfos[e]},e.prototype.sendCloseSession=function(e,t,r,i,n){this.logger.debug("zs.scs.0 call: ",t);var s={reason:r};this.removeSession(t),this.sendMessageWithCallback("CloseSessionReq",e,t,s,i,n)},e.prototype.sendMessage=function(e,t,r,i){if(this.logger.debug("zs.sm.0 call "+e),this.websocket&&1===this.websocket.readyState){var n={header:this.getHeader(e,t,r),body:i},s=JSON.stringify(n);this.websocket.send(s),this.logger.debug("zs.sm.0 success")}else this.logger.error("zs.sm.0 connect not establish")},e.prototype.handleRespondData=function(e,t){this.logger.debug("zs.hrd.0 call");var r=this.sendDataMap[t.header.seq];if(null!=r){var i=r._data;i.cmd!==e?this.logger.error("sz.hrd.0 command is not match"):i.success&&i.success(t.header.seq,t.header.session_id,t.body),delete this.sendDataMap[t.header.seq],this.sendDataList.remove(r)}else{if("CloseSessionRsp"==t.header.cmd)return;this.logger.error("zs.hrd.0 cannot find data "+e)}},e.prototype.addSession=function(e,t){this.logger.info("zs.as.0 call"),this.sessionInfos[e]={token:t}},e.prototype.handlePushData=function(e){this.logger.debug("zs.hpd.0 call "+e.header.cmd+" session "+e.header.session_id);var t=this.pushCallback[e.header.cmd+e.header.session_id];t?t.callback&&t.callback(e.header.seq,e.header.session_id,e.body):this.logger.info("zs.hpd.0 no callbackData "+e.header.cmd+" session: "+e.header.session_id)},e.prototype.handlePushResetSessionData=function(e){this.logger.debug("zs.hprsd.0 call ");var t=[];if(0==e.body.cResetType)t=Object.keys(this.sessionInfos);else if(1==e.body.cResetType)for(var r=0;r<e.body.session_ids.length;r++)t.push(e.body.session_ids[r]);if(this.sendResetSessionAck(e.header.seq,0,0),0!=t.length)for(var i=0;i<t.length;i++){var n=this.pushCallback[e.header.cmd+t[i]];null==n?this.logger.info("zs.hprsd.0 no callbackData "+t[i]):n.callback&&n.callback(n.object,e.header.seq,t[i],e.body)}else this.logger.info("zs.hprsd.0 no session to callback")},e.prototype.sendMediaDesc=function(e,t,r,i,n,s){this.logger.debug("zs.smd.0 call: ",t);var o={type:r,sdp:i.sdp};null!=i.width&&(o.width=i.width),null!=i.height&&(o.height=i.height),null!=i.frameRate&&(o.framerate=i.frameRate),null!=i.video_min_kpbs&&(o.video_min_kpbs=i.video_min_kpbs),null!=i.video_max_kpbs&&(o.video_max_kpbs=i.video_max_kpbs),null!=i.audio_kpbs&&(o.audio_kpbs=i.audio_kpbs),this.sendMessageWithCallback("MediaDescReq",e,t,o,n,s)},e.prototype.sendCandidateInfo=function(e,t,r,i,n){this.logger.debug("zs.sci.0 call: ",t);for(var s=[],o=0;o<r.length;o++){var a={candidate:r[o].candidate,sdpMid:r[o].sdpMid,sdpMLineIndex:r[o].sdpMLineIndex};s.push(a)}var c={infos:s};this.sendMessageWithCallback("CandidateInfoReq",e,t,c,i,n)},e.prototype.sendMediaDescAck=function(e,t,r){this.logger.debug("zs.smda.0 call: ",t);var i={result:r};this.sendMessage("MediaDescAck",e,t,i)},e.prototype.sendCandidateInfoAck=function(e,t,r){this.logger.debug("zs.scia.0 call: ",t);var i={result:r};this.sendMessage("CandidateInfoAck",e,t,i)},e.prototype.sendCloseSessionAck=function(e,t,r){this.logger.debug("zs.scsa.0 call: ",t);var i={result:r};this.sendMessage("CloseSessionAck",e,t,i)},e.prototype.sendResetSessionAck=function(e,t,r){this.logger.debug("zs.ssra.0 call: ",t);var i={result:r};this.sendMessage("SessionResetAck",e,t,i)},e.prototype.registerPushCallback=function(e,t,r){r&&"function"==typeof r&&(this.logger.debug("zs.rpc.0 setcallback"),this.pushCallback[e+t]={callback:r})},e.prototype.unregisterPushCallback=function(e,t){delete this.pushCallback[e+t]},e.prototype.checkMessageTimeout=function(){for(var e=this.sendDataList.getFirst(),t=Date.parse(new Date+""),r=0,i=0,s=0;!(null==e||e._data.time+this.sendDataTimeout>t||(delete this.sendDataMap[e._data.seq],this.sendDataList.remove(e),++i,null==e._data.error||this.sendDataDropTimeout>0&&e._data.time+this.sendDataDropTimeout<t?++s:e._data.error&&e._data.error(n.SEND_MSG_TIMEOUT,e._data.seq),++r>=this.sendDataCheckOnceCount));)e=this.sendDataList.getFirst();0==i&&0==s||this.logger.debug("zs.cmt.0 call success, state: timeout=",i," drop=",s)},e.prototype.sendHeartbeat=function(){var e=this;if(this.logger.debug("zs.shb.0 call"),0!=Object.keys(this.sessionInfos).length){if(++this.tryHeartbeatCount>n.MAX_TRY_HEARTBEAT_COUNT)return this.logger.error("zs.shb.0 heartbeat try limit"),void this.disconnectCallback();var t=[];for(var r in this.sessionInfos)t.push(parseInt(r));var i={session_ids:t};this.sendMessageWithCallback("ClientHBReq",s.getSeq(),0,i,(function(t,r,i){e.heartbeatInterval!=i.hb_interval&&(e.heartbeatInterval=i.hb_interval,e.onUpdateHeartBeartInterval(i.hb_interval)),e.tryHeartbeatCount=0}),(function(t,r){e.tryHeartbeatCount+=1}))}else this.logger.info("zs.shb.0 no need to heartbeat")},e.prototype.QualityReport=function(e,t,r,n,s){this.logger.debug("zs.qr.0 call");var o={streams:[i({},r,{aid:t})]};this.sendMessageWithCallback("QualityReportReq",e,t,o,n,s)},e}();t.ZegoSignal=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=r(2),s=r(4),o=function(){function e(e,t,r,s){this.state=i.ENUM_PLAY_STATE.stop,this.candidateInfo=[],this.waitICETimer=null,this.waitingICETimeInterval=5e3,this.waitingOfferTimer=null,this.waitingOfferTimeInterval=5e3,this.waitingServerTimer=null,this.waitingServerTimerInterval=3e3,this.qualityTimer=null,this.playQualityList=[],this.maxQualityListCount=10,this.lastPlayStats={audioPacketsLost:0,videoPacketsLost:0,time:0,audioTime:0,videoTime:0,audioBytesReceived:0,videoBytesReceived:0,framesDecoded:0,framesReceived:0,framesDropped:0},this.reportSeq=n.getSeq(),this.videoSizeCallback=!1,this.qualityUpload=!1,this.qualityUploadInterval=3e4,this.qualityUploadLastTime=0,this.maxRetryCount=3,this.currentRetryCount=0,this.retryState=i.ENUM_RETRY_STATE.didNotStart,this.closeSessionSignal=!1,this.logger=e,this.signal=t,this.dataReport=r,this.qualityTimeInterval=s,r.newReport(this.reportSeq)}return e.prototype.setAudioDestination=function(e){var t=this;return this.remoteVideo?"undefined"!==this.remoteVideo.sinkId?(this.remoteVideo.setSinkId(e).then((function(){t.logger.info("zp.sad.1 success device: "+e)})).catch((function(e){t.logger.info("zp.sad.1 "+e.name)})),!0):(this.logger.error("zp.sad.1 browser does not suppport"),!1):(this.logger.info("zp.sad.1 no remoteVideo"),!1)},e.prototype.startPlay=function(e,t,r,s){var o=this;this.logger.info("zp.sp.1 called ",e),e?(this.streamId=e,this.remoteVideo=t,this.audioOutput=r,this.playOption=s||{},s&&s.videoDecodeType&&(this.playOption.videoDecodeType=s.videoDecodeType),this.sessionSeq=n.getSeq(),this.dataReport.eventStart(this.reportSeq,"CreateSession"),this.signal.createSession(this.sessionSeq,1,0,e,s&&s.streamParams,(function(e,t,r){o.dataReport.eventEndWithMsg(o.reportSeq,"CreateSession",{sessionId:r.session_id}),o.logger.info("zp.sp.1 sessionId:"+r.session_id),o.sessionSeq==e?0!==r.result?(o.logger.error("zp.sp.1 create error"),o.playStateUpdateError(n.playErrorList.CREATE_SESSION_ERROR)):(o.sessionId=r.session_id,o.onCreatePlaySessionSuccess(r)):o.logger.error("zp.sp.1 seq is not match.")}),(function(e,t){o.dataReport.eventEndWithMsg(o.reportSeq,"CreateSession",{error:e}),o.playStateUpdateError(n.playErrorList.SEND_SESSION_TIMEOUT)})),this.state=i.ENUM_PLAY_STATE.waitingSessionRsp,this.logger.debug("zp.sp.1 called success")):this.logger.warn("zp.sp.1 streamId is null")},e.prototype.onCreatePlaySessionSuccess=function(e){var t=this;this.logger.info("zp.ops.1 success");var r=[];e.turn_server&&r.push(e.turn_server),e.stun_server&&r.push(e.stun_server);var i={iceTransportPolicy:"relay",iceServers:[{urls:r,username:e.turn_username,credential:e.turn_auth_key}]};this.logger.info("zp.ops.1 username: "+e.turn_username),this.logger.info("zp.ops.1 credential: "+e.turn_auth_key),this.peerConnection=new RTCPeerConnection(i),this.peerConnection.onicecandidate=function(e){t.onIceCandidate(e)},this.peerConnection.onsignalingstatechange=function(e){t.onConnectionStateChange(e)},this.peerConnection.oniceconnectionstatechange=function(e){t.onIceConnectionStateChange(e)},this.peerConnection.ontrack=function(e){t.onGotRemoteStream(e.streams[0])},this.remoteVideo.oncanplay=function(){t.logger.debug("zp.ops.1 "+t.remoteVideo.videoWidth+" X "+t.remoteVideo.videoHeight),t.videoSizeCallback||(t.logger.debug("zp.ops.1 onresize callback"),t.onVideoSizeChanged(t.streamId,t.remoteVideo.videoWidth,t.remoteVideo.videoHeight),t.videoSizeCallback=!0)};var s={offerToReceiveAudio:1,offerToReceiveVideo:1};this.playOption&&"audio"===this.playOption.playType&&(s.offerToReceiveVideo=0),this.playOption&&"video"===this.playOption.playType&&(s.offerToReceiveAudio=0),this.logger.info("zp.ops.1 createOffer: "+s),this.dataReport.eventStart(this.reportSeq,"CreateOffer"),this.peerConnection.createOffer(s).then((function(e){t.dataReport.eventEnd(t.reportSeq,"CreateOffer"),t.onCreateOfferSuccess(e)}),(function(e){t.dataReport.eventEndWithMsg(t.reportSeq,"CreateOffer",{error:e.toString()}),t.logger.error("zp.ops.0 create offer error "+e.toString()),t.playStateUpdateError(n.playErrorList.CREATE_OFFER_ERROR)})),this.signal.registerPushCallback("MediaDescPush",this.sessionId,(function(e,r,i){t.onRecvMediaDesc(e,r,i)})),this.signal.registerPushCallback("CandidateInfoPush",this.sessionId,(function(e,r,i){t.onRecvCandidateInfo(e,r,i)})),this.signal.registerPushCallback("CloseSessionPush",this.sessionId,(function(e,r,i){t.onRecvCloseSession(e,r,i)})),this.signal.registerPushCallback("SessionResetPush",this.sessionId,(function(e,r,i){t.onRecvResetSession(e,r,i)})),this.logger.debug("zp.ops.1 call success")},e.prototype.onCreateOfferSuccess=function(e){var t=this;this.logger.info("zp.oco.1 localSdp1 "+e.sdp.substr(0,e.sdp.length/2)),this.logger.info("zp.oco.1 localSdp2 "+e.sdp.substr(e.sdp.length/2)),e.sdp=e.sdp.replace(/sendrecv/g,"recvonly"),this.playOption.videoDecodeType&&(e.sdp=s.sdpUtil.getSDPByVideDecodeType(e.sdp,this.playOption.videoDecodeType)),this.dataReport.eventStart(this.reportSeq,"SetLocalDescription"),this.peerConnection.setLocalDescription(e).then((function(){t.dataReport.eventEnd(t.reportSeq,"SetLocalDescription"),t.onSetLocalDescriptionSuccess(e)}),(function(e){t.logger.error("zp.oca.1 set error "+e.toString()),t.dataReport.eventEnd(t.reportSeq,"SetLocalDescription",{error:e.toString()}),t.playStateUpdateError(n.playErrorList.SET_LOCAL_DESC_ERROR)}))},e.prototype.onSetLocalDescriptionSuccess=function(e){var t=this;this.logger.info("zp.osd.1 success");var r={sdp:e.sdp};this.answerSeq=n.getSeq(),this.dataReport.eventStart(this.reportSeq,"SendMediaDesc"),this.signal.sendMediaDesc(this.answerSeq,this.sessionId,0,r,(function(e,r,s){t.answerSeq==e&&t.sessionId==r?(t.logger.info("zp.osd.1 send success"),t.dataReport.eventEnd(t.reportSeq,"SendMediaDesc"),t.waitingOfferTimer=setTimeout((function(){t.state==i.ENUM_PLAY_STATE.waitingOffserRsp&&(t.logger.error("zp.osd.1 waiting timeout"),t.playStateUpdateError(n.playErrorList.SERVER_CANDIDATE_TIMEOUT))}),t.waitingOfferTimeInterval),t.state=i.ENUM_PLAY_STATE.waitingServerAnswer):t.logger.error("zp.osd.1 seq or sessionId is not equal "+t.answerSeq+" "+e,0+t.sessionId+" "+r)}),(function(e,r){t.logger.error("zp.osd.1 failed to send "+e),t.dataReport.eventEndWithMsg(t.reportSeq,"SendMediaDesc",{error:e}),t.playStateUpdateError(n.playErrorList.SEND_MEDIA_DESC_TIMEOUT)})),this.state=i.ENUM_PLAY_STATE.waitingOffserRsp},e.prototype.onRecvMediaDesc=function(e,t,r){var s=this;if(this.logger.info("zp.orm.1 received ",r),this.state===i.ENUM_PLAY_STATE.waitingServerAnswer){null!=this.waitingOfferTimer&&(clearTimeout(this.waitingOfferTimer),this.waitingOfferTimer=null),this.dataReport.addEvent(this.reportSeq,"RecvMediaDesc"),this.signal.sendMediaDescAck(e,this.sessionId,0);var o={type:"answer",sdp:r.sdp,toJSON:function(){}};this.dataReport.eventStart(this.reportSeq,"SetRemoteDescription"),this.logger.info("zp.orm.1 remoteSdp ",o.sdp),this.peerConnection.setRemoteDescription(new RTCSessionDescription(o)).then((function(){s.dataReport.eventEnd(s.reportSeq,"SetRemoteDescription"),s.logger.info("zp.orm.1 set success")}),(function(e){s.logger.error("zp.orm.1 set remote error "+e.toString()),s.dataReport.eventEndWithMsg(s.reportSeq,"SetRemoteDescription",{error:e.toString()}),s.playStateUpdateError(n.playErrorList.SET_REMOTE_DESC_ERROR)})),this.sendCandidateInfo(this.candidateInfo),this.candidateInfo=[],this.waitICETimer=setTimeout((function(){s.state==i.ENUM_PLAY_STATE.waitingServerICE&&(s.logger.error("zp.orm.1 waiting server timeout"),s.playStateUpdateError(n.playErrorList.SERVER_CANDIDATE_TIMEOUT))}),this.waitingICETimeInterval),this.state=i.ENUM_PLAY_STATE.waitingServerICE,this.logger.debug("zp.orm.1 call success")}else this.logger.error("zp.orm.1 current state "+this.state+" not allowed")},e.prototype.onRecvCandidateInfo=function(e,t,r){var s=this;if(this.logger.debug("zp.orci.1 received "),this.state==i.ENUM_PLAY_STATE.waitingServerICE){null!=this.waitICETimer&&(clearTimeout(this.waitICETimer),this.waitICETimer=null),this.dataReport.addEvent(this.reportSeq,"RecvIceCandidate"),this.signal.sendCandidateInfoAck(e,this.sessionId,0);for(var o=0;o<r.infos.length;o++){var a={sdpMid:r.infos[o].sdpMid,sdpMLineIndex:r.infos[o].sdpMLineIndex,candidate:r.infos[o].candidate};this.logger.debug("zp.orci.1 candidate "+a.candidate),this.peerConnection.addIceCandidate(new RTCIceCandidate(a)).then((function(){s.logger.debug("zp.orci.1 add success")}),(function(e){s.logger.error("zp.orci.1 add error "+e.toString()),s.playStateUpdateError(n.playErrorList.SERVER_CANDIDATE_ERROR)}))}this.state=i.ENUM_PLAY_STATE.connecting,this.logger.debug("zp.orci.1 call success")}else this.logger.warn("zp.orci.1 current state "+this.state+" not allowed")},e.prototype.onIceCandidate=function(e){if(this.logger.info("zp.oic.1 called"),null!=e.candidate)if(this.logger.debug("zp.oic.1 candidate "+e.candidate.candidate),this.state<i.ENUM_PLAY_STATE.waitingServerICE||this.state==i.ENUM_PLAY_STATE.stop)this.logger.debug("zp.oic.1 cached"),this.candidateInfo.push({candidate:e.candidate.candidate,sdpMid:e.candidate.sdpMid,sdpMLineIndex:e.candidate.sdpMLineIndex});else{this.logger.debug("zp.oic.1 send");var t={candidate:e.candidate.candidate,sdpMid:e.candidate.sdpMid,sdpMLineIndex:e.candidate.sdpMLineIndex};this.sendCandidateInfo([t])}},e.prototype.onConnectionStateChange=function(e){this.logger.info("zp.oisc.1 called "+e.target.signalingState)},e.prototype.onIceConnectionStateChange=function(e){this.state!=i.ENUM_PLAY_STATE.stop&&null!=this.peerConnection&&(this.logger.info("zp.oisc.1  stateChanged "+this.peerConnection.iceConnectionState),"connected"===this.peerConnection.iceConnectionState?(this.dataReport.addEvent(this.reportSeq,"IceConnected"),this.state!=i.ENUM_PLAY_STATE.playing&&this.onPlayStateUpdate(n.ENUM_PLAY_STATE_UPDATE.start,this.streamId),this.state=i.ENUM_PLAY_STATE.playing,this.retryState!=i.ENUM_RETRY_STATE.didNotStart&&(this.retryState=i.ENUM_RETRY_STATE.finished,this.currentRetryCount=0),this.dataReport.eventStart(this.reportSeq,"PlayState"),this.setPlayQualityTimer()):"closed"===this.peerConnection.iceConnectionState?(this.dataReport.addEvent(this.reportSeq,"IceClosed"),this.checkPlayConnectionFailedState(this.peerConnection.iceConnectionState)):"failed"===this.peerConnection.iceConnectionState&&(this.dataReport.addEvent(this.reportSeq,"IceFailed"),this.checkPlayConnectionFailedState(this.peerConnection.iceConnectionState)))},e.prototype.checkPlayConnectionFailedState=function(e){var t=null;"failed"==e?t=n.playErrorList.MEDIA_CONNECTION_FAILED:"closed"==e&&(t=n.playErrorList.MEDIA_CONNECTION_CLOSED),null!=t&&(this.state!=i.ENUM_PLAY_STATE.playing&&this.retryState==i.ENUM_PLAY_STATE.didNotStart?(this.logger.info("zp.oics.1  state "+this.state+" retryState "+this.retryState+" connectionState "+e),this.playStateUpdateError(t)):this.shouldRetryPlay()?(this.onPlayStateUpdate(n.ENUM_PLAY_STATE_UPDATE.retry,this.streamId),this.startRetryPlay()):this.playStateUpdateError(t))},e.prototype.shouldRetryPlay=function(){return this.retryState==i.ENUM_RETRY_STATE.didNotStart&&this.state!=i.ENUM_PLAY_STATE.playing?(this.logger.info("zp.srp.1.0 connection didn't success"),!1):this.retryState==i.ENUM_RETRY_STATE.retrying?(this.logger.info("zp.srp.0.0 already retrying"),!1):this.currentRetryCount>this.maxRetryCount?(this.logger.info("zp.srp.1.0 beyond max"),!1):(this.logger.debug("zp.srp.1.0 call success"),!0)},e.prototype.startRetryPlay=function(){this.logger.debug("zp.srp.0 call");var e=this.streamId,t=this.remoteVideo,r=this.audioOutput;this.resetPlay(),this.tryStartPlay(e,t,r)},e.prototype.clearTryPlayTimer=function(){null!=this.waitingServerTimer&&(clearTimeout(this.waitingServerTimer),this.waitingServerTimer=null)},e.prototype.tryStartPlay=function(e,t,r){var s=this;if(this.logger.debug("zp.tsp.1 call"),this.clearTryPlayTimer(),this.streamId=e,this.remoteVideo=t,this.audioOutput=r,this.currentRetryCount>this.maxRetryCount)return this.logger.error("zp.tsp.1 beyond max limit"),void this.playStateUpdateError(n.playErrorList.WEBSOCKET_ERROR);this.retryState=i.ENUM_RETRY_STATE.retrying,this.currentRetryCount+=1,this.signal.isServerConnected()?(this.logger.debug("zp.tsp.1 signal connected"),this.startPlay(e,this.remoteVideo,this.audioOputput)):(this.logger.debug("zp.tsp.1 signal server not connected"),this.waitingServerTimer=setTimeout((function(){s.tryStartPlay(e,s.remoteVideo,s.audioOputput)}),this.waitingServerTimerInterval))},e.prototype.clearPlayQualityTimer=function(){null!=this.qualityTimer&&(clearInterval(this.qualityTimer),this.qualityTimer=null),this.lastPlayStats={audioPacketsLost:null,videoPacketsLost:null,time:null,audioTime:null,videoTime:null,audioBytesReceived:null,videoBytesReceived:null,framesDecoded:null,framesDropped:null,framesReceived:null}},e.prototype.resetPlay=function(){this.logger.info("zp.rp.1 call"),this.streamId=null,this.state=i.ENUM_PLAY_STATE.stop,null!=this.peerConnection&&(this.peerConnection.close(),this.peerConnection=null),null!=this.waitingOfferTimer&&(clearTimeout(this.waitingOfferTimer),this.waitingOfferTimer=null),null!=this.waitICETimer&&(clearTimeout(this.waitICETimer),this.waitICETimer=null),this.clearPlayQualityTimer(),this.remoteVideo&&(this.remoteVideo.srcObject=null,this.remoteVideo.oncanplay=null,this.remoteVideo=null),this.audioOputput=null,this.signal&&(this.signal.unregisterPushCallback("MediaDescPush",this.sessionId),this.signal.unregisterPushCallback("CandidateInfoPush",this.sessionId),this.signal.unregisterPushCallback("CloseSessionPush",this.sessionId)),this.sessionSeq=0,this.answerSeq=0,this.videoSizeCallback=!1,this.currentRetryCount=0,this.retryState=i.ENUM_RETRY_STATE.didNotStart,this.clearTryPlayTimer()},e.prototype.setPlayQualityTimer=function(){var e=this;null==this.qualityTimer&&(this.logger.debug("zp.spq.1 startTimer"),this.clearPlayQualityTimer(),this.qualityTimer=setInterval((function(){e.peerConnection&&e.peerConnection.getStats(null).then((function(t){e.getPlayStats(t)}),(function(t){e.logger.info("zp.spq.1 getStats error "+t.toString())}))}),this.qualityTimeInterval),this.lastPlayStats={audioPacketsLost:0,videoPacketsLost:0,time:0,audioTime:0,videoTime:0,audioBytesReceived:0,videoBytesReceived:0,framesDecoded:0,framesReceived:0,framesDropped:0})},e.prototype.getPlayStats=function(e){var t=this;if(null!=e){var r={audioFractionLost:null,audioPacketsLost:0,audioPacketsLostRate:0,audioBitrate:0,audioLevel:0,audioSendLevel:0,audioSamplingRate:0,audioCodecType:"opus",audioQuality:null,videoQuality:null,videoPacketsLostRate:0,videoBitrate:0,videoFPS:0,playData:0,nackCount:0,pliCount:0,audioJitter:0,videoFractionLost:null,videoFramesDecoded:0,frameHeight:0,frameWidth:0,videoTransferFPS:0,videoFramesDropped:0,totalRoundTripTime:0,currentRoundTripTime:0},i=this.lastPlayStats.time,n=null;e.forEach((function(e){("inbound-rtp"==e.type||"ssrc"==e.type&&null!=e.bytesReceived)&&("audio"==e.mediaType||e.id.indexOf("AudioStream")>=0)?(0!=i&&(r.audioBitrate=8*(e.bytesReceived-t.lastPlayStats.audioBytesReceived)/(e.timestamp-i)),r.audioBitrate<0&&(r.audioBitrate=0),r.audioJitter=e.jitter,r.audioPacketsLost=e.packetsLost,r.audioFractionLost=e.fractionLost,r.audioPacketsLostRate=(e.packetsLost-t.lastPlayStats.audioPacketsLost)/(e.timestamp-t.lastPlayStats.audioTime),t.lastPlayStats.audioBytesReceived=e.bytesReceived,t.lastPlayStats.audioPacketsLost=e.packetsLost,t.lastPlayStats.audioTime=e.timestamp,t.lastPlayStats.time=e.timestamp):("inbound-rtp"==e.type||"ssrc"==e.type&&null!=e.bytesReceived)&&("video"==e.mediaType||e.id.indexOf("VideoStream")>=0)?(0!=i&&(r.videoBitrate=8*(e.bytesReceived-t.lastPlayStats.videoBytesReceived)/(e.timestamp-i),r.videoFPS=1e3*(e.framesDecoded-t.lastPlayStats.framesDecoded)/(e.timestamp-i)),r.videoBitrate<0&&(r.videoBitrate=0),r.videoFPS<0&&(r.videoFPS=0),r.nackCount=e.nackCount,r.pliCount=e.pliCount,r.videoFractionLost=e.fractionLost,r.videoFramesDecoded=e.framesDecoded,r.videoPacketsLostRate=(e.packetsLost-t.lastPlayStats.videoPacketsLost)/(e.timestamp-t.lastPlayStats.videoTime),t.lastPlayStats.videoBytesReceived=e.bytesReceived,t.lastPlayStats.framesDecoded=e.framesDecoded,t.lastPlayStats.videoPacketsLost=e.packetsLost,t.lastPlayStats.videoTime=e.timestamp,t.lastPlayStats.time=e.timestamp):"track"==e.type&&("video"==e.kind||e.id.indexOf("video")>=0)?(r.frameHeight=e.frameHeight,r.frameWidth=e.frameWidth,0!=i&&(r.videoTransferFPS=1e3*(e.framesReceived-t.lastPlayStats.framesReceived)/(e.timestamp-i),r.videoFramesDropped=e.framesDropped-t.lastPlayStats.framesDropped),r.videoTransferFPS<0&&(r.videoTransferFPS=0),r.videoFramesDropped<0&&(r.videoFramesDropped=0),t.lastPlayStats.framesReceived=e.framesReceived,t.lastPlayStats.framesDropped=e.framesDropped):"track"==e.type&&("audio"==e.kind||e.id.indexOf("audio")>=0)?(r.audioLevel=e.audioLevel,r.audioSendLevel=e.totalAudioEnergy,r.audioSamplingRate=e.totalSamplesDuration):"candidate-pair"==e.type&&(null!=e.totalRoundTripTime&&(r.totalRoundTripTime=e.totalRoundTripTime),null!=e.currentRoundTripTime&&(r.currentRoundTripTime=e.currentRoundTripTime,n=1e3*r.currentRoundTripTime))})),r.audioQuality=this.getNetQuality(n,r.audioFractionLost),r.videoQuality=this.getNetQuality(n,r.videoFractionLost),this.uploadPlayQuality(r),0!=i&&this.onPlayQualityUpdate(this.streamId,r)}},e.prototype.getNetQuality=function(e,t){return e&&e<600?t>.4?2:t>.3?4:5:e<900?t>.4?2:t>.2?3:4:t>.2?2:3},e.prototype.uploadPlayQuality=function(e){var t=this;if(this.qualityUpload){var r=Date.parse(new Date+"");(0==this.qualityUploadLastTime||r-this.qualityUploadLastTime>=this.qualityUploadInterval)&&(this.logger.debug("zp.upq.1 upload"),e.stream_type="play",e.stream_id=this.streamId,e.timeStamp=r/1e3,this.signal.QualityReport(n.getSeq(),this.sessionId,e,(function(e,r,i){void 0!==i.report&&(t.qualityUpload=i.report,t.qualityUploadInterval=i.report_interval_ms)}),(function(e,r){t.logger.info("zp.upq.1 upload failed "+e)})),this.qualityUploadLastTime=r)}},e.prototype.onRecvResetSession=function(e,t,r){this.logger.info("zp.orrs.1 received "),t==this.sessionId?(this.dataReport.addEvent(this.reportSeq,"RecvResetSession"),this.shouldRetryPlay()&&this.startRetryPlay()):this.logger.info("zp.orrs.1 cannot find session")},e.prototype.onRecvCloseSession=function(e,t,r){this.logger.info("zp.orcs.1 reason: "+r.reason),this.dataReport.addEvent(this.reportSeq,"RecvCloseSession"),this.signal.sendCloseSessionAck(e,this.sessionId,0);var i=JSON.parse(JSON.stringify(n.playErrorList.SESSION_CLOSED));i.msg+=r.reason,24===r.reason?this.startRetryPlay():this.playStateUpdateError(i)},e.prototype.onGotRemoteStream=function(e){this.logger.info("zp.ogrs.0 called "+e),this.remoteVideo?(this.remoteVideo.srcObject=e,this.audioOputput&&this.setAudioDestination(this.audioOputput),this.dataReport.addEvent(this.reportSeq,"GetRemoteStream")):this.logger.error("zp.ogrs.0 no remoteVideo")},e.prototype.sendCandidateInfo=function(e){var t=this;this.logger.debug("zp.sci.1 called"),!(e=e.filter((function(e){return!(e.candidate.indexOf("tcp")>0)&&(!!e.candidate||void 0)})))||e.length<1?this.logger.info("zp.sci.1 cancelled"):(this.dataReport.eventStart(this.reportSeq,"SendIceCandidate"),this.signal.sendCandidateInfo(n.getSeq(),this.sessionId,e,(function(e,r,i){t.logger.debug("zp.sci.1 send success"),t.dataReport.eventEnd(t.reportSeq,"SendIceCandidate")}),(function(e,r){t.logger.error("zp.sci.1 failed to send: "+e.toString()),t.dataReport.eventEndWithMsg(t.reportSeq,"SendIceCandidate",{error:e}),t.playStateUpdateError(n.playErrorList.SEND_CANDIDATE_ERROR)})))},e.prototype.shouldSendCloseSession=function(e){return this.state!=n.ENUM_PLAY_STATE_UPDATE.stop&&this.state!=i.ENUM_PLAY_STATE.waitingSessionRsp},e.prototype.playStateUpdateError=function(e){this.logger.debug("zp.psue.1 called ",e.code),0!=this.sessionId&&this.shouldSendCloseSession(e)&&(this.signal.sendCloseSession(n.getSeq(),this.sessionId,1),this.closeSessionSignal=!0),this.state=i.ENUM_PLAY_STATE.stop,this.onPlayStateUpdate(n.ENUM_PLAY_STATE_UPDATE.error,this.streamId,e),this.resetPlay()},e.prototype.onPlayStateUpdate=function(e,t,r){},e.prototype.onPlayQualityUpdate=function(e,t){},e.prototype.onVideoSizeChanged=function(e,t,r){},e.prototype.stopPlay=function(){this.logger.debug("zp.sp.1.1 called"),this.sessionId&&!this.closeSessionSignal&&this.signal.sendCloseSession(n.getSeq(),this.sessionId,0),this.dataReport.eventEndWithMsg(this.reportSeq,"PlayState",{state:this.state+""}),this.dataReport.addEvent(this.reportSeq,"StopPlay"),this.dataReport.addMsgExt(this.reportSeq,{stream:this.streamId,sessionId:this.sessionId}),this.dataReport.uploadReport(this.reportSeq,"RTCPlayStream"),this.resetPlay()},e.prototype.onDisconnect=function(){this.logger.info("zp.od.1 call"),this.logger.info("zp.od.1 websocket disconnect"),this.dataReport.addEvent(this.reportSeq,"OnDisconnect"),this.playStateUpdateError(n.playErrorList.WEBSOCKET_ERROR)},e}();t.ZegoPlayWeb=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){this.playerList={},this.publisherList={}}return e.prototype.setSessionInfo=function(e,t,r,i){},e}();t.ZegoStreamCenter=i},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var s=r(18),o=r(0),a=r(1),c=r(19),u=r(20),l=r(21),h=r(22),d=r(23),p=r(24),f=function(e){function t(){return e.call(this)||this}return n(t,e),t.prototype.init=function(){this.bindSocketHandler(),this.bindStreamHandler(),this.bindHeatBeatHandler(),this.bindRoomHandler(),this.bindMessageHandler(),this.bindLiveHandler(),this.bindStreamCenterHandler()},t.prototype.bindSocketHandler=function(){var e=this;this.socketCenter=new c.SocketCenter(this.logger,this.stateCenter),this.socketCenter.registerRouter("push_signal",(function(t){e.liveHandler.handlePushSignalMsg(t)})),this.socketCenter.getSocket=function(t){return e.getSocket(t)},this.socketCenter.handlePushKickout=function(t){e.logger.info("zb.cm.bsh.0  call hpk"),e.roomHandler.setRunState(o.ENUM_RUN_STATE.logout),e.roomHandler.resetRoom(),e.onKickOut({code:o.sdkErrorList.KICK_OUT.code,msg:o.sdkErrorList.KICK_OUT.msg+t.body.reason}),e.logger.debug("zb.cm.bsh.0  call hpk success")},this.socketCenter.handlePushCustomMsg=function(t){e.messageHandler.handlePushCustomMsg(t)},this.socketCenter.handlePushUserStateUpdateMsg=function(t){e.roomHandler.handlePushUserStateUpdateMsg(t)},this.socketCenter.handlePushRoomMsg=function(t){e.onRecvRoomMsg(t.body.chat_data,t.body.server_msg_id,t.body.ret_msg_id)},this.socketCenter.handlePushMergeMsg=function(t){e.messageHandler.handlePushMergeMsg(t)},this.socketCenter.handlePushTransMsg=function(t){e.messageHandler.handlePushTransMsg(t)},this.socketCenter.handleBigImMsgRsp=function(t){e.messageHandler.handleBigImMsgRsp(t)}},t.prototype.bindStreamHandler=function(){var e=this;this.streamHandler=new l.StreamHandler(this.logger,this.stateCenter,this.socketCenter),this.streamHandler.onStreamUpdated=function(t,r){e.onStreamUpdated(t,r)},this.streamHandler.onPublishStateUpdate=function(t,r,i){e.onPublishStateUpdate(t,r,i)},this.streamHandler.onStreamExtraInfoUpdated=function(t){e.onStreamExtraInfoUpdated(t)},this.streamHandler.setCDNInfo=function(t,r){e.setCDNInfo(t,r)}},t.prototype.bindHeatBeatHandler=function(){var e=this;this.heartBeatHandler=new h.HeartBeatHandler(this.logger,this.stateCenter,this.socketCenter),this.heartBeatHandler.onRecvReliableMessage=function(t,r,i){e.onRecvReliableMessage(t,r,i)},this.heartBeatHandler.handleFetchStreamListRsp=function(t){e.streamHandler.handleFetchStreamListRsp(t)},this.heartBeatHandler.fetchUserList=function(){e.roomHandler.fetchUserList()},this.heartBeatHandler.onUpdateOnlineCount=function(t,r){e.onUpdateOnlineCount(t,r)},this.heartBeatHandler.updateStreamInfo=function(t,r,i,n){void 0===i&&(i=""),e.streamHandler.updateStreamInfo(t,r,i,n)},this.heartBeatHandler.hbLogout=function(t){e.onDisconnect(t)}},t.prototype.bindRoomHandler=function(){var e=this;this.roomHandler=new u.RoomHandler(this.logger,this.stateCenter,this.socketCenter),this.roomHandler.loginSuccessCallBack=function(t,r){var i=r.body.hearbeat_interval<o.MINIUM_HEARTBEAT_INTERVAL?o.MINIUM_HEARTBEAT_INTERVAL:r.body.hearbeat_interval;e.heartBeatHandler.start(i),e.heartBeatHandler.resetCheckMessage(),e.heartBeatHandler.startCheckMessageTimeout(),e.streamCenter.setSessionInfo(e.stateCenter.appid,e.stateCenter.idName,e.stateCenter.token,e.stateCenter.testEnvironment),r.body.anchor_info&&e.onGetAnchorInfo(r.body.anchor_info.anchor_id_name,r.body.anchor_info.anchor_nick_name),r.body.online_count&&e.onUpdateOnlineCount(e.stateCenter.roomid,r.body.online_count),e.logger.info("zb.cm.brh hls userStateUpdate "+e.stateCenter.userStateUpdate),e.stateCenter.userStateUpdate&&(e.logger.info("zb.cm.brh hls fetch all new userlist"),e.roomHandler.fetchUserList()),e.streamHandler.handleStreamStart(t,r)},this.roomHandler.onGetTotalUserList=function(t,r){e.onGetTotalUserList(t,r)},this.roomHandler.resetRoomCallBack=function(){e.heartBeatHandler.resetHeartbeat(),e.heartBeatHandler.resetCheckMessage(),e.resetStreamCenter()},this.roomHandler.onUserStateUpdate=function(t,r){e.onUserStateUpdate(t,r)},this.roomHandler.onDisconnect=function(t){e.onDisconnect(t)},this.roomHandler.loginBodyData=function(){return e.loginBodyData()}},t.prototype.bindMessageHandler=function(){var e=this;this.messageHandler=new d.MessageHandler(this.logger,this.stateCenter,this.socketCenter),this.messageHandler.onRecvCustomCommand=function(t,r,i){e.onRecvCustomCommand(t,r,i)},this.messageHandler.onRecvBigRoomMessage=function(t,r){e.onRecvBigRoomMessage(t,r)},this.messageHandler.onRecvReliableMessage=function(t,r,i){e.onRecvReliableMessage(t,r,i)}},t.prototype.bindLiveHandler=function(){var e=this;this.liveHandler=new p.LiveHandler(this.logger,this.stateCenter,this.socketCenter),this.liveHandler.onRecvEndJoinLiveCommand=function(t,r,i,n){e.onRecvEndJoinLiveCommand(t,r,i,n)},this.liveHandler.onRecvInviteJoinLiveRequest=function(t,r,i,n){e.onRecvInviteJoinLiveRequest(t,r,i,n)},this.liveHandler.onRecvJoinLiveRequest=function(t,r,i,n){e.onRecvJoinLiveRequest(t,r,i,n)}},t.prototype.bindStreamCenterHandler=function(){var e=this;this.streamCenter.onPlayStateUpdate=function(t,r,i){e.onPlayStateUpdateHandle(t,r,i)},this.streamCenter.onPlayQualityUpdate=function(t,r){e.onPlayQualityUpdate(t,r)},this.streamCenter.onPublishStateUpdate=function(t,r,i){e.onPublishStateUpdateHandle(t,r,i)},this.streamCenter.onPublishQualityUpdate=function(t,r){e.onPublishQualityUpdate(t,r)},this.streamCenter.onPlayerStreamUrlUpdate=function(t,r,i){e.onStreamUrlUpdate(t,r,i)},this.streamCenter.onVideoSizeChanged=function(t,r,i){e.onVideoSizeChanged(t,r,i)}},t.prototype.config=function(e){return this.logger.debug("zb.cm.cf call"),a.ClientUtil.checkConfigParam(e,this.logger)?(this.stateCenter.appid=e.appid,this.stateCenter.server=e.server,this.stateCenter.idName=e.idName,this.stateCenter.nickName=e.nickName,"boolean"==typeof e.testEnvironment&&(this.stateCenter.testEnvironment=e.testEnvironment),this.logger.setLogLevel(e.logLevel),!1===e.audienceCreateRoom&&(this.stateCenter.roomCreateFlag=0),e.remoteLogLevel?this.logger.setRemoteLogLevel(e.remoteLogLevel):this.logger.setRemoteLogLevel(0),this.logger.setSessionInfo(e.appid,"","",e.idName,"",o.PROTO_VERSION),e.logUrl&&this.logger.openLogServer(e.logUrl),-1==this.stateCenter.server.indexOf("test2-wsliveroom-api.zego.im")&&-1==this.stateCenter.server.indexOf("wsliveroom-test.zegocloud.com")&&-1==this.stateCenter.server.indexOf("wsliveroom-test.zego.im")||(this.stateCenter.testEnvironment=!0),this.stateCenter.configOK=!0,this.logger.debug("zb.cm.cf call success"),!0):(this.logger.error("zb.cm.cf param error"),!1)},t.prototype.login=function(e,t,r,i,n){"string"!=typeof e||"string"!=typeof r||1!==t&&2!==t?this.logger.error("zb.rh.lg params error"):this.roomHandler.login(e,t,r,null,i,n)},t.prototype.loginWithAuthor=function(e,t,r,i,n,s){"string"!=typeof e||"string"!=typeof r||"string"!=typeof i||1!==t&&2!==t?this.logger.error("zb.rh.lg params error"):this.roomHandler.login(e,t,r,i,n,s)},t.prototype.logout=function(){return this.roomHandler.logout()},t.prototype.setUserStateUpdate=function(e){"boolean"==typeof e?this.roomHandler.setUserStateUpdate(e):console.error("setUserStateUpdate param error")},t.prototype.onUserStateUpdate=function(e,t){},t.prototype.onGetTotalUserList=function(e,t){},t.prototype.onUpdateOnlineCount=function(e,t){},t.prototype.onGetAnchorInfo=function(e,t){},t.prototype.release=function(){this.logger.debug("zb.cm.rl call"),this.roomHandler.setRunState(o.ENUM_RUN_STATE.logout),this.roomHandler.resetRoom(),this.logger.stopLogServer(),this.logger.debug("zb.cm.rl call success")},t.prototype.sendCustomCommand=function(e,t,r,i){return"string"!=typeof t&&"object"!=typeof t?(this.logger.error("zb.mh.scc params error"),!1):this.messageHandler.sendCustomCommand(e,t,r,i)},t.prototype.onRecvCustomCommand=function(e,t,r){},t.prototype.sendRoomMsg=function(e,t,r,i,n){this.messageHandler.sendRoomMsg(e,t,r,i,n)},t.prototype.onRecvRoomMsg=function(e,t,r){},t.prototype.sendReliableMessage=function(e,t,r,i){this.messageHandler.sendReliableMessage(e,t,r,i)},t.prototype.onRecvReliableMessage=function(e,t,r){},t.prototype.sendBigRoomMessage=function(e,t,r,i,n){this.messageHandler.sendBigRoomMessage(e,t,r,i,n)},t.prototype.onRecvBigRoomMessage=function(e,t){},t.prototype.sendRelayMessage=function(e,t,r,i){this.messageHandler.sendRelayMessage(e,t,r,i)},t.prototype.requestJoinLive=function(e,t,r,i){return this.liveHandler.requestJoinLive(e,t,r,i)},t.prototype.onRecvJoinLiveRequest=function(e,t,r,i){},t.prototype.inviteJoinLive=function(e,t,r,i){return this.liveHandler.inviteJoinLive(e,t,r,i)},t.prototype.onRecvInviteJoinLiveRequest=function(e,t,r,i){},t.prototype.endJoinLive=function(e,t,r){return this.liveHandler.endJoinLive(e,t,r)},t.prototype.onRecvEndJoinLiveCommand=function(e,t,r,i){},t.prototype.respondJoinLive=function(e,t,r,i){return this.liveHandler.respondJoinLive(e,t,r,i)},t.prototype.updateMixStream=function(e,t,r){return this.streamHandler.updateMixStream(e,t,r)},t.prototype.stopMixStream=function(e,t,r){return this.streamHandler.stopMixStream(e,t,r)},t.prototype.publishTarget=function(e,t,r){return this.streamHandler.publishTarget(e,t,r)},t.prototype.updateStreamExtraInfo=function(e,t){return this.streamHandler.updateStreamExtraInfo(e,t)},t.prototype.onStreamUrlUpdate=function(e,t,r){},t.prototype.onStreamUpdated=function(e,t){},t.prototype.onStreamExtraInfoUpdated=function(e){},t.prototype.onPlayStateUpdate=function(e,t,r){},t.prototype.onVideoSizeChanged=function(e,t,r){},t.prototype.onPlayQualityUpdate=function(e,t){},t.prototype.onPublishStateUpdate=function(e,t,r){},t.prototype.onPublishQualityUpdate=function(e,t){},t.prototype.onDisconnect=function(e){},t.prototype.onKickOut=function(e){},t.getCurrentVersion=function(){return o.PROTO_VERSION},t}(s.Common);t.BaseCenter=f},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=function(){function e(){}return e.prototype.onPlayStateUpdateHandle=function(e,t,r){1==e&&this.stopPlayingStream(t),this.onPlayStateUpdate(e,t,r)},e.prototype.onPublishStateUpdateHandle=function(e,t,r){var n=this;0==e?this.stateCenter.publishStreamList[t]&&(this.stateCenter.publishStreamList[t].state==i.ENUM_PUBLISH_STREAM_STATE.tryPublish?(this.stateCenter.publishStreamList[t].state=i.ENUM_PUBLISH_STREAM_STATE.update_info,this.streamHandler.updateStreamInfo(t,i.ENUM_STREAM_SUB_CMD.liveBegin,this.stateCenter.publishStreamList[t].extra_info,(function(e){n.stateCenter.publishStreamList[t]&&n.stateCenter.publishStreamList[t].state==i.ENUM_PUBLISH_STREAM_STATE.update_info&&(n.stateCenter.publishStreamList[t].state=i.ENUM_PUBLISH_STREAM_STATE.stop,n.onPublishStateUpdate(1,t,e),n.streamCenter.stopPlayingStream(t))}))):this.WebrtcOnPublishStateUpdateHandle(e,t,r)):(this.onPublishStateUpdate(e,t,r),1==e&&this.stopPublishingStream(t))},e.prototype.resetStreamCenter=function(){if(this.stateCenter.customUrl&&(this.stateCenter.customUrl=null),this.streamCenter.reset(),!this.socketCenter.isDisConnect())for(var e in this.stateCenter.publishStreamList)this.stateCenter.publishStreamList[e].state==i.ENUM_PUBLISH_STREAM_STATE.publishing&&this.streamHandler.updateStreamInfo(e,i.ENUM_STREAM_SUB_CMD.liveEnd,this.stateCenter.publishStreamList[e].extra_info)},e.prototype.handleFetchWebRtcUrlRsp=function(e){var t=e.body.stream_id;if("push"===e.body.ptype)this.stateCenter.publishStreamList[t]?this.streamCenter.startPublishingStream(t,e.body.urls):this.logger.error("cb.cm.hfwur no streamid to publish");else if("pull"==e.body.ptype){for(var r=!1,i=0;i<this.stateCenter.streamList.length;i++)if(this.stateCenter.streamList[i].stream_id===t){r=!0;break}0==r&&this.logger.warn("cb.cm.hfwur cannot find stream, continue to play"),this.streamCenter.startPlayingStream(t,e.body.urls)}},e}();t.Common=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=r(1),s=function(){function e(e,t){var r=this;this.cmdSeq=0,this.responseRouters={},this.logger=e,this.stateCenter=t,this.responseRouters={push_kickout:function(e){r.handlePushKickout(e)},push_custommsg:function(e){r.handlePushCustomMsg(e)},push_im_chat:function(e){r.handlePushRoomMsg(e)},push_userlist_update:function(e){r.handlePushUserStateUpdateMsg(e)},push_merge_message:function(e){r.handlePushMergeMsg(e)},trans:function(e){r.handleTransRsp(e)},push_trans:function(e){r.handlePushTransMsg(e)}}}return e.prototype.handlePushKickout=function(e){},e.prototype.handlePushCustomMsg=function(e){},e.prototype.handlePushRoomMsg=function(e){},e.prototype.handlePushUserStateUpdateMsg=function(e){},e.prototype.handlePushMergeMsg=function(e){},e.prototype.handlePushTransMsg=function(e){},e.prototype.handleBigImMsgRsp=function(e){},e.prototype.handleTransRsp=function(e){if(this.stateCenter.isLogin())if(0==e.body.err_code){var t=e.body.trans_type;this.stateCenter.transSeqMap[t]?(this.stateCenter.transSeqMap[t].seq=e.body.trans_seq,this.logger.debug("zb.sc.htr trans "+t+" seq "+e.body.trans_seq)):this.logger.error("zb.sc.htr cannot match send info")}else this.logger.error("zb.sc.htr trans send error "+e.body.err_code);else this.logger.error("zb.sc.htr not login")},e.prototype.handleBizChannelRspCallback=function(e,t){0===e.body.err_code?null!=t.success&&t.success(e.header.seq,e.body.cmd,e.body.rsp_body):null!=t.error&&t.error(e.body.err_code,e.header.seq,e.body.rsp_body)},e.prototype.registerRouter=function(e,t){this.responseRouters[e]=t},e.prototype.getSocket=function(e){return null},e.prototype.getHeaderV2=function(e){return{Protocol:"req_v2",cmd:e,appid:this.stateCenter.appid,seq:++this.cmdSeq,user_id:this.stateCenter.userid,session_id:this.stateCenter.sessionid||"",room_id:this.stateCenter.roomid||""}},e.prototype.getHeader=function(e){return{Protocol:"req",cmd:e,appid:this.stateCenter.appid,seq:++this.cmdSeq,user_id:this.stateCenter.userid,session_id:this.stateCenter.sessionid||"",room_id:this.stateCenter.roomid||""}},e.prototype.sendMessage=function(e,t,r,n){if(this.logger.debug("zb.sc.sm call "+e),this.isDisConnect())return this.logger.error("zb.sc.sm error  "+e+"websocket is disconnected"),-1;var s="V1"===i.ROOMVERSION?this.getHeader(e):this.getHeaderV2(e),o={header:s,body:t};if(null==r&&(r=null),null==n&&(n=null),null!=r||null!=n){var a={data:o,seq:s.seq,deleted:!1,time:Date.parse(new Date+""),success:r,error:n},c=this.stateCenter.sendCommandList.push(a);this.stateCenter.sendCommandMap[a.seq]=c}return this.websocket.send(JSON.stringify(o)),this.logger.debug("zb.sc.sm success"),s.seq},e.prototype.sendCustomMessage=function(e,t,r,n){if(this.logger.debug("zb.sc.scm call"),this.isDisConnect())return this.logger.error("zb.sc.scm error"),!1;var s="V1"===i.ROOMVERSION?this.getHeader(e):this.getHeaderV2(e),o={header:s,body:t},a=JSON.stringify(o);null==r&&(r=null),null==n&&(n=null);var c={data:o,seq:s.seq,deleted:!1,time:Date.parse(new Date+""),success:r,error:n},u=this.stateCenter.sendDataList.push(c);return this.stateCenter.sendDataMap[c.seq]=u,this.websocket.send(a),this.logger.debug("zb.sc.scm success seq: ",s.seq),!0},e.prototype.isDisConnect=function(){return!this.websocket||1!==this.websocket.readyState},e.prototype.closeSocket=function(){this.websocket&&(this.logger.info("zb.sc.cs close websocket"),this.websocket.onclose=null,this.websocket.onerror=null,this.websocket.close(),this.websocket=null)},e.prototype.createSocket=function(e){this.websocket=this.getSocket(e)},e.prototype.openHandler=function(e){this.websocket.onopen=e},e.prototype.closeHandler=function(e){this.websocket.onclose=e},e.prototype.errorHandler=function(){var e=this;this.websocket.onerror=function(t){e.logger.error("zb.sc.oe msg="+JSON.stringify(t))}},e.prototype.checkResponse=function(e){return(e.header.appid!==this.stateCenter.appid||e.header.session_id!==this.stateCenter.sessionid||e.header.user_id!==this.stateCenter.userid||e.header.room_id!==this.stateCenter.roomid||this.stateCenter.runState!==i.ENUM_RUN_STATE.login)&&(this.logger.error("zb.sc.crp check session fail."),!0)},e.prototype.responseHandler=function(){var e=this;this.websocket.onmessage=function(t){var r=JSON.parse(t.data);e.logger.info("zb.sc.ws.rph jsonmsg= ",r.header.cmd),e.logger.info("zb.sc.ws.rph jsonmsg= ",t.data),0!==r.body.err_code&&r.body.err_message&&e.logger.error("zb.sc.ws.rph cmd="+r.header.cmd+", err_code="+r.body.err_code+", err_message="+r.body.err_message+" "),"login"!==r.header.cmd?"logout"!==r.header.cmd?e.stateCenter.isLogin()?e.checkResponse(r)?e.logger.error("zb.sc.ws.rph check session fail."):(e.handleSendCommandMsgRsp(r),e.logger.info("zb.sc.ws.rph cmd="+r.header.cmd+",function="+!!e.responseRouters[r.header.cmd]),e.responseRouters[r.header.cmd]&&e.responseRouters[r.header.cmd](r)):e.logger.warn("zb.sc.ws.rph  already logout"):e.responseRouters.logout(r,e.cmdSeq):e.responseRouters.login(r,e.cmdSeq)}},e.prototype.handleSendCommandMsgRsp=function(e){this.logger.debug("zb.sc.hscmr call");var t,r=this.stateCenter.sendCommandMap[e.header.seq];null!=r&&("login"==(t=r._data).data.header.cmd?this.logger.debug("zb.sc.hscmr don't check "+t.data.header.cmd):"relay"==t.data.header.cmd?this.handleRelayRspCallback(e,t):"bigim_chat"==t.data.header.cmd?this.handleBigImRspCallback(e,t):"biz_channel"==t.data.header.cmd?this.handleBizChannelRspCallback(e,t):0===e.body.err_code?null!=t.success&&t.success(e.header.seq):null!=t.error&&t.error(n.ClientUtil.getServerError(e.body.err_code),e.header.seq),delete this.stateCenter.sendCommandMap[e.header.seq],this.stateCenter.sendCommandList.remove(r)),this.logger.debug("zb.sc.hscmr call success")},e.prototype.handleRelayRspCallback=function(e,t){0===e.body.err_code?null!=t.success&&t.success(e.header.seq,e.body.relay_result):null!=t.error&&t.error(n.ClientUtil.getServerError(e.body.err_code),e.header.seq)},e.prototype.handleBigImRspCallback=function(e,t){0===e.body.err_code?null!=t.success&&this.handleBigImMsgRsp(e):null!=t.error&&t.error(n.ClientUtil.getServerError(e.body.err_code),e.header.seq)},e}();t.SocketCenter=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=r(1),s=function(){function e(e,t,r){this.logger=e,this.socketCenter=r,this.stateCenter=t}return e.prototype.setRunState=function(e){this.logger.debug("zb.rh.srs old="+this.stateCenter.runState+", new="+e),this.stateCenter.lastRunState=this.stateCenter.runState,this.stateCenter.runState=e},e.prototype.resetTryLogin=function(){this.logger.debug("zb.rh.rtl call"),clearTimeout(this.stateCenter.tryLoginTimer),this.stateCenter.tryLoginTimer=null,this.stateCenter.tryLoginCount=0,this.logger.debug("zb.rh.rtl call success")},e.prototype.resetBigRoomInfo=function(){this.stateCenter.transSeqMap={},this.stateCenter.realyMessageList=[],this.stateCenter.relayTimer&&(clearTimeout(this.stateCenter.relayTimer),this.stateCenter.relayTimer=null),this.stateCenter.bigImLastTimeIndex=0,this.stateCenter.bigIMmessageList=[],this.stateCenter.bigImCallbackMap={},this.stateCenter.bigImTimer&&(clearTimeout(this.stateCenter.bigImTimer),this.stateCenter.bigImTimer=null),this.stateCenter.serverTimeOffset=0,this.stateCenter.datiTimeWindow=0,this.stateCenter.bigimTimeWindow=0},e.prototype.resetRoom=function(){var e=this;this.logger.debug("zb.rh.rr call"),this.resetTryLogin(),this.resetRoomCallBack(),this.stateCenter.streamList=[],this.stateCenter.streamQuerying=!1,this.stateCenter.publishStreamList={},this.stateCenter.joinLiveCallbackMap={},this.stateCenter.joinLiveRequestMap={},this.stateCenter.streamUrlMap={},this.resetBigRoomInfo(),this.stateCenter.cmdCallback={},this.logger.debug("zb.rh.rr call send logout=",this.stateCenter.sessionid),"0"!==this.stateCenter.sessionid&&(this.socketCenter.registerRouter("logout",(function(t){e.handleLogoutRsp(t)})),this.socketCenter.sendMessage("logout",{reserve:0})),this.socketCenter.closeSocket(),this.setRunState(i.ENUM_RUN_STATE.logout),this.stateCenter.userid="",this.stateCenter.sessionid="",this.logger.setSessionInfo(this.stateCenter.appid,this.stateCenter.roomid,this.stateCenter.userid,this.stateCenter.idName,this.stateCenter.sessionid,i.PROTO_VERSION),this.logger.debug("zb.rh.rr call success")},e.prototype.resetRoomCallBack=function(){},e.prototype.onDisconnect=function(e){},e.prototype.loginSuccessCallBack=function(e,t){},e.prototype.onGetTotalUserList=function(e,t){},e.prototype.login=function(e,t,r,s,o,a){if(this.logger.setSessionInfo(this.stateCenter.appid,e,"",this.stateCenter.idName,"",i.PROTO_VERSION),this.logger.info("zb.rh.lg call:",e,r),s&&(this.stateCenter.third_token=s),!this.stateCenter.configOK||!n.ClientUtil.checkLoginParam(e,r))return this.logger.error("zb.rh.lg param error"),void a({code:"",msg:"param error"});this.stateCenter.runState!==i.ENUM_RUN_STATE.logout&&(this.logger.debug("zb.rh.lg reset"),this.setRunState(i.ENUM_RUN_STATE.logout),this.resetRoom()),this.logger.debug("zb.rh.lg begin"),this.setRunState(i.ENUM_RUN_STATE.trylogin),this.stateCenter.roomid=e,this.stateCenter.token=r,this.stateCenter.role=t,n.ClientUtil.registerCallback("login",{success:o,error:a},this.stateCenter.callbackList),this.resetTryLogin(),this.tryLogin(),this.logger.info("zb.rh.lg call success")},e.prototype.loginBodyData=function(){return null},e.prototype.tryLogin=function(){var e=this;if(this.logger.debug("zb.rh.tl call"),this.stateCenter.runState===i.ENUM_RUN_STATE.trylogin){if(++this.stateCenter.tryLoginCount>i.MAX_TRY_LOGIN_COUNT){this.logger.error("zb.rh.tl fail times limit");var t=this.stateCenter.lastRunState;return this.setRunState(i.ENUM_RUN_STATE.logout),this.resetRoom(),void(t==i.ENUM_RUN_STATE.login?(this.logger.error("zb.rh.tl fail and disconnect"),this.onDisconnect(i.sdkErrorList.LOGIN_DISCONNECT)):(this.logger.info("zb.rh.tl fail and callback user"),n.ClientUtil.actionErrorCallback("login",this.stateCenter.callbackList)(i.sdkErrorList.LOGIN_TIMEOUT)))}if(this.stateCenter.startConnceTime=(new Date).getTime(),console.warn("start connect",this.stateCenter.startConnceTime),this.socketCenter.isDisConnect()){this.logger.debug("zb.rh.tl need new websocket");try{this.socketCenter.closeSocket(),this.logger.debug("zb.rh.tl new websocket"),this.socketCenter.createSocket(this.stateCenter.server),this.socketCenter.registerRouter("login",(function(t,r){e.handleLoginRsp(t,r)})),this.socketCenter.closeHandler((function(t){e.socketCenter.closeSocket(),e.closeHandler(t)})),this.socketCenter.openHandler((function(){e.openHandler()}))}catch(e){this.logger.error("zb.rh.tl websocket err:"+e)}}else{var r=this.loginBodyData();this.logger.info("zb.rh.tl use current websocket and sent login"),this.socketCenter.sendMessage("login",r)}this.stateCenter.tryLoginTimer=setTimeout((function(){e.tryLogin()}),i.TRY_LOGIN_INTERVAL[this.stateCenter.tryLoginCount%i.MAX_TRY_LOGIN_COUNT]),this.logger.info("zb.rh.tl call success")}else this.logger.error("zb.rh.tl state error")},e.prototype.handleLoginRsp=function(e,t){if(this.logger.debug("zb.rh.hlr call"),this.stateCenter.runState===i.ENUM_RUN_STATE.trylogin){if(e.header.seq===t)return 0!==e.body.err_code?(this.handleLoginFail(e),void this.logger.error("zb.rh.hlr server error=",e.body.err_code)):(this.handleLoginSuccess(e),void this.logger.info("zb.rh.hlr call success."));this.logger.error("zb.rh.hlr in wrong seq, local=",t,",recv=",e.header.seq)}else this.logger.error("zb.rh.hlr state error")},e.prototype.handleLoginFail=function(e){if(this.logger.debug("zb.rh.hlf call"),n.ClientUtil.isKeepTryLogin(e.body.err_code))this.logger.warn("zb.rh.hlf KeepTry true");else{var t=this.stateCenter.lastRunState;this.setRunState(i.ENUM_RUN_STATE.logout),this.resetRoom();var r=n.ClientUtil.getServerError(e.body.err_code);t===i.ENUM_RUN_STATE.login?(this.logger.info("zb.rh.hlf callback disconnect"),this.onDisconnect(r)):(this.logger.info("zb.rh.hlf callback error"),n.ClientUtil.actionErrorCallback("login",this.stateCenter.callbackList)(r)),this.logger.debug("zb.rh.hlf call success")}},e.prototype.handleLoginSuccess=function(e){this.stateCenter.startloginSucTime=(new Date).getTime(),console.warn("login suc",this.stateCenter.startloginSucTime,this.stateCenter.startloginSucTime-this.stateCenter.startloginTime,this.stateCenter.startloginSucTime-this.stateCenter.startConnceTime),this.logger.info("zb.rh.hls call");var t=this.stateCenter.lastRunState;if(this.setRunState(i.ENUM_RUN_STATE.login),this.stateCenter.userid=e.body.user_id,this.stateCenter.sessionid=e.body.session_id,this.stateCenter.anchor_info=e.body.anchor_info||this.stateCenter.anchor_info,this.logger.setSessionInfo(this.stateCenter.appid,this.stateCenter.roomid,this.stateCenter.userid,this.stateCenter.idName,this.stateCenter.sessionid,i.PROTO_VERSION),e.body.config_info&&(this.logger.setRemoteLogLevel(e.body.config_info.log_level),""!=e.body.config_info.log_url&&this.logger.openLogServer(e.body.config_info.log_url)),null!=e.body.ret_timestamp&&"string"==typeof e.body.ret_timestamp){var r=parseFloat(e.body.ret_timestamp);this.stateCenter.serverTimeOffset=0==r?0:e.body.ret_timestamp-(new Date).getTime()}e.body.bigim_time_window&&"number"==typeof e.body.bigim_time_window&&(this.stateCenter.bigimTimeWindow=e.body.bigim_time_window),e.body.dati_time_window&&"number"==typeof e.body.dati_time_window&&(this.stateCenter.datiTimeWindow=e.body.dati_time_window),this.resetTryLogin(),this.loginSuccessCallBack(t,e)},e.prototype.openHandler=function(){this.logger.info("zb.rh.oh websocket.onpen call"),this.socketCenter.responseHandler();var e=this.loginBodyData();this.logger.info("zb.rh.oh websocket.onpen send login"),this.stateCenter.startloginTime=(new Date).getTime(),console.warn("start login",this.stateCenter.startloginTime,this.stateCenter.startloginTime-this.stateCenter.startConnceTime),this.socketCenter.sendMessage("login",e),this.logger.debug("zb.rh.oh websocket.onpen call success")},e.prototype.closeHandler=function(e){this.logger.info("zb.rh.ws.oc msg="+JSON.stringify(e)),this.stateCenter.runState!==i.ENUM_RUN_STATE.logout?this.stateCenter.runState===i.ENUM_RUN_STATE.trylogin&&this.stateCenter.tryLoginCount<=i.MAX_TRY_LOGIN_COUNT?this.logger.info("zb.rh.ws.oc is called because of try login"):this.stateCenter.runState===i.ENUM_RUN_STATE.login?(this.logger.info("zb.rh.ws.oc is called because of network broken, try again"),this.setRunState(i.ENUM_RUN_STATE.trylogin),this.resetTryLogin(),this.tryLogin()):(this.logger.error("zb.rh.ws.oc out of think!!!"),this.setRunState(i.ENUM_RUN_STATE.logout),this.resetRoom(),this.onDisconnect(i.sdkErrorList.UNKNOWN)):this.logger.info("zb.rh.ws.oc onclose logout flow call websocket.close")},e.prototype.logout=function(){return this.logger.debug("zb.rh.lo call"),this.stateCenter.runState===i.ENUM_RUN_STATE.logout?(this.logger.warn("zb.rh.lo at logout"),!1):(this.resetRoom(),this.logger.info("zb.rh.lo call success"),!0)},e.prototype.setUserStateUpdate=function(e){return this.logger.debug("zb.rh.su call"),"boolean"!=typeof e?(this.logger.info("zb.rh.su param error"),!1):(this.stateCenter.userStateUpdate=e,this.logger.info("zb.rh.su call success "+e),!0)},e.prototype.fetchUserList=function(){this.logger.debug("zb.rh.ful call"),this.stateCenter.userQuerying?this.logger.warn("zb.rh.ful is already querying"):(this.stateCenter.userQuerying=!0,this.stateCenter.userTempList=[],"V1"===i.ROOMVERSION?this.fetchUserListWithPage(0):this.fetchUserListWithPageV2(0),this.logger.info("zb.rh.ful the first time call"))},e.prototype.fetchUserListWithPageV2=function(e){var t=this;this.logger.debug("zb.rh.fulwp call"),this.socketCenter.registerRouter("user_list_v2",(function(r){t.handleFetchUserListRspV2(e,r)})),this.socketCenter.sendMessage("user_list_v2",{marker:0===e?"":e+"",mode:0,limit:100}),this.logger.info("zb.rh.fulwp call success")},e.prototype.fetchUserListWithPage=function(e){var t=this;this.logger.debug("zb.rh.fulwp call"),this.socketCenter.registerRouter("user_list",(function(e){t.handleFetchUserListRsp(e)})),this.socketCenter.sendMessage("user_list",{user_index:e,sort_type:0}),this.logger.info("zb.rh.fulwp call success")},e.prototype.handleFetchUserListRspV2=function(e,t){if(this.logger.debug("zb.rh.hfulr call"),0!=t.body.err_code)return this.stateCenter.userQuerying=!1,void this.logger.info("zb.rh.hfulr fetch error "+t.body.err_code);if(this.stateCenter.userStateUpdate){if(this.stateCenter.userTempList=this.stateCenter.userTempList.concat(t.body.user_baseinfos),e!=t.body.marker)return this.logger.warn("zb.rh.hfulr fetch another page"),void this.fetchUserListWithPageV2(e+1);this.stateCenter.userSeq=t.body.server_user_seq,this.logger.info("zb.rh.hfulr set user Seq "+this.stateCenter.userSeq);for(var r=[],i=0;i<this.stateCenter.userTempList.length;i++){var n={idName:this.stateCenter.userTempList[i].id_name,nickName:this.stateCenter.userTempList[i].nick_name,role:this.stateCenter.userTempList[i].role};r.push(n)}this.stateCenter.userQuerying=!1,this.onGetTotalUserList(this.stateCenter.roomid,r),this.stateCenter.userTempList=[],this.logger.info("zb.rh.hfulr call success user_list "+r+" count "+r.length)}},e.prototype.handleFetchUserListRsp=function(e){if(this.logger.debug("zb.rh.hfulr call"),0!=e.body.err_code)return this.stateCenter.userQuerying=!1,void this.logger.info("zb.rh.hfulr fetch error "+e.body.err_code);if(this.stateCenter.userStateUpdate){this.stateCenter.userTempList=this.stateCenter.userTempList.concat(e.body.user_baseinfos);var t=e.body.ret_user_index;if(t!=e.body.server_user_index)return this.logger.warn("zb.rh.hfulr fetch another page"),void this.fetchUserListWithPage(t+1);this.stateCenter.userSeq=e.body.server_user_seq,this.logger.info("zb.rh.hfulr set user Seq "+this.stateCenter.userSeq);for(var r=[],i=0;i<this.stateCenter.userTempList.length;i++){var n={idName:this.stateCenter.userTempList[i].id_name,nickName:this.stateCenter.userTempList[i].nick_name,role:this.stateCenter.userTempList[i].role};r.push(n)}this.stateCenter.userQuerying=!1,this.onGetTotalUserList(this.stateCenter.roomid,r),this.stateCenter.userTempList=[],this.logger.info("zb.rh.hfulr call success user_list "+r+" count "+r.length)}},e.prototype.handleLogoutRsp=function(e){this.logger.debug("zb.rh.hlor result=",e.body.err_code)},e.prototype.handlePushUserStateUpdateMsg=function(e){if(this.logger.info("zb.rh.hpus call"),this.stateCenter.isLogin())if(this.stateCenter.userStateUpdate){if(this.stateCenter.userSeq+e.body.user_actions.length!==e.body.user_list_seq)return this.logger.warn("zb.rh.hpus fetch new userlist "+this.stateCenter.userSeq,NaN+e.body.user_list_seq),void this.fetchUserList();this.stateCenter.userSeq=e.body.user_list_seq,this.logger.debug("zb.rh.hpus push userSeq "+this.stateCenter.userSeq);for(var t=[],r=0;r<e.body.user_actions.length;r++){var i={action:e.body.user_actions[r].Action,idName:e.body.user_actions[r].IdName,nickName:e.body.user_actions[r].NickName,role:e.body.user_actions[r].Role,loginTime:e.body.user_actions[r].LoginTime};t.push(i)}this.onUserStateUpdate(e.body.room_id,t),this.logger.info("zb.rh.hpus call success")}else this.logger.error("zb.rh.hpus no userStateUpdate flag");else this.logger.error("zb.rh.hpus not login")},e.prototype.onUserStateUpdate=function(e,t){},e}();t.RoomHandler=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=r(1),s=function(){function e(e,t,r){this.logger=e,this.socketCenter=r,this.stateCenter=t}return e.prototype.setCDNInfo=function(e,t){},e.prototype.onStreamUpdated=function(e,t){},e.prototype.onStreamExtraInfoUpdated=function(e){},e.prototype.handleStreamStart=function(e,t){var r=this;if(this.stateCenter.streamQuerying=!1,this.socketCenter.registerRouter("stream",(function(e){r.handleStreamUpdateRsp(e)})),this.socketCenter.registerRouter("push_stream_update",(function(e){r.handlePushStreamUpdateMsg(e)})),e==i.ENUM_RUN_STATE.login)this.logger.info("zb.sh.hss recover from disconnect so call streamupdate"),this.handleFullUpdateStream(t.body.stream_seq,t.body.stream_info||[]);else{this.logger.info("zb.sh.hss success callback user"),this.stateCenter.streamList=t.body.stream_info||[],this.stateCenter.streamSeq=t.body.stream_seq;for(var s=0;s<this.stateCenter.streamList.length;s++)this.stateCenter.streamList[s].anchor_id_name==this.stateCenter.idName&&(this.updateStreamInfo(this.stateCenter.streamList[s].stream_id,i.ENUM_STREAM_SUB_CMD.liveEnd),this.stateCenter.streamList.splice(s,1));var o=this.makeCallbackStreamList(this.stateCenter.streamList);n.ClientUtil.actionSuccessCallback("login",this.stateCenter.callbackList)(o)}},e.prototype.onPublishStateUpdate=function(e,t,r){},e.prototype.updateStreamInfo=function(e,t,r,i){var n=this;void 0===r&&(r=""),this.logger.debug("zb.sh.usi call");var s={stream_id:e,extra_info:r},o={sub_cmd:t,stream_msg:JSON.stringify(s)};this.socketCenter.registerRouter("stream",(function(e){n.handleStreamUpdateRsp(e)})),this.socketCenter.sendMessage("stream",o,void 0,i),this.logger.info("zb.sh.usi call success cmd "+t)},e.prototype.handleStreamUpdateRsp=function(e){if(this.stateCenter.isLogin())if(0==e.body.err_code){this.logger.info("zb.sh.hsur stream seq "+this.stateCenter.streamSeq+" server seq "+e.body.stream_seq),this.stateCenter.streamSeq=e.body.stream_seq;for(var t=0;t<e.body.stream_info.length;t++){var r=e.body.stream_info[t].stream_id;if(!this.stateCenter.publishStreamList[r])return void this.logger.info("hsur.0 stream is not exist");this.stateCenter.publishStreamList[r].state==i.ENUM_PUBLISH_STREAM_STATE.update_info&&(this.stateCenter.publishStreamList[r].state=i.ENUM_PUBLISH_STREAM_STATE.publishing,this.onPublishStateUpdate(0,r,0))}}else this.logger.error("zb.sh.hsur stream update error "+e.body.err_code);else this.logger.error("zb.sh.hsur not login")},e.prototype.handleFetchStreamListRsp=function(e){this.logger.info("zb.sh.hfslr call"),this.stateCenter.streamQuerying=!1,0===e.body.err_code?this.stateCenter.streamSeq!==e.body.stream_seq?(this.handleFullUpdateStream(e.body.stream_seq,e.body.stream_info),this.logger.debug("zb.sh.hfslr call success")):this.logger.info("zb.sh.hfslr same seq"):this.logger.info("zb.sh.hfslr server error=",e.body.err_code)},e.prototype.handleFullUpdateStream=function(e,t){var r=this;this.logger.debug("zb.sh.hfus call"),this.stateCenter.streamSeq=e,this.logger.debug("zb.sh.hfus server seq "+this.stateCenter.streamSeq),n.ClientUtil.mergeStreamList(this.logger,this.stateCenter.idName,this.stateCenter.streamList,t,(function(e,t,n){0!==e.length&&(r.logger.debug("zb.sh.hfus callback addstream"),r.onStreamUpdated(i.ENUM_STREAM_UPDATE_TYPE.added,r.makeCallbackStreamList(e))),0!==t.length&&(r.logger.debug("zb.sh.hfus callback delstream"),r.onStreamUpdated(i.ENUM_STREAM_UPDATE_TYPE.deleted,r.makeCallbackStreamList(t))),0!==n.length&&(r.logger.debug("zb.sh.hfus callback updatestream"),r.onStreamExtraInfoUpdated(r.makeCallbackStreamList(n)))})),this.logger.info("zb.sh.hfus call success")},e.prototype.handlePushStreamUpdateMsg=function(e){if(this.logger.info("zb.sh.hpsum call"),e.body.stream_info&&0!==e.body.stream_info.length){if(e.body.stream_info.length+this.stateCenter.streamSeq!==e.body.stream_seq)return this.logger.info("zb.sh.hpsum call updatestream"),void this.fetchStreamList();switch(this.stateCenter.streamSeq=e.body.stream_seq,e.body.stream_cmd){case i.ENUM_STREAM_UPDATE_CMD.added:this.handleAddedStreamList(e.body.stream_info);break;case i.ENUM_STREAM_UPDATE_CMD.deleted:this.handleDeletedStreamList(e.body.stream_info);break;case i.ENUM_STREAM_UPDATE_CMD.updated:this.handleUpdatedStreamList(e.body.stream_info)}this.logger.info("zb.sh.hpsum call success")}else this.logger.info("zb.sh.hpsum, emtpy list")},e.prototype.handleAddedStreamList=function(e){this.logger.debug("zb.sh.hasl call");for(var t,r=[],n=0;n<e.length;n++)if(e[n].anchor_id_name!=this.stateCenter.idName){t=!1;for(var s=0;s<this.stateCenter.streamList.length;s++)if(e[n].stream_id===this.stateCenter.streamList[s].stream_id){t=!0;break}t||r.push(e[n])}else this.logger.debug("hdsl.0 have self stream added");if(0!==r.length){this.logger.debug("zb.sh.hasl callback addstream");for(var o=0;o<r.length;o++)this.stateCenter.streamList.push(r[o]);this.onStreamUpdated(i.ENUM_STREAM_UPDATE_TYPE.added,this.makeCallbackStreamList(r))}this.logger.info("zb.sh.hasl call success")},e.prototype.handleDeletedStreamList=function(e){this.logger.debug("zb.sh.hdsl call");for(var t=[],r=0;r<e.length;r++)if(e[r].anchor_id_name!=this.stateCenter.idName){for(var n=this.stateCenter.streamList.length-1;n>=0;n--)if(e[r].stream_id===this.stateCenter.streamList[n].stream_id){this.stateCenter.streamList.splice(n,1),t.push(e[r]);break}}else this.logger.debug("zb.sh.hdsl have self stream deleted");0!==t.length&&(this.logger.debug("zb.sh.hdsl callback delstream"),this.onStreamUpdated(i.ENUM_STREAM_UPDATE_TYPE.deleted,this.makeCallbackStreamList(t))),this.logger.info("zb.sh.hdsl call")},e.prototype.handleUpdatedStreamList=function(e){this.logger.debug("zb.sh.husl call");for(var t=[],r=0;r<e.length;r++)if(e[r].anchor_id_name!=this.stateCenter.idName){for(var i=0;i<this.stateCenter.streamList.length;i++)if(e[r].stream_id===this.stateCenter.streamList[i].stream_id){e[r].extra_info!==this.stateCenter.streamList[i].extra_info&&(this.stateCenter.streamList[i]=e[r],t.push(e[r]));break}}else this.logger.debug("hsul.0 have self stream updated");0!==t.length&&(this.logger.debug("zb.sh.husl callback updatestream"),this.onStreamExtraInfoUpdated(this.makeCallbackStreamList(t))),this.logger.info("zb.sh.husl call success")},e.prototype.fetchStreamList=function(){this.logger.info("zb.sh.fsl call"),this.stateCenter.isLogin()?this.logger.info("zb.sh.fsl state error"):this.stateCenter.streamQuerying?this.logger.info("zb.sh.fsl already doing"):(this.stateCenter.streamQuerying=!0,this.logger.debug("zb.sh.fsl send fetch request"),this.socketCenter.registerRouter("stream_info",this.handleFetchStreamListRsp),this.socketCenter.sendMessage("stream_info",{reserve:0}),this.logger.debug("zb.sh.fsl call success"))},e.prototype.makeCallbackStreamList=function(e){var t=[];if(e&&e.length>0)for(var r=0;r<e.length;r++){var i={anchor_id_name:e[r].anchor_id_name,stream_gid:e[r].stream_gid,anchor_nick_name:e[r].anchor_nick_name,extra_info:e[r].extra_info,stream_id:e[r].stream_id,urls_flv:"",urls_rtmp:"",urls_hls:"",urls_https_flv:"",urls_https_hls:""};this.setCDNInfo(i,e[r]),t.push(i)}return t},e.prototype.updateMixStream=function(e,t,r){var s=this;if(this.logger.info("zb.sh.ums call"),null==e.outputStreamId&&null==e.outputUrl)return this.logger.error("zb.sh.ums no mix stream info"),!1;if(0==e.streamList.length)return this.logger.error("zb.sh.ums no input stream"),!1;var o={id_name:this.stateCenter.idName,live_channel:this.stateCenter.roomid,appid:this.stateCenter.appid,version:i.PROTO_VERSION};"string"==typeof e.userData&&e.userData.length<=1e4&&(o.UserData=e.userData);for(var a=[],c=0;c<e.streamList.length;c++){var u=e.streamList[c],l=u.streamId;this.stateCenter.testEnvironment&&(l="zegotest-"+this.stateCenter.appid+"-"+u.streamId),a.push({stream_id:l,rect:{layer:c,top:u.top,left:u.left,bottom:u.bottom,right:u.right}})}o.MixInput=a;var h={};if(null!=e.outputStreamId?this.stateCenter.testEnvironment?h.stream_id="zegotest-"+this.stateCenter.appid+"-"+e.outputStreamId:h.stream_id=e.outputStreamId:null!=e.outputUrl&&(h.mixurl=e.outputUrl),!e.outputBitrate)return this.logger.error("zb.sh.ums no bitrate param"),!1;if(h.bitrate=e.outputBitrate,!e.outputFps)return this.logger.error("zb.sh.ums no fps param"),!1;if(h.fps=e.outputFps,!e.outputWidth)return this.logger.error("zb.sh.ums no width param"),!1;if(h.width=e.outputWidth,!e.outputHeight)return this.logger.error("zb.sh.ums no height param"),!1;if(h.height=e.outputHeight,e.outputAudioConfig&&(h.audio_enc_id=e.outputAudioConfig),e.outputAudioBitrate&&(h.audio_bitrate=e.outputAudioBitrate),e.outputAudioChannels&&(h.audio_channel_cnt=e.outputAudioChannels),e.outputBgColor){if("number"!=typeof e.outputBgColor)return this.logger.error("zb.sh.ums param outputBgColor error"),!1;o.output_bg_color=e.outputBgColor}if(e.outputBgImage){if("string"!=typeof e.outputBgImage||!e.outputBgImage.startsWith("preset-id://"))return this.logger.error("zb.sh.ums param outputBgImage error"),!1;o.output_bg_image=e.outputBgImage}this.stateCenter.testEnvironment?h.testenv=1:h.testenv=0,o.MixOutput=[h],e.extraParams&&(o.extra_params=e.extraParams);var d={channel:"zeus",cmd:"start_mix",req_body:JSON.stringify(o)};return this.logger.debug("zb.sh.ums send command"),this.socketCenter.sendMessage("biz_channel",d,(function(o,a,c){s.logger.debug("zb.sh.ums receive message");var u="zegotest-"+s.stateCenter.appid+"-";if(0!=c.length){for(var l=JSON.parse(c),h=[],d=e.outputStreamId,p=0;p<l.play.length;p++){var f={rtmpUrls:null,hlsUrls:null,flvUrls:null};s.stateCenter.testEnvironment&&d&&d.startsWith(u)&&(d=d.slice(u.length)),l.play[p].rtmp_url&&l.play[p].rtmp_url.length>0&&(f.rtmpUrls=[l.play[p].rtmp_url]),l.play[p].hls_url&&l.play[p].hls_url.length>0&&(f.hlsUrls=[l.play[p].hls_url]),l.play[p].hdl_url&&l.play[p].hdl_url.length>0&&(f.flvUrls=[l.play[p].hdl_url]),h.push(f)}t&&t(d,h)}else r&&r(n.ClientUtil.getServerError(i.MIXSTREAM_ERROR_CODE+1))}),(function(e,t,o){if("number"==typeof e){s.logger.debug("zb.sh.ums error: "+e);var a=[];if(1000000150==e&&0!=o.length)for(var c=JSON.parse(o),u="zegotest-"+s.stateCenter.appid+"-",l=0;l<c.non_exist_streams.length;l++){var h=c.non_exist_streams[l];s.stateCenter.testEnvironment&&h.startsWith(u)?a.push(h.slice(u.length)):a.push(h)}r&&r(n.ClientUtil.getServerError(i.MIXSTREAM_ERROR_CODE+e),a)}else s.logger.debug("zb.sh.ums error code "+e.code),r&&r(e)})),!0},e.prototype.publishTarget=function(e,t,r){var s=this;if(e.type&&-1!=["addpush","delpush","clearpush"].indexOf(e.type))if(this.logger.info("zb.sh.ptcall"),e.streamId&&"string"==typeof e.streamId)if(e.pushUrl&&"string"==typeof e.pushUrl)if(this.stateCenter.publishStreamList[e.streamId]){var o=Math.ceil((new Date).getTime()/1e3),a=e.streamId;this.stateCenter.testEnvironment&&(a="zegotest-"+this.stateCenter.appid+"-"+e.streamId);var c={appid:this.stateCenter.appid,biz_type:0,timestamp:o,seq:this.stateCenter.cdnSeq++,version:1*i.PROTO_VERSION,stream_id:a,pushurl:e.pushUrl},u={channel:"media",cmd:e.type,req_body:JSON.stringify(c)};this.logger.debug("zb.sh.pt send command"),this.socketCenter.sendMessage("biz_channel",u,(function(o,a,c){if(s.logger.debug("zb.sh.pt receive message"),0!=c.length){var u=JSON.parse(c),l=u.code,h=u.message;if(l&&0!=l)return s.logger.error("zb.sh.pt "+e.type+" error code: "+l+" "+h),void(r&&r({code:l,message:h}));s.logger.info("zb.sh.pt "+e.type+" success"),t&&t()}else r&&r(n.ClientUtil.getServerError(i.MIXSTREAM_ERROR_CODE+1))}),(function(e,t,i){s.logger.debug("zb.sh.pt error: "+r);var n="";2001==e?n="invalid channel":2002==e&&(n="bizchannel error"),r&&r({code:e,message:n})}))}else this.logger.error("zb.sh.pt publish stream no found");else this.logger.error("zb.sh.pt pushurl error");else this.logger.error("zb.sh.pt streamid error");else this.logger.error("zb.sh.pt cdn push type error")},e.prototype.stopMixStream=function(e,t,r){if(this.logger.info("zb.sh.sms call"),null==e.outputStreamId&&null==e.outputUrl)return this.logger.error("zb.sh.sms no mix stream info"),!1;var s={id_name:this.stateCenter.idName,live_channel:this.stateCenter.roomid,appid:this.stateCenter.appid,version:i.PROTO_VERSION};null!=e.outputStreamId?this.stateCenter.testEnvironment?s.stream_id="zegotest-"+this.stateCenter.appid+"-"+e.outputStreamId:s.stream_id=e.outputStreamId:null!=e.outputUrl&&(s.mixurl=e.outputUrl);var o={channel:"zeus",cmd:"stop_mix",req_body:JSON.stringify(s)};return this.socketCenter.sendMessage("biz_channel",o,(function(e,r){t&&t()}),(function(e,t){"number"==typeof e?r&&r(n.ClientUtil.getServerError(i.MIXSTREAM_ERROR_CODE+e)):r&&r(e)})),!0},e.prototype.updateStreamExtraInfo=function(e,t){return this.logger.info("zb.sh.usei call"),e?"string"==typeof t&&(this.stateCenter.publishStreamList[e]&&(this.stateCenter.publishStreamList[e].extra_info=t,this.stateCenter.publishStreamList[e].state>=i.ENUM_PUBLISH_STREAM_STATE.update_info&&this.updateStreamInfo(e,i.ENUM_STREAM_SUB_CMD.liveUpdate,t)),!0):(this.logger.error("zb.sh.usei param error"),!1)},e}();t.StreamHandler=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=r(1),s=function(){function e(e,t,r){this.logger=e,this.socketCenter=r,this.stateCenter=t}return e.prototype.resetHeartbeat=function(){this.logger.debug("zb.hb.rht call"),clearTimeout(this.stateCenter.heartbeatTimer),this.stateCenter.heartbeatTimer=null,this.stateCenter.tryHeartbeatCount=0,this.logger.debug("zb.hb.rht call success")},e.prototype.hbLogout=function(e){},e.prototype.start=function(e){var t=this;if(this.logger.debug("zb.hb.sht call"),this.stateCenter.isLogin()){if(++this.stateCenter.tryHeartbeatCount>3)return this.logger.error("zb.hb.sht come to try limit"),void this.hbLogout(i.sdkErrorList.HEARTBEAT_TIMEOUT);this.logger.debug("zb.hb.sht send packet"),this.socketCenter.registerRouter("hb",(function(e){t.handleHeartbeatRsp(e)})),this.socketCenter.sendMessage("hb",{reserve:0}),this.logger.debug("zb.hb.sht call success"),this.stateCenter.heartbeatInterval=e,this.stateCenter.heartbeatTimer=setTimeout((function(){t.start(t.stateCenter.heartbeatInterval)}),this.stateCenter.heartbeatInterval)}else this.logger.error("zb.hb.sht state error")},e.prototype.handleHeartbeatRsp=function(e){if(this.logger.debug("zb.hb.hhbr call"),0!==e.body.err_code)return this.logger.error("zb.hb.hhbr call disconnect, server error=",e.body.err_code),void this.hbLogout(n.ClientUtil.getServerError(e.body.err_code));for(var t in this.stateCenter.tryHeartbeatCount=0,this.stateCenter.heartbeatInterval=e.body.hearbeat_interval,this.stateCenter.heartbeatInterval<i.MINIUM_HEARTBEAT_INTERVAL&&(this.stateCenter.heartbeatInterval=i.MINIUM_HEARTBEAT_INTERVAL),e.body.bigim_time_window&&"number"==typeof e.body.bigim_time_window&&(this.stateCenter.bigimTimeWindow=e.body.bigim_time_window),e.body.dati_time_window&&"number"==typeof e.body.dati_time_window&&(this.stateCenter.datiTimeWindow=e.body.dati_time_window),this.ReliableMessageHandler(e),this.fetchStreamList(e),e.body.server_user_seq!==this.stateCenter.userSeq&&this.stateCenter.userStateUpdate&&(this.logger.info("zb.hb.hhbr call update user "+e.body.server_user_seq,this.stateCenter.userSeq),this.fetchUserList()),this.stateCenter.publishStreamList)this.stateCenter.publishStreamList[t].state==i.ENUM_PUBLISH_STREAM_STATE.update_info&&(this.logger.info("zb.hb.hhbr try to update stream info"),this.updateStreamInfo(t,i.ENUM_STREAM_SUB_CMD.liveBegin,this.stateCenter.publishStreamList[t].extra_info));null!=e.body.online_count&&0!=e.body.online_count&&this.onUpdateOnlineCount(this.stateCenter.roomid,e.body.online_count),this.logger.debug("zb.hb.hhbr call success")},e.prototype.ReliableMessageHandler=function(e){var t=this;if(e.body.trans_seqs)for(var r=0;r<e.body.trans_seqs.length;r++){var i=e.body.trans_seqs[r].trans_channel,n=e.body.trans_seqs[r].trans_seq_array;(n=n.filter((function(e){var r=e.trans_type,i=e.trans_seq;return!t.stateCenter.transSeqMap[r]||t.stateCenter.transSeqMap[r].seq!==i}))).length>0&&this.fetchReliableMessage(i,n)}},e.prototype.fetchReliableMessage=function(e,t){var r=this;this.logger.debug("zb.hb.frm call");var i={trans_channel:e,fetch_array:t};this.socketCenter.registerRouter("trans_fetch",(function(e){r.handleFetchTransRsp(e)})),this.socketCenter.sendMessage("trans_fetch",i),this.logger.debug("zb.hb.frm call success")},e.prototype.handleFetchTransRsp=function(e){var t=this;this.stateCenter.isLogin()?0==e.body.err_code?e.body.trans_fetch_results.forEach((function(r){var i=r.trans_type,n=r.trans_seq;t.stateCenter.transSeqMap[i]={seq:n},e.body.trans_user_idname!=t.stateCenter.idName&&t.onRecvReliableMessage(i,n,r.trans_data),t.logger.debug("zb.hb.hftr trans "+i+" seq "+n)})):this.logger.error("zb.hb.hftr trans send error "+e.body.err_code):this.logger.error("zb.hb.hftr not login")},e.prototype.fetchStreamList=function(e){var t=this;e.body.stream_seq!==this.stateCenter.streamSeq&&(this.logger.debug("zb.hb.fsl current seq "+this.stateCenter.streamSeq+" server Seq "+e.body.stream_seq),this.logger.debug("zb.hb.fsl call"),this.stateCenter.isLogin()?this.stateCenter.streamQuerying?this.logger.warn("zb.hb.fsl already doing"):(this.stateCenter.streamQuerying=!0,this.logger.debug("zb.hb.fsl send fetch request"),this.socketCenter.registerRouter("stream_info",(function(e){t.handleFetchStreamListRsp(e)})),this.socketCenter.sendMessage("stream_info",{reserve:0}),this.logger.debug("zb.hb.fsl call success")):this.logger.error("zb.hb.fsl state error"))},e.prototype.handleFetchStreamListRsp=function(e){},e.prototype.fetchUserList=function(){},e.prototype.updateStreamInfo=function(e,t,r,i){void 0===r&&(r="")},e.prototype.onUpdateOnlineCount=function(e,t){},e.prototype.onRecvReliableMessage=function(e,t,r){},e.prototype.resetCheckMessage=function(){this.logger.debug("zb.hb.rcm call"),clearTimeout(this.stateCenter.sendDataCheckTimer),this.stateCenter.sendDataCheckTimer=null,this.checkSendMessageList(this.stateCenter.sendDataList),this.checkSendMessageList(this.stateCenter.sendCommandList),this.stateCenter.sendDataMap={},this.stateCenter.sendCommandMap={},this.logger.debug("zb.hb.rcm call success")},e.prototype.checkSendMessageList=function(e){for(var t=e.getFirst();null!=t;)e.remove(t),t._data.error&&(t._data.data.body.custom_msg?t._data.error(i.sdkErrorList.SEND_MSG_TIMEOUT,t._data.data.header.seq,t._data.data.body.custom_msg):t._data.error(i.sdkErrorList.SEND_MSG_TIMEOUT,t._data.data.header.seq)),t=e.getFirst()},e.prototype.checkMessageListTimeout=function(e,t){for(var r=e.getFirst(),n=Date.parse(new Date+""),s=0,o=0,a=0;!(null==r||r._data.time+this.stateCenter.sendDataTimeout>n||(delete t[r._data.data.header.seq],e.remove(r),++o,null==r._data.error||this.stateCenter.sendDataDropTimeout>0&&r._data.time+this.stateCenter.sendDataDropTimeout<n?++a:r._data.data.body.custom_msg?r._data.error(i.sdkErrorList.SEND_MSG_TIMEOUT,r._data.data.header.seq,r._data.data.body.custom_msg):r._data.error(i.sdkErrorList.SEND_MSG_TIMEOUT,r._data.data.header.seq),++s>=this.stateCenter.sendDataCheckOnceCount));)r=e.getFirst();0==o&&0==a||this.logger.debug("zb.hb.cmt call success, stat: timeout=",o,"drop=",a)},e.prototype.startCheckMessageTimeout=function(){var e=this;this.stateCenter.isLogin()?(this.checkMessageListTimeout(this.stateCenter.sendDataList,this.stateCenter.sendDataMap),this.checkMessageListTimeout(this.stateCenter.sendCommandList,this.stateCenter.sendCommandMap),this.stateCenter.sendDataCheckTimer=setTimeout((function(){e.startCheckMessageTimeout()}),this.stateCenter.sendDataCheckInterval)):this.logger.error("zb.hb.scmt state error")},e}();t.HeartBeatHandler=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=r(1),s=function(){function e(e,t,r){this.logger=e,this.socketCenter=r,this.stateCenter=t}return e.prototype.sendCustomCommand=function(e,t,r,i){var s=this;if(this.logger.debug("zb.mh.scc call"),!this.stateCenter.isLogin())return this.logger.error("zb.mh.scc state error"),!1;if(!e)return this.logger.error("zb.mh.scc dstMembers error"),!1;var o={from_userid:this.stateCenter.idName,from_username:this.stateCenter.nickName,request_id:this.stateCenter.getRequestId(),custom_content:t||"",room_id:this.stateCenter.roomid},a={dest_id_name:e,custom_msg:JSON.stringify(o)};return n.ClientUtil.checkCustomCommandParam(a)?(this.socketCenter.registerRouter("custommsg",(function(e){s.handleSendCustomMsgRsp(e)})),this.socketCenter.sendCustomMessage("custommsg",a,r,i),this.logger.info("zb.mh.scc call success"),!0):(this.logger.info("zb.mh.scc param error"),!1)},e.prototype.handleSendCustomMsgRsp=function(e){this.logger.debug("zb.mh.hscmrcall");var t,r=this.stateCenter.sendDataMap[e.header.seq];null!=r?("custommsg"!=(t=r._data).data.header.cmd?this.logger.error("zb.mh.hscmrcmd wrong"+t.data.header.cmd):0===e.body.err_code?null!=t.success&&t.success(e.header.seq,t.data.body.custom_msg):null!=t.error&&t.error(n.ClientUtil.getServerError(e.body.err_code),e.header.seq,t.data.body.custom_msg),delete this.stateCenter.sendDataMap[e.header.seq],this.stateCenter.sendDataList.remove(r)):this.logger.error("zb.mh.hscmrno found seq="+e.header.seq),this.logger.debug("zb.mh.hscmr  call success")},e.prototype.handlePushCustomMsg=function(e){var t=JSON.parse(e.body.custommsg);this.logger.debug("zb.mh.hpcm submsg=",t),this.onRecvCustomCommand(t.from_userid,t.from_username,t.custom_content)},e.prototype.onRecvCustomCommand=function(e,t,r){},e.prototype.sendRoomMsg=function(e,t,r,n,s){var o=this;if(this.logger.debug("zb.mh.srm call"),this.stateCenter.isLogin()){var a=Date.parse(new Date+"");if(this.stateCenter.sendRoomMsgTime>0&&this.stateCenter.sendRoomMsgTime+this.stateCenter.SendRoomMsgInterval>a)return this.logger.info("zb.mh.srm freq error"),void(s&&s(i.sdkErrorList.FREQ_LIMITED,0,e,t,r));this.stateCenter.sendRoomMsgTime=a,this.logger.debug("zb.mh.srm send fetch request");var c={msg_category:e,msg_type:t,msg_content:r};this.socketCenter.registerRouter("im_chat",(function(e){o.handleSendRoomMsgRsp(e)})),this.socketCenter.sendCustomMessage("im_chat",c,n,s),this.logger.info("zb.mh.srm call success")}else this.logger.error("zb.mh.srm state error")},e.prototype.handleSendRoomMsgRsp=function(e){this.logger.debug("zb.mh.hsrmr call");var t,r=this.stateCenter.sendDataMap[e.header.seq];null!=r?("im_chat"!=(t=r._data).data.header.cmd?this.logger.error("zb.mh.hsrmr cmd wrong"+t.data.header.cmd):0===e.body.err_code?t.success&&t.success(e.header.seq,e.body.msg_id,t.data.body.msg_category,t.data.body.msg_type,t.data.body.msg_content):t.error&&t.error(n.ClientUtil.getServerError(e.body.err_code),e.header.seq,t.data.body.msg_category,t.data.body.msg_type,t.data.body.msg_content),delete this.stateCenter.sendDataMap[e.header.seq],this.stateCenter.sendDataList.remove(r)):this.logger.error("hzb.mh.hsrmr no found seq="+e.header.seq),this.logger.info("zb.mh.hsrmr call success")},e.prototype.onRecvRoomMsg=function(e,t,r){},e.prototype.sendReliableMessage=function(e,t,r,i){this.logger.debug("zb.mh.srirm call"),this.stateCenter.transSeqMap[e]||(this.stateCenter.transSeqMap[e]={seq:0});var n={trans_type:e,trans_data:t,trans_local_seq:this.stateCenter.transSeqMap[e].seq,trans_channel:"clt"};this.socketCenter.sendMessage("trans",n,r,i)},e.prototype.sendBigRoomMessage=function(e,t,r,i,n){var s=this;this.logger.debug("zb.mh.sbim call");var o=this.stateCenter.bigimTimeWindow,a=this.stateCenter.serverTimeOffset,c=(new Date).getTime()+a,u=(++this.stateCenter.cmdSeq).toString();if(null==i&&(i=null),null==n&&(n=null),this.stateCenter.bigImCallbackMap[u]={success:i,error:n},0==o){var l={msg_category:e,msg_type:t,msg_content:r,bigmsg_client_id:u};this.logger.debug("zb.mh.sbim no time window"),this.sendBigRoomMessageInternal([l],(function(e){s.handleBigImMsgRsp(e)}),n)}else{var h=Math.floor(c/o);if(this.logger.debug("currentIndex "+h+" lastTimeIndex "+this.stateCenter.bigImLastTimeIndex),this.stateCenter.bigImLastTimeIndex<h&&0==this.stateCenter.bigImMessageList.length){this.stateCenter.bigImLastTimeIndex=h;var d={msg_category:e,msg_type:t,msg_content:r,bigmsg_client_id:u};this.sendBigRoomMessageInternal([d],(function(e){s.handleBigImMsgRsp(e)}),n)}else this.stateCenter.bigImMessageList.push({msg_category:e,msg_type:t,msg_content:r,bigmsg_client_id:u}),1==this.stateCenter.bigImMessageList.length&&this.setBigImTimer(a,o)}},e.prototype.handlePushMergeMsg=function(e){if(this.stateCenter.isLogin()){for(var t=0;t<e.body.messages.length;t++)14001===e.body.messages[t].sub_cmd&&this.handlePushBigRooMsg(e.body.messages[t].msg_body);this.logger.debug("zb.mh.hpmm call success")}else this.logger.error("zb.mh.hpmmnot login")},e.prototype.handlePushBigRooMsg=function(e){var t;try{t=JSON.parse(e)}catch(e){return void this.logger.warn("zb.mh.hpbrm parse json error")}if(t){for(var r=t.room_id,i=[],n=0;n<t.msg_data.length;n++){var s=t.msg_data[n];s.id_name!=this.stateCenter.idName?i.push({idName:s.id_name,nickName:s.nick_name,messageId:s.bigmsg_id,category:s.msg_category,type:s.msg_type,content:s.msg_content,time:s.send_time}):this.logger.debug("zb.mh.hpbrm self message")}0==i.length?this.logger.debug("zb.mh.hpbrm no other pushData except self"):this.onRecvBigRoomMessage(i,r),this.logger.debug("zb.mh.hpbrm call success")}else this.logger.warn("zb.mh.hpbrm cann't find message body")},e.prototype.onRecvBigRoomMessage=function(e,t){},e.prototype.sendBigRoomMessageInternal=function(e,t,r){this.logger.debug("zb.mh.sbim call");var i={msgs:e};this.socketCenter.sendMessage("bigim_chat",i,t,r)},e.prototype.handleBigImMsgRsp=function(e){if(this.stateCenter.isLogin()){this.stateCenter.bigimTimeWindow!=e.body.bigim_time_window&&(this.stateCenter.bigimTimeWindow=e.body.bigim_time_window);for(var t=0;t<e.body.msgs.length;t++){var r=e.body.msgs[t].bigmsg_client_id,i=e.body.msgs[t].bigmsg_id;if(this.stateCenter.bigImCallbackMap[r]){var n=this.stateCenter.bigImCallbackMap[r].success;null!=n&&n(e.header.seq,i),delete this.stateCenter.bigImCallbackMap[r]}}}else this.logger.info("zb.mh.hbmr not login")},e.prototype.setBigImTimer=function(e,t){var r=this,i=t-((new Date).getTime()+e)%t,s=n.ClientUtil.generateRandumNumber(t)+i;this.logger.info("zb.mh.sbt setTimer "+s),this.stateCenter.bigImTimer=setTimeout((function(){r.onBigImTimer()}),s)},e.prototype.onBigImTimer=function(){var e=this,t=(new Date).getTime()+this.stateCenter.serverTimeOffset;this.stateCenter.bigImLastTimeIndex=Math.floor(t/this.stateCenter.bigimTimeWindow);for(var r=[],i=[],n=0;n<this.stateCenter.bigImMessageList.length&&!(n>=20);n++){var s=this.stateCenter.bigImMessageList[n];r.push({msg_category:s.msg_category,msg_type:s.msg_type,msg_content:s.msg_content,bigmsg_client_id:s.bigmsg_client_id}),i.push(s.bigmsg_client_id)}this.stateCenter.bigImMessageList.length>20?this.stateCenter.bigImMessageList.splice(0,20):this.stateCenter.bigImMessageList=[],this.sendBigRoomMessageInternal(r,(function(t){e.handleBigImMsgRsp(t)}),(function(t,r){for(var n=0;n<i.length;n++){var s=i[n],o=e.stateCenter.bigImCallbackMap[s];o&&(null!=o.error&&o.error(t,r),delete e.stateCenter.bigImCallbackMap[s])}})),clearTimeout(this.stateCenter.bigImTimer),this.stateCenter.bigImTimer=null,this.stateCenter.bigImMessageList.length>0&&this.setBigImTimer(this.stateCenter.serverTimeOffset,this.stateCenter.bigimTimeWindow)},e.prototype.sendRelayMessage=function(e,t,r,i){this.logger.debug("zb.mh.srm call");var n=this.stateCenter.datiTimeWindow,s=this.stateCenter.serverTimeOffset;n>0?(this.stateCenter.realyMessageList.push({type:e,data:t,success:r,error:i}),1==this.stateCenter.realyMessageList.length&&this.setRelayTimer(s,n)):this.sendRelayMessageInternal(e,t,r,i)},e.prototype.sendRelayMessageInternal=function(e,t,r,i){this.logger.debug("zb.mh.srmi call");var n={relay_type:e,relay_data:t};this.socketCenter.sendMessage("relay",n,r,i)},e.prototype.setRelayTimer=function(e,t){var r=this,i=2*t-((new Date).getTime()+e)%t,s=n.ClientUtil.generateRandumNumber(i);this.logger.info("zb.mh.srt setTimer "+s),this.stateCenter.relayTimer=setTimeout((function(){r.onRelayTimer()}),s)},e.prototype.onRelayTimer=function(){if(0!=this.stateCenter.realyMessageList.length){var e=this.stateCenter.realyMessageList[0];this.sendRelayMessageInternal(e.type,e.data,e.success,e.error),clearTimeout(this.stateCenter.relayTimer),this.stateCenter.relayTimer=null,this.stateCenter.realyMessageList.splice(0,1),this.stateCenter.realyMessageList.length>0&&this.setRelayTimer(this.stateCenter.serverTimeOffset,this.stateCenter.datiTimeWindow)}else this.logger.info("zb.mh.ort no relay data")},e.prototype.handlePushTransMsg=function(e){if(this.stateCenter.isLogin()){var t=e.body.trans_type,r=e.body.trans_seq;this.stateCenter.transSeqMap[t]?this.stateCenter.transSeqMap[t].seq=r:this.stateCenter.transSeqMap[t]={seq:r},e.body.trans_user_idname!=this.stateCenter.idName?this.onRecvReliableMessage(t,r,e.body.trans_data):this.logger.debug("zb.mh.hptr receive self trans message"),this.logger.info("zb.mh.hptr trans "+t+" seq "+r)}else this.logger.error("zb.mh.hptr not login")},e.prototype.onRecvReliableMessage=function(e,t,r){},e}();t.MessageHandler=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=function(){function e(e,t,r){this.logger=e,this.socketCenter=r,this.stateCenter=t}return e.prototype.requestJoinLive=function(e,t,r,n){this.logger.debug("zb.lh.rjl call");var s=this.stateCenter.getRequestId(),o=this.stateCenter.getSignalCmdContent(s,e);return null!=n&&(this.stateCenter.joinLiveCallbackMap[s]=n,this.sendSignalCmd(i.ENUM_SIGNAL_SUB_CMD.joinLiveRequest,o,e,t,r),!0)},e.prototype.inviteJoinLive=function(e,t,r,n){this.logger.debug("zb.lh.ijl call");var s=this.stateCenter.getRequestId(),o=this.stateCenter.getSignalCmdContent(s,e);return null!=n&&(this.stateCenter.joinLiveCallbackMap[s]=n,this.sendSignalCmd(i.ENUM_SIGNAL_SUB_CMD.joinLiveInvite,o,e,t,r),!0)},e.prototype.endJoinLive=function(e,t,r){this.logger.debug("zb.lh.ejl call");var n=this.stateCenter.getRequestId(),s=this.stateCenter.getSignalCmdContent(n,e);return this.sendSignalCmd(i.ENUM_SIGNAL_SUB_CMD.joinLiveStop,s,e,t,r),!0},e.prototype.respondJoinLive=function(e,t,r,n){this.logger.debug("zb.lh.rpjl call");var s=this.stateCenter.joinLiveRequestMap[e];if(!s)return this.logger.info("zb.lh.rpjl no dest id name"),!1;var o=0;!0===t&&(o=1);var a=this.stateCenter.getSignalCmdContent(e,s,o);return this.sendSignalCmd(i.ENUM_SIGNAL_SUB_CMD.joinLiveResult,a,s,r,n),delete this.stateCenter.joinLiveRequestMap[e],!0},e.prototype.sendSignalCmd=function(e,t,r,i,n){if(this.logger.debug("zb.lh.ssc call"),this.stateCenter.isLogin()){this.logger.debug("zb.lh.ssc send signal cmd "+e);var s={sub_cmd:e,signal_msg:t,dest_id_name:[r]};this.socketCenter.sendMessage("signal",s,i,n),this.logger.info("zb.lh.ssc call success")}else this.logger.error("zb.lh.ssc state error")},e.prototype.handlePushSignalMsg=function(e){if(this.stateCenter.isLogin()){var t=JSON.parse(e.body.signal_msg);switch(this.logger.debug("zb.lh.hpcm hpsm= ",t),e.body.sub_cmd){case i.ENUM_PUSH_SIGNAL_SUB_CMD.pushJoinLiveRequest:this.handlePushJoinLiveRequestMsg(t);break;case i.ENUM_PUSH_SIGNAL_SUB_CMD.pushJoinLiveResult:this.handlePushJoinLiveResultMsg(t);break;case i.ENUM_PUSH_SIGNAL_SUB_CMD.pushJoinLiveInvite:this.handlePushJoinLiveInviteMsg(t);break;case i.ENUM_PUSH_SIGNAL_SUB_CMD.pushJoinLiveStop:this.handlePushJoinLiveStopMsg(t)}this.logger.debug("zb.lh.hpsm call end")}else this.logger.warn("zb.lh.hpsm not login")},e.prototype.handlePushJoinLiveRequestMsg=function(e){var t=e.request_id;if("string"==typeof t){var r=e.from_userid;"string"==typeof r?(this.stateCenter.joinLiveRequestMap[t]=r,this.logger.info("zb.lh.hpjlrm onRecvJoinLiveRequest "+r),this.onRecvJoinLiveRequest(t,e.from_userid,e.from_username,e.room_id)):this.logger.error("zb.lh.hpjlrm no from user")}else this.logger.error("zb.lh.hpjlrm no requestId")},e.prototype.onRecvJoinLiveRequest=function(e,t,r,i){},e.prototype.handlePushJoinLiveInviteMsg=function(e){var t=e.request_id;if("string"==typeof t){var r=e.from_userid;"string"==typeof r?(this.stateCenter.joinLiveRequestMap[t]=r,this.logger.info("zb.lh.hpjlim onRecvInviteJoinLiveRequest "+r),this.onRecvInviteJoinLiveRequest(t,e.from_userid,e.from_username,e.room_id)):this.logger.error("zb.lh.hpjlim no from user")}else this.logger.error("zb.lh.hpjlim no requestId")},e.prototype.onRecvInviteJoinLiveRequest=function(e,t,r,i){},e.prototype.handlePushJoinLiveResultMsg=function(e){var t=e.request_id;if("string"==typeof t){var r=e.result;if(null!=r){var i=1==r;if(this.stateCenter.joinLiveCallbackMap[t]){var n=this.stateCenter.joinLiveCallbackMap[t];if(!n)return void this.logger.info("hpjlrm.o no callback");this.logger.info("zb.lh.hpjlrm joinLiveRequest/invite result "+i),delete this.stateCenter.joinLiveCallbackMap[t],n(i,e.from_userid,e.from_username)}}else this.logger.info("zb.lh.hpjlrm no result")}else this.logger.error("zb.lh.hpjlrm no requestId")},e.prototype.handlePushJoinLiveStopMsg=function(e){var t=e.request_id;"string"==typeof t?(this.logger.info("zb.lh.hpjlsm onRecvEndJoinLiveCommand "+e.from_userid),this.onRecvEndJoinLiveCommand(t,e.from_userid,e.from_username,e.room_id)):this.logger.error("zb.lh.hpjlsm no requestId")},e.prototype.onRecvEndJoinLiveCommand=function(e,t,r,i){},e}();t.LiveHandler=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),n=r(2),s=function(){function e(){this.testEnvironment=!1,this.third_token="",this.pullLimited=!0,this.configOK=!1,this.roomCreateFlag=1,this.runState=i.ENUM_RUN_STATE.logout,this.lastRunState=i.ENUM_RUN_STATE.logout,this.callbackList={},this.streamList=[],this.publishStreamList={},this.userQuerying=!1,this.userTempList=[],this.userSeq=0,this.anchor_info={anchor_id:"",anchor_id_name:"",anchor_nick_name:""},this.sendCommandMap={},this.sendCommandList=new i.LinkedList,this.sendDataMap={},this.sendDataList=new i.LinkedList,this.joinLiveCallbackMap={},this.joinLiveRequestMap={},this.streamUrlMap={},this.cmdCallback={},this.transSeqMap={},this.realyMessageList=[],this.relayTimer=null,this.bigImLastTimeIndex=0,this.bigIMmessageList=[],this.bigImCallbackMap={},this.bigImTimer=null,this.serverTimeOffset=0,this.datiTimeWindow=0,this.bigimTimeWindow=0,this.bigImMessageList=[],this.tryLoginCount=0,this.tryLoginTimer=null,this.heartbeatTimer=null,this.sendDataCheckTimer=null,this.sendDataCheckInterval=2e3,this.sendDataTimeout=5e3,this.sendDataDropTimeout=1e4,this.sendDataCheckOnceCount=100,this.sendRoomMsgTime=0,this.SendRoomMsgInterval=500,this.cmdSeq=0,this.audioEffectBuffer={},this.audioBitRate=48e3,this.cdnSeq=0}return e.prototype.isLogin=function(){return this.runState===i.ENUM_RUN_STATE.login},e.prototype.getRequestId=function(){return this.idName+"-"+n.getSeq()},e.prototype.getSignalCmdContent=function(e,t,r){var i={request_id:e,room_id:this.roomid,from_userid:this.idName,from_username:this.nickName,to_userid:t};return null!=r&&(i.result=r),JSON.stringify(i)},e}();t.StateCenter=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(1),n=function(){function e(e){var t=e.type,r=e.channels,i=void 0===r?1:r,n=e.bufferSize,s=void 0===n?0:n,o=e.sampleBit,a=void 0===o?16:o,c=e.sampleRate,u=void 0===c?44100:c,l=this;this.instant=0,this.slow=0,this.clip=0;var h=new("undefined"!=typeof webkitAudioContext?webkitAudioContext:AudioContext);this.context=h,this.type=t,this.channels=i,this.bufferSize=s,this.sampleBit=a,this.sampleRate=u,this.script=h.createScriptProcessor(s,i,i),(new Date).getTime(),this.script.addEventListener("audioprocess",(function(e){var r,i=e.inputBuffer.getChannelData(0),n=0,s=0;for(r=0;r<i.length;++r)n+=i[r]*i[r],Math.abs(i[r])>.99&&(s+=1);if(l.instant=Math.sqrt(n/i.length),l.slow=.95*l.slow+.05*l.instant,l.clip=s/i.length,"pcm"===t||"wav"===t){for(var o=[],a=0;a<l.channels;a++)o.push(e.inputBuffer.getChannelData(a));l.recorderBuffer(o)}})),"pcm"!==t&&"wav"!==t||this.initRecorderBuffer(t)}return e.prototype.connectToSource=function(e,t){console.log("SoundMeter connecting");try{this.mic=this.context.createMediaStreamSource(e),this.mic.connect(this.script),this.script.connect(this.context.destination),void 0!==t&&t(null)}catch(e){console.error(e),void 0!==t&&t(e)}return this},e.prototype.recorderBuffer=function(e){this.worker.postMessage({command:"record",val:e})},e.prototype.initRecorderBuffer=function(e){var t=this;this.worker=i.ClientUtil.inlineWorker((function(){var e,t,r,i,n,s,o=[],a=this;function c(t){var r,i;if(1==e)r=l(o[0],n,o),1!=t&&(i=u(t,r));else if(2==e){var s=l(o[0],n,o),a=l(o[1],n,o);1!=t?i=h(u(t,s),u(t,a)):r=h(s,a)}return 1!=t?i:r}function u(e,t){for(var r=new Float32Array(t.length/e),i=0,n=0;i<r.length;)r[i]=t[n],n+=e,i++;return r}function l(e,t,r){for(var i=new Float32Array(t*e.length),n=0,s=0;s<r[0].length;s++)i.set(r[0][s],n),n+=r[0][s].length;return i}function h(e,t){for(var r=new Float32Array(e.length+t.length),i=0;i<e.length+t.length;i+=2)r[i]=e[i/2>>0],r[i+1]=t[i/2>>0];return r}function d(e,t,r){for(var i=0;i<r.length;i++)e.setUint8(t+i,r.charCodeAt(i))}function p(e,t,r){for(var i=0;i<r.length;i++,t+=2){var n=Math.max(-1,Math.min(1,r[i]));e.setInt16(t,n<0?32768*n:32767*n,!0)}}function f(e,t,r){for(var i=0;i<r.length;i++,t++){var n=Math.max(-1,Math.min(1,r[i])),s=n<0?128*n:127*n;s+=128,e.setInt8(t,s)}}this.onmessage=function(u){switch(u.data.command){case"init":l=u.data.val,e=l.sampleChannel,t=l.sampleBit,r=l.sampleRate,i=l.oldSampleRate,n=l.bufferSize,s=l.type;break;case"record":!function(n){for(var u=0;u<e;u++)o[u]||(o[u]=[]),o[u].push(n[u]);var l=Math.round(i/r);"pcm"===s?function(e){var r=function(e,r){var i;8==r?i=e.length:16==r&&(i=e.length,i*=2);var n=new ArrayBuffer(i),s=new DataView(n);return 8==r?f(s,0,e):16==t&&p(s,0,e),s}(c(e),t);a.postMessage({command:"exportPcmLive",val:r})}(l):"wav"===s&&function(i){var n=function(i,n){var s;8==n?s=i.length:16==t&&(s=i.length,s*=2);var o=new ArrayBuffer(s+44),a=new DataView(o),c=r,u=t,l=e;return d(a,0,"RIFF"),a.setUint32(4,36+s,!0),d(a,8,"WAVE"),d(a,12,"fmt "),a.setUint32(16,16,!0),a.setUint16(20,1,!0),a.setUint16(22,l,!0),a.setUint32(24,c,!0),a.setUint32(28,c*l*(u/8),!0),a.setUint16(32,l*(u/8),!0),a.setUint16(34,u,!0),d(a,36,"data"),a.setUint32(40,s,!0),8==t?f(a,44,i):16==t&&p(a,44,i),a}(c(i),t);a.postMessage({command:"exportWav",val:n})}(l),o=[]}(u.data.val)}var l}})),this.worker.postMessage({command:"init",val:{sampleChannel:this.channels,sampleBit:this.sampleBit,sampleRate:this.sampleRate,oldSampleRate:this.context.sampleRate,bufferSize:this.bufferSize,type:e}}),this.worker.onmessage=function(e){switch(e.data.command){case"exportPcmLive":t.onReceiveBuffer(e.data.val);break;case"exportWav":t.onReceiveWav(e.data.val)}}},e.prototype.onReceiveBuffer=function(e){},e.prototype.onReceiveWav=function(e){},e.prototype.writeString=function(e,t,r){for(var i=0;i<r.length;i++)e.setUint8(t+i,r.charCodeAt(i))},e.prototype.writeBuffer=function(e,t,r){for(var i=0;i<r.byteLength;i++)e.setUint8(t+i,r[i])},e.prototype.concatenation=function(e){for(var t=0,r=0;r<e.length;++r)t+=e[r].buffer.byteLength;var i=new Uint8Array(t),n=0;for(r=0;r<e.length;++r)i.set(new Uint8Array(e[r].buffer),n),n+=e[r].buffer.byteLength;return i},e.prototype.encodeWave=function(e){var t=this.concatenation(e),r=t.byteLength,i=new ArrayBuffer(r+44),n=new DataView(i),s=this.sampleRate,o=this.sampleBit,a=this.channels;return this.writeString(n,0,"RIFF"),n.setUint32(4,36+r,!0),this.writeString(n,8,"WAVE"),this.writeString(n,12,"fmt "),n.setUint32(16,16,!0),n.setUint16(20,1,!0),n.setUint16(22,a,!0),n.setUint32(24,s,!0),n.setUint32(28,s*a*(o/8),!0),n.setUint16(32,a*(o/8),!0),n.setUint16(34,o,!0),this.writeString(n,36,"data"),n.setUint32(40,r,!0),this.writeBuffer(n,44,t),n},e.prototype.stop=function(){this.mic.disconnect(),this.script.disconnect()},e}();t.MediaUtil=n}])},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={appid:364280480,remoteLogLevel:0,logLevel:0,server:"wss://webliveroom364280480-api.zego.im/ws",logUrl:"wss://weblogger364280480-api.zego.im/log",audienceCreateRoom:!1,testEnvironment:!1}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={appid:364280480,remoteLogLevel:0,logLevel:0,server:"wss://webliveroom-test.zego.im/ws",logUrl:"wss://weblogger-test.zego.im/log",audienceCreateRoom:!1,testEnvironment:!0}},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__assign||function(){return(s=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},o=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},a=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}},c=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var u=c(r(7)),l=c(r(15)),h=c(r(32)),d=c(r(0)),p=c(r(2)),f=function(e){function t(t){var r=e.call(this)||this;return r._roomId="",r._userId="",r._logLevel=1,r._userName="",r._role=0,r._emit=r.emit.bind(r),r._signal=null,r._media=null,r._logger=null,r._mediaContraints=null,r._mediaList={},r._bitRate={start:p.default.bitRate.start,min:p.default.bitRate.min,max:p.default.bitRate.max},r._octopusConfig=null,r._env="prod",r.onPublishStateUpdate=null,r.onPlayStateUpdate=null,r.onStreamUpdated=null,r.onDisconnect=null,r.onKickOut=null,r._userId=t.userId,void 0!==t&&(void 0!==t.userName&&(r._userName=t.userName),void 0!==t.logLevel&&(r._logLevel=t.logLevel),void 0!==t.env&&(r._env=t.env),void 0!==t.octopusConfig&&(r._octopusConfig=s(s({},p.default),t.octopusConfig))),r._logger=new d.default({level:r._logLevel}),r.onPublishStateUpdate=function(e,t,r){},r.onPlayStateUpdate=function(e,t,r){},r.onStreamUpdated=function(e,t){},r.onDisconnect=function(e){},r.onKickOut=function(e){},r}return n(t,e),t.prototype.login=function(e,t,r,i,n){this._logger&&this._logger.info("start login",{roomId:e}),this._octopusConfig&&(this._signal=new l.default({env:this._env,roomId:e,userId:this._userId,octopusConfig:this._octopusConfig,emit:this._emit,onStreamUpdated:this.onStreamUpdated.bind(this),onDisconnect:this.onDisconnect.bind(this),onKickOut:this.onKickOut.bind(this)}),this._signal.init({token:r,role:t,success:i,error:n}),this._role=t,this._roomId=e)},t.prototype.loginout=function(){this._signal&&this._signal.leaveRoom()},t.prototype.startPreview=function(e,t,r,i){return o(this,void 0,void 0,(function(){var n,s,o,c,l,d;return a(this,(function(a){switch(a.label){case 0:return this._logger&&this._logger.info("start preview."),this._media=new u.default({userId:this._userId}),this._mediaContraints=new h.default(t),n=this._mediaContraints.transformConstraints(),s=this._mediaContraints.transformBitRate(),o=s.bitRateStart,c=s.bitRateMin,l=s.bitRateMax,this._bitRate={start:o,min:c,max:l},this._logger&&this._logger.debug("constraints",n,{bitRateStart:o,bitRateMin:c,bitRateMax:l}),[4,this._media.startPreview(e,n,r,i)];case 1:return d=a.sent(),this._mediaList["m_"+d]={videoElement:e,streamId:d,roomId:this._roomId},[2]}}))}))},t.prototype.stopPreview=function(e){this._media&&(this._media.stopPreview(e),this._logger&&this._logger.info("stop preview success."))},t.prototype.getDevicesSettings=function(){if(this._media)return this._media.getDeviceCapabilities()},t.prototype.changeConstrains=function(e){this._media&&this._media.changeConstraints(e)},t.prototype.startPublishingStream=function(e,t,r,i){this._signal&&this._media&&(this._logger&&this._logger.info("start publish stream.",{streamid:e}),this._signal.createICEconnection({direction:"push",publishStreamInfo:{streamId:e,videoElement:t,extraInfo:r,publishOption:{}},bitRate:this._bitRate,onPublishStateUpdate:this.onPublishStateUpdate.bind(this)}))},t.prototype.stopPublishingStream=function(e){if(this._signal&&this._media){if(1==this._role)for(var t=0,r=Object.keys(this._mediaList);t<r.length;t++){var i=r[t];this._mediaList[i].roomId==this._roomId&&(this._media.stopPreview(this._mediaList[i].videoElement),this._signal.stopPush({streamId:this._mediaList[i].streamId}))}else{if(!this._mediaList["m_"+e])return;this._media.stopPreview(this._mediaList["m_"+e].videoElement),this._signal.stopPush({streamId:this._mediaList["m_"+e].streamId})}this._logger&&this._logger.info("stop publish stream success.")}},t.prototype.startPlayingStream=function(e,t,r,i){this._signal&&(this._logger&&this._logger.info("start play stream.",{streamid:e}),this._signal.createICEconnection({direction:"pull",playStreamInfo:{streamId:e,videoElement:t,audioOutput:r,playOption:{}},bitRate:this._bitRate,onPlayStateUpdate:this.onPlayStateUpdate.bind(this)}),this._mediaList["m_"+e]={videoElement:t,streamId:e,roomId:this._roomId})},t.prototype.stopPlayingStream=function(e){this._signal&&(this._media||(this._media=new u.default({userId:this._userId})),this._mediaList["m_"+e]&&(this._media.stopPreview(this._mediaList["m_"+e].videoElement),this._signal.stopPlay({streamId:this._mediaList["m_"+e].streamId}),this._logger&&this._logger.info("stop play stream success.")))},t.prototype.updateMixStream=function(e,t,r){this._signal&&this._signal.updateMixStream(e)},t.prototype.muted=function(e){this._signal&&this._signal.switchMuted(e)},t}(c(r(6)).default);t.default=f},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){function t(t){return e.call(this,t)||this}return n(t,e),t}(s(r(16)).default);t.default=o},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}},a=this&&this.__spreadArrays||function(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var i=Array(e),n=0;for(t=0;t<r;t++)for(var s=arguments[t],o=0,a=s.length;o<a;o++,n++)i[n]=s[o];return i},c=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var u=c(r(17)),l=c(r(3)),h=function(e){function t(t){var r=e.call(this,t)||this;return r._timer=null,r._timeouter=null,r.signalTimeout=30,r._initJoinRoomParams=null,r._succeed=!1,r}return n(t,e),t.prototype.init=function(e){return s(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return this._initJoinRoomParams=e,[4,this.connect()];case 1:return t.sent(),this.wsEvents(e),[2]}}))}))},t.prototype.getNewEdgeList=function(){return s(this,void 0,void 0,(function(){var e;return o(this,(function(t){switch(t.label){case 0:return this._api?[4,this._api.getEdgeList({roomId:this._roomId,userId:this._userId,role:""})]:[2];case 1:return e=t.sent(),this._edgeUrls=Array.from(new Set(a(this._edgeUrls,e))),this._logger.debug("getNewEdgeList:::",this._edgeUrls),[2]}}))}))},t.prototype.errorFun=function(e){switch(e){case"joinSuccess":this._joinRoomParams&&this._joinRoomParams.error&&(this._logger.debug("login room error."),this._joinRoomParams.error({code:500,msg:"login room error."}))}},t.prototype.signalReconnect=function(e){var t=this;this._logger.debug("reconnect:::",this._retryTimes,this._maxRetryTimes,this._currentEdgeUrl,this._edgeUrls),this._timeouter||(this._timeouter=setTimeout((function(){clearTimeout(t._timer),clearTimeout(t._timeouter),e?t.errorFun(e):t.changeVendor()}),1e3*this.signalTimeout)),this.leaveRoom(),this._timer,this._timer=setTimeout((function(){var e=Math.ceil(t.signalTimeout/8);t._edgeUrls.length<e&&t.getNewEdgeList(),t._retryTimes>=t._maxRetryTimes?(t._retryTimes=0,t._edgeUrls.length>1&&t._edgeListIndex++,t._currentEdgeUrl=t._edgeUrls[t._edgeListIndex],t._initJoinRoomParams&&t.init(t._initJoinRoomParams),t._succeed&&(t._connectState=2),t._edgeUrls.length>1&&t._edgeListIndex+1>=t._edgeUrls.length&&(t._edgeListIndex=-1)):t._retryTimes<t._maxRetryTimes&&(t._initJoinRoomParams&&t.init(t._initJoinRoomParams),t._succeed&&(t._connectState=1),t._retryTimes++)}),2e3)},t.prototype.formatResMsg=function(e,t){return 200==e.code||t&&"answerSDP"===t?l.default.formatKey(e):500==e.code?(this.leaveRoom(),this._onDisconnect({code:5e5,msg:"WS_DISCONNECT"}),!1):void(t?"joinSuccess"===t&&this.resetSignalState("joinSuccess"):this.resetSignalState())},t.prototype.resetSignalState=function(e){e||(this._retryTimes=this._maxRetryTimes),clearTimeout(this._timer),clearTimeout(this._timeouter),this.signalReconnect(e)},t.prototype.wsEvents=function(e){var t=this;this._ws&&(this._ws.on("connect",(function(){t._logger.debug("signal connect success."),t._retryTimes=0,clearTimeout(t._timer),clearTimeout(t._timeouter),t.onConnect(e)})),this._ws.on("joinRoom",(function(e){t.formatResMsg(e)})),this._ws.on("joinSuccess",(function(e){t.formatResMsg(e,"joinSuccess")&&(t._succeed=!0,t.onjoinSuccess(t.formatResMsg(e,"joinSuccess")))})),this._ws.on("offerSDP",(function(e){t.formatResMsg(e)})),this._ws.on("answerSDP",(function(e){t.formatResMsg(e,"answerSDP")&&t.onAnswerSDP(t.formatResMsg(e,"answerSDP"))})),this._ws.on("onIceCandidate",(function(e){t.formatResMsg(e)})),this._ws.on("iceCandidate",(function(e){t.formatResMsg(e)&&t.onIceCandidate(t.formatResMsg(e))})),this._ws.on("addParticipants",(function(e){e.code=200,t.formatResMsg(e)&&t.onAddParticipants(t.formatResMsg(e))})),this._ws.on("updateParticipant",(function(e){e.code=200,t.formatResMsg(e)&&t.onUpdateParticipant(t.formatResMsg(e))})),this._ws.on("removeParticipant",(function(e){e.code=200,t.formatResMsg(e)&&t.onRemoveParticipant(t.formatResMsg(e))})),this._ws.on("kickParticipant",(function(e){t.formatResMsg(e)&&t.onKickParticipant(t.formatResMsg(e))})),this._ws.on("mixResult",(function(e){t.onMixResult(e)})),this._ws.on("muteParticipant_resp",(function(e){t.onMuteParticipant(e)})),this._ws.on("connect_error",(function(e){t._logger.debug("ws connect_error:",e),t.resetSignalState("err")})),this._ws.on("connect_timeout",(function(e){t._logger.debug("ws connect_timeout:",e),t.resetSignalState("err")})),this._ws.on("error",(function(e){t._logger.debug("ws error:",e),t.resetSignalState("err")})),this._ws.on("disconnect",(function(e){t._logger.debug("disconnect reason:",e),"io client disconnect"!==e&&t.resetSignalState("err")})),this._ws.on("close",(function(){t._logger.debug("ws status: close")})),this._ws.on("closeRoom",(function(){t.onCloseRoom()})),this._ws.sendMessage=function(e,r){t._ws&&t._ws.emit(e,r)})},t}(u.default);t.default=h},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var c=a(r(2)),u=a(r(18)),l=a(r(19)),h=function(e){function t(t){var r=e.call(this,t)||this;return r._statistics={},r._destory={},r._publishStreamInfo=null,r._playStreamInfo=null,r._iceDirection="push",r._onPublishStateUpdate=null,r._onPlayStateUpdate=null,r._roomOnlineList=[],r._bitRate={start:c.default.bitRate.start,min:c.default.bitRate.min,max:c.default.bitRate.max},r._reCreateOffer=!1,r._offerPeerId=0,r._appId="",r._connectState=0,r._joinRoomParams=null,r._role=0,r._sessionId="",r.iceConnection={},r._reSendOfferQueue={},r._octopusConfig&&(r._appId=r._octopusConfig.APPID),r}return n(t,e),t.prototype.onConnect=function(e){return s(this,void 0,void 0,(function(){var t,r,i,n;return o(this,(function(s){if(this._joinRoomParams=e,0==(t=Object.keys(this.iceConnection)).length)this._syncRole=0;else if(1==t.length)t[0].split("_")[1]==this._userId?this._syncRole=2:this._syncRole=1;else if(t.length>1)for(r=0,i=t;r<i.length;r++){if(i[r].split("_")[1]==this._userId){this._syncRole=3;break}this._syncRole=1}return n={c_stream_id:""+(new Date).getTime(),app_id:this._appId,token:this._joinRoomParams.token,connect_state:this._connectState,room_id:this._roomId,user_id:this._userId,session_id:this._sessionId,host:1==this._joinRoomParams.role?1:0,role:2==this._joinRoomParams.role?"viewer_cdn":"participant",show:"all",user_source:c.default.SIGNALUSERSOURCE,timestamp:(new Date).getTime(),sdk_version:c.default.SDKVERSION,sync_role:this._syncRole},this._role=this._joinRoomParams.role,this._ws.sendMessage("joinRoom",n),[2]}))}))},t.prototype.onjoinSuccess=function(e){if(e){e.participants&&0!==e.participants.length&&(this._roomOnlineList=e.participants),this._sessionId=e.sessionId;var t=e.connectState;if(1!=t){2!=t&&3!=t||this.reCreateICEconnection();for(var r=[],i=0,n=this._roomOnlineList;i<n.length;i++){var s=n[i];s.userId!=this._userId&&r.push({streamId:s.streamId,userId:s.userId,roomId:s.roomId})}this._joinRoomParams&&this._joinRoomParams.success(r)}}},t.prototype.onIceCandidate=function(e){var t=this,r=new RTCIceCandidate(e.candidate),i=this.iceConnection["ice_"+e.userId],n=i&&i.peer;n&&(n.answerSdp?n.pc.addIceCandidate(r).catch((function(e){t._logger.error("addIceCandidate",e)})):n.candidateArray&&n.candidateArray.push(e.candidate),this._ws.sendMessage("response",{response_timestamp:e.timestamp,user_id:this._userId,room_id:this._roomId,session_id:e.sessionId}))},t.prototype.reSendOfferSDP=function(e){var t=this;return function(){var r=setTimeout((function(){t.reCreateICEconnection({iceConnectionUserId:e}),clearTimeout(r)}),2e3)}},t.prototype.onAnswerSDP=function(e){var t=this,r=this.iceConnection["ice_"+e.userId];if(r&&e.peerId==r._offerPeerId){if(200!=e.code)return this._reSendOfferQueue[e.userId]||(this._reSendOfferQueue[e.userId]=[]),this._reSendOfferQueue[e.userId].push(e.peerId),this._logger.debug("reSendOfferQueue:::",this._reSendOfferQueue,this._reSendOfferQueue[e.userId].length),void(this._reSendOfferQueue[e.userId].length<4?this.reSendOfferSDP(e.userId)():e.userId==this._userId?this._onPublishStateUpdate(1,this._userId,""):this._onPlayStateUpdate(1,e.userId,""));for(var i=";x-google-max-bitrate="+this._bitRate.max+";x-google-min-bitrate="+this._bitRate.min+";x-google-start-bitrate="+this._bitRate.start,n=e.sdpAnswer.split("\r\n"),s=0;s<n.length;s++)-1!=n[s].indexOf("profile-level-id")&&(n[s]+=i);var o=new RTCSessionDescription({type:"answer",sdp:n.join("\r\n")}),a=r.peer;a&&(a.pc.setRemoteDescription(o).then((function(){if(a.candidateArray){for(var r=0;r<a.candidateArray.length;r+=1){var i=new RTCIceCandidate(a.candidateArray[r]);a.pc.addIceCandidate(i).catch((function(e){t._logger.error("addIceCandidate",e),t._emit("error",{code:501,msg:""})}))}if(a.answerSdp=!0,t._statistics[e.userId]){var n=t._statistics[e.userId];n&&n.startInterval()}}})).catch((function(e){t._logger.error("setRemotesdp error",e)})),this._ws.sendMessage("response",{response_timestamp:e.timestamp,user_id:this._userId,room_id:this._roomId,session_id:e.sessionId}),this._reCreateOffer=!1)}},t.prototype.onAddParticipants=function(e){var t=[];e.participants.forEach((function(e,r){t[r]=e,t[r].streamId=e.userId}));for(var r={},i=0,n=this._roomOnlineList.concat(t);i<n.length;i++){var s=n[i];r[s.userId]=s}for(var o in r)this._roomOnlineList=[],this._roomOnlineList.push(r[o]);this._onStreamUpdated(0,t),this._emit("addParticipants",t)},t.prototype.onUpdateParticipant=function(e){this.reCreateICEconnection({iceConnectionUserId:e.userId})},t.prototype.onRemoveParticipant=function(e){console.log("onRemoveParticipant:::",e),this._destory={userId:e.userId},this._onStreamUpdated(1,[e]),this._emit("removeParticipant",e)},t.prototype.onKickParticipant=function(e){this._onKickOut({code:600001,msg:e})},t.prototype.onCloseRoom=function(){console.log("ws::closeRoom",this)},t.prototype.updateMixStream=function(e){console.log("mixstreamParams:::",e);var t=[],r={auto_mix:0,layout:t,mix_push_urls:[e.outputUrl],room_id:this._roomId,session_id:this._sessionId,timestamp:(new Date).getTime(),user_id:this._userId};t.push({layout_params:{bitrate:e.outputBitrate,canvas_color:e.outputBgColor,canvas_height:e.outputHeight,canvas_image:e.outputBgImage||"http://orion-rtc-center.ksmobile.net/static/images/pk.jpg",canvas_width:e.outputWidth,fps:e.outputFps},session_id:"canvas"});for(var i=1,n=0,s=e.streamList;n<s.length;n++){var o=s[n];o.roomId=this._appId+"-"+o.roomId,o.streamId=this._appId+"-"+o.streamId,t.push({layout_params:{image_height:o.bottom-o.top,image_layer:i++,image_width:o.right-o.left,location_x:o.left,location_y:o.top},room_id:o.roomId?o.roomId:this._roomId,session_id:this._sessionId,user_id:o.streamId})}console.log("sendMixstreamParams:::",JSON.stringify(r)),this._ws.sendMessage("mixStream",r)},t.prototype.stopMixStream=function(){this._ws.sendMessage("mixStream",{room_id:this._roomId,session_id:this._sessionId,timestamp:(new Date).getTime(),user_id:this._userId})},t.prototype.onMixResult=function(e){console.log("ws::mixresult",e)},t.prototype.onMuteParticipant=function(e){this._logger.debug("onMuteParticipant:::",e),200!==e.code&&this._emit("error",{code:10001,msg:"muted failed."})},t.prototype.switchMuted=function(e){var t=e?"start":"stop";this._ws.sendMessage("muteParticipant",{method:t,room_id:this._roomId,session_id:this._sessionId,timestamp:(new Date).getTime()})},t.prototype.createICEconnection=function(e){void 0!==e.bitRate&&(this._bitRate=e.bitRate),void 0!==e.publishStreamInfo&&(this._publishStreamInfo=e.publishStreamInfo),void 0!==e.playStreamInfo&&(this._playStreamInfo=e.playStreamInfo),void 0!==e.direction&&(this._iceDirection=e.direction),void 0!==e.onPublishStateUpdate&&(this._onPublishStateUpdate=e.onPublishStateUpdate),void 0!==e.onPlayStateUpdate&&(this._onPlayStateUpdate=e.onPlayStateUpdate);for(var t="push"==this._iceDirection?this._publishStreamInfo&&this._publishStreamInfo.streamId:this._playStreamInfo&&this._playStreamInfo.streamId,r=0,i=this._roomOnlineList;r<i.length;r++){var n=i[r];"push"==this._iceDirection&&n.userId==this._userId&&this.createEveryICEconnection(n),"pull"==this._iceDirection&&n.streamId==t&&this.createEveryICEconnection(n)}},t.prototype.reCreateICEconnection=function(e){if(e&&e.iceConnectionUserId)for(var t=0,r=this._roomOnlineList;t<r.length;t++){if((a=r[t]).userId==e.iceConnectionUserId){e.iceConnectionUserId==this._userId?this._iceDirection="push":this._iceDirection="pull",this.iceConnection["ice_"+e.iceConnectionUserId]=null,this.createEveryICEconnection(a);break}}else for(var i in this.iceConnection){if(!i)return;for(var n=i.split("_")[1],s=0,o=this._roomOnlineList;s<o.length;s++){var a;(a=o[s]).userId==n&&(n==this._userId?this._iceDirection="push":this._iceDirection="pull",this.iceConnection[i]=null,this.createEveryICEconnection(a))}}},t.prototype.createEveryICEconnection=function(e){this._offerPeerId+=1,this.iceConnection["ice_"+e.userId]=new l.default({env:this._env,appId:this._appId,offerPeerId:this._offerPeerId,userId:this._userId,roomId:this._roomId,sessionId:this._sessionId,direction:this._iceDirection,ws:this._ws,roomOnlineCurrent:e,bitRate:this._bitRate,onPublishStateUpdate:this._onPublishStateUpdate,onPlayStateUpdate:this._onPlayStateUpdate,emit:this._emit.bind(this),publishStreamInfo:this._publishStreamInfo||void 0,playStreamInfo:this._playStreamInfo||void 0});var t=this.iceConnection["ice_"+e.userId];if(t&&(t.init(),t.peer)){var r=t.peer.pc;this._statistics[e.userId]=new u.default({direction:this._iceDirection,appId:this._appId,sessionId:this._sessionId,roomId:this._roomId,userId:this._userId,currentIceUserInfo:e,ws:this._ws,connection:r})}},t.prototype.leaveRoom=function(){if(this._ws){for(var e in this._statistics){var t=this._statistics[e];t&&t.stopInterval(),t=null}for(var r in this.iceConnection){var i=this.iceConnection[r];if(!i||!i.peer)return;i.iceDisconnect(i.peer.pc),i=null}this._ws.sendMessage&&this._ws.sendMessage("leaveRoom",{room_id:this._roomId,session_id:this._sessionId}),this._ws.close(),this._ws=null}},t.prototype.stopPush=function(e){this.disconnectICE(e);var t=this._statistics[this._userId];t&&t.stopInterval(),this._ws&&this._ws.sendMessage("stopBeam",{room_id:this._roomId,user_id:this._userId,session_id:this._sessionId,timestamp:(new Date).getTime()})},t.prototype.stopPlay=function(e){this.disconnectICE(e);var t=this._statistics[this._destory.userId];t&&t.stopInterval(),this._ws&&this._ws.sendMessage("stop_play",{room_id:this._roomId,user_id:this._userId,session_id:this._sessionId,timestamp:(new Date).getTime(),play_user_id:this._destory.userId})},t.prototype.disconnectICE=function(e){var t=this;e&&e.streamId&&(this._roomOnlineList.forEach((function(r,i){r.streamId==e.streamId?(delete t.iceConnection["ice_"+r.userId],t._destory={userId:r.userId,sessionId:r.sessionId}):r.userId==t._userId&&delete t.iceConnection["ice_"+t._userId]})),this._destory.userId||(this._destory={userId:this._userId,sessionId:this._sessionId}));var r=this._destory.userId,i=this.iceConnection["ice_"+r];if(i&&i.peer){if(i.iceDisconnect(i.peer.pc),i=null,!this._statistics[this._destory.userId])return;var n=this._statistics[this._destory.userId];n&&n.stopInterval(),n=null}this._ws&&this._ws.sendMessage("reportIceClosed",{room_id:this._roomId,user_id:this._userId,session_id:this._sessionId,timestamp:(new Date).getTime()})},t}(a(r(24)).default);t.default=h},function(e,t,r){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},n=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},s=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}};Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e){this._direction="push",this._appId="",this._roomId="",this._userId="",this._sessionId="",this._currentIceUserInfo=null,this._reportInterval=5e3,this._reportHandle=0,this._reportCollecting=!1,this._currentRTCStatsReport=null,this._previousRTCStatsReport=null,this._collectRTCStatsReport=this._collectRTCStatsReport.bind(this),this._ws=e.ws,this._connection=e.connection,this._currentIceUserInfo=e.currentIceUserInfo,void 0!==e.direction&&(this._direction=e.direction),void 0!==e.appId&&(this._appId=e.appId),void 0!==e.roomId&&(this._roomId=e.roomId),void 0!==e.userId&&(this._userId=e.userId),void 0!==e.sessionId&&(this._sessionId=e.sessionId),void 0!==e.reportInterval&&(this._reportInterval=e.reportInterval)}return Object.defineProperty(e.prototype,"reportCollecting",{get:function(){return this._reportCollecting},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"defaultStatsParams",{get:function(){return"push"==this._direction?{session_id:this._sessionId,room_id:this._roomId,user_id:this._userId}:this._currentIceUserInfo?{session_id:this._currentIceUserInfo.sessionId,room_id:this._currentIceUserInfo.roomId,user_id:this._currentIceUserInfo.userId}:void 0},enumerable:!0,configurable:!0}),e.prototype.getStats=function(e){return n(this,void 0,void 0,(function(){return s(this,(function(t){return[2,this._connection.getStats(e)]}))}))},e.prototype.startInterval=function(){this.stopInterval(),this._reportCollecting=!0,this._currentRTCStatsReport=null,this._previousRTCStatsReport=null,this._collectRTCStatsReport()},e.prototype.stopInterval=function(){this._reportCollecting&&(this._reportCollecting=!1,this._currentRTCStatsReport=null,this._previousRTCStatsReport=null,clearTimeout(this._reportHandle))},e.prototype._collectRTCStatsReport=function(){return n(this,void 0,void 0,(function(){var e;return s(this,(function(t){switch(t.label){case 0:return e=this,[4,this.getStats(null)];case 1:return e._currentRTCStatsReport=t.sent(),null!==this._currentRTCStatsReport&&null!==this._previousRTCStatsReport&&this._analysisRTCStatsReport(this._currentRTCStatsReport,this._previousRTCStatsReport),this._previousRTCStatsReport=this._currentRTCStatsReport,this._reportCollecting&&(this._reportHandle=setTimeout(this._collectRTCStatsReport,this._reportInterval)),[2]}}))}))},e.prototype._analysisRTCStatsReport=function(e,t){var r=this,n=[],s=null,o=null;e.forEach((function(a){var c=a.id;switch(a.type){case"outbound-rtp":switch(null===s&&(s=i(i({},r.defaultStatsParams),{direction:1,ScastNums:1}),n.push(s)),a.kind){case"audio":r._appendOutAudioMediaStreamStatistics(s,a,t.get(c));break;case"video":r._appendOutVideoMediaStreamStatistics(s,a,t.get(c)),r._appendOutVideoMediaSourceStatistics(s,e.get(a.mediaSourceId),t.get(a.mediaSourceId)),r._appendOutVideoMediaTrackStatistics(s,e.get(a.trackId),t.get(a.trackId))}break;case"inbound-rtp":switch(null===o&&(o=i(i({},r.defaultStatsParams),{direction:2}),n.push(o)),a.kind){case"audio":r._appendInAudioMediaStreamStatistics(o,a,t.get(c));break;case"video":r._appendInVideoMediaStreamStatistics(o,a,t.get(c)),r._appendInVideoMediaTrackStatistics(o,e.get(a.trackId),t.get(a.trackId))}}})),this._ws.sendMessage("clientstatistics",{room_id:this._roomId,sdk_version:101e4,session_id:this._sessionId,user_id:this._userId,timestamp:(new Date).getTime(),statistics:n})},e.prototype._appendOutVideoMediaStreamStatistics=function(e,t,r){var i,n=e.ScastNums;Object.assign(e,((i={})["VSSRC"+n]=t.ssrc,i["VSendBytes"+n]=t.bytesSent-r.bytesSent,i["VSendBitrate"+n]=this._calculateKBitsPerSecond(t.bytesSent-r.bytesSent,t.timestamp-r.timestamp),i))},e.prototype._appendOutAudioMediaStreamStatistics=function(e,t,r){var i;Object.assign(e,((i={}).ASSRC=t.ssrc,i.ASendBytes=t.bytesSent-r.bytesSent,i.ASendBitrate=this._calculateKBitsPerSecond(t.bytesSent-r.bytesSent,t.timestamp-r.timestamp),i))},e.prototype._appendOutVideoMediaSourceStatistics=function(e,t,r){var i,n=e.ScastNums;Object.assign(e,((i={})["VBitrate"+n]=1024,i["VFrameRate"+n]=t.framesPerSecond,i["VResolution"+n]=t.width+"*"+t.height,i))},e.prototype._appendOutVideoMediaTrackStatistics=function(e,t,r){var i,n=e.ScastNums;Object.assign(e,((i={})["VSendFrameTimeStamp"+n]=t.timestamp,i["VSendFrameDuration"+n]=t.timestamp-r.timestamp,i["VSendFrameHeight"+n]=t.frameHeight,i["VSendFrameWidth"+n]=t.frameWidth,i["VSendFrameRate"+n]=this._calcuateFramesPerSecond(t.framesSent-r.framesSent,t.timestamp-r.timestamp),i))},e.prototype._appendInVideoMediaStreamStatistics=function(e,t,r){var i;Object.assign(e,((i={}).FrozenTimes=0,i.VFisrtFrameTime=0,i.VRecvBytes=t.bytesReceived-r.bytesReceived,i.VRecvBitrate=this._calculateKBitsPerSecond(t.bytesReceived-r.bytesReceived,t.timestamp-r.timestamp),i.VLostRatio=this._calcuatePacketsLostRatio(t.packetsLost-r.packetsLost,t.packetsReceived-r.packetsReceived),i))},e.prototype._appendInAudioMediaStreamStatistics=function(e,t,r){var i;Object.assign(e,((i={}).ARecvBytes=t.bytesReceived-r.bytesReceived,i.ARecvBitrate=this._calculateKBitsPerSecond(t.bytesReceived-r.bytesReceived,t.timestamp-r.timestamp),i.ALostRatio=this._calcuatePacketsLostRatio(t.packetsLost-r.packetsLost,t.packetsReceived-r.packetsReceived),i))},e.prototype._appendInVideoMediaTrackStatistics=function(e,t,r){var i;Object.assign(e,((i={}).VRecvFrameRate=this._calcuateFramesPerSecond(t.framesReceived-r.framesReceived,t.timestamp-r.timestamp),i.VFramesRate=this._calcuateFramesPerSecond(t.framesReceived-r.framesReceived,t.timestamp-r.timestamp),i))},e.prototype._calculateKBitsPerSecond=function(e,t){return.0078125*e/(.001*t)},e.prototype._calculatePacketsPerSecond=function(e,t){return e/(.001*t)},e.prototype._calcuateFramesPerSecond=function(e,t){return e/(.001*t)},e.prototype._calcuatePacketsLostRatio=function(e,t){return 0===e&&0===t?0:e/(e+t)},e}();t.default=o},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=s(r(20)),a=s(r(7)),c=function(e){function t(t){var r=e.call(this,t)||this;return r._media=null,r}return n(t,e),t.prototype.init=function(){var e=new RTCPeerConnection({iceTransportPolicy:"all"});this.peer={sessionId:this._sessionId,pc:e,candidateArray:[],answerSdp:!1},this._publishStreamInfo&&this._publishStreamInfo.streamId&&"push"==this._direction&&(this._media=new a.default({userId:this._userId}),this._media.publishMedia(e)),this._playStreamInfo&&this._playStreamInfo.streamId&&"pull"==this._direction&&this.createOfferSdp(),this.iceEvents(this.peer.pc)},t.prototype.iceDisconnect=function(e){e.close(),e.onicecandidate=null,e.oniceconnectionstatechange=null,e.onicegatheringstatechange=null,e.onsignalingstatechange=null,e.onnegotiationneeded=null,e.onconnectionstatechange=null,e.ontrack=null},t}(o.default);t.default=c},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){function t(t){return e.call(this,t)||this}return n(t,e),t.prototype.iceEvents=function(e){var t=this;e&&(e.onicecandidate=function(e){"dev"==t._env&&t.ICECandidateHandleEvent(e)},e.oniceconnectionstatechange=function(e){t.ICEConnectionStateChangeHandleEvent(e)},e.onicegatheringstatechange=function(e){t.ICEGatheringStateChangeHandleEvent(e)},e.onsignalingstatechange=function(e){t.signalingStateChangeHandleEvent(e)},e.onnegotiationneeded=function(e){t.negotiationNeededHandleEvent(e)},e.onconnectionstatechange=function(e){t.connectionStateHandleEvent(e)},e.ontrack=function(e){t.trackHandleEvent(e)})},t}(s(r(21)).default);t.default=o},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){function t(t){var r=e.call(this,t)||this;return r._isNegotiating=!1,r._hasTrack=!1,r._reConnectIce=null,r._reTryTimes=1,r}return n(t,e),t.prototype.negotiationNeededHandleEvent=function(e){this._logger.debug("negotiationNeededEvent:::",e),this._isNegotiating||(this._isNegotiating=!0,this.createOfferSdp())},t.prototype.ICECandidateHandleEvent=function(e){if(e.candidate){this._logger.debug("icecandidate::"+e.candidate.candidate);var t={session_id:this.peer&&this.peer.sessionId,candidate:e.candidate,timestamp:(new Date).getTime()};this._ws.sendMessage("onIceCandidate",t)}},t.prototype.ICEConnectionStateChangeHandleEvent=function(e){if(this.peer){this._logger.debug("iceconnectionstatechange::",this.peer.pc.iceConnectionState,e);var t=!1;switch("push"==this._direction&&this._publishStreamInfo&&this._publishStreamInfo.publishOption&&this._publishStreamInfo.publishOption.cdnUrl&&(t=!0),this.peer.pc.iceConnectionState){case"closed":case"completed":t&&this.mixStream();break;case"connected":if("push"==this._direction){if(t&&this.mixStream(),!this._onPublishStateUpdate)return;this._onPublishStateUpdate(0,this._streamId,null),this._logger.info("publish stream success.")}break;case"failed":this._logger.debug("failed"),this._onPublishStateUpdate&&"push"===this._direction&&(this._onPublishStateUpdate(1,this._streamId,null),this._logger.info("publish stream failed.")),this._onPlayStateUpdate&&"pull"===this._direction&&(this._onPlayStateUpdate(1,this._streamId,null),this._logger.info("pull stream failed"));break;case"disconnected":this._logger.debug("disconnected")}}},t.prototype.trackHandleEvent=function(e){if(this._logger.debug("handleTrack:::",e,this._playStreamInfo),!this._hasTrack&&(this._hasTrack=!0,this._playStreamInfo))if(e.streams){if(!this._onPlayStateUpdate)return;this._onPlayStateUpdate(0,this._streamId,null),this._logger.info("play stream success."),this._playStreamInfo.videoElement.srcObject=e.streams[0]}else this._onPlayStateUpdate(1,this._streamId,null)},t.prototype.connectionStateHandleEvent=function(e){this._logger.debug("handleConnectionState",e)},t.prototype.signalingStateChangeHandleEvent=function(e){this.peer&&this._logger.debug("SignalingStateChange::"+this.peer.pc.signalingState)},t.prototype.ICEGatheringStateChangeHandleEvent=function(e){this.peer&&this._logger.debug("ICEGatheringStateChange:"+this.peer.pc.iceGatheringState)},t}(s(r(22)).default);t.default=o},function(e,t,r){"use strict";var i,n=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=s(r(23)),a=s(r(2)),c=function(e){function t(t){return e.call(this,t)||this}return n(t,e),t.prototype.createOfferSdp=function(e){var t=this,r={offerAudio:!1,offerVideo:!1},i={sessionId:this._sessionId,userId:this._userId,roomId:this._roomId};if("pull"==this._direction){if(r={offerAudio:!0,offerVideo:!0},!this._roomOnlineCurrent)return;i={sessionId:this._roomOnlineCurrent.sessionId,userId:this._roomOnlineCurrent.userId,roomId:this._roomId}}this.peer&&this.peer.pc.createOffer({offerToReceiveAudio:r.offerAudio,offerToReceiveVideo:r.offerVideo,iceRestart:!(!e||!e.restart)}).then((function(e){if(void 0!==e.sdp){var r={peer_id:t._offerPeerId,offer_session_id:i.sessionId,offer_user_id:i.userId,offer_room_id:i.roomId,sdp_offer:e.sdp,timestamp:(new Date).getTime(),room_id:t._roomId,session_id:t._sessionId,user_id:t._userId,sdk_version:a.default.SDKVERSION};t._ws.sendMessage("offerSDP",r);for(var n=";x-google-max-bitrate="+t._bitRate.max+";x-google-min-bitrate="+t._bitRate.min+";x-google-start-bitrate="+t._bitRate.start,s=e.sdp.split("\r\n"),o=0;o<s.length;o++)-1!=s[o].indexOf("profile-level-id")&&(s[o]+=n);e.sdp=s.join("\r\n"),t._logger.debug("offerSDP:::",e),t.peer&&t.peer.pc.setLocalDescription(e)}})).catch((function(e){t._logger.error("creat offer error",e)}))},t.prototype.mixStream=function(){},t}(o.default);t.default=c},function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var n=i(r(2)),s=i(r(0)),o=function(e){this._logger=new s.default,this._roomId="",this._userId="",this._role=0,this._sessionId="",this._direction="push",this._bitRate={start:n.default.bitRate.start,min:n.default.bitRate.min,max:n.default.bitRate.max},this._roomOnlineCurrent=null,this._publishStreamInfo=null,this._playStreamInfo=null,this._onPublishStateUpdate=null,this._onPlayStateUpdate=null,this._emit=null,this._streamId="",this._offerPeerId=1,this._env="prod",this.peer=null,this._userId=e.userId,this._roomId=e.roomId,this._sessionId=e.sessionId,this._direction=e.direction,this._roomOnlineCurrent=e.roomOnlineCurrent,this._ws=e.ws,this._emit=e.emit,this._offerPeerId=e.offerPeerId,void 0!==e.role&&(this._role=e.role),void 0!==e.publishStreamInfo&&(this._publishStreamInfo=e.publishStreamInfo),void 0!==e.playStreamInfo&&(this._playStreamInfo=e.playStreamInfo),void 0!==e.onPublishStateUpdate&&(this._onPublishStateUpdate=e.onPublishStateUpdate),void 0!==e.onPlayStateUpdate&&(this._onPlayStateUpdate=e.onPlayStateUpdate),void 0!==e.bitRate&&(this._bitRate=e.bitRate),this._roomOnlineCurrent.streamId&&(this._streamId=this._roomOnlineCurrent.streamId),void 0!==e.env&&(this._env=e.env)};t.default=o},function(e,t,r){"use strict";var i=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},n=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=s(r(25)),a=s(r(31)),c=s(r(0)),u=function(){function e(e){this._logger=new c.default,this._retryTimes=0,this._maxRetryTimes=3,this._edgeListIndex=0,this._emit=null,this._roomId="",this._userId="",this._currentEdgeUrl="",this._edgeUrls=[],this._api=null,this._ws=null,this._onDisconnect=null,this._onKickOut=null,this._onStreamUpdated=null,this._octopusConfig=null,this._syncRole=0,this._env="prod",this._userId=e.userId,this._roomId=e.roomId,this._emit=e.emit,void 0!==e.onDisconnect&&(this._onDisconnect=e.onDisconnect),void 0!==e.onKickOut&&(this._onKickOut=e.onKickOut),void 0!==e.onStreamUpdated&&(this._onStreamUpdated=e.onStreamUpdated),void 0!==e.octopusConfig&&(this._octopusConfig=e.octopusConfig),void 0!==e.env&&(this._env=e.env),this._logger.debug("signal options",e)}return e.prototype.connect=function(){return i(this,void 0,void 0,(function(){var e;return n(this,(function(t){switch(t.label){case 0:return this._currentEdgeUrl||!this._octopusConfig?[3,3]:this._octopusConfig.EDGEURL?(this._currentEdgeUrl="wss://"+this._octopusConfig.EDGEURL+":443/octopus",[3,3]):[3,1];case 1:return this._api=new a.default(this._octopusConfig),e=this,[4,this._api.getEdgeList({roomId:this._roomId,userId:this._userId,role:""})];case 2:if(e._edgeUrls=t.sent(),!this._edgeUrls||0===this._edgeUrls.length)return this._logger.info("edge is null"),[2];this._currentEdgeUrl=this._edgeUrls[this._edgeListIndex],this.getIntervalEdgeUrls(),t.label=3;case 3:return this._ws=o.default(this._currentEdgeUrl,{transports:["websocket"]}),this._ws.connect(),[2]}}))}))},e.prototype.getIntervalEdgeUrls=function(){var e=this;setInterval((function(){return i(e,void 0,void 0,(function(){var e;return n(this,(function(t){switch(t.label){case 0:return this._api?(e=this,[4,this._api.getEdgeList({roomId:this._roomId,userId:this._userId,role:""})]):[2];case 1:return e._edgeUrls=t.sent(),[2]}}))}))}),36e5)},e.prototype.changeVendor=function(){this._onDisconnect({code:60002001,msg:"kRoomInvalidSocketError"}),this._emit&&this._emit("error",{code:60002001,msg:"kRoomInvalidSocketError"})},e}();t.default=u},function(e,t,r){(function(t){e.exports=function(e){function t(i){if(r[i])return r[i].exports;var n=r[i]={exports:{},id:i,loaded:!1};return e[i].call(n.exports,n,n.exports,t),n.loaded=!0,n.exports}var r={};return t.m=e,t.c=r,t.p="",t(0)}([function(e,t,r){"use strict";function i(e,t){"object"===(void 0===e?"undefined":n(e))&&(t=e,e=void 0),t=t||{};var r,i=s(e),o=i.source,u=i.id,l=i.path,h=c[u]&&l in c[u].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||h?r=a(o,t):(c[u]||(c[u]=a(o,t)),r=c[u]),i.query&&!t.query&&(t.query=i.query),r.socket(i.path,t)}var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s=r(1),o=r(4),a=r(9);r(3)("socket.io-client"),e.exports=t=i;var c=t.managers={};t.protocol=o.protocol,t.connect=i,t.Manager=r(9),t.Socket=r(33)},function(e,t,r){"use strict";var i=r(2);r(3)("socket.io-client:url"),e.exports=function(e,t){var r=e;t=t||"undefined"!=typeof location&&location,null==e&&(e=t.protocol+"//"+t.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?t.protocol+e:t.host+e),/^(https?|wss?):\/\//.test(e)||(e=void 0!==t?t.protocol+"//"+e:"https://"+e),r=i(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";var n=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+n+":"+r.port,r.href=r.protocol+"://"+n+(t&&t.port===r.port?"":":"+r.port),r}},function(e,t){var r=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,i=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];e.exports=function(e){var t=e,n=e.indexOf("["),s=e.indexOf("]");-1!=n&&-1!=s&&(e=e.substring(0,n)+e.substring(n,s).replace(/:/g,";")+e.substring(s,e.length));for(var o=r.exec(e||""),a={},c=14;c--;)a[i[c]]=o[c]||"";return-1!=n&&-1!=s&&(a.source=t,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a}},function(e,t){"use strict";e.exports=function(){return function(){}}},function(e,t,r){function i(){}function n(e){var r=""+e.type;if(t.BINARY_EVENT!==e.type&&t.BINARY_ACK!==e.type||(r+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(r+=e.nsp+","),null!=e.id&&(r+=e.id),null!=e.data){var i=function(e){try{return JSON.stringify(e)}catch(e){return!1}}(e.data);if(!1===i)return p;r+=i}return r}function s(){this.reconstructor=null}function o(e){var r=0,i={type:Number(e.charAt(0))};if(null==t.types[i.type])return c("unknown packet type "+i.type);if(t.BINARY_EVENT===i.type||t.BINARY_ACK===i.type){for(var n="";"-"!==e.charAt(++r)&&(n+=e.charAt(r),r!=e.length););if(n!=Number(n)||"-"!==e.charAt(r))throw new Error("Illegal attachments");i.attachments=Number(n)}if("/"===e.charAt(r+1))for(i.nsp="";++r&&","!==(o=e.charAt(r))&&(i.nsp+=o,r!==e.length););else i.nsp="/";var s=e.charAt(r+1);if(""!==s&&Number(s)==s){for(i.id="";++r;){var o;if(null==(o=e.charAt(r))||Number(o)!=o){--r;break}if(i.id+=e.charAt(r),r===e.length)break}i.id=Number(i.id)}if(e.charAt(++r)){var a=function(e){try{return JSON.parse(e)}catch(e){return!1}}(e.substr(r));if(!1===a||i.type!==t.ERROR&&!h(a))return c("invalid payload");i.data=a}return i}function a(e){this.reconPack=e,this.buffers=[]}function c(e){return{type:t.ERROR,data:"parser error: "+e}}var u=(r(3)("socket.io-parser"),r(5)),l=r(6),h=r(7),d=r(8);t.protocol=4,t.types=["CONNECT","DISCONNECT","EVENT","ACK","ERROR","BINARY_EVENT","BINARY_ACK"],t.CONNECT=0,t.DISCONNECT=1,t.EVENT=2,t.ACK=3,t.ERROR=4,t.BINARY_EVENT=5,t.BINARY_ACK=6,t.Encoder=i,t.Decoder=s;var p=t.ERROR+'"encode error"';i.prototype.encode=function(e,r){t.BINARY_EVENT===e.type||t.BINARY_ACK===e.type?function(e,t){l.removeBlobs(e,(function(e){var r=l.deconstructPacket(e),i=n(r.packet),s=r.buffers;s.unshift(i),t(s)}))}(e,r):r([n(e)])},u(s.prototype),s.prototype.add=function(e){var r;if("string"==typeof e)r=o(e),t.BINARY_EVENT===r.type||t.BINARY_ACK===r.type?(this.reconstructor=new a(r),0===this.reconstructor.reconPack.attachments&&this.emit("decoded",r)):this.emit("decoded",r);else{if(!d(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");(r=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,this.emit("decoded",r))}},s.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},a.prototype.takeBinaryData=function(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t=l.reconstructPacket(this.reconPack,this.buffers);return this.finishedReconstruction(),t}return null},a.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]}},function(e,t,r){function i(e){if(e)return function(e){for(var t in i.prototype)e[t]=i.prototype[t];return e}(e)}e.exports=i,i.prototype.on=i.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},i.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},i.prototype.off=i.prototype.removeListener=i.prototype.removeAllListeners=i.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i,n=0;n<r.length;n++)if((i=r[n])===t||i.fn===t){r.splice(n,1);break}return this},i.prototype.emit=function(e){this._callbacks=this._callbacks||{};var t=[].slice.call(arguments,1),r=this._callbacks["$"+e];if(r)for(var i=0,n=(r=r.slice(0)).length;i<n;++i)r[i].apply(this,t);return this},i.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},i.prototype.hasListeners=function(e){return!!this.listeners(e).length}},function(e,t,r){var i=r(7),n=r(8),s=Object.prototype.toString,o="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===s.call(Blob),a="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===s.call(File);t.deconstructPacket=function(e){var t=[],r=e.data,s=e;return s.data=function e(t,r){if(!t)return t;if(n(t)){var s={_placeholder:!0,num:r.length};return r.push(t),s}if(i(t)){for(var o=new Array(t.length),a=0;a<t.length;a++)o[a]=e(t[a],r);return o}if("object"==typeof t&&!(t instanceof Date)){for(var c in o={},t)o[c]=e(t[c],r);return o}return t}(r,t),s.attachments=t.length,{packet:s,buffers:t}},t.reconstructPacket=function(e,t){return e.data=function e(t,r){if(!t)return t;if(t&&t._placeholder)return r[t.num];if(i(t))for(var n=0;n<t.length;n++)t[n]=e(t[n],r);else if("object"==typeof t)for(var s in t)t[s]=e(t[s],r);return t}(e.data,t),e.attachments=void 0,e},t.removeBlobs=function(e,t){var r=0,s=e;(function e(c,u,l){if(!c)return c;if(o&&c instanceof Blob||a&&c instanceof File){r++;var h=new FileReader;h.onload=function(){l?l[u]=this.result:s=this.result,--r||t(s)},h.readAsArrayBuffer(c)}else if(i(c))for(var d=0;d<c.length;d++)e(c[d],d,c);else if("object"==typeof c&&!n(c))for(var p in c)e(c[p],p,c)})(s),r||t(s)}},function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},function(e,r){e.exports=function(e){return i&&t.isBuffer(e)||n&&(e instanceof ArrayBuffer||s(e))};var i="function"==typeof t&&"function"==typeof t.isBuffer,n="function"==typeof ArrayBuffer,s=function(e){return"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer}},function(e,t,r){"use strict";function i(e,t){if(!(this instanceof i))return new i(e,t);e&&"object"===(void 0===e?"undefined":n(e))&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.nsps={},this.subs=[],this.opts=t,this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(t.randomizationFactor||.5),this.backoff=new d({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this.readyState="closed",this.uri=e,this.connecting=[],this.lastPing=null,this.encoding=!1,this.packetBuffer=[];var r=t.parser||c;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this.autoConnect=!1!==t.autoConnect,this.autoConnect&&this.open()}var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s=r(10),o=r(33),a=r(5),c=r(4),u=r(35),l=r(36),h=(r(3)("socket.io-client:manager"),r(32)),d=r(37),p=Object.prototype.hasOwnProperty;e.exports=i,i.prototype.emitAll=function(){for(var e in this.emit.apply(this,arguments),this.nsps)p.call(this.nsps,e)&&this.nsps[e].emit.apply(this.nsps[e],arguments)},i.prototype.updateSocketIds=function(){for(var e in this.nsps)p.call(this.nsps,e)&&(this.nsps[e].id=this.generateId(e))},i.prototype.generateId=function(e){return("/"===e?"":e+"#")+this.engine.id},a(i.prototype),i.prototype.reconnection=function(e){return arguments.length?(this._reconnection=!!e,this):this._reconnection},i.prototype.reconnectionAttempts=function(e){return arguments.length?(this._reconnectionAttempts=e,this):this._reconnectionAttempts},i.prototype.reconnectionDelay=function(e){return arguments.length?(this._reconnectionDelay=e,this.backoff&&this.backoff.setMin(e),this):this._reconnectionDelay},i.prototype.randomizationFactor=function(e){return arguments.length?(this._randomizationFactor=e,this.backoff&&this.backoff.setJitter(e),this):this._randomizationFactor},i.prototype.reconnectionDelayMax=function(e){return arguments.length?(this._reconnectionDelayMax=e,this.backoff&&this.backoff.setMax(e),this):this._reconnectionDelayMax},i.prototype.timeout=function(e){return arguments.length?(this._timeout=e,this):this._timeout},i.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},i.prototype.open=i.prototype.connect=function(e,t){if(~this.readyState.indexOf("open"))return this;this.engine=s(this.uri,this.opts);var r=this.engine,i=this;this.readyState="opening",this.skipReconnect=!1;var n=u(r,"open",(function(){i.onopen(),e&&e()})),o=u(r,"error",(function(t){if(i.cleanup(),i.readyState="closed",i.emitAll("connect_error",t),e){var r=new Error("Connection error");r.data=t,e(r)}else i.maybeReconnectOnOpen()}));if(!1!==this._timeout){var a=this._timeout,c=setTimeout((function(){n.destroy(),r.close(),r.emit("error","timeout"),i.emitAll("connect_timeout",a)}),a);this.subs.push({destroy:function(){clearTimeout(c)}})}return this.subs.push(n),this.subs.push(o),this},i.prototype.onopen=function(){this.cleanup(),this.readyState="open",this.emit("open");var e=this.engine;this.subs.push(u(e,"data",l(this,"ondata"))),this.subs.push(u(e,"ping",l(this,"onping"))),this.subs.push(u(e,"pong",l(this,"onpong"))),this.subs.push(u(e,"error",l(this,"onerror"))),this.subs.push(u(e,"close",l(this,"onclose"))),this.subs.push(u(this.decoder,"decoded",l(this,"ondecoded")))},i.prototype.onping=function(){this.lastPing=new Date,this.emitAll("ping")},i.prototype.onpong=function(){this.emitAll("pong",new Date-this.lastPing)},i.prototype.ondata=function(e){this.decoder.add(e)},i.prototype.ondecoded=function(e){this.emit("packet",e)},i.prototype.onerror=function(e){this.emitAll("error",e)},i.prototype.socket=function(e,t){function r(){~h(n.connecting,i)||n.connecting.push(i)}var i=this.nsps[e];if(!i){i=new o(this,e,t),this.nsps[e]=i;var n=this;i.on("connecting",r),i.on("connect",(function(){i.id=n.generateId(e)})),this.autoConnect&&r()}return i},i.prototype.destroy=function(e){var t=h(this.connecting,e);~t&&this.connecting.splice(t,1),this.connecting.length||this.close()},i.prototype.packet=function(e){var t=this;e.query&&0===e.type&&(e.nsp+="?"+e.query),t.encoding?t.packetBuffer.push(e):(t.encoding=!0,this.encoder.encode(e,(function(r){for(var i=0;i<r.length;i++)t.engine.write(r[i],e.options);t.encoding=!1,t.processPacketQueue()})))},i.prototype.processPacketQueue=function(){if(this.packetBuffer.length>0&&!this.encoding){var e=this.packetBuffer.shift();this.packet(e)}},i.prototype.cleanup=function(){for(var e=this.subs.length,t=0;t<e;t++)this.subs.shift().destroy();this.packetBuffer=[],this.encoding=!1,this.lastPing=null,this.decoder.destroy()},i.prototype.close=i.prototype.disconnect=function(){this.skipReconnect=!0,this.reconnecting=!1,"opening"===this.readyState&&this.cleanup(),this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},i.prototype.onclose=function(e){this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",e),this._reconnection&&!this.skipReconnect&&this.reconnect()},i.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1;else{var t=this.backoff.duration();this.reconnecting=!0;var r=setTimeout((function(){e.skipReconnect||(e.emitAll("reconnect_attempt",e.backoff.attempts),e.emitAll("reconnecting",e.backoff.attempts),e.skipReconnect||e.open((function(t){t?(e.reconnecting=!1,e.reconnect(),e.emitAll("reconnect_error",t.data)):e.onreconnect()})))}),t);this.subs.push({destroy:function(){clearTimeout(r)}})}},i.prototype.onreconnect=function(){var e=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",e)}},function(e,t,r){e.exports=r(11),e.exports.parser=r(18)},function(e,t,r){function i(e,t){return this instanceof i?(t=t||{},e&&"object"==typeof e&&(t=e,e=null),e?(e=c(e),t.hostname=e.host,t.secure="https"===e.protocol||"wss"===e.protocol,t.port=e.port,e.query&&(t.query=e.query)):t.host&&(t.hostname=c(t.host).host),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.agent=t.agent||!1,this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?443:80),this.query=t.query||{},"string"==typeof this.query&&(this.query=u.decode(this.query)),this.upgrade=!1!==t.upgrade,this.path=(t.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!t.forceJSONP,this.jsonp=!1!==t.jsonp,this.forceBase64=!!t.forceBase64,this.enablesXDR=!!t.enablesXDR,this.withCredentials=!1!==t.withCredentials,this.timestampParam=t.timestampParam||"t",this.timestampRequests=t.timestampRequests,this.transports=t.transports||["polling","websocket"],this.transportOptions=t.transportOptions||{},this.readyState="",this.writeBuffer=[],this.prevBufferLen=0,this.policyPort=t.policyPort||843,this.rememberUpgrade=t.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=t.onlyBinaryUpgrades,this.perMessageDeflate=!1!==t.perMessageDeflate&&(t.perMessageDeflate||{}),!0===this.perMessageDeflate&&(this.perMessageDeflate={}),this.perMessageDeflate&&null==this.perMessageDeflate.threshold&&(this.perMessageDeflate.threshold=1024),this.pfx=t.pfx||null,this.key=t.key||null,this.passphrase=t.passphrase||null,this.cert=t.cert||null,this.ca=t.ca||null,this.ciphers=t.ciphers||null,this.rejectUnauthorized=void 0===t.rejectUnauthorized||t.rejectUnauthorized,this.forceNode=!!t.forceNode,this.isReactNative="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),("undefined"==typeof self||this.isReactNative)&&(t.extraHeaders&&Object.keys(t.extraHeaders).length>0&&(this.extraHeaders=t.extraHeaders),t.localAddress&&(this.localAddress=t.localAddress)),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingIntervalTimer=null,this.pingTimeoutTimer=null,void this.open()):new i(e,t)}var n=r(12),s=r(5),o=(r(3)("engine.io-client:socket"),r(32)),a=r(18),c=r(2),u=r(26);e.exports=i,i.priorWebsocketSuccess=!1,s(i.prototype),i.protocol=a.protocol,i.Socket=i,i.Transport=r(17),i.transports=r(12),i.parser=r(18),i.prototype.createTransport=function(e){var t=function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t}(this.query);t.EIO=a.protocol,t.transport=e;var r=this.transportOptions[e]||{};return this.id&&(t.sid=this.id),new n[e]({query:t,socket:this,agent:r.agent||this.agent,hostname:r.hostname||this.hostname,port:r.port||this.port,secure:r.secure||this.secure,path:r.path||this.path,forceJSONP:r.forceJSONP||this.forceJSONP,jsonp:r.jsonp||this.jsonp,forceBase64:r.forceBase64||this.forceBase64,enablesXDR:r.enablesXDR||this.enablesXDR,withCredentials:r.withCredentials||this.withCredentials,timestampRequests:r.timestampRequests||this.timestampRequests,timestampParam:r.timestampParam||this.timestampParam,policyPort:r.policyPort||this.policyPort,pfx:r.pfx||this.pfx,key:r.key||this.key,passphrase:r.passphrase||this.passphrase,cert:r.cert||this.cert,ca:r.ca||this.ca,ciphers:r.ciphers||this.ciphers,rejectUnauthorized:r.rejectUnauthorized||this.rejectUnauthorized,perMessageDeflate:r.perMessageDeflate||this.perMessageDeflate,extraHeaders:r.extraHeaders||this.extraHeaders,forceNode:r.forceNode||this.forceNode,localAddress:r.localAddress||this.localAddress,requestTimeout:r.requestTimeout||this.requestTimeout,protocols:r.protocols||void 0,isReactNative:this.isReactNative})},i.prototype.open=function(){var e;if(this.rememberUpgrade&&i.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))e="websocket";else{if(0===this.transports.length){var t=this;return void setTimeout((function(){t.emit("error","No transports available")}),0)}e=this.transports[0]}this.readyState="opening";try{e=this.createTransport(e)}catch(e){return this.transports.shift(),void this.open()}e.open(),this.setTransport(e)},i.prototype.setTransport=function(e){var t=this;this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",(function(){t.onDrain()})).on("packet",(function(e){t.onPacket(e)})).on("error",(function(e){t.onError(e)})).on("close",(function(){t.onClose("transport close")}))},i.prototype.probe=function(e){function t(){if(h.onlyBinaryUpgrades){var e=!this.supportsBinary&&h.transport.supportsBinary;l=l||e}l||(u.send([{type:"ping",data:"probe"}]),u.once("packet",(function(e){if(!l)if("pong"===e.type&&"probe"===e.data){if(h.upgrading=!0,h.emit("upgrading",u),!u)return;i.priorWebsocketSuccess="websocket"===u.name,h.transport.pause((function(){l||"closed"!==h.readyState&&(c(),h.setTransport(u),u.send([{type:"upgrade"}]),h.emit("upgrade",u),u=null,h.upgrading=!1,h.flush())}))}else{var t=new Error("probe error");t.transport=u.name,h.emit("upgradeError",t)}})))}function r(){l||(l=!0,c(),u.close(),u=null)}function n(e){var t=new Error("probe error: "+e);t.transport=u.name,r(),h.emit("upgradeError",t)}function s(){n("transport closed")}function o(){n("socket closed")}function a(e){u&&e.name!==u.name&&r()}function c(){u.removeListener("open",t),u.removeListener("error",n),u.removeListener("close",s),h.removeListener("close",o),h.removeListener("upgrading",a)}var u=this.createTransport(e,{probe:1}),l=!1,h=this;i.priorWebsocketSuccess=!1,u.once("open",t),u.once("error",n),u.once("close",s),this.once("close",o),this.once("upgrading",a),u.open()},i.prototype.onOpen=function(){if(this.readyState="open",i.priorWebsocketSuccess="websocket"===this.transport.name,this.emit("open"),this.flush(),"open"===this.readyState&&this.upgrade&&this.transport.pause)for(var e=0,t=this.upgrades.length;e<t;e++)this.probe(this.upgrades[e])},i.prototype.onPacket=function(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emit("packet",e),this.emit("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"pong":this.setPing(),this.emit("pong");break;case"error":var t=new Error("server error");t.code=e.data,this.onError(t);break;case"message":this.emit("data",e.data),this.emit("message",e.data)}},i.prototype.onHandshake=function(e){this.emit("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this.upgrades=this.filterUpgrades(e.upgrades),this.pingInterval=e.pingInterval,this.pingTimeout=e.pingTimeout,this.onOpen(),"closed"!==this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},i.prototype.onHeartbeat=function(e){clearTimeout(this.pingTimeoutTimer);var t=this;t.pingTimeoutTimer=setTimeout((function(){"closed"!==t.readyState&&t.onClose("ping timeout")}),e||t.pingInterval+t.pingTimeout)},i.prototype.setPing=function(){var e=this;clearTimeout(e.pingIntervalTimer),e.pingIntervalTimer=setTimeout((function(){e.ping(),e.onHeartbeat(e.pingTimeout)}),e.pingInterval)},i.prototype.ping=function(){var e=this;this.sendPacket("ping",(function(){e.emit("ping")}))},i.prototype.onDrain=function(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0===this.writeBuffer.length?this.emit("drain"):this.flush()},i.prototype.flush=function(){"closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},i.prototype.write=i.prototype.send=function(e,t,r){return this.sendPacket("message",e,t,r),this},i.prototype.sendPacket=function(e,t,r,i){if("function"==typeof t&&(i=t,t=void 0),"function"==typeof r&&(i=r,r=null),"closing"!==this.readyState&&"closed"!==this.readyState){(r=r||{}).compress=!1!==r.compress;var n={type:e,data:t,options:r};this.emit("packetCreate",n),this.writeBuffer.push(n),i&&this.once("flush",i),this.flush()}},i.prototype.close=function(){function e(){i.onClose("forced close"),i.transport.close()}function t(){i.removeListener("upgrade",t),i.removeListener("upgradeError",t),e()}function r(){i.once("upgrade",t),i.once("upgradeError",t)}if("opening"===this.readyState||"open"===this.readyState){this.readyState="closing";var i=this;this.writeBuffer.length?this.once("drain",(function(){this.upgrading?r():e()})):this.upgrading?r():e()}return this},i.prototype.onError=function(e){i.priorWebsocketSuccess=!1,this.emit("error",e),this.onClose("transport error",e)},i.prototype.onClose=function(e,t){"opening"!==this.readyState&&"open"!==this.readyState&&"closing"!==this.readyState||(clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",e,t),this.writeBuffer=[],this.prevBufferLen=0)},i.prototype.filterUpgrades=function(e){for(var t=[],r=0,i=e.length;r<i;r++)~o(this.transports,e[r])&&t.push(e[r]);return t}},function(e,t,r){var i=r(13),n=r(15),s=r(29),o=r(30);t.polling=function(e){var t=!1,r=!1,o=!1!==e.jsonp;if("undefined"!=typeof location){var a="https:"===location.protocol,c=location.port;c||(c=a?443:80),t=e.hostname!==location.hostname||c!==e.port,r=e.secure!==a}if(e.xdomain=t,e.xscheme=r,"open"in new i(e)&&!e.forceJSONP)return new n(e);if(!o)throw new Error("JSONP disabled");return new s(e)},t.websocket=o},function(e,t,r){var i=r(14);e.exports=function(e){var t=e.xdomain,r=e.xscheme,n=e.enablesXDR;try{if("undefined"!=typeof XMLHttpRequest&&(!t||i))return new XMLHttpRequest}catch(e){}try{if("undefined"!=typeof XDomainRequest&&!r&&n)return new XDomainRequest}catch(e){}if(!t)try{return new(self[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(e){}}},function(e,t){try{e.exports="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){e.exports=!1}},function(e,t,r){function i(){}function n(e){if(c.call(this,e),this.requestTimeout=e.requestTimeout,this.extraHeaders=e.extraHeaders,"undefined"!=typeof location){var t="https:"===location.protocol,r=location.port;r||(r=t?443:80),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||r!==e.port,this.xs=e.secure!==t}}function s(e){this.method=e.method||"GET",this.uri=e.uri,this.xd=!!e.xd,this.xs=!!e.xs,this.async=!1!==e.async,this.data=void 0!==e.data?e.data:null,this.agent=e.agent,this.isBinary=e.isBinary,this.supportsBinary=e.supportsBinary,this.enablesXDR=e.enablesXDR,this.withCredentials=e.withCredentials,this.requestTimeout=e.requestTimeout,this.pfx=e.pfx,this.key=e.key,this.passphrase=e.passphrase,this.cert=e.cert,this.ca=e.ca,this.ciphers=e.ciphers,this.rejectUnauthorized=e.rejectUnauthorized,this.extraHeaders=e.extraHeaders,this.create()}function o(){for(var e in s.requests)s.requests.hasOwnProperty(e)&&s.requests[e].abort()}var a=r(13),c=r(16),u=r(5),l=r(27);if(r(3)("engine.io-client:polling-xhr"),e.exports=n,e.exports.Request=s,l(n,c),n.prototype.supportsBinary=!0,n.prototype.request=function(e){return(e=e||{}).uri=this.uri(),e.xd=this.xd,e.xs=this.xs,e.agent=this.agent||!1,e.supportsBinary=this.supportsBinary,e.enablesXDR=this.enablesXDR,e.withCredentials=this.withCredentials,e.pfx=this.pfx,e.key=this.key,e.passphrase=this.passphrase,e.cert=this.cert,e.ca=this.ca,e.ciphers=this.ciphers,e.rejectUnauthorized=this.rejectUnauthorized,e.requestTimeout=this.requestTimeout,e.extraHeaders=this.extraHeaders,new s(e)},n.prototype.doWrite=function(e,t){var r="string"!=typeof e&&void 0!==e,i=this.request({method:"POST",data:e,isBinary:r}),n=this;i.on("success",t),i.on("error",(function(e){n.onError("xhr post error",e)})),this.sendXhr=i},n.prototype.doPoll=function(){var e=this.request(),t=this;e.on("data",(function(e){t.onData(e)})),e.on("error",(function(e){t.onError("xhr poll error",e)})),this.pollXhr=e},u(s.prototype),s.prototype.create=function(){var e={agent:this.agent,xdomain:this.xd,xscheme:this.xs,enablesXDR:this.enablesXDR};e.pfx=this.pfx,e.key=this.key,e.passphrase=this.passphrase,e.cert=this.cert,e.ca=this.ca,e.ciphers=this.ciphers,e.rejectUnauthorized=this.rejectUnauthorized;var t=this.xhr=new a(e),r=this;try{t.open(this.method,this.uri,this.async);try{if(this.extraHeaders)for(var i in t.setDisableHeaderCheck&&t.setDisableHeaderCheck(!0),this.extraHeaders)this.extraHeaders.hasOwnProperty(i)&&t.setRequestHeader(i,this.extraHeaders[i])}catch(e){}if("POST"===this.method)try{this.isBinary?t.setRequestHeader("Content-type","application/octet-stream"):t.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{t.setRequestHeader("Accept","*/*")}catch(e){}"withCredentials"in t&&(t.withCredentials=this.withCredentials),this.requestTimeout&&(t.timeout=this.requestTimeout),this.hasXDR()?(t.onload=function(){r.onLoad()},t.onerror=function(){r.onError(t.responseText)}):t.onreadystatechange=function(){if(2===t.readyState)try{var e=t.getResponseHeader("Content-Type");(r.supportsBinary&&"application/octet-stream"===e||"application/octet-stream; charset=UTF-8"===e)&&(t.responseType="arraybuffer")}catch(e){}4===t.readyState&&(200===t.status||1223===t.status?r.onLoad():setTimeout((function(){r.onError("number"==typeof t.status?t.status:0)}),0))},t.send(this.data)}catch(e){return void setTimeout((function(){r.onError(e)}),0)}"undefined"!=typeof document&&(this.index=s.requestsCount++,s.requests[this.index]=this)},s.prototype.onSuccess=function(){this.emit("success"),this.cleanup()},s.prototype.onData=function(e){this.emit("data",e),this.onSuccess()},s.prototype.onError=function(e){this.emit("error",e),this.cleanup(!0)},s.prototype.cleanup=function(e){if(void 0!==this.xhr&&null!==this.xhr){if(this.hasXDR()?this.xhr.onload=this.xhr.onerror=i:this.xhr.onreadystatechange=i,e)try{this.xhr.abort()}catch(e){}"undefined"!=typeof document&&delete s.requests[this.index],this.xhr=null}},s.prototype.onLoad=function(){var e;try{var t;try{t=this.xhr.getResponseHeader("Content-Type")}catch(e){}e=("application/octet-stream"===t||"application/octet-stream; charset=UTF-8"===t)&&this.xhr.response||this.xhr.responseText}catch(e){this.onError(e)}null!=e&&this.onData(e)},s.prototype.hasXDR=function(){return"undefined"!=typeof XDomainRequest&&!this.xs&&this.enablesXDR},s.prototype.abort=function(){this.cleanup()},s.requestsCount=0,s.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",o);else if("function"==typeof addEventListener){var h="onpagehide"in self?"pagehide":"unload";addEventListener(h,o,!1)}},function(e,t,r){function i(e){var t=e&&e.forceBase64;u&&!t||(this.supportsBinary=!1),n.call(this,e)}var n=r(17),s=r(26),o=r(18),a=r(27),c=r(28);r(3)("engine.io-client:polling"),e.exports=i;var u=null!=new(r(13))({xdomain:!1}).responseType;a(i,n),i.prototype.name="polling",i.prototype.doOpen=function(){this.poll()},i.prototype.pause=function(e){function t(){r.readyState="paused",e()}var r=this;if(this.readyState="pausing",this.polling||!this.writable){var i=0;this.polling&&(i++,this.once("pollComplete",(function(){--i||t()}))),this.writable||(i++,this.once("drain",(function(){--i||t()})))}else t()},i.prototype.poll=function(){this.polling=!0,this.doPoll(),this.emit("poll")},i.prototype.onData=function(e){var t=this;o.decodePayload(e,this.socket.binaryType,(function(e,r,i){return"opening"===t.readyState&&t.onOpen(),"close"===e.type?(t.onClose(),!1):void t.onPacket(e)})),"closed"!==this.readyState&&(this.polling=!1,this.emit("pollComplete"),"open"===this.readyState&&this.poll())},i.prototype.doClose=function(){function e(){t.write([{type:"close"}])}var t=this;"open"===this.readyState?e():this.once("open",e)},i.prototype.write=function(e){var t=this;this.writable=!1;var r=function(){t.writable=!0,t.emit("drain")};o.encodePayload(e,this.supportsBinary,(function(e){t.doWrite(e,r)}))},i.prototype.uri=function(){var e=this.query||{},t=this.secure?"https":"http",r="";return!1!==this.timestampRequests&&(e[this.timestampParam]=c()),this.supportsBinary||e.sid||(e.b64=1),e=s.encode(e),this.port&&("https"===t&&443!==Number(this.port)||"http"===t&&80!==Number(this.port))&&(r=":"+this.port),e.length&&(e="?"+e),t+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+r+this.path+e}},function(e,t,r){function i(e){this.path=e.path,this.hostname=e.hostname,this.port=e.port,this.secure=e.secure,this.query=e.query,this.timestampParam=e.timestampParam,this.timestampRequests=e.timestampRequests,this.readyState="",this.agent=e.agent||!1,this.socket=e.socket,this.enablesXDR=e.enablesXDR,this.withCredentials=e.withCredentials,this.pfx=e.pfx,this.key=e.key,this.passphrase=e.passphrase,this.cert=e.cert,this.ca=e.ca,this.ciphers=e.ciphers,this.rejectUnauthorized=e.rejectUnauthorized,this.forceNode=e.forceNode,this.isReactNative=e.isReactNative,this.extraHeaders=e.extraHeaders,this.localAddress=e.localAddress}var n=r(18),s=r(5);e.exports=i,s(i.prototype),i.prototype.onError=function(e,t){var r=new Error(e);return r.type="TransportError",r.description=t,this.emit("error",r),this},i.prototype.open=function(){return"closed"!==this.readyState&&""!==this.readyState||(this.readyState="opening",this.doOpen()),this},i.prototype.close=function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this},i.prototype.send=function(e){if("open"!==this.readyState)throw new Error("Transport not open");this.write(e)},i.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},i.prototype.onData=function(e){var t=n.decodePacket(e,this.socket.binaryType);this.onPacket(t)},i.prototype.onPacket=function(e){this.emit("packet",e)},i.prototype.onClose=function(){this.readyState="closed",this.emit("close")}},function(e,t,r){function i(e,r){return r("b"+t.packets[e.type]+e.data.data)}function n(e,r,i){if(!r)return t.encodeBase64Packet(e,i);var n=e.data,s=new Uint8Array(n),o=new Uint8Array(1+n.byteLength);o[0]=m[e.type];for(var a=0;a<s.length;a++)o[a+1]=s[a];return i(o.buffer)}function s(e,r,i){if(!r)return t.encodeBase64Packet(e,i);if(g)return function(e,r,i){if(!r)return t.encodeBase64Packet(e,i);var n=new FileReader;return n.onload=function(){t.encodePacket({type:e.type,data:n.result},r,!0,i)},n.readAsArrayBuffer(e.data)}(e,r,i);var n=new Uint8Array(1);return n[0]=m[e.type],i(new _([n.buffer,e.data]))}function o(e,t,r){for(var i=new Array(e.length),n=h(e.length,r),s=function(e,r,n){t(r,(function(t,r){i[e]=r,n(t,i)}))},o=0;o<e.length;o++)s(o,e[o],n)}var a,c=r(19),u=r(20),l=r(21),h=r(22),d=r(23);"undefined"!=typeof ArrayBuffer&&(a=r(24));var p="undefined"!=typeof navigator&&/Android/i.test(navigator.userAgent),f="undefined"!=typeof navigator&&/PhantomJS/i.test(navigator.userAgent),g=p||f;t.protocol=3;var m=t.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6},v=c(m),y={type:"error",data:"parser error"},_=r(25);t.encodePacket=function(e,t,r,o){"function"==typeof t&&(o=t,t=!1),"function"==typeof r&&(o=r,r=null);var a=void 0===e.data?void 0:e.data.buffer||e.data;if("undefined"!=typeof ArrayBuffer&&a instanceof ArrayBuffer)return n(e,t,o);if(void 0!==_&&a instanceof _)return s(e,t,o);if(a&&a.base64)return i(e,o);var c=m[e.type];return void 0!==e.data&&(c+=r?d.encode(String(e.data),{strict:!1}):String(e.data)),o(""+c)},t.encodeBase64Packet=function(e,r){var i,n="b"+t.packets[e.type];if(void 0!==_&&e.data instanceof _){var s=new FileReader;return s.onload=function(){var e=s.result.split(",")[1];r(n+e)},s.readAsDataURL(e.data)}try{i=String.fromCharCode.apply(null,new Uint8Array(e.data))}catch(t){for(var o=new Uint8Array(e.data),a=new Array(o.length),c=0;c<o.length;c++)a[c]=o[c];i=String.fromCharCode.apply(null,a)}return n+=btoa(i),r(n)},t.decodePacket=function(e,r,i){if(void 0===e)return y;if("string"==typeof e){if("b"===e.charAt(0))return t.decodeBase64Packet(e.substr(1),r);if(i&&!1===(e=function(e){try{e=d.decode(e,{strict:!1})}catch(e){return!1}return e}(e)))return y;var n=e.charAt(0);return Number(n)==n&&v[n]?e.length>1?{type:v[n],data:e.substring(1)}:{type:v[n]}:y}n=new Uint8Array(e)[0];var s=l(e,1);return _&&"blob"===r&&(s=new _([s])),{type:v[n],data:s}},t.decodeBase64Packet=function(e,t){var r=v[e.charAt(0)];if(!a)return{type:r,data:{base64:!0,data:e.substr(1)}};var i=a.decode(e.substr(1));return"blob"===t&&_&&(i=new _([i])),{type:r,data:i}},t.encodePayload=function(e,r,i){"function"==typeof r&&(i=r,r=null);var n=u(e);return r&&n?_&&!g?t.encodePayloadAsBlob(e,i):t.encodePayloadAsArrayBuffer(e,i):e.length?void o(e,(function(e,i){t.encodePacket(e,!!n&&r,!1,(function(e){i(null,function(e){return e.length+":"+e}(e))}))}),(function(e,t){return i(t.join(""))})):i("0:")},t.decodePayload=function(e,r,i){if("string"!=typeof e)return t.decodePayloadAsBinary(e,r,i);var n;if("function"==typeof r&&(i=r,r=null),""===e)return i(y,0,1);for(var s,o,a="",c=0,u=e.length;c<u;c++){var l=e.charAt(c);if(":"===l){if(""===a||a!=(s=Number(a)))return i(y,0,1);if(a!=(o=e.substr(c+1,s)).length)return i(y,0,1);if(o.length){if(n=t.decodePacket(o,r,!1),y.type===n.type&&y.data===n.data)return i(y,0,1);if(!1===i(n,c+s,u))return}c+=s,a=""}else a+=l}return""!==a?i(y,0,1):void 0},t.encodePayloadAsArrayBuffer=function(e,r){return e.length?void o(e,(function(e,r){t.encodePacket(e,!0,!0,(function(e){return r(null,e)}))}),(function(e,t){var i=t.reduce((function(e,t){var r;return e+(r="string"==typeof t?t.length:t.byteLength).toString().length+r+2}),0),n=new Uint8Array(i),s=0;return t.forEach((function(e){var t="string"==typeof e,r=e;if(t){for(var i=new Uint8Array(e.length),o=0;o<e.length;o++)i[o]=e.charCodeAt(o);r=i.buffer}n[s++]=t?0:1;var a=r.byteLength.toString();for(o=0;o<a.length;o++)n[s++]=parseInt(a[o]);for(n[s++]=255,i=new Uint8Array(r),o=0;o<i.length;o++)n[s++]=i[o]})),r(n.buffer)})):r(new ArrayBuffer(0))},t.encodePayloadAsBlob=function(e,r){o(e,(function(e,r){t.encodePacket(e,!0,!0,(function(e){var t=new Uint8Array(1);if(t[0]=1,"string"==typeof e){for(var i=new Uint8Array(e.length),n=0;n<e.length;n++)i[n]=e.charCodeAt(n);e=i.buffer,t[0]=0}var s=(e instanceof ArrayBuffer?e.byteLength:e.size).toString(),o=new Uint8Array(s.length+1);for(n=0;n<s.length;n++)o[n]=parseInt(s[n]);if(o[s.length]=255,_){var a=new _([t.buffer,o.buffer,e]);r(null,a)}}))}),(function(e,t){return r(new _(t))}))},t.decodePayloadAsBinary=function(e,r,i){"function"==typeof r&&(i=r,r=null);for(var n=e,s=[];n.byteLength>0;){for(var o=new Uint8Array(n),a=0===o[0],c="",u=1;255!==o[u];u++){if(c.length>310)return i(y,0,1);c+=o[u]}n=l(n,2+c.length),c=parseInt(c);var h=l(n,0,c);if(a)try{h=String.fromCharCode.apply(null,new Uint8Array(h))}catch(e){var d=new Uint8Array(h);for(h="",u=0;u<d.length;u++)h+=String.fromCharCode(d[u])}s.push(h),n=l(n,c)}var p=s.length;s.forEach((function(e,n){i(t.decodePacket(e,r,!0),n,p)}))}},function(e,t){e.exports=Object.keys||function(e){var t=[],r=Object.prototype.hasOwnProperty;for(var i in e)r.call(e,i)&&t.push(i);return t}},function(e,r,i){var n=i(7),s=Object.prototype.toString,o="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===s.call(Blob),a="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===s.call(File);e.exports=function e(r){if(!r||"object"!=typeof r)return!1;if(n(r)){for(var i=0,s=r.length;i<s;i++)if(e(r[i]))return!0;return!1}if("function"==typeof t&&t.isBuffer&&t.isBuffer(r)||"function"==typeof ArrayBuffer&&r instanceof ArrayBuffer||o&&r instanceof Blob||a&&r instanceof File)return!0;if(r.toJSON&&"function"==typeof r.toJSON&&1===arguments.length)return e(r.toJSON(),!0);for(var c in r)if(Object.prototype.hasOwnProperty.call(r,c)&&e(r[c]))return!0;return!1}},function(e,t){e.exports=function(e,t,r){var i=e.byteLength;if(t=t||0,r=r||i,e.slice)return e.slice(t,r);if(t<0&&(t+=i),r<0&&(r+=i),r>i&&(r=i),t>=i||t>=r||0===i)return new ArrayBuffer(0);for(var n=new Uint8Array(e),s=new Uint8Array(r-t),o=t,a=0;o<r;o++,a++)s[a]=n[o];return s.buffer}},function(e,t){function r(){}e.exports=function(e,t,i){function n(e,r){if(n.count<=0)throw new Error("after called too many times");--n.count,e?(s=!0,t(e),t=i):0!==n.count||s||t(null,r)}var s=!1;return i=i||r,n.count=e,0===e?t():n}},function(e,t){function r(e){for(var t,r,i=[],n=0,s=e.length;n<s;)(t=e.charCodeAt(n++))>=55296&&t<=56319&&n<s?56320==(64512&(r=e.charCodeAt(n++)))?i.push(((1023&t)<<10)+(1023&r)+65536):(i.push(t),n--):i.push(t);return i}function i(e,t){if(e>=55296&&e<=57343){if(t)throw Error("Lone surrogate U+"+e.toString(16).toUpperCase()+" is not a scalar value");return!1}return!0}function n(e,t){return h(e>>t&63|128)}function s(e,t){if(0==(4294967168&e))return h(e);var r="";return 0==(4294965248&e)?r=h(e>>6&31|192):0==(4294901760&e)?(i(e,t)||(e=65533),r=h(e>>12&15|224),r+=n(e,6)):0==(4292870144&e)&&(r=h(e>>18&7|240),r+=n(e,12),r+=n(e,6)),r+h(63&e|128)}function o(){if(l>=u)throw Error("Invalid byte index");var e=255&c[l];if(l++,128==(192&e))return 63&e;throw Error("Invalid continuation byte")}function a(e){var t,r;if(l>u)throw Error("Invalid byte index");if(l==u)return!1;if(t=255&c[l],l++,0==(128&t))return t;if(192==(224&t)){if((r=(31&t)<<6|o())>=128)return r;throw Error("Invalid continuation byte")}if(224==(240&t)){if((r=(15&t)<<12|o()<<6|o())>=2048)return i(r,e)?r:65533;throw Error("Invalid continuation byte")}if(240==(248&t)&&(r=(7&t)<<18|o()<<12|o()<<6|o())>=65536&&r<=1114111)return r;throw Error("Invalid UTF-8 detected")}var c,u,l,h=String.fromCharCode;e.exports={version:"2.1.2",encode:function(e,t){for(var i=!1!==(t=t||{}).strict,n=r(e),o=n.length,a=-1,c="";++a<o;)c+=s(n[a],i);return c},decode:function(e,t){var i=!1!==(t=t||{}).strict;c=r(e),u=c.length,l=0;for(var n,s=[];!1!==(n=a(i));)s.push(n);return function(e){for(var t,r=e.length,i=-1,n="";++i<r;)(t=e[i])>65535&&(n+=h((t-=65536)>>>10&1023|55296),t=56320|1023&t),n+=h(t);return n}(s)}}},function(e,t){!function(){"use strict";for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=new Uint8Array(256),i=0;i<e.length;i++)r[e.charCodeAt(i)]=i;t.encode=function(t){var r,i=new Uint8Array(t),n=i.length,s="";for(r=0;r<n;r+=3)s+=e[i[r]>>2],s+=e[(3&i[r])<<4|i[r+1]>>4],s+=e[(15&i[r+1])<<2|i[r+2]>>6],s+=e[63&i[r+2]];return n%3==2?s=s.substring(0,s.length-1)+"=":n%3==1&&(s=s.substring(0,s.length-2)+"=="),s},t.decode=function(e){var t,i,n,s,o,a=.75*e.length,c=e.length,u=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);var l=new ArrayBuffer(a),h=new Uint8Array(l);for(t=0;t<c;t+=4)i=r[e.charCodeAt(t)],n=r[e.charCodeAt(t+1)],s=r[e.charCodeAt(t+2)],o=r[e.charCodeAt(t+3)],h[u++]=i<<2|n>>4,h[u++]=(15&n)<<4|s>>2,h[u++]=(3&s)<<6|63&o;return l}}()},function(e,t){function r(e){return e.map((function(e){if(e.buffer instanceof ArrayBuffer){var t=e.buffer;if(e.byteLength!==t.byteLength){var r=new Uint8Array(e.byteLength);r.set(new Uint8Array(t,e.byteOffset,e.byteLength)),t=r.buffer}return t}return e}))}function i(e,t){t=t||{};var i=new s;return r(e).forEach((function(e){i.append(e)})),t.type?i.getBlob(t.type):i.getBlob()}function n(e,t){return new Blob(r(e),t||{})}var s=void 0!==s?s:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder&&MozBlobBuilder,o=function(){try{return 2===new Blob(["hi"]).size}catch(e){return!1}}(),a=o&&function(){try{return 2===new Blob([new Uint8Array([1,2])]).size}catch(e){return!1}}(),c=s&&s.prototype.append&&s.prototype.getBlob;"undefined"!=typeof Blob&&(i.prototype=Blob.prototype,n.prototype=Blob.prototype),e.exports=o?a?Blob:n:c?i:void 0},function(e,t){t.encode=function(e){var t="";for(var r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t},t.decode=function(e){for(var t={},r=e.split("&"),i=0,n=r.length;i<n;i++){var s=r[i].split("=");t[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return t}},function(e,t){e.exports=function(e,t){var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},function(e,t){"use strict";function r(e){var t="";do{t=s[e%o]+t,e=Math.floor(e/o)}while(e>0);return t}function i(){var e=r(+new Date);return e!==n?(c=0,n=e):e+"."+r(c++)}for(var n,s="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),o=64,a={},c=0,u=0;u<o;u++)a[s[u]]=u;i.encode=r,i.decode=function(e){var t=0;for(u=0;u<e.length;u++)t=t*o+a[e.charAt(u)];return t},e.exports=i},function(e,t,r){(function(t){function i(){}function n(){return"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==t?t:{}}function s(e){if(o.call(this,e),this.query=this.query||{},!c){var t=n();c=t.___eio=t.___eio||[]}this.index=c.length;var r=this;c.push((function(e){r.onData(e)})),this.query.j=this.index,"function"==typeof addEventListener&&addEventListener("beforeunload",(function(){r.script&&(r.script.onerror=i)}),!1)}var o=r(16),a=r(27);e.exports=s;var c,u=/\n/g,l=/\\n/g;a(s,o),s.prototype.supportsBinary=!1,s.prototype.doClose=function(){this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),this.form&&(this.form.parentNode.removeChild(this.form),this.form=null,this.iframe=null),o.prototype.doClose.call(this)},s.prototype.doPoll=function(){var e=this,t=document.createElement("script");this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),t.async=!0,t.src=this.uri(),t.onerror=function(t){e.onError("jsonp poll error",t)};var r=document.getElementsByTagName("script")[0];r?r.parentNode.insertBefore(t,r):(document.head||document.body).appendChild(t),this.script=t,"undefined"!=typeof navigator&&/gecko/i.test(navigator.userAgent)&&setTimeout((function(){var e=document.createElement("iframe");document.body.appendChild(e),document.body.removeChild(e)}),100)},s.prototype.doWrite=function(e,t){function r(){i(),t()}function i(){if(n.iframe)try{n.form.removeChild(n.iframe)}catch(e){n.onError("jsonp polling iframe removal error",e)}try{var e='<iframe src="javascript:0" name="'+n.iframeId+'">';s=document.createElement(e)}catch(e){(s=document.createElement("iframe")).name=n.iframeId,s.src="javascript:0"}s.id=n.iframeId,n.form.appendChild(s),n.iframe=s}var n=this;if(!this.form){var s,o=document.createElement("form"),a=document.createElement("textarea"),c=this.iframeId="eio_iframe_"+this.index;o.className="socketio",o.style.position="absolute",o.style.top="-1000px",o.style.left="-1000px",o.target=c,o.method="POST",o.setAttribute("accept-charset","utf-8"),a.name="d",o.appendChild(a),document.body.appendChild(o),this.form=o,this.area=a}this.form.action=this.uri(),i(),e=e.replace(l,"\\\n"),this.area.value=e.replace(u,"\\n");try{this.form.submit()}catch(e){}this.iframe.attachEvent?this.iframe.onreadystatechange=function(){"complete"===n.iframe.readyState&&r()}:this.iframe.onload=r}}).call(t,function(){return this}())},function(e,r,i){function n(e){e&&e.forceBase64&&(this.supportsBinary=!1),this.perMessageDeflate=e.perMessageDeflate,this.usingBrowserWebSocket=s&&!e.forceNode,this.protocols=e.protocols,this.usingBrowserWebSocket||(d=o),a.call(this,e)}var s,o,a=i(17),c=i(18),u=i(26),l=i(27),h=i(28);if(i(3)("engine.io-client:websocket"),"undefined"!=typeof WebSocket?s=WebSocket:"undefined"!=typeof self&&(s=self.WebSocket||self.MozWebSocket),"undefined"==typeof window)try{o=i(31)}catch(e){}var d=s||o;e.exports=n,l(n,a),n.prototype.name="websocket",n.prototype.supportsBinary=!0,n.prototype.doOpen=function(){if(this.check()){var e=this.uri(),t=this.protocols,r={agent:this.agent,perMessageDeflate:this.perMessageDeflate};r.pfx=this.pfx,r.key=this.key,r.passphrase=this.passphrase,r.cert=this.cert,r.ca=this.ca,r.ciphers=this.ciphers,r.rejectUnauthorized=this.rejectUnauthorized,this.extraHeaders&&(r.headers=this.extraHeaders),this.localAddress&&(r.localAddress=this.localAddress);try{this.ws=this.usingBrowserWebSocket&&!this.isReactNative?t?new d(e,t):new d(e):new d(e,t,r)}catch(e){return this.emit("error",e)}void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.supports&&this.ws.supports.binary?(this.supportsBinary=!0,this.ws.binaryType="nodebuffer"):this.ws.binaryType="arraybuffer",this.addEventListeners()}},n.prototype.addEventListeners=function(){var e=this;this.ws.onopen=function(){e.onOpen()},this.ws.onclose=function(){e.onClose()},this.ws.onmessage=function(t){e.onData(t.data)},this.ws.onerror=function(t){e.onError("websocket error",t)}},n.prototype.write=function(e){var r=this;this.writable=!1;for(var i=e.length,n=0,s=i;n<s;n++)!function(e){c.encodePacket(e,r.supportsBinary,(function(n){if(!r.usingBrowserWebSocket){var s={};e.options&&(s.compress=e.options.compress),r.perMessageDeflate&&("string"==typeof n?t.byteLength(n):n.length)<r.perMessageDeflate.threshold&&(s.compress=!1)}try{r.usingBrowserWebSocket?r.ws.send(n):r.ws.send(n,s)}catch(e){}--i||(r.emit("flush"),setTimeout((function(){r.writable=!0,r.emit("drain")}),0))}))}(e[n])},n.prototype.onClose=function(){a.prototype.onClose.call(this)},n.prototype.doClose=function(){void 0!==this.ws&&this.ws.close()},n.prototype.uri=function(){var e=this.query||{},t=this.secure?"wss":"ws",r="";return this.port&&("wss"===t&&443!==Number(this.port)||"ws"===t&&80!==Number(this.port))&&(r=":"+this.port),this.timestampRequests&&(e[this.timestampParam]=h()),this.supportsBinary||(e.b64=1),(e=u.encode(e)).length&&(e="?"+e),t+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+r+this.path+e},n.prototype.check=function(){return!(!d||"__initialize"in d&&this.name===n.prototype.name)}},function(e,t){},function(e,t){var r=[].indexOf;e.exports=function(e,t){if(r)return e.indexOf(t);for(var i=0;i<e.length;++i)if(e[i]===t)return i;return-1}},function(e,t,r){"use strict";function i(e,t,r){this.io=e,this.nsp=t,this.json=this,this.ids=0,this.acks={},this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0,this.flags={},r&&r.query&&(this.query=r.query),this.io.autoConnect&&this.open()}var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s=r(4),o=r(5),a=r(34),c=r(35),u=r(36),l=(r(3)("socket.io-client:socket"),r(26)),h=r(20);e.exports=i;var d={connect:1,connect_error:1,connect_timeout:1,connecting:1,disconnect:1,error:1,reconnect:1,reconnect_attempt:1,reconnect_failed:1,reconnect_error:1,reconnecting:1,ping:1,pong:1},p=o.prototype.emit;o(i.prototype),i.prototype.subEvents=function(){if(!this.subs){var e=this.io;this.subs=[c(e,"open",u(this,"onopen")),c(e,"packet",u(this,"onpacket")),c(e,"close",u(this,"onclose"))]}},i.prototype.open=i.prototype.connect=function(){return this.connected?this:(this.subEvents(),this.io.open(),"open"===this.io.readyState&&this.onopen(),this.emit("connecting"),this)},i.prototype.send=function(){var e=a(arguments);return e.unshift("message"),this.emit.apply(this,e),this},i.prototype.emit=function(e){if(d.hasOwnProperty(e))return p.apply(this,arguments),this;var t=a(arguments),r={type:(void 0!==this.flags.binary?this.flags.binary:h(t))?s.BINARY_EVENT:s.EVENT,data:t,options:{}};return r.options.compress=!this.flags||!1!==this.flags.compress,"function"==typeof t[t.length-1]&&(this.acks[this.ids]=t.pop(),r.id=this.ids++),this.connected?this.packet(r):this.sendBuffer.push(r),this.flags={},this},i.prototype.packet=function(e){e.nsp=this.nsp,this.io.packet(e)},i.prototype.onopen=function(){if("/"!==this.nsp)if(this.query){var e="object"===n(this.query)?l.encode(this.query):this.query;this.packet({type:s.CONNECT,query:e})}else this.packet({type:s.CONNECT})},i.prototype.onclose=function(e){this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",e)},i.prototype.onpacket=function(e){var t=e.nsp===this.nsp,r=e.type===s.ERROR&&"/"===e.nsp;if(t||r)switch(e.type){case s.CONNECT:this.onconnect();break;case s.EVENT:case s.BINARY_EVENT:this.onevent(e);break;case s.ACK:case s.BINARY_ACK:this.onack(e);break;case s.DISCONNECT:this.ondisconnect();break;case s.ERROR:this.emit("error",e.data)}},i.prototype.onevent=function(e){var t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?p.apply(this,t):this.receiveBuffer.push(t)},i.prototype.ack=function(e){var t=this,r=!1;return function(){if(!r){r=!0;var i=a(arguments);t.packet({type:h(i)?s.BINARY_ACK:s.ACK,id:e,data:i})}}},i.prototype.onack=function(e){var t=this.acks[e.id];"function"==typeof t&&(t.apply(this,e.data),delete this.acks[e.id])},i.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},i.prototype.emitBuffered=function(){var e;for(e=0;e<this.receiveBuffer.length;e++)p.apply(this,this.receiveBuffer[e]);for(this.receiveBuffer=[],e=0;e<this.sendBuffer.length;e++)this.packet(this.sendBuffer[e]);this.sendBuffer=[]},i.prototype.ondisconnect=function(){this.destroy(),this.onclose("io server disconnect")},i.prototype.destroy=function(){if(this.subs){for(var e=0;e<this.subs.length;e++)this.subs[e].destroy();this.subs=null}this.io.destroy(this)},i.prototype.close=i.prototype.disconnect=function(){return this.connected&&this.packet({type:s.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this},i.prototype.compress=function(e){return this.flags.compress=e,this},i.prototype.binary=function(e){return this.flags.binary=e,this}},function(e,t){e.exports=function(e,t){for(var r=[],i=(t=t||0)||0;i<e.length;i++)r[i-t]=e[i];return r}},function(e,t){"use strict";e.exports=function(e,t,r){return e.on(t,r),{destroy:function(){e.removeListener(t,r)}}}},function(e,t){var r=[].slice;e.exports=function(e,t){if("string"==typeof t&&(t=e[t]),"function"!=typeof t)throw new Error("bind() requires a function");var i=r.call(arguments,2);return function(){return t.apply(e,i.concat(r.call(arguments)))}}},function(e,t){function r(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}e.exports=r,r.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=0==(1&Math.floor(10*t))?e-r:e+r}return 0|Math.min(e,this.max)},r.prototype.reset=function(){this.attempts=0},r.prototype.setMin=function(e){this.ms=e},r.prototype.setMax=function(e){this.max=e},r.prototype.setJitter=function(e){this.jitter=e}}])}).call(this,r(26).Buffer)},function(e,t,r){"use strict";(function(e){var i=r(28),n=r(29),s=r(30);function o(){return c.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function a(e,t){if(o()<t)throw new RangeError("Invalid typed array length");return c.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=c.prototype:(null===e&&(e=new c(t)),e.length=t),e}function c(e,t,r){if(!(c.TYPED_ARRAY_SUPPORT||this instanceof c))return new c(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return h(this,e)}return u(this,e,t,r)}function u(e,t,r,i){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,i){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(i||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===i?new Uint8Array(t):void 0===i?new Uint8Array(t,r):new Uint8Array(t,r,i);c.TYPED_ARRAY_SUPPORT?(e=t).__proto__=c.prototype:e=d(e,t);return e}(e,t,r,i):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!c.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var i=0|f(t,r),n=(e=a(e,i)).write(t,r);n!==i&&(e=e.slice(0,n));return e}(e,t,r):function(e,t){if(c.isBuffer(t)){var r=0|p(t.length);return 0===(e=a(e,r)).length?e:(t.copy(e,0,0,r),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(i=t.length)!=i?a(e,0):d(e,t);if("Buffer"===t.type&&s(t.data))return d(e,t.data)}var i;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function l(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function h(e,t){if(l(t),e=a(e,t<0?0:0|p(t)),!c.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function d(e,t){var r=t.length<0?0:0|p(t.length);e=a(e,r);for(var i=0;i<r;i+=1)e[i]=255&t[i];return e}function p(e){if(e>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|e}function f(e,t){if(c.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return q(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return H(e).length;default:if(i)return q(e).length;t=(""+t).toLowerCase(),i=!0}}function g(e,t,r){var i=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return M(this,t,r);case"utf8":case"utf-8":return w(this,t,r);case"ascii":return I(this,t,r);case"latin1":case"binary":return k(this,t,r);case"base64":return E(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return L(this,t,r);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function m(e,t,r){var i=e[t];e[t]=e[r],e[r]=i}function v(e,t,r,i,n){if(0===e.length)return-1;if("string"==typeof r?(i=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(n)return-1;r=e.length-1}else if(r<0){if(!n)return-1;r=0}if("string"==typeof t&&(t=c.from(t,i)),c.isBuffer(t))return 0===t.length?-1:y(e,t,r,i,n);if("number"==typeof t)return t&=255,c.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):y(e,[t],r,i,n);throw new TypeError("val must be string, number or Buffer")}function y(e,t,r,i,n){var s,o=1,a=e.length,c=t.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return-1;o=2,a/=2,c/=2,r/=2}function u(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(n){var l=-1;for(s=r;s<a;s++)if(u(e,s)===u(t,-1===l?0:s-l)){if(-1===l&&(l=s),s-l+1===c)return l*o}else-1!==l&&(s-=s-l),l=-1}else for(r+c>a&&(r=a-c),s=r;s>=0;s--){for(var h=!0,d=0;d<c;d++)if(u(e,s+d)!==u(t,d)){h=!1;break}if(h)return s}return-1}function _(e,t,r,i){r=Number(r)||0;var n=e.length-r;i?(i=Number(i))>n&&(i=n):i=n;var s=t.length;if(s%2!=0)throw new TypeError("Invalid hex string");i>s/2&&(i=s/2);for(var o=0;o<i;++o){var a=parseInt(t.substr(2*o,2),16);if(isNaN(a))return o;e[r+o]=a}return o}function b(e,t,r,i){return V(q(t,e.length-r),e,r,i)}function S(e,t,r,i){return V(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,i)}function C(e,t,r,i){return S(e,t,r,i)}function T(e,t,r,i){return V(H(t),e,r,i)}function R(e,t,r,i){return V(function(e,t){for(var r,i,n,s=[],o=0;o<e.length&&!((t-=2)<0);++o)r=e.charCodeAt(o),i=r>>8,n=r%256,s.push(n),s.push(i);return s}(t,e.length-r),e,r,i)}function E(e,t,r){return 0===t&&r===e.length?i.fromByteArray(e):i.fromByteArray(e.slice(t,r))}function w(e,t,r){r=Math.min(e.length,r);for(var i=[],n=t;n<r;){var s,o,a,c,u=e[n],l=null,h=u>239?4:u>223?3:u>191?2:1;if(n+h<=r)switch(h){case 1:u<128&&(l=u);break;case 2:128==(192&(s=e[n+1]))&&(c=(31&u)<<6|63&s)>127&&(l=c);break;case 3:s=e[n+1],o=e[n+2],128==(192&s)&&128==(192&o)&&(c=(15&u)<<12|(63&s)<<6|63&o)>2047&&(c<55296||c>57343)&&(l=c);break;case 4:s=e[n+1],o=e[n+2],a=e[n+3],128==(192&s)&&128==(192&o)&&128==(192&a)&&(c=(15&u)<<18|(63&s)<<12|(63&o)<<6|63&a)>65535&&c<1114112&&(l=c)}null===l?(l=65533,h=1):l>65535&&(l-=65536,i.push(l>>>10&1023|55296),l=56320|1023&l),i.push(l),n+=h}return function(e){var t=e.length;if(t<=P)return String.fromCharCode.apply(String,e);var r="",i=0;for(;i<t;)r+=String.fromCharCode.apply(String,e.slice(i,i+=P));return r}(i)}t.Buffer=c,t.SlowBuffer=function(e){+e!=e&&(e=0);return c.alloc(+e)},t.INSPECT_MAX_BYTES=50,c.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=o(),c.poolSize=8192,c._augment=function(e){return e.__proto__=c.prototype,e},c.from=function(e,t,r){return u(null,e,t,r)},c.TYPED_ARRAY_SUPPORT&&(c.prototype.__proto__=Uint8Array.prototype,c.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&c[Symbol.species]===c&&Object.defineProperty(c,Symbol.species,{value:null,configurable:!0})),c.alloc=function(e,t,r){return function(e,t,r,i){return l(t),t<=0?a(e,t):void 0!==r?"string"==typeof i?a(e,t).fill(r,i):a(e,t).fill(r):a(e,t)}(null,e,t,r)},c.allocUnsafe=function(e){return h(null,e)},c.allocUnsafeSlow=function(e){return h(null,e)},c.isBuffer=function(e){return!(null==e||!e._isBuffer)},c.compare=function(e,t){if(!c.isBuffer(e)||!c.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,i=t.length,n=0,s=Math.min(r,i);n<s;++n)if(e[n]!==t[n]){r=e[n],i=t[n];break}return r<i?-1:i<r?1:0},c.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(e,t){if(!s(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return c.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var i=c.allocUnsafe(t),n=0;for(r=0;r<e.length;++r){var o=e[r];if(!c.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(i,n),n+=o.length}return i},c.byteLength=f,c.prototype._isBuffer=!0,c.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},c.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},c.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},c.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?w(this,0,e):g.apply(this,arguments)},c.prototype.equals=function(e){if(!c.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===c.compare(this,e)},c.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},c.prototype.compare=function(e,t,r,i,n){if(!c.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),t<0||r>e.length||i<0||n>this.length)throw new RangeError("out of range index");if(i>=n&&t>=r)return 0;if(i>=n)return-1;if(t>=r)return 1;if(this===e)return 0;for(var s=(n>>>=0)-(i>>>=0),o=(r>>>=0)-(t>>>=0),a=Math.min(s,o),u=this.slice(i,n),l=e.slice(t,r),h=0;h<a;++h)if(u[h]!==l[h]){s=u[h],o=l[h];break}return s<o?-1:o<s?1:0},c.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},c.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},c.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)},c.prototype.write=function(e,t,r,i){if(void 0===t)i="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)i=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===i&&(i="utf8")):(i=r,r=void 0)}var n=this.length-t;if((void 0===r||r>n)&&(r=n),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var s=!1;;)switch(i){case"hex":return _(this,e,t,r);case"utf8":case"utf-8":return b(this,e,t,r);case"ascii":return S(this,e,t,r);case"latin1":case"binary":return C(this,e,t,r);case"base64":return T(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,e,t,r);default:if(s)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),s=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var P=4096;function I(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n)i+=String.fromCharCode(127&e[n]);return i}function k(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n)i+=String.fromCharCode(e[n]);return i}function M(e,t,r){var i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);for(var n="",s=t;s<r;++s)n+=j(e[s]);return n}function L(e,t,r){for(var i=e.slice(t,r),n="",s=0;s<i.length;s+=2)n+=String.fromCharCode(i[s]+256*i[s+1]);return n}function U(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function O(e,t,r,i,n,s){if(!c.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<s)throw new RangeError('"value" argument is out of bounds');if(r+i>e.length)throw new RangeError("Index out of range")}function D(e,t,r,i){t<0&&(t=65535+t+1);for(var n=0,s=Math.min(e.length-r,2);n<s;++n)e[r+n]=(t&255<<8*(i?n:1-n))>>>8*(i?n:1-n)}function A(e,t,r,i){t<0&&(t=4294967295+t+1);for(var n=0,s=Math.min(e.length-r,4);n<s;++n)e[r+n]=t>>>8*(i?n:3-n)&255}function x(e,t,r,i,n,s){if(r+i>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function N(e,t,r,i,s){return s||x(e,0,r,4),n.write(e,t,r,i,23,4),r+4}function z(e,t,r,i,s){return s||x(e,0,r,8),n.write(e,t,r,i,52,8),r+8}c.prototype.slice=function(e,t){var r,i=this.length;if((e=~~e)<0?(e+=i)<0&&(e=0):e>i&&(e=i),(t=void 0===t?i:~~t)<0?(t+=i)<0&&(t=0):t>i&&(t=i),t<e&&(t=e),c.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=c.prototype;else{var n=t-e;r=new c(n,void 0);for(var s=0;s<n;++s)r[s]=this[s+e]}return r},c.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var i=this[e],n=1,s=0;++s<t&&(n*=256);)i+=this[e+s]*n;return i},c.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var i=this[e+--t],n=1;t>0&&(n*=256);)i+=this[e+--t]*n;return i},c.prototype.readUInt8=function(e,t){return t||U(e,1,this.length),this[e]},c.prototype.readUInt16LE=function(e,t){return t||U(e,2,this.length),this[e]|this[e+1]<<8},c.prototype.readUInt16BE=function(e,t){return t||U(e,2,this.length),this[e]<<8|this[e+1]},c.prototype.readUInt32LE=function(e,t){return t||U(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},c.prototype.readUInt32BE=function(e,t){return t||U(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},c.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var i=this[e],n=1,s=0;++s<t&&(n*=256);)i+=this[e+s]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*t)),i},c.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var i=t,n=1,s=this[e+--i];i>0&&(n*=256);)s+=this[e+--i]*n;return s>=(n*=128)&&(s-=Math.pow(2,8*t)),s},c.prototype.readInt8=function(e,t){return t||U(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},c.prototype.readInt16LE=function(e,t){t||U(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},c.prototype.readInt16BE=function(e,t){t||U(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},c.prototype.readInt32LE=function(e,t){return t||U(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},c.prototype.readInt32BE=function(e,t){return t||U(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},c.prototype.readFloatLE=function(e,t){return t||U(e,4,this.length),n.read(this,e,!0,23,4)},c.prototype.readFloatBE=function(e,t){return t||U(e,4,this.length),n.read(this,e,!1,23,4)},c.prototype.readDoubleLE=function(e,t){return t||U(e,8,this.length),n.read(this,e,!0,52,8)},c.prototype.readDoubleBE=function(e,t){return t||U(e,8,this.length),n.read(this,e,!1,52,8)},c.prototype.writeUIntLE=function(e,t,r,i){(e=+e,t|=0,r|=0,i)||O(this,e,t,r,Math.pow(2,8*r)-1,0);var n=1,s=0;for(this[t]=255&e;++s<r&&(n*=256);)this[t+s]=e/n&255;return t+r},c.prototype.writeUIntBE=function(e,t,r,i){(e=+e,t|=0,r|=0,i)||O(this,e,t,r,Math.pow(2,8*r)-1,0);var n=r-1,s=1;for(this[t+n]=255&e;--n>=0&&(s*=256);)this[t+n]=e/s&255;return t+r},c.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,255,0),c.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},c.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):D(this,e,t,!0),t+2},c.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):D(this,e,t,!1),t+2},c.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):A(this,e,t,!0),t+4},c.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):A(this,e,t,!1),t+4},c.prototype.writeIntLE=function(e,t,r,i){if(e=+e,t|=0,!i){var n=Math.pow(2,8*r-1);O(this,e,t,r,n-1,-n)}var s=0,o=1,a=0;for(this[t]=255&e;++s<r&&(o*=256);)e<0&&0===a&&0!==this[t+s-1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+r},c.prototype.writeIntBE=function(e,t,r,i){if(e=+e,t|=0,!i){var n=Math.pow(2,8*r-1);O(this,e,t,r,n-1,-n)}var s=r-1,o=1,a=0;for(this[t+s]=255&e;--s>=0&&(o*=256);)e<0&&0===a&&0!==this[t+s+1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+r},c.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,127,-128),c.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},c.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):D(this,e,t,!0),t+2},c.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):D(this,e,t,!1),t+2},c.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,2147483647,-2147483648),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):A(this,e,t,!0),t+4},c.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):A(this,e,t,!1),t+4},c.prototype.writeFloatLE=function(e,t,r){return N(this,e,t,!0,r)},c.prototype.writeFloatBE=function(e,t,r){return N(this,e,t,!1,r)},c.prototype.writeDoubleLE=function(e,t,r){return z(this,e,t,!0,r)},c.prototype.writeDoubleBE=function(e,t,r){return z(this,e,t,!1,r)},c.prototype.copy=function(e,t,r,i){if(r||(r=0),i||0===i||(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<r&&(i=r),i===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-r&&(i=e.length-t+r);var n,s=i-r;if(this===e&&r<t&&t<i)for(n=s-1;n>=0;--n)e[n+t]=this[n+r];else if(s<1e3||!c.TYPED_ARRAY_SUPPORT)for(n=0;n<s;++n)e[n+t]=this[n+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+s),t);return s},c.prototype.fill=function(e,t,r,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),1===e.length){var n=e.charCodeAt(0);n<256&&(e=n)}if(void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!c.isEncoding(i))throw new TypeError("Unknown encoding: "+i)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var s;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(s=t;s<r;++s)this[s]=e;else{var o=c.isBuffer(e)?e:q(new c(e,i).toString()),a=o.length;for(s=0;s<r-t;++s)this[s+t]=o[s%a]}return this};var B=/[^+\/0-9A-Za-z-_]/g;function j(e){return e<16?"0"+e.toString(16):e.toString(16)}function q(e,t){var r;t=t||1/0;for(var i=e.length,n=null,s=[],o=0;o<i;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!n){if(r>56319){(t-=3)>-1&&s.push(239,191,189);continue}if(o+1===i){(t-=3)>-1&&s.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&s.push(239,191,189),n=r;continue}r=65536+(n-55296<<10|r-56320)}else n&&(t-=3)>-1&&s.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;s.push(r)}else if(r<2048){if((t-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return s}function H(e){return i.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(B,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function V(e,t,r,i){for(var n=0;n<i&&!(n+r>=t.length||n>=e.length);++n)t[n+r]=e[n];return n}}).call(this,r(27))},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t,r){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],i=t[1];return 3*(r+i)/4-i},t.toByteArray=function(e){var t,r,i=u(e),o=i[0],a=i[1],c=new s(function(e,t,r){return 3*(t+r)/4-r}(0,o,a)),l=0,h=a>0?o-4:o;for(r=0;r<h;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[l++]=t>>16&255,c[l++]=t>>8&255,c[l++]=255&t;2===a&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[l++]=255&t);1===a&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[l++]=t>>8&255,c[l++]=255&t);return c},t.fromByteArray=function(e){for(var t,r=e.length,n=r%3,s=[],o=0,a=r-n;o<a;o+=16383)s.push(l(e,o,o+16383>a?a:o+16383));1===n?(t=e[r-1],s.push(i[t>>2]+i[t<<4&63]+"==")):2===n&&(t=(e[r-2]<<8)+e[r-1],s.push(i[t>>10]+i[t>>4&63]+i[t<<2&63]+"="));return s.join("")};for(var i=[],n=[],s="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,c=o.length;a<c;++a)i[a]=o[a],n[o.charCodeAt(a)]=a;function u(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function l(e,t,r){for(var n,s,o=[],a=t;a<r;a+=3)n=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),o.push(i[(s=n)>>18&63]+i[s>>12&63]+i[s>>6&63]+i[63&s]);return o.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,r,i,n){var s,o,a=8*n-i-1,c=(1<<a)-1,u=c>>1,l=-7,h=r?n-1:0,d=r?-1:1,p=e[t+h];for(h+=d,s=p&(1<<-l)-1,p>>=-l,l+=a;l>0;s=256*s+e[t+h],h+=d,l-=8);for(o=s&(1<<-l)-1,s>>=-l,l+=i;l>0;o=256*o+e[t+h],h+=d,l-=8);if(0===s)s=1-u;else{if(s===c)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,i),s-=u}return(p?-1:1)*o*Math.pow(2,s-i)},t.write=function(e,t,r,i,n,s){var o,a,c,u=8*s-n-1,l=(1<<u)-1,h=l>>1,d=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,p=i?0:s-1,f=i?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(c=Math.pow(2,-o))<1&&(o--,c*=2),(t+=o+h>=1?d/c:d*Math.pow(2,1-h))*c>=2&&(o++,c/=2),o+h>=l?(a=0,o=l):o+h>=1?(a=(t*c-1)*Math.pow(2,n),o+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,n),o=0));n>=8;e[r+p]=255&a,p+=f,a/=256,n-=8);for(o=o<<n|a,u+=n;u>0;e[r+p]=255&o,p+=f,o/=256,u-=8);e[r+p-f]|=128*g}},function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},function(e,t,r){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},n=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},s=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}};Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e){this.config=e,this._config=e,console.log("configconfigconfig:::",this._config)}return e.prototype.getEdgeList=function(e){return n(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return[4,fetch(this._config.CENTERURL+"/api/v1/join",{method:"POST",body:JSON.stringify(i({app_id:this._config.APPID,client_ip:this._config.CLIENTIP,proto:this._config.PROTO,role:e.role||"",room_id:e.roomId||"",user_source:this._config.USERSOURCE,user_id:e.userId||""},this._config.ipInfo)),headers:{"Content-Type":"application/json"}})];case 1:return[4,t.sent().json().then(this.unwrap)];case 2:return[2,t.sent().edge_urls]}}))}))},e.prototype.unwrap=function(e){return n(this,void 0,void 0,(function(){return s(this,(function(t){return 200!==e.code?[2,Promise.reject(e)]:[2,e.data]}))}))},e}();t.default=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e){this._audio=!0,this._video=!0,this._width=544,this._height=720,this._frameRate=16,this._bitRateStart=900,this._bitRateMin=600,this._bitRateMax=1800,this._videoQuality=0,this._audio=e.audio,this._video=Boolean(e.video),void 0!==e.width&&(this._width=e.width),void 0!==e.height&&(this._height=e.height),void 0!==e.frameRate&&(this._frameRate=e.frameRate),void 0!==e.bitRate&&(this._bitRateStart=this._bitRateMin=this._bitRateMax=e.bitRate),void 0!==e.videoQuality&&(this._videoQuality=e.videoQuality)}return e.prototype.transformBitRate=function(){return{bitRateStart:this._bitRateStart,bitRateMax:this._bitRateMax,bitRateMin:this._bitRateMin}},e.prototype.transformConstraints=function(){var e={};if(this._videoQuality)switch(this._videoQuality){case 1:e={width:320,height:240,frameRate:15};break;case 2:e={width:640,height:480,frameRate:15};break;case 3:e={width:1280,height:720,frameRate:20};break;case 4:e={width:this._width,height:this._height,frameRate:this._frameRate}}else e=this._video;return{audio:this._audio,video:e}},e}();t.default=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e){this._cameras=[],this._microphones=[],this._speakers=[];for(var t=0,r=e;t<r.length;t++){var i=r[t];this.append(i)}}return Object.defineProperty(e.prototype,"cameras",{get:function(){return this._cameras},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"microphones",{get:function(){return this._microphones},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"speakers",{get:function(){return this._speakers},enumerable:!0,configurable:!0}),e.prototype.append=function(e){switch(e.kind){case"videoinput":this._cameras.push(e);break;case"audioinput":this._microphones.push(e);break;case"audiooutput":this._speakers.push(e)}},e}();t.default=i},function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var n=i(r(1)),s=function(){function e(e){this._roomId="",this._role=0,this._appId="",this._appId=e.appId,void 0!==e&&(void 0!==e.roomId&&(this._roomId=e.roomId),void 0!==e.role&&(this._role=e.role)),this.init()}return e.prototype.init=function(){var e=this;window.addEventListener("unload",(function(t){1==e._role&&navigator.sendBeacon(n.default.VENDORURL+"/api/v1/update_room",JSON.stringify({room_id:e._roomId,app_id:e._appId,status:"3"}))}),!1)},e}();t.default=s},function(e,t,r){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},n=this&&this.__awaiter||function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))},s=this&&this.__generator||function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=(n=o.trys).length>0&&n[n.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}};Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e){this.config=e,this._config=e,console.log("configconfigconfig:::",this._config)}return e.prototype.getLocalInfo=function(){return n(this,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,fetch(this._config.IPURL)];case 1:return[2,e.sent().json()]}}))}))},e.prototype.getVendor=function(e){return n(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return[4,fetch(this._config.VENDORURL+"/api/v1/get_vendor",{method:"POST",body:JSON.stringify(i(i({},e),{app_id:e.appId}))})];case 1:return[2,t.sent().json().then(this.unwrap)]}}))}))},e.prototype.unwrap=function(e){return n(this,void 0,void 0,(function(){return s(this,(function(t){return 200!==e.code?[2,Promise.reject(e)]:[2,e.data]}))}))},e.prototype.updateRoom=function(e){return n(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return[4,fetch(this._config.VENDORURL+"/api/v1/update_room",{body:JSON.stringify(i(i({},e),{app_id:e.appId,room_id:e.roomId})),method:"POST"})];case 1:return[2,t.sent().json().then(this.unwrap)]}}))}))},e}();t.default=o}]).default}));