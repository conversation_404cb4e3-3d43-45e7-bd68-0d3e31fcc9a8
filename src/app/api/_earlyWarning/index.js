import request from '@utils/request'

//获取任务和状态列表
export const getTaskStateList = (data) => {
  return request('/api/control/alarm_task_list', {
    method: 'get',
    data,
  })
}

//获取通知组列表
export const getNoticerGroupList = (data) => {
  return request('/api/control/alarm_user_group_list', {
    method: 'get',
    data: {
      keyword: data.keyword,
      page_num: data.page_num,
      page_size: data.page_size,
    },
  })
}

//添加通知组
export const addNoticerGroup = (data) => {
  return request('/api/control/alarm_user_group_add', {
    method: 'post',
    data: data,
  })
}

//修改通知组
export const modifyNoticerGroup = (data) => {
  return request('/api/control/alarm_user_group_modify', {
    method: 'post',
    data: data,
  })
}

//删除通知组
export const delNoticerGroup = (data) => {
  return request('/api/control/alarm_user_group_delete', {
    method: 'post',
    data: data,
  })
}

//获取已添加预警列表
export const getWarningList = (data) => {
  return request('/api/control/alarm_setting_list', {
    method: 'get',
    data: data,
  })
}

//添加预警
export const addWarning = (data) => {
  return request('/api/control/alarm_setting_add', {
    method: 'post',
    data: data,
  })
}

//修改预警
export const modifyWarning = (data) => {
  return request('/api/control/alarm_setting_modify', {
    method: 'post',
    data: data,
  })
}

//删除预警
export const delWarning = (data) => {
  return request('/api/control/alarm_setting_delete', {
    method: 'post',
    data: data,
  })
}

//获取预警历史
export const getWarningHistory = (data) => {
  return request('/api/control/alarm_history_list', {
    method: 'get',
    data: data,
  })
}

//获取选中机器人所有的地点
export const getRobotMapInfo = (data) => {
  return request('/api/control/robot_map_pkg_info', {
    method: 'get',
    data: data,
  })
}

//日报订制
//获取已添加日报列表
export const getDailyAddedList = (data) => {
  return request('/api/control/report_setting_list', {
    method: 'get',
    data: data,
  })
}

//添加日报
export const addDaily = (data) => {
  return request('/api/control/report_setting_add', {
    method: 'post',
    data: data,
  })
}

//修改日报
export const modifyDaily = (data) => {
  return request('/api/control/report_setting_modify', {
    method: 'post',
    data: data,
  })
}

//删除日报
export const deleteDaily = (data) => {
  return request('/api/control/report_setting_delete', {
    method: 'post',
    data: data,
  })
}

//查询日报历史记录
export const getDailyHistoryList = (data) => {
  return request('/api/control/report_history_list', {
    method: 'get',
    data: data,
  })
}
