import { GET_GUIDE_OBJ } from '@redux/action-types'

const initialState = {
  // question:[],
  way: [
    {
      qaFlag: true,
      palyFlag: true,
      station: null, //每一个站点的名称
      question: [''], //问答
      leadVoice: null, //过渡地点引领声音
      playList: [],
      Introduction: null, //讲解声音
    },
  ], //路线

  mJobSequence: [
    {
      palyFlag: true, // 0关，1开
      qaFlag: true, // 0关，1开
      stationSwitch: true,
      wResoureType: 'tts',
      mEnterParam: '', //引领到_
      mCoverTitle: '', //标题
      mCoverImage: '', //封面图片_
      mFeatureType: '默认值', //兼容字段_
      mCoverImgIndexQ: '',
      mCoverImgNameQ: '',
      //兼容字段_
      mErrorResponse: [
        {
          mErrorCondition: 1,
          //"注释":"启动播放资源",
          mPlayRes: [
            {
              mResType: 'image',
              mPlayRes: [],
              mDurations: [0],
              mLoopTimes: 0,
              mWaitResId: '-1',
            },
          ],
          mResponse: 200,
        },
      ],
      //主播放资源_ "注释":"抵达播放资源",
      mCompleteResList: [
        // {
        //     "mResType": "image",
        //     "mPlayRes": ["t1/image1.jpg"],
        //     "mDurations": [0],  //兼容字段_
        //     "mLoopTimes": 0,   //兼容字段_
        //     "mWaitResId": "0"  //兼容字段_
        // },
        // {
        //     "mResType": "image",
        //     "mPlayRes": ["t1/image1.jpg"],
        //     "mDurations": [0],  //兼容字段_
        //     "mLoopTimes": 0,   //兼容字段_
        //     "mWaitResId": "0"   //兼容字段_
        // }
      ],
      //副资源_ 播放列表
      mExtendRes: {
        mTimeOut: '30000', //资源列表等候时间_
        mIsQa: '1', //问答开关 0关，1开
        //产品默认
        buttoninfos: [
          {
            type: '2',
            title: '前往下个点',
            image: '/module_public/module_guide/guide_next_icon.png',
            cmds: 'button_next_place',
          },
          {
            type: '2',
            title: '结束',
            image: '/module_public/module_guide/guide_over_icon.png',
            cmds: 'button_stop',
          },
        ],

        mListResInfo: [
          {
            mCoverTitle: '', //封面内容_
            mCoverImage: '', //封面图片_
            mCoverType: 'image', //视频是video,其他都是image
            mCoverImgNameQ: '',
            mCoverImgIndexQ: '',
            //点击之后的播放效果_  "注释":"抵达播放资源", "注释":"任务结束待命位置点播放资源_默认值",
            mCompleteResList: [
              // {
              //     "mResType": "text",
              //     "mPlayRes": ["我是文本资源"],
              //     "mDurations": [0],
              //     "mLoopTimes": 0,
              //     "mWaitResId": "0"
              // },
              // {
              //     "mResType": "text",
              //     "mPlayRes": ["我是文本资源"],
              //     "mDurations": [0],
              //     "mLoopTimes": 0,
              //     "mWaitResId": "0"
              // }
            ],
          },
        ],
      },
      //问答
      mInterruptOption: [
        {
          mScene: 'emoji', //引领走路
          buttoninfos: [
            {
              type: '1',
              title: '暂停导览',
              title2: '恢复导览',
              image: '/module_public/module_guide/guide_pause1_btn.png',
              // "image2": "/module_public/module_guide/guide_play_btn.png",
              image2: '/module_public/module_guide/guide_leading_pause.png',
              cmds: 'button_leading_pause',
            },
            {
              type: '2',
              title: '结束',
              image: '/module_public/module_guide/guide_over_icon.png',
              cmds: 'button_stop',
            },
          ],
        },
        {
          mScene: 'play', //播放资源时候
          buttoninfos: [
            {
              type: '2',
              title: '前往下个点',
              image: '/module_public/module_guide/guide_next_icon.png',
              cmds: 'button_next_place',
            },
            {
              type: '2',
              title: '播放列表',
              image: '/module_public/module_guide/guide_playline_icon.png',
              cmds: 'button_extend_res',
            },
            // {
            //     "type": "2",
            //     "title": "自由问答",
            //     "image": "/module_public/module_guide/guide_interlocution_icon.png",
            //     "cmds": "button_tell_me_why"
            // },
            {
              type: '2',
              title: '结束',
              image: '/module_public/module_guide/guide_over_icon.png',
              cmds: 'button_stop',
            },
          ],
        },
      ],
      //前往下一地点过渡语_  "注释":"启动播放资源",  "注释":"启动播放资源",
      mPreResList: [
        //去除
        // {
        //     "mResType": "image",
        //     "mPlayRes": ["t1/image1.jpg"],
        //     "mDurations": [0],
        //     "mLoopTimes": 0,
        //     "mWaitResId": "0"
        // },
        // {
        //     "mResType": "image",
        //     "mPlayRes": ["t1/image1.jpg"],
        //     "mDurations": [0],
        //     "mLoopTimes": 0,
        //     "mWaitResId": "0"
        // }
      ],
      mButtonInfo: [
        {
          mBtnTitle: '按钮名字',
          mAction: '字符标注',
        },
      ],
      //问答
      mQA: {
        mStartCmd: [' '],
      },
    },
  ],

  initmJobSequence: [
    {
      palyFlag: true,
      qaFlag: true,
      stationSwitch: true,
      wResoureType: 'tts',
      mEnterParam: '', //引领到_
      mCoverTitle: '', //封面名称_
      // "mCoverImage": "",	 //封面图片_
      mCoverImage: null, //封面图片_
      mFeatureType: '默认值', //兼容字段_
      mCoverImgNameQ: '',
      mCoverImgIndexQ: '',
      //兼容字段_
      mErrorResponse: [
        {
          mErrorCondition: 1,
          //"注释":"启动播放资源",
          mPlayRes: [
            {
              mResType: 'image',
              mPlayRes: [],
              mDurations: [0],
              mLoopTimes: 0,
              mWaitResId: '-1',
            },
          ],
          mResponse: 200,
        },
      ],
      //主播放资源_ "注释":"抵达播放资源",
      mCompleteResList: [
        // {
        //     "mResType": "image",
        //     "mPlayRes": ["t1/image1.jpg"],
        //     "mDurations": [0],  //兼容字段_
        //     "mLoopTimes": 0,   //兼容字段_
        //     "mWaitResId": "0"  //兼容字段_
        // },
        // {
        //     "mResType": "image",
        //     "mPlayRes": ["t1/image1.jpg"],
        //     "mDurations": [0],  //兼容字段_
        //     "mLoopTimes": 0,   //兼容字段_
        //     "mWaitResId": "0"   //兼容字段_
        // }
      ],
      //副资源_ 播放列表
      mExtendRes: {
        mTimeOut: '30000', //资源列表等候时间_
        mIsQa: '1', //问答开关 0关，1开
        //产品默认
        buttoninfos: [
          {
            type: '2',
            title: '前往下个点',
            image: '/module_public/module_guide/guide_next_icon.png',
            cmds: 'button_next_place',
          },
          {
            type: '2',
            title: '结束',
            image: '/module_public/module_guide/guide_over_icon.png',
            cmds: 'button_stop',
          },
        ],

        mListResInfo: [
          {
            mCoverTitle: '', //封面内容_
            mCoverImage: '', //封面图片_
            mCoverType: 'image', //视频是video,其他都是image
            mCoverImgNameQ: '',
            mCoverImgIndexQ: '',
            //点击之后的播放效果_  "注释":"抵达播放资源", "注释":"任务结束待命位置点播放资源_默认值",
            mCompleteResList: [
              // {
              //     "mResType": "text",
              //     "mPlayRes": ["我是文本资源"],
              //     "mDurations": [0],
              //     "mLoopTimes": 0,
              //     "mWaitResId": "0"
              // },
              // {
              //     "mResType": "text",
              //     "mPlayRes": ["我是文本资源"],
              //     "mDurations": [0],
              //     "mLoopTimes": 0,
              //     "mWaitResId": "0"
              // }
            ],
          },
        ],
      },
      //问答
      mInterruptOption: [
        {
          mScene: 'emoji', //引领走路
          buttoninfos: [
            {
              type: '1',
              title: '暂停导览',
              title2: '恢复导览',
              image: '/module_public/module_guide/guide_pause1_btn.png',
              // "image2": "/module_public/module_guide/guide_play_btn.png",
              image2: '/module_public/module_guide/guide_leading_pause.png',
              cmds: 'button_leading_pause',
            },
            {
              type: '2',
              title: '结束',
              image: '/module_public/module_guide/guide_over_icon.png',
              cmds: 'button_stop',
            },
          ],
        },
        {
          mScene: 'play',
          buttoninfos: [
            {
              type: '2',
              title: '前往下个点',
              image: '/module_public/module_guide/guide_next_icon.png',
              cmds: 'button_next_place',
            },
            {
              type: '2',
              title: '播放列表',
              image: '/module_public/module_guide/guide_playline_icon.png',
              cmds: 'button_extend_res',
            },
            // {
            //     "type": "2",
            //     "title": "自由问答",
            //     "image": "/module_public/module_guide/guide_interlocution_icon.png",
            //     "cmds": "button_tell_me_why"
            // },
            {
              type: '2',
              title: '结束',
              image: '/module_public/module_guide/guide_over_icon.png',
              cmds: 'button_stop',
            },
          ],
        },
      ],
      //前往下一地点过渡语_  "注释":"启动播放资源",  "注释":"启动播放资源",
      mPreResList: [
        //去除
        // {
        //     "mResType": "image",
        //     "mPlayRes": ["t1/image1.jpg"],
        //     "mDurations": [0],
        //     "mLoopTimes": 0,
        //     "mWaitResId": "0"
        // },
        // {
        //     "mResType": "image",
        //     "mPlayRes": ["t1/image1.jpg"],
        //     "mDurations": [0],
        //     "mLoopTimes": 0,
        //     "mWaitResId": "0"
        // }
      ],
      mButtonInfo: [
        {
          mBtnTitle: '按钮名字',
          mAction: '字符标注',
        },
      ],
      //问答
      mQA: {
        mStartCmd: [' '],
      },
    },
  ],

  routeStr: [], //['第一站休息值'，‘第二站卫生间’]
  route: [], //引领路线
  routeVoiceSrc: null, //路线播报声音
  routerSize: 0,
  startCommand: null, //听到的声音
  end: null, //结束地点
  mapId: null, //地图id
  robotidArray: [],
  defineRouteStr: '',
}

export default function (state = initialState, { type, ...obj }) {
  switch (type) {
    /*
        case SAMPLE: {
            return Object.assign({}, state, data)
        }
        */
    case GET_GUIDE_OBJ: {
      return Object.assign({}, state, obj.data)
    }
  }

  return state
}
