import request from '@utils/request'
//同步信息ota2
export const postGuideInfo = (data) => {
  return request('/api/control/update_save_module_config', {
    method: 'post',
    data: data,
  })
}
//同步信息ota1
export const postGuideInfoVOnes = (data) => {
  return request('/api/control/save_module_config', {
    method: 'post',
    data: data,
  })
}

export const uploadResource = (data) => {
  return request('/api/control/upload_config_resource', {
    method: 'post',
    data: data,
  })
}
//获取地图列表(ota1,ota2)
export const getMap = () => {
  return request('/api/control/org_map', {
    method: 'get',
  })
}
//获取地图列表(ota1,ota2)
export const getNewMap = () => {
  return request('/api/control/corp_map_pkg_list', {
    method: 'get',
  })
}
//获取引导路线
export const getRoute = (data) => {
  return request('/api/control/map_info', {
    method: 'get',
    data: data,
  })
}
//获取引导路线
export const getNewRoute = (data) => {
  return request('/api/control/map_pkg_info', {
    method: 'get',
    data: data,
  })
}
//获得详情
export const getModuleDetail = (data) => {
  console.log('详情- 5')
  return request('/api/control/module_config_detail', {
    method: 'post',
    data,
  })
}

//保存并发布给机器人（单个导览发布到多个机器人）
export const saveandPublish = (data) => {
  console.log('api save_and_publish_module5')
  return request('/api/control/save_and_publish_module', {
    method: 'post',
    data: data,
  })
}
