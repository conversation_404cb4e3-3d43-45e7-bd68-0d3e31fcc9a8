import {} from '@redux/action-types'

import request from '@utils/request'
/**
 * Redux action: 示例
 * @param {String} arg1
 */
// export const sample = (arg1) => dispatch => dispatch({
//     type: INIT_ROBOTS,
//     data: arg1
// })

//设为超级管理员
export const SuperAdmin = (roleId, uuid, gender) => {
  const formData = new FormData()
  formData.append('user', JSON.stringify({ role_id: roleId }))
  formData.append('user_uuid', uuid)
  // if(gender != undefined && gender != ''){
  //     formData.append('gender',gender)
  // }
  return request('/capi/v1/corp/modify_user', {
    method: 'post',
    data: formData,
  })
}

export const editUserData = (data) => {
  return request('/capi/v1/corp/modify_user', {
    method: 'post',
    data: data,
  })
}

//获取搜索列表
export const getList = (data) => {
  return request('/api/user/company_visitor_search', {
    method: 'post',
    data,
  })
}

//获取用户详情
export const getUserInfo = (uuid) => {
  const formData = new FormData()
  // formData.append('user', JSON.stringify({ "role_id": roleId ,"user_uuid":uuid}))
  formData.append('user_uuid', uuid)
  return request('/capi/v1/corp/user_profile', {
    method: 'post',
    data: formData,
  })
}

//添加管理员
export const addUser = (data) => {
  return request('/capi/v1/corp/add_user', {
    method: 'post',
    data: data,
  })
}
