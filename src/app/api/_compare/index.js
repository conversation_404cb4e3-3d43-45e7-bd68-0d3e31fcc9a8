import request from '@utils/request'
//获取词库
export const getCpExclusiveLexicon = (data) => {
  return request('/api/control/exclusive_vocabulary', {
    method: 'get',
    data,
  })
}
//新增词库
export const addCpExclusiveLexicon = (data) => {
  return request('/api/control/exclusive_vocabulary', {
    method: 'post',
    data: data,
  })
}
//删除词条
export const delVocabulary = (data) => {
  return request('/api/control/del_vocabulary', {
    method: 'post',
    data: data,
  })
}
//编辑词条
export const editVocabulary = (data) => {
  return request('/api/control/edit_vocabulary', {
    method: 'post',
    data: data,
  })
}

//导入
export const uploadVocabularyFile = (data) => {
  return request('/api/control/excel_in_vocabulary', {
    method: 'POST',
    data,
  })
}
export const downLoadExcel = (data) => {
  return request('/api/control/excel_in_vocabulary', {
    method: 'GET',
    data,
  })
}
