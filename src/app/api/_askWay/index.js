import request from '@utils/request'
import {} from '@redux/action-types'

//问路

export const askWayList = (data) => {
  return request('/api/control/ask_way_config_list', {
    method: 'GET',
    data,
  })
}
//新建
export const askWayAdd = (data) => {
  return request('/api/control/ask_way_config_add', {
    method: 'POST',
    data,
  })
}
//编辑

export const askWayModify = (data) => {
  return request('/api/control/ask_way_config_modify', {
    method: 'POST',
    data,
  })
}
//删除

export const askWayDelete = (data) => {
  return request('/api/control/ask_way_config_delete', {
    method: 'POST',
    data,
  })
}
//引领中是否等人
export const askWayFlag = (data) => {
  return request('/api/control/corp_config', {
    method: 'POST',
    data,
  })
}
export const getAskWayFlag = (data) => {
  return request('/api/control/corp_config', {
    method: 'GET',
    data,
  })
}

//批量上传
export const uploadExcel = (data) => {
  return request('/api/control/ask_way_config_list_add', {
    method: 'POST',
    data,
  })
}
//批量上传
export const downLoadExcel = (data) => {
  return request('/api/control/ask_way_config_list_add', {
    method: 'GET',
    data,
  })
}
//new
//获取企业级地点
export const getSiteList = (data) => {
  return request('/api/control/in_use_site_name/list', {
    method: 'GET',
    data,
  })
}

//多语言配置
//获取企业语言
export const getLangList = (data) => {
  return request('/api/control/lang/list', {
    method: 'GET',
    data,
  })
}
//获取机器人地点列表
export const getRobotSiteList = (data) => {
  return request('/api/control/robot/site/list', {
    method: 'GET',
    data,
  })
}
//保存地点信息
export const robotSiteUpdate = (data) => {
  return request('/api/control/robot/site/update', {
    method: 'POST',
    data,
  })
}

//发布
export const publish = (data) => {
  return request('/api/control/robot/site/publish', {
    method: 'POST',
    data,
  })
}
//获取发布状态
export const getPublishStatus = (data) => {
  return request('/api/control/robot/site/check_status', {
    method: 'GET',
    data,
  })
}
