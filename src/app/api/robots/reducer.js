import {
  INIT_ROBOTS,
  SELECT_ROBOT,
  UPDATE_ROBOT_DATA,
  GET_ROBOT_STATISTIC,
  GET_ROBOT_PROFILE,
  INIT_ROBOTS_DATA,
} from '@redux/action-types'

const initialState = {
  // 所有机器人的 ID
  list: [],

  // 按 ID 存储的机器人数据
  robots: {},

  // 当前机器人 ID
  current: undefined,
  fetchStatus: false,
}

export default function (state = initialState, { type, ...obj }) {
  switch (type) {
    case INIT_ROBOTS_DATA: {
      return Object.assign({}, initialState)
    }
    case INIT_ROBOTS: {
      let ids = []
      for (let i in obj.robots) {
        const robotId = obj.robots[i].robot_id
        ids.push(robotId)
      }
      return Object.assign({}, state, {
        robots: obj.robots,
        current: obj.id || ids[0],
        fetchStatus: true,
      })
    }

    case SELECT_ROBOT: {
      if (!obj.id) return state
      return Object.assign({}, state, {
        current: obj.id,
      })
    }

    case UPDATE_ROBOT_DATA: {
      if (!obj.id) return state
      if (typeof obj.data !== 'object') return state
      return Object.assign({}, state, {
        robots: Object.assign({}, state.robots, {
          [obj.id]: Object.assign({}, state.robots[obj.id], obj.data),
        }),
      })
    }

    case GET_ROBOT_STATISTIC: {
      return Object.assign({}, state, obj.data)
    }

    case GET_ROBOT_PROFILE: {
      return Object.assign({}, state, obj.data)
    }
  }

  return state
}
