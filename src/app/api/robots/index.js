import {
  INIT_ROBOTS,
  SELECT_ROBOT,
  UPDATE_ROBOT_DATA,
  GET_ROBOT_STATISTIC,
  GET_ROBOT_PROFILE,
} from '@redux/action-types'
import request from '@utils/request'
/**
 * Redux action: 初始化所有机器人信息
 * @param {Array} list 所有机器人信息
 */
export const initRobots = (robots, id) => (dispatch) =>
  dispatch({
    type: INIT_ROBOTS,
    robots,
    id,
  })

/**
 * 按 ID 切换机器人
 * @param {String} id 目标机器人ID
 */
export const selectRobot = (id) => (dispatch) =>
  dispatch({
    type: SELECT_ROBOT,
    id,
  })

/**
 * 按 ID 更新机器人状态 / 信息
 * @param {String} id 目标机器人ID
 * @param {Object} data 要修改的状态 / 信息
 */
export const updateRobot = (id, data) => (dispatch) =>
  dispatch({
    type: UPDATE_ROBOT_DATA,
    id,
    data,
  })

export const getRobotProfile = (robotId) => (dispatch) => {
  return request('/capi/v1/corp/robot_profile', {
    method: 'GET',
    data: {
      robot_uuid: robotId,
    },
  }).then((res) => {
    dispatch({
      type: GET_ROBOT_PROFILE,
      data: {
        profile: res.robot,
      },
    })
  })
}

export const getRobotStatistics = (robotId) => (dispatch) => {
  if (robotId) {
    request('/api/control/robot_statistics', {
      method: 'GET',
      data: {
        robot_id: robotId,
      },
    }).then((res) => {
      dispatch({
        type: GET_ROBOT_STATISTIC,
        data: {
          statistics: res,
        },
      })
    })
  }
}
//sn后6位绑定验证码
export const bindRobotByCode = (data) => {
  return request('/capi/v1/corp/bind_robot_by_code', {
    method: 'get',
    data: data,
  })
}

//首页显示机器人的一些大数据透传的详情
export const getRobotBigData = (data) => {
  return request('/api/statistics/robot_big_data', {
    method: 'get',
    data: data,
  })
}

//获取机器人列表
export const getRobotList = () => {
  return request('/api/control/robot_list', {
    method: 'post',
  })
}

//获取机器人列表
export const getRobotOpkList = (data) => {
  return request('/capi/v1/corp/robot_list', {
    method: 'post',
    data,
  })
}

//更改机器人角色
export const setRobotMode = (data) => {
  return request('/api/control/set_robot_mode', {
    method: 'post',
    data: data,
  })
}

//获取机器人列表
export const getAllRobotList = (data) => {
  return request('/capi/v1/corp/robot_list', {
    method: 'get',
    data,
  })
}
