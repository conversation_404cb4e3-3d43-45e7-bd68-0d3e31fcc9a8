import request from '@utils/request'
import { GET_READING_GROUP_LIST } from '@redux/action-types'

//问答改版
//获取
export const getList = (data) => {
  return request('/api/control/mrc', {
    method: 'GET',
    data,
  })
}
export const search = (data) => {
  return request('/api/control/mrc_search', {
    method: 'GET',
    data,
  })
}

//分类列表
export const groupsSearch = (data) => (dispatch) => {
  //ok
  return request('/api/control/mrc_groups', {
    method: 'GET',
    data,
  }).then((res) => {
    dispatch({
      type: GET_READING_GROUP_LIST,
      groups: res.data.data,
    })
    return res
  })
}

export const groupsAdd = (data) => {
  return request('/api/control/mrc_groups', {
    //ok
    method: 'POST',
    data,
  })
}
export const groupsEditName = (data) => {
  return request('/api/control/edit_mrc_groups', {
    //ok
    method: 'POST',
    data,
  })
}
export const groupsDel = (data) => {
  return request('/api/control/del_mrc_groups', {
    //ok
    method: 'POST',
    data,
  })
}

//添加 文章
export const add = (data) => {
  return request('/api/control/mrc', {
    method: 'POST',
    data,
  })
}
//编辑 文章
export const edit = (data) => {
  return request('/api/control/mrc_edit', {
    method: 'POST',
    data,
  })
}
//详情
export const info = (data) => {
  return request('/api/control/mrc_info', {
    method: 'GET',
    data,
  })
}
//测试
export const test = (data) => {
  return request('/api/control/mrc_test', {
    method: 'POST',
    data,
  })
}

//删除
export const del = (data) => {
  return request('/api/control/del_mrc', {
    method: 'POST',
    data,
  })
}

//生成问答
export const getQa = (data) => {
  return request('/api/control/mrc_qa', {
    method: 'GET',
    data,
  })
}

export const addQa = (data) => {
  return request('/api/control/mrc_qa', {
    method: 'POST',
    data,
  })
}
