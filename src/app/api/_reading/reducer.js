import { GET_READING_GROUP_LIST, SET_READING_CURRENT } from '@redux/action-types'

const initialState = {
  groups: {},
  current: null,
}

export default function (state = initialState, { type, ...data }) {
  switch (type) {
    case GET_READING_GROUP_LIST: {
      return Object.assign({}, state, data)
    }
    case SET_READING_CURRENT: {
      return Object.assign({}, state, data)
    }
  }
  return state
}
