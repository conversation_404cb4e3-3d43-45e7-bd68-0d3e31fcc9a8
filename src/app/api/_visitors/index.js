// import {
//     // INIT_LIST
// } from '@redux/action-types'

import {} from '@redux/action-types'
// import getStore from '@utils/store'
import request from '@utils/request'
/**
 * Redux action: 示例
 * @param {String} arg1
 */
// export const sample = (arg1) => dispatch => dispatch({
//     type: INIT_ROBOTS,
//     data: arg1
// })

//获取访客数据列表
export const getVisitorListData = () => {
  return request('/api/user/company_visitor_list', {
    method: 'GET',
    data: {
      page: 1,
      size: 12,
    },
  })
}

//获取访客搜索列表
export const getVisitorSearchList = (data) => {
  return request('/api/user/company_visitor_search', {
    method: 'post',
    data,
  })
}

//获得数量
export const getList = (data) => {
  return request('/capi/v1/corp/user_list', {
    method: 'get',
    data,
  })
}

//删除访客记录
export const getDelVisitor = (data) => {
  return request('/api/user/company_visitor_del', {
    method: 'post',
    data,
  })
}

//获取访客详情
export const getVisitorInfo = (id) => {
  return request('/api/user/visit_info', {
    method: 'get',
    data: {
      task_id: id,
    },
  })
}

//提交访客详情编辑的表单
export const postEditVisitorInfo = (id) => {
  return request('/api/user/visit_info', {
    method: 'get',
    data: {
      task_id: id,
    },
  })
}

//员工获取搜索列表

//删除用户
export const getDelUser = (data) => {
  return request('/capi/v1/corp/delete_user', {
    method: 'post',
    data,
  })
}

//添加单个员工
export const addUserData = (data) => {
  return request('/capi/v1/corp/add_user', {
    method: 'post',
    data,
  })
}

//获取员工筛选条件的信息
export const filtrateData = () => {
  return request('/capi/v1/corp/user_stat', {
    method: 'get',
    query: {
      filter_role_id: [5, 6, 9],
    },
  })
}

//获取访客已来访，待来访人数
export const filtrateState = () => {
  return request('/api/user/company_visitor_count_list', {
    method: 'get',
  })
}
//编辑访客信息
export const editVisitorInfo = (data) => {
  return request('/api/user/invite_modify', {
    method: 'post',
    data: data,
  })
}
//获取
export const argumentList = (data) => {
  return request('/api/user/visitor_base_info', {
    method: 'get',
  })
}

//访客照片上传
// export const postPic = (data) => {
//     return request('/api/user/visitor_images', {
//         method: 'post',
//         data
//     })
// }
