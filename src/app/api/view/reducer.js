import { INIT_VIEW, UPDATE_VIEW } from '@redux/action-types'

const initialState = {}

export default function (state = initialState, { type, ...obj }) {
  const { id, data = {} } = obj
  switch (type) {
    case INIT_VIEW: {
      return Object.assign({}, state, {
        [id]: data,
      })
    }

    case UPDATE_VIEW: {
      return Object.assign({}, state, {
        [id]: Object.assign({}, state[id], data),
      })
    }
  }

  return state
}
