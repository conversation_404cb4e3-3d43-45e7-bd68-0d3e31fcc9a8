import { INIT_VIEW, UPDATE_VIEW } from '@redux/action-types'
import getStore from '@utils/store'

/**************************************
 * Redux actions
 *************************************/

/**
 * 初始化 page view 数据
 * @param {string} id ID
 * @param {Object} initialData 数据
 */
export const init = (
  id,
  initialData, //dispatch =>
) =>
  getStore().dispatch({
    type: INIT_VIEW,
    id,
    data: initialData,
  })

/**
 * 更新 page view 数据
 * @param {string} id ID
 * @param {Object} newData 新数据
 */
export const update = (
  id,
  newData, //dispatch =>
) =>
  getStore().dispatch({
    type: UPDATE_VIEW,
    id,
    data: newData,
  })
