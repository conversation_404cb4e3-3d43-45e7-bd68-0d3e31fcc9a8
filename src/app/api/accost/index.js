import request from '@utils/request'

export const uploadResource = (data) => {
  return request('/api/control/upload_config_resource', {
    method: 'post',
    data: data,
  })
}

//获得详情
export const exposurNum = (data) => {
  // return request('/api/control/ride_count_list', {
  return request('/api/control/recommend_count_list', {
    method: 'get',
    data: data,
  })
}

//发布 模块
export const publishModule = (data) => {
  return request('/api/control/publish_module', {
    method: 'post',
    data,
  })
}

//同步保存信息
export const postGuideInfo = (data) => {
  return request('/api/control/save_module_config', {
    method: 'post',
    data: data,
  })
}

//同步信息ota2（分包）
export const postAdvertInfo = (data) => {
  return request('/api/control/update_save_module_config', {
    method: 'post',
    data: data,
  })
}

//保存并发布给机器人（单个导览发布到多个机器人）
export const saveandPublish = (data) => {
  console.log('api save_and_publish_module9')
  return request('/api/control/save_and_publish_module', {
    method: 'post',
    data: data,
  })
}
