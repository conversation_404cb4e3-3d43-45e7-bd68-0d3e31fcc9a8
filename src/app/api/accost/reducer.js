import { GET_ADVERT_OBJ } from '@redux/action-types'

const initialState = {
  // advert:{
  //     "mTaskName":"",
  //     "adverts": [
  //         {
  //             "res_mode": "", //1、图片加tts 2、图片加语音 3、视频
  //             "title": "",
  //             "voice_url": "",
  //             "image_url": "",
  //             "video_url": "",
  //             "img_name": "",
  //             "voice_name": "",
  //             "video_name":"",
  //             "voice_str": ""
  //         }
  //     ]
  // },
  advert: {
    version: 'v4.2',
    mTaskName: null,
    adverts: [
      {
        res_mode: '1', //1、图片加tts 2、图片加语音 3、视频
        id: null,
        voice_url: null,
        image_url: null,
        image_urls: [],
        video_url: null,
        trace_code: null,
        voice_str: null,
      },
    ],
    times_before_rest: null, //休息前循环播报次数
    minuts_for_rest: null, //休息时间长度
    task_start_time: null, //开始时间
    task_end_time: null, //结束时间
    turn_body: null, //0,120,180,360
  },
  initAdvert: {
    version: 'v4.2',
    mTaskName: null,
    adverts: [
      {
        res_mode: '1', //1、图片加tts 2、图片加语音 3、视频
        id: null,
        voice_url: null,
        image_url: null,
        image_urls: [],
        video_url: null,
        trace_code: null,
        voice_str: null,
      },
    ],
    times_before_rest: 1, //休息前循环播报次数
    minutes_for_rest: 5, //休息时间长度
    task_start_time: '00:00', //开始时间
    task_end_time: '23:59', //结束时间
    turn_body: 0, //0不转动,120小幅度,180大幅度,360整圈转
  },
  // initAdvert:{
  //     "mTaskName":null,
  //     "adverts": [
  //         {
  //             "res_mode": 1, //1、图片加tts 2、图片加语音 3、视频
  //             "title": "",
  //             "voice_url": "",
  //             "image_url": "",
  //             "video_url": "",
  //             "img_name": "",
  //             "voice_name": "",
  //             "video_name":"",
  //             "voice_str": ""
  //         }
  //     ]
  // },
  robotidArray: [],
  robotObj: [],
}

export default function (state = initialState, { type, ...obj }) {
  switch (type) {
    /*
        case SAMPLE: {
            return Object.assign({}, state, data)
        }
        */
    case GET_ADVERT_OBJ: {
      return Object.assign({}, state, obj.data)
    }
  }

  return state
}
