import {
  GET_TTS_MODULE,
  ADD_TTSMODULE,
  REMOVE_TTSMODULE,
  ADD_CONTENTLIST,
  EDIT_CONTENTLIST,
  REMOVE_CONTENTLIST,
  CHANGE_TTS_SORT,
} from '@redux/action-types'

export default function (state = {}, { type, ...obj }) {
  switch (type) {
    case GET_TTS_MODULE: {
      let ttsModule = JSON.parse(JSON.stringify(obj.data.ttsModule))
      return Object.assign({}, state, obj.data, { ttsModule: ttsModule })
    }
    case REMOVE_TTSMODULE: {
      let newTTS = []
      state.ttsModule.map((item, index) => {
        if (index != obj.data.index) {
          newTTS.push(item)
        }
      })
      return Object.assign([], state, { ttsModule: Object.assign([], newTTS) })
    }
    case ADD_TTSMODULE: {
      let newTTS = []
      let json = {}
      json['type'] = obj.data.type
      json['contentList'] = obj.data.contentList
      console.log(json)
      state.ttsModule.map((item, index) => {
        newTTS.push(item)
      })
      newTTS.push(json)
      return Object.assign([], state, { ttsModule: Object.assign([], state.ttsModule, newTTS) })
    }
    case ADD_CONTENTLIST: {
      //console.log(obj.data)
      let newTTS = []
      state.ttsModule.map((item, index) => {
        if (index == obj.data.index) {
          item.contentList.unshift(obj.data.content)
          newTTS.push(item)
        } else {
          newTTS.push(item)
        }
      })
      //console.log(newTTS)
      return Object.assign([], state, { ttsModule: Object.assign([], state.ttsModule, newTTS) })
    }
    case EDIT_CONTENTLIST: {
      console.log(obj.data)
      let newTTS = []
      state.ttsModule.map((item, index) => {
        if (index == obj.data.index) {
          item.contentList[obj.data.contentIndex] = obj.data.content
          newTTS.push(item)
        } else {
          newTTS.push(item)
        }
      })
      console.log(newTTS)
      return Object.assign([], state, { ttsModule: Object.assign([], state.ttsModule, newTTS) })
    }
    case REMOVE_CONTENTLIST: {
      console.log(obj.data)
      let newTTS = []
      state.ttsModule.map((item, index) => {
        if (index == obj.data.index) {
          item.contentList.splice(obj.data.contentIndex, 1)
          newTTS.push(item)
        } else {
          newTTS.push(item)
        }
      })
      console.log(newTTS)
      return Object.assign([], state, { ttsModule: Object.assign([], state.ttsModule, newTTS) })
    }

    case CHANGE_TTS_SORT: {
      console.log('第', obj.data.index, '组的拖动前后位置', obj.data.dragBefore, '=>', obj.data.dragAfter)
      let newTTS = []
      let newContentList = [],
        moveItem = {},
        moveVar = null,
        moveId = 0
      state.ttsModule.map((item, index) => {
        if (index == obj.data.index) {
          moveItem['type'] = item.type
          item.contentList.map((el, indexs) => {
            if (indexs != obj.data.dragBefore) {
              newContentList.push(el)
            } else {
              moveVar = el
            }
          })
          newContentList.splice(obj.data.dragAfter, 0, moveVar)
          moveItem['contentList'] = newContentList
          newTTS.push(moveItem)
        } else {
          newTTS.push(item)
        }
      })
      return Object.assign([], state, { ttsModule: Object.assign([], state.ttsModule, newTTS) })
    }

    default:
      return state
  }

  return state
}
