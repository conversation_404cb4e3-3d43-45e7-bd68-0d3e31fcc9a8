import request from '@utils/request'

export const getModuleConfigList = (data) => {
  return request('/api/screen/robot_quick_answer_config', {
    method: 'post',
    data: data,
  })
}

export const saveModuleConfigList = (data) => {
  return request('/api/screen/save_quick_answer_config', {
    method: 'post',
    data: data,
  })
}

export const saveMsgLog = (data) => {
  return request('/api/control/manual_intervention_record', {
    method: 'post',
    data: data,
  })
}
export const getMsgLog = (data) => {
  return request('/api/control/manual_intervention_record', {
    method: 'get',
    ext: 'download',
    data: data,
  })
}
