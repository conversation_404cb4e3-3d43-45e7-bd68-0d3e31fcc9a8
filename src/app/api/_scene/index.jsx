import request from '@utils/request'
import { RESTORE_LOGIN, RESET_LOGIN, UPDATE_USER_PROFILE } from '@redux/action-types'

/**************************************
 * Redux actions
 *************************************/

// TODO: promise login
// export const login = (data) => dispatch => dispatch({
//     type: USER_LOGIN,
//     data,
// })

export const getScene = (data) => {
  return request('/api/control/module_config_list', {
    method: 'post',
    data,
  })
}

export const publishModule = (data) => {
  return request('/api/control/publish_module', {
    method: 'post',
    data,
  })
}

export const setModule = (data) => {
  return request('/api/control/set_config_status', {
    method: 'post',
    data,
  })
}

export const deleteModule = (data) => {
  return request('/api/control/set_config_status', {
    method: 'post',
    data,
  })
}

// /api/control/set_module_pkg_status
export const setPkgModule = (data) => {
  return request('/api/control/set_module_pkg_status', {
    method: 'post',
    data,
  })
}

// /api/control/direct_publish_module
export const directPublishModule = (data) => {
  return request('/api/control/direct_publish_module', {
    method: 'post',
    data,
  })
}
