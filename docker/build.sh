#!/bin/bash
cd ../
if [ -d "./dist" ];then
	rm -rf ./dist
fi
npm run build
cd docker

REPO=10.60.242.17:5000
CONTAINER=pc-web

TAG=4.20.1



# 2. add commit info
CURRENT_BRANCH=`git symbolic-ref --short -q HEAD`
COMMIT_LOG_FILE=../dist/commit.html
# git tag >$COMMIT_LOG_FILE
echo "commit:" >$COMMIT_LOG_FILE
git log -1 --pretty=format:"%H" >>$COMMIT_LOG_FILE
echo "<br/>" >>$COMMIT_LOG_FILE
echo "branch:" $CURRENT_BRANCH >>$COMMIT_LOG_FILE
echo "<br/> date:" >>$COMMIT_LOG_FILE
date >>$COMMIT_LOG_FILE


# 3. build docker
DOCKER_IMAGE=$REPO/$CONTAINER:$TAG${CURRENT_BRANCH:0:1}
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BUILDROOT=$DIR/..

# 4. Build docker
cmd="docker build \
-t $DOCKER_IMAGE \
-f Dockerfile $BUILDROOT"
echo $cmd
eval $cmd

echo -n "You want auto push docker(y/n)"
read C

if [ "$C" == "y" ];then
cmd2="docker push $DOCKER_IMAGE"
echo $cmd2
eval $cmd2
fi

