#!/bin/bash
# NOTE:
# DO NOT touch anything outside <EDIT_ME></EDIT_ME>,
# unless you really know what you are doing.



REPO=10.60.242.17:5000
# The name of the container, should use the name of the repo is possible
# <EDIT_ME>


# </EDIT_ME>
# TAG=$(git rev-parse --short HEAD)
TAG=1.0.93r



CONTAINER=pc-web
DOCKER_IMAGE=$REPO/$CONTAINER:$TAG


# Load env file
source $1
if [ "$?" -ne 0 ];then
echo "Erorr, can't open envfile: $1"
echo "Usage: $0 <envfile> nginx.conf"
echo "e.g., $0 dev.env"
else
echo "# Using envfile: $envfile"
fi

# global config:
# - use local timezone
# - max memory = 5G
# - restart = always
#-v /etc/localtime:/etc/localtime \
globalConf="

-m 5125m \
--restart always \
"


""
# Get runroot
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Compose module config
moduleConf="
  -p $RES_PORT:80 \
  -v $DIR/../logs:/var/log/nginx/ \
"

# -v $DIR/$2:/etc/nginx/conf.d \
#  -v $DIR/../dist:/www \
# </EDIT_ME>

docker rm -f -v $CONTAINER
cmd="docker run -d --name $CONTAINER \
$globalConf \
$moduleConf \
$DOCKER_IMAGE \
"
echo $cmd
eval $cmd
